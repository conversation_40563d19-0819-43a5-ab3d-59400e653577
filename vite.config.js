import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue2'
import jsx from '@vitejs/plugin-vue2-jsx'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { resolve } from 'path'
import { createHtmlPlugin } from 'vite-plugin-html'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isProduction = command === 'build'

  return {
    root: process.cwd(),
    base: '/',
    plugins: [
      vue(),
      jsx(),
      createSvgIconsPlugin({
        iconDirs: [resolve(process.cwd(), 'src/assets/icons/svg')],
        symbolId: 'icon-[name]',
        inject: 'body-last',
        customDomId: '__svg__icons__dom__'
      }),
      createHtmlPlugin({
        inject: {
          data: {
            title: env.VITE_APP_TITLE || '推广服务管理系统'
          }
        }
      })
    ],

    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        'vue': 'vue/dist/vue.esm.js'
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          outputStyle: 'expanded'
        }
      },
      // 确保不覆盖 postcss.config.js
      // postcss: {
      //   plugins: [
      //     // ...
      //   ]
      // }
    },

    server: {
      host: '0.0.0.0',
      port: parseInt(env.VITE_APP_PORT) || 80,
      open: false,
      proxy: {
        [env.VITE_APP_BASE_API]: {
          // target: `http://************:36012/prod-api`,
          // target: 'http://**************:9080',
          target: 'http://************:36035/prod-api',
          // target: 'https://fans-test.pangdasc.com/prod-api',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_APP_BASE_API}`), '')
        }
      }
    },

    build: {
      outDir: 'dist',
      assetsDir: 'static',
      sourcemap: false,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction
        }
      },
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            'chunk-libs': ['vue', 'vue-router', 'vuex', 'axios'],
            'chunk-elementUI': ['element-ui'],
            'chunk-commons': ['js-cookie', 'nprogress']
          }
        }
      },
      chunkSizeWarningLimit: 1500
    },

    define: {
      'process.env': env
    },

    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'vuex',
        'axios',
        'element-ui',
        'js-cookie',
        'nprogress',
      ]
    },

    esbuild: {
      loader: 'jsx',
      include: /src\/.*\.[jt]sx?$/,
      exclude: []
    }
  }
})
