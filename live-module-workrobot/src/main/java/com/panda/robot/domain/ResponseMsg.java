
/**  
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：ResponseMsg.java <br>
 * Package：com.panda.robot.domin <br> 
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2025年6月8日 上午10:40:03 <br>
 * @version v1.0 <br>
 */ 
package com.panda.robot.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**   
 * ClassName：com.panda.robot.domin.ResponseMsg <br>
 * Description：返回消息内容 <br>
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2025年6月8日 上午10:40:03 <br>
 * @version v1.0 <br>  
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class ResponseMsg {

	/** 指令类型 */
	private String type;
	/** 昵称,群名/备注 */
	private String searchText;
	/** 消息内容 */
	private String message;
	/** 任务ID */
	private String taskId;
	
	private String link;
    
    private String title;
    
    private String desc;
    
    private String imgUrl;
    
    /** 文件名 */
    private String fileName;
    /** 文件扩展名 */
    private String fileExtension;
	
}
