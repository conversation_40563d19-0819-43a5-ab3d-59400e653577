
/**  
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：MessageBuilder.java <br>
 * Package：com.panda.robot.util <br> 
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2025年6月8日 下午5:08:28 <br>
 * @version v1.0 <br>
 */ 
package com.panda.robot.util;

import java.util.List;

import com.panda.robot.domain.dto.Content;
import com.panda.robot.domain.dto.Message;
import com.panda.robot.domain.dto.MsgContent;
import com.panda.robot.enums.CommandType;

/**   
 * ClassName：com.panda.robot.util.MessageBuilder <br>
 * Description：Message构建器 <br>
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2025年6月8日 下午5:08:28 <br>
 * @version v1.0 <br>  
 */
public class MessageBuilder {

	/**
	 * Description：基于参数构建Message对象) <br>
	 * author：罗江林 <br>
	 * date：2025年6月9日 下午9:52:58 <br>
	 * @param robotId 机器人ID
	 * @param recipient 接收者
	 * @param content 消息内容
	 * @return <br>
	 */
	public static Message builder(String robotId, String recipient, String content) { 
		return builder(robotId, null, recipient, content);
	}
	
	/**
	 * Description：基于参数构建Message对象 <br>
	 * author：罗江林 <br>
	 * date：2025年6月9日 下午9:53:36 <br>
	 * @param robotId 机器人ID
	 * @param type 指令类型{@code CommandType}
	 * @param recipient 接收者
	 * @param content 消息内容
	 * @return <br>
	 */
	public static Message builder(String robotId, CommandType type, String recipient, String content) {
		return builder(robotId, buildContent(type, recipient, content));
	}
	
	/**
	 * Description：基于MsgContent对象和robotId构建单个消息的Message <br>
	 * author：罗江林 <br>
	 * date：2025年6月9日 下午9:52:17 <br>
	 * @param robotId 
	 * @param ctn 消息对象
	 * @return <br>
	 */
	public static Message builder(String robotId, Content ctn) {
		Message msg = Message.builder().robotId(robotId).command(ctn.getType()).build();
		msg.addContent(ctn); 
		return msg;
	}
	
	public static Message builderFile(String robotId, List<Content> contents) {
        Message msg = Message.builder().robotId(robotId).command(CommandType.FILE).build();
        addContents(msg, contents);
        return msg;
    }
	
	public static Message builderLink(String robotId, List<Content> contents) {
        Message msg = Message.builder().robotId(robotId).command(CommandType.LINK).build();
        addContents(msg, contents);
        return msg;
    }
	
	/**
	 * Description：基于MsgContent对象和robotId构建多个消息(可多类型)的Message，多类型消息里使用 WeComRobotUtil.sendMix() <br>
	 * author：罗江林 <br>
	 * date：2025年6月9日 下午9:55:01 <br>
	 * @param robotId 机器人ID
	 * @param contents 消息内容集
	 * @return <br>
	 */
	public static Message builder(String robotId, List<Content> contents, Integer limit) {
		Message msg = Message.builder().command(CommandType.MIX).robotId(robotId).limit(limit).build();
		addContents(msg, contents);
		return msg;
	} 
	
	private static void addContents(Message msg, List<Content> contents) {
	    if(contents == null || contents.size() <= 0) {
	        return;
	    }
	    contents.forEach(c -> {
	        msg.addContent(c);
	    });
	}
	
	public static MsgContent buildContent(String recipient, String content) {
		return MsgContent.builder().recipient(recipient).msg(content).build();
	}
	
	public static MsgContent buildContent(CommandType type, String recipient, String content) {
		return MsgContent.builder().type(type).recipient(recipient).msg(content).build();
	}
	
}
