<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'
import { useLiveRoom } from '@/composables/useLiveRoom'
import { useIMStore } from '@/stores/im'

interface Props {
  roomId: string
  maxMessages?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxMessages: 100,
})

const emit = defineEmits<{
  messageSend: [content: string]
  likeSend: []
}>()

const imStore = useIMStore()
const { canSendMessage, sendComment, sendLike: sendLikeAction, roomInfo } = useLiveRoom()

const inputText = ref('')
const messageListRef = ref<HTMLElement>()

// 计算属性
const displayMessages = computed(() =>
  imStore.commentMessages.slice(-props.maxMessages),
)

const onlineCount = computed(() => imStore.onlineCount)
const likeCount = computed(() => imStore.likeCount)
const roomTitle = computed(() => roomInfo.value?.title || '直播间')

// 工具方法
function formatTime(timestamp: number) {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

const nameColors = [
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399',
  '#606266',
  '#303133',
  '#1890ff',
]

function getSenderColor(name: string): string {
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  return nameColors[Math.abs(hash) % nameColors.length]
}

function getMessageClasses(message: any) {
  return {
    'self-message': message.isSelf,
    'anchor-message': message.isAnchor,
    'system-message': message.type === 'system',
  }
}

// 方法
async function sendMessage() {
  const content = inputText.value.trim()
  if (!content || !canSendMessage.value)
    return

  try {
    await sendComment(content)
    inputText.value = ''
    emit('messageSend', content)
  }
  catch (error) {
    console.error('发送消息失败:', error)
  }
}

async function sendLike() {
  if (!canSendMessage.value)
    return

  try {
    await sendLikeAction()
    emit('likeSend')
  }
  catch (error) {
    console.error('发送点赞失败:', error)
  }
}

function scrollToBottom() {
  if (messageListRef.value) {
    nextTick(() => {
      messageListRef.value!.scrollTop = messageListRef.value!.scrollHeight
    })
  }
}

// 监听消息变化
watch(() => imStore.messages.length, scrollToBottom)
</script>

<template>
  <div class="desktop-chat-box">
    <!-- 房间信息栏 -->
    <div class="room-info">
      <h3>{{ roomTitle }}</h3>
      <div class="stats">
        <span class="stat-item">在线: {{ onlineCount }}</span>
        <span class="stat-item">点赞: {{ likeCount }}</span>
      </div>
    </div>

    <!-- 消息列表 -->
    <div ref="messageListRef" class="message-list">
      <div class="welcome-message">
        <div class="alert-box">
          <strong>欢迎来到直播间</strong>
          <p>请遵守直播间规则，文明互动</p>
        </div>
      </div>

      <div
        v-for="message in displayMessages"
        :key="message.id"
        class="message-item"
        :class="getMessageClasses(message)"
      >
        <div class="message-header">
          <span class="sender-name" :style="{ color: getSenderColor(message.sender.name) }">
            {{ message.sender.name }}
          </span>
          <span v-if="message.isSelf" class="user-tag self-tag">我</span>
          <span v-if="message.isAnchor" class="user-tag anchor-tag">主播</span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
        </div>
        <div class="message-content">{{ message.content }}</div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-section">
      <textarea
        v-model="inputText"
        placeholder="说点什么..."
        :disabled="!canSendMessage"
        :maxlength="200"
        rows="2"
        class="message-textarea"
        @keyup.ctrl.enter="sendMessage"
      />

      <div class="input-actions">
        <div class="action-buttons">
          <button
            class="primary-button"
            :disabled="!canSendMessage || !inputText.trim()"
            @click="sendMessage"
          >
            发送 (Ctrl+Enter)
          </button>

          <button
            class="like-button"
            :disabled="!canSendMessage"
            @click="sendLike"
          >
            👍 点赞
          </button>
        </div>

        <div class="char-count">
          {{ inputText.length }}/200
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.desktop-chat-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.room-info {
  padding: 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;

  h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #303133;
  }

  .stats {
    display: flex;
    gap: 16px;
  }

  .stat-item {
    font-size: 12px;
    color: #909399;

    &::before {
      content: '•';
      margin-right: 4px;
      color: #409EFF;
    }
  }
}

.message-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
}

.welcome-message {
  margin-bottom: 16px;

  .alert-box {
    background: #e1f3ff;
    border: 1px solid #409EFF;
    border-left: 4px solid #409EFF;
    padding: 12px 16px;
    border-radius: 4px;

    strong {
      color: #409EFF;
      display: block;
      margin-bottom: 4px;
    }

    p {
      margin: 0;
      font-size: 12px;
      color: #606266;
    }
  }
}

.message-item {
  margin-bottom: 12px;

  &.system-message {
    text-align: center;

    .message-content {
      background: #f0f2f5;
      color: #909399;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 12px;
      display: inline-block;
    }

    .message-header {
      display: none;
    }
  }

  &.anchor-message {
    .sender-name {
      font-weight: bold;
    }
  }
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.sender-name {
  font-weight: 500;
  font-size: 14px;
}

.user-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: bold;

  &.self-tag {
    background: #409EFF;
    color: white;
  }

  &.anchor-tag {
    background: #F56C6C;
    color: white;
  }
}

.message-time {
  font-size: 11px;
  color: #909399;
  margin-left: auto;
}

.message-content {
  font-size: 14px;
  line-height: 1.4;
  color: #303133;
  padding-left: 4px;
  border-left: 3px solid #f0f0f0;
  word-wrap: break-word;
}

.input-section {
  padding: 16px;
  border-top: 1px solid #ebeef5;
  background: #fafafa;
}

.message-textarea {
  width: 100%;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  font-family: inherit;
  resize: none;
  outline: none;

  &:focus {
    border-color: #409EFF;
  }

  &:disabled {
    background: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
  }

  &::placeholder {
    color: #c0c4cc;
  }
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.primary-button {
  background: #409EFF;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:hover:not(:disabled) {
    background: #66b1ff;
  }

  &:disabled {
    background: #c0c4cc;
    cursor: not-allowed;
  }
}

.like-button {
  background: #E6A23C;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:hover:not(:disabled) {
    background: #ebb563;
  }

  &:disabled {
    background: #c0c4cc;
    cursor: not-allowed;
  }
}

.char-count {
  font-size: 12px;
  color: #909399;
}
</style>
