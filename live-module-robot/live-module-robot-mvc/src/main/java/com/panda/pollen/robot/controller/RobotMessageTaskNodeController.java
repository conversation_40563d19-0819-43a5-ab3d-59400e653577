package com.panda.pollen.robot.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.robot.converter.MessageTaskNodeDOConvert;
import com.panda.pollen.robot.dto.RobotMessageTaskNodeDTO;
import com.panda.pollen.robot.entity.RobotMessageTaskNodeDO;
import com.panda.pollen.robot.service.RobotMessageTaskNodeService;
import com.panda.pollen.robot.service.RobotMessageTaskService;
import com.panda.pollen.robot.vo.RobotMessageTaskNodeVO;
import com.panda.pollen.robot.vo.TaskNodeComboVO;

import lombok.extern.slf4j.Slf4j;

/**   
 * Robot消息任务节点接口
 * ClassName：com.panda.pollen.robot.controller.RobotMessageTaskNodeController <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月24日 10:46:17 <br>
 * @version v1.0 <br>  
 */
@Slf4j
@RestController
@RequestMapping("/robot/task_node")
public class RobotMessageTaskNodeController extends BaseController{

    @Autowired
    private RobotMessageTaskNodeService taskNodeService;
    @Autowired
    private RobotMessageTaskService taskService;
    
    /**
     * <p> 根据任务ID获取任务节点 <p/>
     * <AUTHOR> 
     * @date 2025年7月1日 15:43:27 
     * @param data 
     * @return void 
     */
    @GetMapping(value = "/get")
    public AjaxResultV2<List<TaskNodeComboVO>> getTaskNodesByTaskId(RobotMessageTaskNodeDTO p) {
        return AjaxResultV2.success(taskNodeService.getTaskNodesByTaskId(p));
    }
    
    /**
     * <p> 加载任务节点详细 <p/>
     * <AUTHOR> 
     * @date 2025年7月3日 10:33:33 
     * @param p
     * @return AjaxResultV2<RobotMessageTaskNodeVO> 
     */
    @GetMapping(value = "/{id}")
    public AjaxResultV2<RobotMessageTaskNodeVO> getTaskNodeStat(@NotNull @PathVariable Long id){
    	RobotMessageTaskNodeDO node = taskNodeService.getById(id);
    	return AjaxResultV2.success(MessageTaskNodeDOConvert.INSTANCE.toVO(node));
    }
    
    /**
     * <p> 根据任务节点ID取消任务节点<p/>
     * <AUTHOR> 
     * @date 2025年7月8日 14:58:14 
     * @param nodeId 任务节点ID
     * @return AjaxResultV2<RobotMessageTaskNodeVO> 
     * @throws
     */
    @PostMapping(value = "/cancel")
    public AjaxResult cancelTaskNode(@NotNull Long nodeId){
    	if(!taskService.cancelTaskNode(nodeId)) {
    		throw new ServiceException("任务节点取消操作失败");
    	}
    	return success();
    }
    
}
