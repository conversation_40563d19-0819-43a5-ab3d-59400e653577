package com.panda.pollen.robot.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.panda.pollen.common.annotation.Log;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxPageResult;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.common.enums.BusinessType;
import com.panda.pollen.common.utils.poi.ExcelUtil;
import com.panda.pollen.robot.app.RobotMessageDepositoryApplication;
import com.panda.pollen.robot.dto.RobotMessageDetailsDTO;
import com.panda.pollen.robot.param.MessageDepositoryParam;
import com.panda.pollen.robot.service.RobotMessageDepositoryService;
import com.panda.pollen.robot.service.RobotMessageDetailsService;
import com.panda.pollen.robot.task.dto.TaskParamDTO;
import com.panda.pollen.robot.vo.MessageDepositoryGroupPageVO;
import com.panda.pollen.robot.vo.MessageDepositoryPageVO;
import com.panda.pollen.robot.vo.RobotMessageDetailsVO;

/**
 * 消息仓库接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/robot/depository")
public class RobotMessageDepositoryController extends BaseController {

    @Autowired
    private RobotMessageDepositoryService robotMessageDepositoryService;
    @Autowired
    private RobotMessageDepositoryApplication depositoryApplication;
    @Autowired
    private RobotMessageDetailsService messageDetailService;

    /**
     * <p> 任务发送客户列表，权限字符（robot:message:clist） <p/>
     * <AUTHOR> 
     * @date 2025年7月1日 16:12:46 
     * @param dep 查询参数
     * @return TableDataInfo<?> 
     * @throws
     */
    @PreAuthorize("@ss.hasPermi('robot:message:clist')")
    @GetMapping("/customer_list")
    public AjaxPageResult<MessageDepositoryPageVO> custimerPage(@Valid MessageDepositoryParam dep) {
        startPage();
        List<MessageDepositoryPageVO> list = robotMessageDepositoryService.getPageList(dep);
        return AjaxPageResult.build(list);
    }
    
    /**
     * <p> 任务发送社群列表，权限字符（robot:message:glist） <p/>
     * <AUTHOR> 
     * @date 2025年7月1日 16:12:40 
     * @param dep 查询参数
     * @return TableDataInfo<?> 
     * @throws
     */
    @PreAuthorize("@ss.hasPermi('robot:message:glist')")
    @GetMapping("/group_list")
    public AjaxPageResult<MessageDepositoryGroupPageVO> groupPage(@Valid MessageDepositoryParam dep) {
        startPage();
        List<MessageDepositoryGroupPageVO> list = robotMessageDepositoryService.getGroupPageList(dep);
        return AjaxPageResult.build(list);
    }
    
    /**
     * <p> 任务消息库定时任务执行接口 <p/>
     * <AUTHOR> 
     * @date 2025年7月3日 15:00:11 
     * @return AjaxResult 
     * @throws
     */
    @GetMapping("/run_send")
    public AjaxResult executeRobotSend(TaskParamDTO param) {
    	depositoryApplication.executeRobotSend(param.getStart(), param.getMode());
    	return success();
    }

    /**
     * 导出消息仓库列表
     */
    @PreAuthorize("@ss.hasPermi('robot:message:export')")
    @Log(title = "消息仓库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MessageDepositoryParam dep) {
        List<MessageDepositoryPageVO> list = robotMessageDepositoryService.getPageList(dep);
        ExcelUtil<MessageDepositoryPageVO> util = new ExcelUtil<MessageDepositoryPageVO>(MessageDepositoryPageVO.class);
        util.exportExcel(response, list, "消息仓库数据");
    }

    /**
     * 获取消息仓库详细信息
     */
    @PreAuthorize("@ss.hasPermi('robot:message:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(robotMessageDepositoryService.getById(id));
    }
    
    /**
     * <p> 任务节点消息列表（robot:message:list）<p/>
     * <AUTHOR> 
     * @date 2025年7月26日 上午10:59:00 
     * @param param
     * @return AjaxPageResult<RobotMessageDetailsVO> 
     * @throws
     */
    @PreAuthorize("@ss.hasPermi('robot:message:list')")
    @GetMapping("/task_message_list")
    public AjaxPageResult<RobotMessageDetailsVO> taskMessage(@Valid RobotMessageDetailsDTO param) {
        startPage();
        List<RobotMessageDetailsVO> list = messageDetailService.getPageList(param);
        return AjaxPageResult.build(list);
    }

    /**
     * 新增消息仓库
     */
    /*@PreAuthorize("@ss.hasPermi('robot:message:add')")
    @Log(title = "消息仓库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RobotMessageDepositoryDTO dep) {
        RobotMessageDepository md = MessageDepositoryAssembler.INSTANCE.fromDTO(dep);
        return toAjax(md.save());
    }*/

    /**
     * 修改消息仓库
     */
    /*@PreAuthorize("@ss.hasPermi('robot:message:edit')")
    @Log(title = "消息仓库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RobotMessageDepositoryDTO dep) {
        return toAjax(robotMessageDepositoryService.updateById(MessageDepositoryDOConvert.INSTANCE.toDO(dep)));
    }*/

    /**
     * 删除消息仓库
     */
    /*@PreAuthorize("@ss.hasPermi('robot:message:remove')")
    @Log(title = "消息仓库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(robotMessageDepositoryService.removeByIds(Arrays.asList(ids)));
    }*/
    
}
