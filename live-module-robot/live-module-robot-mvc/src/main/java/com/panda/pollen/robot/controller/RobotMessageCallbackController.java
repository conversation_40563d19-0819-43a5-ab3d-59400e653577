package com.panda.pollen.robot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.panda.pollen.common.annotation.Anonymous;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.robot.consts.RobotConst;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**   
 * Robot消息回调
 * ClassName：com.panda.pollen.robot.controller.RobotMessageCallbackController <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月24日 10:46:17 <br>
 * @version v1.0 <br>  
 */
@Slf4j
@Anonymous
@RestController
//@RequestMapping("/live/robot")
public class RobotMessageCallbackController extends BaseController{

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;
    
    /**
     * Robot消息执行回调接口 <br/> 
     * <AUTHOR> luojl <br/> 
     * @Date :  2025年6月30日 16:59:17 <br/>
     * @param data <br/> 
     * @return: void <br/> 
     * @throws
     */
//    @PostMapping(value = "/callback")
    public AjaxResult pushRobotMessageCallback(@RequestBody Object data) {
        try {
            log.info("【callback】---->>> {}", data);
            String jsonData = JSON.toJSONString(data);
            JSONObject obj = JSON.parseObject(jsonData);
            String robotId = obj.getString("robotId");
            if(StrUtil.isNotEmpty(robotId)) {
            	pushToKafka(robotId, jsonData); 
            }
        } catch (Exception e) {
           log.error("【RobotCallback-Error】---->>{}", e);
        }
        return success();
    } 
    
    private void pushToKafka(String key, String data) {
        ListenableFuture<SendResult<String, Object>> future = kafkaTemplate.send(RobotConst.ROBOT_CALLBACK_TOPIC, key, data);
        future.addCallback(success -> {
            String topic = success.getRecordMetadata().topic();
            int partition = success.getRecordMetadata().partition();
            long offset = success.getRecordMetadata().offset();
            log.info("->>>>>>>>>-生产者 Robot回调成功：topic:{},partition:{},offset:{}", topic, partition, offset);
        }, failure -> log.error("->>>>>>>>>-生产者 Robot回调消息失败：{}", failure.getMessage()));
    }
    
}
