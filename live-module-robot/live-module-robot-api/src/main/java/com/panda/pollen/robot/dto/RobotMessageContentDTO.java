package com.panda.pollen.robot.dto;

import java.util.List;

import com.panda.pollen.robot.enums.ContentType;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息内容DTO对象 
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "消息内容DTO", value = "RobotMessageContentDTO")
public class RobotMessageContentDTO {

    /** 主键ID */
    private Long id;
    /** 任务ID */
    private Long taskId;
    /** 内容序号 */
    private Integer serialNumber;
    /** 内容类型 {@link ContentType}*/
    private ContentType contentType;
    /** 链接标题 */
    private String title;
    /** 消息内容 */
    private String msgContent;
    /** 链接地址 */
    private String linkUrl;
    /** 图片地址 */
    private String imgUrl;
    /** 文件名 */
    private String fileName;
    /** 文件扩展名 */
    private String fileExtension;
	/** 消息任务ID集 */
	private List<Long> taskIds;
    
}
