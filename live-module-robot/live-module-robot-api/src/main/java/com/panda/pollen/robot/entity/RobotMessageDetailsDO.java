package com.panda.pollen.robot.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.panda.pollen.common.base.BaseEntityV2;
import com.panda.pollen.robot.enums.ContentType;
import com.panda.pollen.robot.enums.MsgExecuteStatus;
import com.panda.pollen.robot.handler.ContentTypeHandler;
import com.panda.pollen.robot.handler.MsgExecuteStatusHandler;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Robot消息详情Entity对象 live_robot_message_details
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "live_robot_message_details")
public class RobotMessageDetailsDO extends BaseEntityV2 {

    private static final long serialVersionUID = 1L;

    /** 消息任务ID */
    private Long taskId;
    /** 任务节点ID */
    private Long nodeId;
    /** 接收者编码 */
    private String receiveCode;
    
    /** RobotID */
    private String robotId;
    /** 员工账号 */
    private String wecomUserId;
    /** 客户ID */
    private String customerId;
    
    /** 客户/群昵称 */
    private String receiveCustomer;
    
    /** 消息类型 */
    @TableField(typeHandler = ContentTypeHandler.class)
    private ContentType msgType;
    
    /** 消息内容 */
    private String msgContent;
    
    /** robot平台任务ID */
    private String robotTaskId;
    
    /** robot执行状态,0-执行中、1-成功、-1-失败 */
    @TableField(typeHandler = MsgExecuteStatusHandler.class)
    private MsgExecuteStatus execStatus;
    
    /** 备注 */
    private String remark;
    
    @TableField(exist = false)
    private String updateBy;
     
}
