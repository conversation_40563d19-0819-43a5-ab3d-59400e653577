export const LeftOutlineSvg = () => (
  <svg
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 32 32"
    fill="currentColor"
  >
    <path d="M10 16l10-10 1.41 1.41-8.58 8.59 8.58 8.59-1.41 1.41z"></path>
  </svg>
);

export const LiveSvg = () => (
  <svg
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 32 32"
    fill="currentColor"
  >
    <path d="M8.5 19.054h2v4.5h-2v-4.5z"></path>
    <path d="M17.5 14.554h2v9h-2v-9z"></path>
    <path d="M22 8.554h2v15h-2v-15z"></path>
    <path d="M13 10.804h2v12.75h-2v-12.75z"></path>
  </svg>
);

export const PlaybackSvg = () => (
  <svg
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 32 32"
    fill="currentColor"
  >
    <path d="M19.442 20.5l-4.192-4.192v-7.058h1.5v6.435l3.75 3.758z"></path>
    <path d="M16 5.5c-2.873 0-5.543 1.17-7.5 3.172v-3.172h-1.5v6h6v-1.5h-3.69c1.695-1.89 4.103-3 6.69-3 4.965 0 9 4.035 9 9s-4.035 9-9 9c-4.965 0-9-4.035-9-9h-1.5c0 5.79 4.71 10.5 10.5 10.5s10.5-4.71 10.5-10.5c0-5.79-4.71-10.5-10.5-10.5z"></path>
  </svg>
);


export const AudienceSvg = () => (
  <svg width="1em" height="1em" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient x1="0%" y1="69.7898256%" x2="100%" y2="69.7898256%" id="linearGradient-audience">
        <stop stopColor="#FF442C" offset="0%"></stop>
        <stop stopColor="#FF9C15" offset="100%"></stop>
      </linearGradient>
    </defs>
  <path transform="translate(1.910949, 1.861267)" fill="url(#linearGradient-audience)" fillRule="evenodd" d="M7.0890507,0 C5.07157985,0 3.43609659,1.63548326 3.43609659,3.6529541 C3.43609659,5.67042494 5.07157985,7.3059082 7.0890507,7.3059082 C9.10652154,7.3059082 10.7420048,5.67042494 10.7420048,3.6529541 C10.7420048,1.63548326 9.10652154,0 7.0890507,0 Z M11.8196263,7.82775879 L2.35847513,7.82775879 C1.30984368,7.82791042 0.424089159,8.6060165 0.28881571,9.64588623 L0.0174534057,11.7332886 C-0.0600112518,12.3287723 0.12272991,12.928773 0.518982567,13.3799775 C0.915235224,13.831182 1.48661166,14.089879 2.08711283,14.0899658 L12.0909886,14.0899658 C12.6914897,14.089879 13.2628662,13.831182 13.6591188,13.3799775 C14.0553715,12.928773 14.2381126,12.3287723 14.160648,11.7332886 L13.8892857,9.64588623 C13.7540122,8.6060165 12.8682577,7.82791042 11.8196263,7.82775879 Z"></path>
</svg>
);

export const NoticeSvg = () => (
  <svg width="1em" height="1em" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-notice">
        <stop stopColor="#FF2742" offset="0%"></stop>
        <stop stopColor="#FF8193" offset="61.5166084%"></stop>
        <stop stopColor="#FFCFD6" offset="100%"></stop>
      </linearGradient>
    </defs>
    <path fill="url(#linearGradient-notice)" d="M6.352,11.455 L6.3523333,13.9477394 C6.3523333,14.528807 5.83349215,15 5.1946206,15 L5.0428402,15 C4.40396865,15 3.88590189,14.528807 3.88590189,13.9477394 L3.88517951,11.253326 C3.35807329,10.9676511 3,10.4078559 3,9.76405277 L3,6.90801753 C3,5.97408343 3.75352605,5.21693476 4.68348747,5.21693476 L7.0616892,5.21693476 L11.9055752,3.06733684 C12.4179729,2.83984007 12.9935933,3.2166536 12.9935933,3.77940876 L12.9935933,12.9821107 C12.9935933,13.5497962 12.408416,13.9266097 11.894548,13.6892524 L7.0616892,11.4551355 L6.352,11.455 Z M13.6489527,6.3403321 C14.9476043,6.3403321 16,7.29750889 16,8.4779565 C16,9.65910844 14.9476043,10.6162852 13.6489527,10.6162852 L13.6489527,10.6162852 Z"></path>
  </svg>
);

export const ReplySvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 26 26"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
  >
    <g transform="translate(13.000000, 13.000000) scale(-1, 1) translate(-13.000000, -13.000000) ">
      <path d="M23.465,24.375 C23.180625,24.375 22.89625,24.220625 22.75,23.96875 C20.6375,20.271875 16.713125,18.005 12.1875,17.875 L12.1875,22.75 C12.1875,23.075 11.9925,23.375625 11.68375,23.4975 C11.375,23.6275 11.03375,23.554375 10.798125,23.31875 L1.048125,13.56875 C0.73125,13.251875 0.73125,12.74 1.048125,12.423125 L10.798125,2.673125 C11.03375,2.4375 11.375,2.3725 11.68375,2.494375 C11.984375,2.61625 12.1875,2.916875 12.1875,3.241875 L12.1875,8.20625 C19.183125,9.18125 24.375,14.99875 24.375,22.01875 C24.375,22.5225 24.3425,23.05875 24.269375,23.651875 C24.22875,24.00125 23.96875,24.285625 23.6275,24.350625 C23.570625,24.366875 23.521875,24.375 23.465,24.375 Z"></path>
    </g>
  </svg>
);

export const HeartSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 26 26"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
  >
    <g transform="translate(0, 0.8)">
      <path d="M18.240625,3.25 C16.6725,3.25 15.09625,3.859375 13.901875,5.07 L13,5.988125 L12.098125,5.07 C10.90375,3.859375 9.3275,3.25 7.759375,3.25 C6.19125,3.25 4.615,3.859375 3.420625,5.07 C1.02375,7.49125 1.02375,11.44 3.420625,13.86125 L13,23.5625 L22.579375,13.86125 C24.97625,11.44 24.97625,7.49125 22.579375,5.07 C21.385,3.859375 19.80875,3.25 18.240625,3.25 L18.240625,3.25 Z"></path>
    </g>
  </svg>
);

export const ResetSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 22 22"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
  >
    <path d="M12.375,19.25 C16.9125,19.25 20.625,15.5375 20.625,11 C20.625,6.4625 16.9125,2.75 12.375,2.75 C7.8375,2.75 4.125,6.4625 4.125,11 L4.125,15.2625 L1.65,12.7875 L0.6875,13.75 L4.8125,17.875 L8.9375,13.75 L7.975,12.7875 L5.5,15.2625 L5.5,11 L5.5,11 C5.5,7.21875 8.59375,4.125 12.375,4.125 C16.15625,4.125 19.25,7.21875 19.25,11 C19.25,14.78125 16.15625,17.875 12.375,17.875 L12.375,19.25 Z"></path>
  </svg>
);

export const ChevronsDownSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 16 16"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
  >
    <path d="M12.295,7.795 L13,8.5 L8,13.5 L3,8.5 L3.705,7.795 L8,12.085 L12.295,7.795 Z M12.295,3.295 L13,4 L8,9 L3,4 L3.705,3.295 L8,7.585 L12.295,3.295 Z"></path>
  </svg>
);

export const ExitSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 22 22"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
  >
    <path d="M4.1249999,20.625 L12.3749999,20.625 C13.1312499,20.625 13.7499999,20.00625 13.7499999,19.25 L13.7499999,17.1875 L12.3749999,17.1875 L12.3749999,19.25 L4.1249999,19.25 L4.1249999,2.75000003 L12.3749999,2.75000003 L12.3749999,4.81250003 L13.7499999,4.81250003 L13.7499999,2.75000003 C13.7499999,1.99375003 13.1312499,1.37500003 12.3749999,1.37500003 L4.1249999,1.37500003 C3.3687499,1.37500003 2.7499999,1.99375003 2.7499999,2.75000003 L2.7499999,19.25 C2.7499999,20.00625 3.3687499,20.625 4.1249999,20.625 Z"></path>
    <polygon points="14.1556249 14.155625 16.6168749 11.6875 6.8749999 11.6875 6.8749999 10.3125 16.6168749 10.3125 14.1556249 7.84437503 15.1249999 6.87500003 19.2499999 11 15.1249999 15.125"></polygon>
  </svg>
);

export const CrescentSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 22 22"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
  >
    <path d="M9.28125,3.719375 C8.48375,7.1775 9.508125,10.87625 12.051875,13.42 C13.475,14.843125 15.29,15.819375 17.25625,16.231875 C15.82625,17.71 13.853125,18.5625 11.776875,18.5625 C11.680625,18.5625 11.584375,18.569375 11.488125,18.5625 C7.57625,18.418125 4.338125,15.241875 4.13875,11.336875 C3.96,7.8925 6.11875,4.8125 9.28125,3.719375 M10.29875,2.0625 C10.2575,2.0625 10.21625,2.069375 10.181875,2.07625 C5.81625,2.853125 2.53,6.765 2.76375,11.405625 C2.9975,16.005 6.83375,19.7725 11.433125,19.9375 C11.543125,19.944375 11.66,19.9375 11.77,19.9375 C14.815625,19.9375 17.496875,18.425 19.12625,16.115 C19.435625,15.675 19.119375,15.08375 18.59,15.0425 C16.561875,14.86375 14.575,14.004375 13.02125,12.450625 C10.470625,9.9 9.769375,6.194375 10.924375,3.01125 C11.089375,2.550625 10.759375,2.0625 10.29875,2.0625 L10.29875,2.0625 Z"></path>
  </svg>
);

export const SunSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 22 22"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
  >
    <rect x="10.3125" y="1.375" width="1.375" height="3.4375"></rect>
    <polygon transform="translate(16.578650, 5.380904) rotate(-45.000000) translate(-16.578650, -5.380904) " points="14.8736664 4.69341097 18.2836336 4.69341097 18.2836336 6.06839778 14.8736664 6.06839778"></polygon>
    <rect x="17.1875" y="10.3125" width="3.4375" height="1.375"></rect>
    <polygon transform="translate(16.581902, 16.585663) rotate(-45.000000) translate(-16.581902, -16.585663) " points="15.8944085 14.8806789 17.2693953 14.8806789 17.2693953 18.2906461 15.8944085 18.2906461"></polygon>
    <rect x="10.3125" y="17.1875" width="1.375" height="3.4375"></rect>
    <polygon transform="translate(5.377213, 16.589052) rotate(-45.000000) translate(-5.377213, -16.589052) " points="3.67222885 15.9015585 7.08219615 15.9015585 7.08219615 17.2765453 3.67222885 17.2765453"></polygon>
    <rect x="1.375" y="10.3125" width="3.4375" height="1.375"></rect>
    <polygon transform="translate(5.373754, 5.384294) rotate(-45.000000) translate(-5.373754, -5.384294) " points="4.68626097 3.6793101 6.06124778 3.6793101 6.06124778 7.0892774 4.68626097 7.0892774"></polygon>
    <path d="M11,8.25 C12.519375,8.25 13.75,9.480625 13.75,11 C13.75,12.519375 12.519375,13.75 11,13.75 C9.480625,13.75 8.25,12.519375 8.25,11 C8.25,9.480625 9.480625,8.25 11,8.25 M11,6.875 C8.724375,6.875 6.875,8.724375 6.875,11 C6.875,13.275625 8.724375,15.125 11,15.125 C13.275625,15.125 15.125,13.275625 15.125,11 C15.125,8.724375 13.275625,6.875 11,6.875 L11,6.875 Z"></path>
  </svg>
);

export const ApplyCallSvg = () => (
  <svg
    width="24px"
    height="24px" 
    viewBox="0 0 24 24" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g>
        <polygon fillOpacity="0" fill="#FCFCFD" fillRule="nonzero" points="0 24 24 24 24 0 0 0"></polygon>
        <path d="M3.61052632,9.44385965 C3.61052632,6.95857827 5.62524494,4.94385965 8.11052632,4.94385965 L15.1105263,4.94385965 C17.5958077,4.94385965 19.6105263,6.95857827 19.6105263,9.44385965 C19.6105263,11.929141 17.5958077,13.9438596 15.1105263,13.9438596 L8.73052632,13.9438596 L8.73052632,13.9438596" stroke="#FCFCFD" strokeWidth="1.5" strokeLinecap="round"></path>
        <path d="M4.25338346,13.6887576 C4.25338346,11.2034762 6.26810208,9.18875761 8.75338346,9.18875761 L15.7533835,9.18875761 C18.2386648,9.18875761 20.2533835,11.2034762 20.2533835,13.6887576 C20.2533835,16.174039 18.2386648,18.1887576 15.7533835,18.1887576 L9.37338346,18.1887576 L9.37338346,18.1887576" stroke="#FCFCFD" strokeWidth="1.5" strokeLinecap="round" transform="translate(12.253383, 13.688758) scale(-1, -1) translate(-12.253383, -13.688758) "></path>
      </g>
    </g>
  </svg>
);

export const ManagerMicOnSvg = () => (
  <svg
    width="20px" 
    height="20px" 
    viewBox="0 0 20 20" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g>
        <path d="M14.375,8.75 L14.375,10.625 C14.375,13.04375 12.41875,15 10,15 C7.58125,15 5.625,13.04375 5.625,10.625 L5.625,8.75 L4.37498645,8.75 L4.37498645,10.625 C4.36875,13.49375 6.525,15.9 9.375,16.2125 L9.375,17.5 L6.875,17.5 L6.875,18.75 L13.125,18.75 L13.125,17.5 L10.625,17.5 L10.625,16.2125 C13.46875,15.89375 15.61875,13.4875 15.625,10.625 L15.625,8.75 L14.375,8.75 Z" fill="#FCFCFD" fillRule="nonzero"></path>
        <path d="M10,13.75 C11.725,13.75 13.125,12.35 13.125,10.625 L13.125,4.375 C13.125,2.65 11.725,1.25 10,1.25 C8.275,1.25 6.875,2.65 6.875,4.375 L6.875,10.625 C6.875,12.35 8.275,13.75 10,13.75 Z" fill="#FCFCFD" fillRule="nonzero"></path>
        <rect x="0" y="0" width="20" height="20"></rect>
      </g>
    </g>
  </svg>
);

export const ManagerMicOffSvg = () => (
  <svg
    width="20px" 
    height="20px" 
    viewBox="0 0 20 20" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g>
        <path d="M14.375,10.625 C14.375,13.04375 12.41875,15 10,15 C8.85625,15 7.825,14.55625 7.04375,13.8375 L7.925,12.95625 C8.475,13.45 9.2,13.75 9.99375,13.75 C11.71875,13.75 13.11875,12.35 13.11875,10.625 L13.11875,7.7625 L18.74375,2.1375 L17.8625,1.25625 L1.25,17.86875 L2.13125,18.75 L6.15625,14.725 C7.0125,15.53125 8.125,16.075 9.375,16.20625 L9.375,17.49375 L6.875,17.49375 L6.875,18.74375 L13.125,18.74375 L13.125,17.49375 L10.625,17.49375 L10.625,16.20625 C13.46875,15.8875 15.61875,13.48125 15.625,10.61875 L15.625,8.74375 L14.375,8.74375 L14.375,10.625 Z" fill="#FCFCFD" fillRule="nonzero"></path>
        <rect x="0" y="0" width="20" height="20"></rect>
        <path d="M5.6375,10.825 C5.63125,10.75625 5.625,10.69375 5.625,10.625 L5.625,8.75 L4.375,8.75 L4.375,10.625 C4.375,11.075 4.43125,11.5125 4.53125,11.93125 L5.6375,10.825 Z" fill="#FCFCFD" fillRule="nonzero"></path>
        <path d="M12.975,3.4875 C12.59375,2.19375 11.4125,1.25 10,1.25 C8.275,1.25 6.875,2.65 6.875,4.375 L6.875,9.5875 L12.975,3.4875 Z" fill="#FCFCFD" fillRule="nonzero"></path>
      </g>
    </g>
  </svg>
);

export const ManagerVideoOnSvg = () => (
  <svg 
    width="20px" 
    height="20px" 
    viewBox="0 0 20 20" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g >
        <path d="M13.125,16.25 L2.5,16.25 C1.8125,16.25 1.25,15.6875 1.25,15 L1.25,5 C1.25,4.3125 1.8125,3.75 2.5,3.75 L13.125,3.75 C13.8125,3.75 14.375,4.3125 14.375,5 L14.375,7.5375 L17.7625,5.11875 C17.95,4.98125 18.20625,4.9625 18.4125,5.06875 C18.61875,5.175 18.75,5.3875 18.75,5.625 L18.75,14.375 C18.75,14.6125 18.61875,14.825 18.4125,14.93125 C18.20625,15.0375 17.95625,15.01875 17.7625,14.88125 L14.375,12.4625 L14.375,15 C14.375,15.6875 13.8125,16.25 13.125,16.25 Z" fill="#FCFCFD" fillRule="nonzero"></path>
        <rect x="0" y="0" width="20" height="20"></rect>
      </g>
    </g>
  </svg>
);

export const ManagerVideoOffSvg = () => (
  <svg 
    width="20px" 
    height="20px" 
    viewBox="0 0 20 20" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g>
        <path d="M12.69375,3.75 L2.5,3.75 C1.8125,3.75 1.25,4.3125 1.25,5 L1.25,15 C1.25,15.0625 1.2625,15.11875 1.26875,15.18125 L12.69375,3.75 Z" fill="#FCFCFD" fillRule="nonzero"></path>
        <path d="M18.4125,5.06875 C18.20625,4.9625 17.95625,4.98125 17.7625,5.11875 L14.375,7.5375 L14.375,6.53125 L18.75,2.15625 L17.85,1.25625 L1.25,17.85625 L2.15,18.75625 L4.65,16.25625 L13.125,16.25625 C13.8125,16.25625 14.375,15.69375 14.375,15.00625 L14.375,12.46875 L17.7625,14.8875 C17.95625,15.025 18.20625,15.04375 18.4125,14.9375 C18.61875,14.83125 18.75,14.61875 18.75,14.38125 L18.75,5.63125 C18.75,5.3875 18.61875,5.175 18.4125,5.06875 Z" fill="#FCFCFD" fillRule="nonzero"></path>
        <rect x="0" y="0" width="20" height="20"></rect>
      </g>
    </g>
  </svg>
);

export const ManagerCameraSvg = () => (
  <svg 
    width="20px" 
    height="20px" 
    viewBox="0 0 20 20" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g>
              <rect x="0" y="0" width="20" height="20"></rect>
              <g transform="translate(1.666667, 3.000000)" fill="#FCFCFD">
                  <path d="M0.778177833,13.7138487 C0.35421036,13.7193188 0.00589176478,13.3888055 0,12.9754497 L0,2.81429322 C0.0533995956,2.3716182 0.431539052,2.03400084 0.888509178,2.02099825 L3.85020647,2.02099825 C3.85020647,2.02099825 4.61829499,0 5.03651268,0 L10.9257338,0 C11.3895161,0 12.1475153,2.01972897 12.1475153,2.01972899 L15.223124,2.07716354 C15.6462288,2.07204478 15.9937519,2.40178804 16,2.81429322 L16,12.9757671 C15.9980273,13.1739305 15.9152325,13.3631782 15.7698979,13.5017209 C15.6245634,13.6402636 15.428644,13.7167052 15.2254022,13.7142273 L0.778177833,13.7138487 Z M10.3639877,7.70797119 L11.7813714,9.74673924 L13.1987551,7.70797119 L12.3131751,7.70797119 C12.3125915,6.8479735 12.0398105,6.00910468 11.5320681,5.30587404 C11.5258791,5.29378696 11.5190347,5.28202957 11.511564,5.27065175 C11.4503773,5.18814906 11.3843087,5.10977154 11.3182401,5.0320286 L11.2948069,5.00283537 C10.848728,4.4834811 10.2820477,4.07533264 9.64178922,3.81225827 L9.5890645,3.78877675 C9.48643572,3.74879469 9.38239661,3.71209157 9.27694717,3.6786674 C9.23984458,3.66692663 9.20274201,3.65423392 9.16368665,3.64281048 C9.07168961,3.61679041 8.97893316,3.59383774 8.8854173,3.57395248 C8.83366895,3.56221171 8.78224609,3.55047094 8.72952136,3.53999945 C8.70380993,3.53523968 8.67972579,3.52825868 8.65401435,3.52349891 C8.58436564,3.51175814 8.51374055,3.50636374 8.44409186,3.49779616 C8.39559814,3.49176713 8.3480808,3.48478613 8.29893614,3.48034367 C8.18198706,3.4701895 8.06482102,3.464372 7.94743801,3.46289117 C7.92693395,3.46289117 7.90610444,3.46003322 7.87355831,3.46003322 C6.97055972,3.4591702 6.08874719,3.72671408 5.34570087,4.22699288 C5.23065131,4.30341358 5.15200894,4.42178187 5.1275155,4.55539385 C5.10302205,4.68900583 5.13474093,4.82660556 5.21551637,4.93715054 C5.38589671,5.1685124 5.71498261,5.22412862 5.95528976,5.06217383 C6.61284664,4.61929143 7.41015205,4.41803777 8.20552876,4.49417463 C8.23514574,4.49703049 8.26443724,4.50115563 8.29372876,4.50528075 C8.37335827,4.51395411 8.45255385,4.52569488 8.53131548,4.54050306 C8.56613984,4.54653209 8.59966234,4.55509969 8.63318484,4.56271532 C8.70999368,4.58016781 8.78680255,4.59793761 8.8610077,4.62014987 C8.88509184,4.62713087 8.90819959,4.63601576 8.93163279,4.64363141 C9.01690364,4.66933416 9.10152355,4.69916206 9.18353979,4.73121116 L9.20925123,4.74295193 C9.70319004,4.94682756 10.140362,5.26223393 10.4847338,5.66317408 C10.9777734,6.23627241 11.2481926,6.96027964 11.2485914,7.7082885 L10.3639877,7.70797119 Z M5.25717539,9.71913256 C4.78082363,9.15192673 4.52006865,8.44169487 4.51902932,7.70860582 L5.40395843,7.70860582 L3.98722565,5.66983777 L2.57049287,7.70924045 L3.45542198,7.70924045 C3.45553405,8.57131865 3.7294211,9.41221595 4.23945811,10.1164147 C4.24564186,10.1259342 4.24987287,10.1357711 4.2563821,10.1449733 C4.30747951,10.2151006 4.36443523,10.2807854 4.41911271,10.3461529 L4.48029942,10.4223092 C4.56068834,10.5175046 4.64661011,10.6047671 4.73481011,10.6913949 C5.03613154,10.9850592 5.37974098,11.2343407 5.75480564,11.4313804 L5.78377168,11.4472463 C5.8894381,11.5009788 5.99684031,11.550692 6.10597832,11.5963858 L6.18766908,11.6316081 C6.28183586,11.6684169 6.37719601,11.7019469 6.47374952,11.7321979 C6.51931409,11.7467945 6.56487866,11.7639297 6.61174507,11.7753531 C6.69571407,11.7997866 6.78098491,11.8204123 6.86983585,11.8388167 C6.92744249,11.8515094 6.98374728,11.8657887 7.04363215,11.8762602 C7.06771629,11.88102 7.09049858,11.8886356 7.11490816,11.8921261 C7.19659892,11.9060881 7.27926608,11.9137038 7.36160778,11.9238579 C7.39155022,11.9267138 7.42051626,11.9314736 7.45045869,11.934964 C7.59821811,11.948926 7.74500112,11.9584456 7.89276051,11.9584456 C8.79745754,11.9583181 9.68066376,11.6895816 10.424849,11.1879975 C10.5398985,11.1115768 10.6185409,10.9932085 10.6430343,10.8595966 C10.6675278,10.7259846 10.6358089,10.5883849 10.5550335,10.4778399 C10.3846531,10.246478 10.0555672,10.1908618 9.81526007,10.3528166 C9.23655865,10.74184 8.54775218,10.9450643 7.84491771,10.9341431 C7.75313763,10.9341431 7.66157454,10.9299122 7.57022842,10.9214504 C7.53312583,10.9179599 7.49602327,10.9125655 7.45859522,10.9078057 C7.38666828,10.8982862 7.31506681,10.8897186 7.24574358,10.8760739 C7.20245724,10.867189 7.16047273,10.8567175 7.11848822,10.8468807 C7.05057532,10.8322841 6.98298787,10.8156778 6.91572588,10.7970618 C6.88317975,10.7875422 6.85063363,10.7767534 6.81971481,10.76533 C6.74312293,10.7410023 6.66718198,10.7143475 6.59189195,10.6853658 L6.54209638,10.6637882 C6.45118421,10.6259216 6.36200783,10.5843529 6.27456724,10.5390822 C5.90277142,10.3425273 5.57168701,10.0803057 5.29818352,9.76577831 L5.25880271,9.71722867 L5.25717539,9.71913256 Z"></path>
              </g>
          </g>
      </g>
  </svg>
);

export const GridMicOnSvg = () => (
  <svg 
    width="16px" 
    height="16px" 
    viewBox="0 0 16 16" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <path 
        d="M1.00002164,6 L1.00002164,7.5 C1.00002164,9.435 2.56502164,11 4.50002164,11 C6.43502164,11 8.00002164,9.435 8.00002164,7.5 L8.00002164,6 L9.00002164,6 L9.00002164,7.5 C8.99502164,9.79 7.27502164,11.715 5.00002164,11.97 L5.00002164,13 L7.00002164,13 L7.00002164,14 L2.00002164,14 L2.00002164,13 L4.00002164,13 L4.00002164,11.97 C1.72002164,11.72 -0.00497836351,9.795 1.07947927e-05,7.5 L1.07947927e-05,6 L1.00002164,6 Z M4.50002164,0 C5.88002164,0 7.00002164,1.12 7.00002164,2.5 L7.00002164,7.5 C7.00002164,8.88 5.88002164,10 4.50002164,10 C3.12002164,10 2.00002164,8.88 2.00002164,7.5 L2.00002164,2.5 C2.00002164,1.12 3.12002164,0 4.50002164,0 Z" 
        id="path-mic-on"
      ></path>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(3.499978, 1.000000)">
        <mask id="mask-mic-on" fill="white">
            <use xlinkHref="#path-mic-on"></use>
        </mask>
        <use fill="#000000" fillRule="nonzero" xlinkHref="#path-mic-on"></use>
        <rect fill="#3A3D48" mask="url(#mask-mic-on)" x="-3.49997836" y="-1" width="16" height="16"></rect>
      </g>
    </g>
  </svg>
);

export const GridMicOffSvg = () => (
  <svg
    width="16px"
    height="16px"
    viewBox="0 0 16 16" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <defs>
        <path 
          d="M13.29,0.005 L13.995,0.71 L9.495,5.21 L9.495,7.5 C9.495,8.88 8.375,10 6.995,10 C6.36,10 5.78,9.76 5.34,9.365 L4.635,10.07 C5.26,10.645 6.085,11 7,11 C8.935,11 10.5,9.435 10.5,7.5 L10.5,5.995 L11.5,5.995 L11.5,7.495 C11.495,9.785 9.775,11.71 7.5,11.965 L7.5,12.995 L9.5,12.995 L9.5,13.995 L4.5,13.995 L4.5,12.995 L6.5,12.995 L6.5,11.965 C5.5,11.86 4.61,11.425 3.925,10.78 L0.705,14 L0,13.295 L13.29,0.005 Z M3.5,6 L3.5,7.5 L3.51,7.66 L2.625,8.545 C2.545,8.21 2.5,7.86 2.5,7.5 L2.5,6 L3.5,6 Z M7,0 C8.13,0 9.075,0.755 9.38,1.79 L4.5,6.67 L4.5,2.5 C4.5,1.12 5.62,0 7,0 Z"
          id="grid-mic-off-path"
        ></path>
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-225.000000, -662.000000)">
            <g transform="translate(225.000000, 662.000000)">
                <rect x="0" y="0" width="16" height="16"></rect>
                <g transform="translate(1.000000, 1.000000)">
                    <mask id="grid-mic-off-mask" fill="white">
                        <use xlinkHref="#grid-mic-off-path"></use>
                    </mask>
                    <use fill="#000000" fillRule="nonzero" xlinkHref="#grid-mic-off-path"></use>
                    <rect fill="#3A3D48" mask="url(#grid-mic-off-mask)" x="-1" y="-1" width="16" height="16"></rect>
                    <polygon fill="#F53F3F" fillRule="nonzero" mask="url(#grid-mic-off-mask)" points="13.29 0.005 13.995 0.71 9.495 5.21 5.34 9.365 4.635 10.07 3.925 10.78 0.705 14 0 13.295"></polygon>
                </g>
            </g>
        </g>
    </g>
  </svg>
);

export const PlayerEmptySvg = () => (
  <svg 
    width="1em" 
    height="1em" 
    viewBox="0 0 108 108" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <path 
        d="M98.8571429,4 L104,9.14285714 L79,34.1428571 L79,39.8928571 L98.3571429,26.0714286 C99.4642857,25.2857143 100.892857,25.1785714 102.071429,25.7857143 C103.25,26.3928571 104,27.6071429 104,29 L104,29 L104,79 C104,80.3571429 103.25,81.5714286 102.071429,82.1785714 C100.892857,82.7857143 99.4642857,82.6785714 98.3571429,81.8928571 L98.3571429,81.8928571 L79,68.0714286 L79,82.5714286 C79,86.5 75.7857143,89.7142857 71.8571429,89.7142857 L71.8571429,89.7142857 L23.4285714,89.7142857 L9.14285714,104 L4,98.8571429 L98.8571429,4 Z M69.3928571,18.25 L4.10714286,83.5714286 C4.07142857,83.2142857 4,82.8928571 4,82.5357143 L4,82.5357143 L4,25.3928571 C4,21.4642857 7.21428571,18.25 11.1428571,18.25 L11.1428571,18.25 L69.3928571,18.25 Z"
        id="player-empty-svg-path"
      />
    </defs>
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <mask fill="white">
        <use xlinkHref="#player-empty-svg-path"/>
      </mask>
      <use fill="#B2B7C4" xlinkHref="#player-empty-svg-path"/>
    </g>
  </svg>
);
