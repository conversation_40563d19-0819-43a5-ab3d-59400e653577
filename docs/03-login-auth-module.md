# 花粉直播登录认证模块功能文档

## 1. 功能概述

花粉直播登录认证模块为学员侧应用提供完整的身份认证解决方案，支持PC端扫码登录和移动端微信登录两种认证方式。该模块设计遵循OAuth2.0标准，与现有OpenAPI体系完全对齐，确保用户在不同设备间的无缝登录体验。

### 1.1 核心功能

- **PC端扫码登录**：通过微信扫码实现快速登录，支持实时状态轮询
- **移动端微信登录**：在微信环境内一键授权登录
- **用户会话管理**：统一的Token管理和用户信息维护
- **安全防护机制**：二维码时效控制、Token自动刷新、异常处理
- **多端同步**：PC和移动端登录状态实时同步

### 1.2 设计目标

- **用户体验优先**：简化登录流程，减少用户操作步骤
- **安全性保障**：确保认证过程的安全性和数据传输的可靠性
- **高可用性**：支持高并发访问和异常情况下的优雅降级
- **可扩展性**：模块化设计，便于后续功能扩展和维护

## 2. 技术架构

### 2.1 整体架构设计

```mermaid
graph TB
    subgraph "前端层"
        A[PC Web端] --> C[认证模块]
        B[移动H5端] --> C
        C --> D["状态管理<br/>Pinia Store"]
        C --> E["API服务层<br/>@live/core"]
    end

    subgraph "服务层"
        E --> F["HTTP拦截器<br/>Axios"]
        F --> G[OpenAPI接口]
    end

    subgraph "外部服务"
        G --> H[微信OAuth服务]
        G --> I[后端认证服务]
    end

    subgraph "数据存储"
        D --> J["LocalStorage<br/>Token存储"]
        D --> K["SessionStorage<br/>临时数据"]
    end
```

### 2.2 技术栈

- **前端框架**：Vue 3 + TypeScript + Composition API
- **状态管理**：Pinia
- **网络请求**：Axios + 统一拦截器
- **UI组件**：Element Plus（PC）/ Vant（移动）动态映射
- **二维码生成**：QRCode.js
- **构建工具**：Vite
- **包管理**：pnpm monorepo

### 2.3 模块化设计

```
packages/
├── core/                    # 核心API SDK
│   ├── api/                # 认证相关API
│   ├── types/              # 类型定义
│   └── utils/              # 工具函数
├── ui/                     # UI组件库
│   ├── login/              # 登录相关组件
│   └── common/             # 通用组件
└── im/                     # IM互动模块
```

## 3. API接口详细说明

### 3.1 PC端扫码登录接口

#### 3.1.1 生成扫码登录URL

**接口地址**：`POST /customer-login/generatePCScanUrl`

**功能描述**：生成PC端扫码登录的二维码授权URL（有效期120秒）

**请求参数**：

```typescript
interface GeneratePCScanRequest {
  items?: Array<{
    paramKey: string // 参数键名，会拼接到返回的URL中
    paramValue: string // 参数键值，会拼接到返回的URL中
  }> // 自定义参数数组（可选），用于传递额外的业务参数
}
```

**响应数据**：

```typescript
interface GeneratePCScanResponse {
  code: number // 状态码，0表示成功
  msg: string // 响应消息
  data: string // 完整的微信授权URL
}
```

**示例响应**：

```json
{
  "code": 0,
  "msg": "",
  "data": "https://live-test.fanspvt.com?scanFlag=123&paramKey=paramValue"
}
```

**URL格式说明**：

- 返回的URL格式：`https://live-test.fanspvt.com?scanFlag={scanFlag}&{paramKey}={paramValue}`
- 正式环境域名：`https://live.fanspvt.com`
- `scanFlag`：系统生成的扫码标识符，用于后续轮询扫码状态
- 自定义参数：请求中的 `paramKey` 和 `paramValue` 会直接拼接到URL查询参数中
- 前端需要将完整URL生成二维码供用户扫描

#### 3.1.2 检查扫码结果

**接口地址**：`GET /customer-login/checkScanResult/{scanFlag}`

**功能描述**：轮询检查扫码登录状态

**请求参数**：

```typescript
interface CheckScanRequest {
  scanFlag: string // 扫码标识符
}
```

**响应数据**：

```typescript
interface CheckScanResponse {
  code: number
  msg: string
  data: {
    scanStatus: number // 扫码状态：0-未扫码，1-已扫码等待确认，2-登录成功，3-二维码失效
    token?: string // 登录成功时返回的访问令牌
  }
}
```

**扫码状态说明**：

- `scanStatus = 0`：未扫码
- `scanStatus = 1`：用户已扫码，等待用户确认授权
- `scanStatus = 2`：用户确认授权，登录成功，返回token
- `scanStatus = 3`：二维码已失效，需要重新生成

**轮询策略**：

- 轮询间隔：2秒
- 最大轮询时间：120秒（2分钟，匹配二维码有效期）
- 失败重试：最多3次，超过后提示用户刷新

### 3.2 移动端微信登录接口

#### 3.2.1 微信登录

**接口地址**：`POST /customer-login/wechat-login`

**功能描述**：通过微信授权码完成登录

**请求参数**：

```typescript
interface WechatLoginRequest {
  code: string // 微信授权回调code（必填）
  scanFlag?: string // PC端扫码标识符（扫码登录场景必填）
}
```

**响应数据**：

```typescript
interface WechatLoginResponse {
  code: number
  msg: string
  data: string // 登录成功返回的token字符串
}
```

#### 3.2.2 绑定UnionID

**接口地址**：`POST /customer-login/bindUnionId`

**功能描述**：绑定微信UnionID，支持跨应用用户识别

**请求参数**：

```typescript
interface BindUnionIdRequest {
  openId: string // 微信OpenID
  unionId?: string // 微信UnionID
  appId: string // 微信应用ID
}
```

**响应数据**：

```typescript
interface BindUnionIdResponse {
  code: number
  message: string
  data: boolean // 绑定结果
  timestamp: number
}
```

### 3.3 用户信息接口

#### 3.3.1 获取登录用户信息

**接口地址**：`GET /login-info/getLoginInfo`

**功能描述**：获取当前登录用户的详细信息

**请求头**：

```
Authorization: Bearer {token}
```

**响应数据**：

```typescript
interface LoginInfoResponse {
  code: number
  msg: string
  data: {
    loginType: number // 登录类型
    userId: number // 用户ID
    token: string // 用户唯一标识
    loginTime: number // 登录时间
    expireTime: number // 过期时间
    ipaddr: string // 登录IP地址
    loginLocation: string // 登录地点
    browser: string // 浏览器类型
    os: string // 操作系统
    permissions: string[] // 权限列表
    customerUserInfo: {
      // C端用户信息
      id: number // 用户ID
      wechatOpenId: string // 微信OpenID
      wechatUnionId: string // 微信UnionID
      userName: string // 用户昵称
      mobile: string // 手机号码
      email: string // 邮箱
      gender: number // 性别：0-男，1-女，2-未知
      avatar: string // 用户头像
      loginIp: string // 最后登录IP
      loginDate: string // 最后登录时间
      status: number // 状态：0-正常，1-禁用
    }
  }
}
```

## 4. 前端实现关键技术点

### 4.1 状态管理设计

#### 4.1.1 认证Store结构

```typescript
// stores/auth.ts
import { defineStore } from 'pinia'

export interface AuthState {
  // 用户信息
  user: CustomerUserInfo | null
  // 访问令牌
  token: string | null
  // 登录状态
  isLoggedIn: boolean
  // 登录方式
  loginType: 'qrcode' | 'wechat' | null
}

export interface CustomerUserInfo {
  id: number // 用户ID
  wechatOpenId: string // 微信OpenID
  wechatUnionId: string // 微信UnionID
  userName: string // 用户昵称
  mobile: string // 手机号码
  email: string // 邮箱
  gender: number // 性别：0-男，1-女，2-未知
  avatar: string // 用户头像
  loginIp: string // 最后登录IP
  loginDate: string // 最后登录时间
  status: number // 状态：0-正常，1-禁用
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: localStorage.getItem('access_token'),
    isLoggedIn: false,
    loginType: null,
  }),

  getters: {
    // 获取用户头像
    userAvatar: (state) => state.user?.avatar || '/default-avatar.png',
    // 获取用户昵称
    userNickname: (state) => state.user?.userName || '未知用户',
    // 检查Token是否有效
    isTokenValid: (state) => {
      if (!state.token) return false
      // 检查Token是否过期的逻辑
      return true
    },
  },

  actions: {
    // 设置登录信息
    setLoginInfo(token: string, user: CustomerUserInfo) {
      this.token = token
      this.user = user
      this.isLoggedIn = true

      // 持久化存储
      localStorage.setItem('access_token', token)
      localStorage.setItem('user_info', JSON.stringify(user))
    },

    // 清除登录信息
    clearLoginInfo() {
      this.token = null
      this.user = null
      this.isLoggedIn = false
      this.loginType = null

      // 清除存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_info')
    },

    // 刷新Token（重新登录）
    async refreshAccessToken() {
      try {
        // 由于当前API设计中没有refreshToken机制，这里可以实现重新获取用户信息的逻辑
        const userInfo = await authApi.getLoginInfo()
        this.user = userInfo.data.customerUserInfo
        return true
      } catch (error) {
        this.clearLoginInfo()
        return false
      }
    },
  },
})
```

### 4.2 PC端扫码登录实现

#### 4.2.1 登录组合函数

```typescript
// composables/useLogin.ts
import { ref, onUnmounted } from 'vue'
import QRCode from 'qrcode'
import { authApi } from '@live/core'
import type { CustomerUserInfo } from '@live/core'
import { useAuthStore } from '@/stores/auth'

export function useQRCodeLogin() {
  const authStore = useAuthStore()

  // 响应式状态
  const qrCodeUrl = ref<string>('')
  const scanFlag = ref<string>('')
  const loginStatus = ref<
    'idle' | 'loading' | 'scanned' | 'success' | 'expired'
  >('idle')
  const errorMessage = ref<string>('')

  // 轮询相关
  let pollTimer: number | null = null
  let pollCount = 0
  const MAX_POLL_COUNT = 60 // 2分钟，每2秒轮询一次

  // 生成二维码
  const generateQRCode = async () => {
    try {
      loginStatus.value = 'loading'
      errorMessage.value = ''

      // 调用后端接口生成授权URL，可以传入自定义参数
      const response = await authApi.generatePCScanUrl({
        items: [
          { paramKey: 'source', paramValue: 'pc' },
          { paramKey: 'channel', paramValue: 'web' },
        ],
      })
      const authUrl = response.data

      // 提取scanFlag
      const urlParams = new URLSearchParams(authUrl.split('?')[1])
      const scanFlagValue = urlParams.get('scanFlag')
      if (scanFlagValue) {
        scanFlag.value = scanFlagValue
      }

      // 生成二维码图片
      const qrDataUrl = await QRCode.toDataURL(authUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      })

      qrCodeUrl.value = qrDataUrl
      loginStatus.value = 'idle'

      // 开始轮询
      startPolling()
    } catch (error) {
      console.error('生成二维码失败:', error)
      errorMessage.value = '生成二维码失败，请重试'
      loginStatus.value = 'idle'
    }
  }

  // 开始轮询扫码状态
  const startPolling = () => {
    if (pollTimer) {
      clearInterval(pollTimer)
    }

    pollCount = 0
    pollTimer = setInterval(async () => {
      pollCount++

      // 超过最大轮询次数
      if (pollCount > MAX_POLL_COUNT) {
        stopPolling()
        loginStatus.value = 'expired'
        errorMessage.value = '二维码已过期，请重新获取'
        return
      }

      try {
        const response = await authApi.checkScanResult(scanFlag.value)
        const { scanStatus, token } = response.data

        switch (scanStatus) {
          case 0:
            // 未扫码，继续轮询
            break

          case 1:
            // 已扫码，等待确认
            loginStatus.value = 'scanned'
            break

          case 2:
            // 登录成功
            stopPolling()
            loginStatus.value = 'success'

            // 获取用户信息
            const userInfo = await authApi.getLoginInfo()
            authStore.setLoginInfo(token, userInfo.data.customerUserInfo)
            authStore.loginType = 'qrcode'

            // 跳转到首页或指定页面
            await navigateAfterLogin()
            break

          case 3:
            // 二维码失效
            stopPolling()
            loginStatus.value = 'expired'
            errorMessage.value = '二维码已失效，请重新获取'
            break
        }
      } catch (error) {
        console.error('轮询扫码状态失败:', error)
        // 网络错误不中断轮询，继续尝试
      }
    }, 2000) // 每2秒轮询一次
  }

  // 停止轮询
  const stopPolling = () => {
    if (pollTimer) {
      clearInterval(pollTimer)
      pollTimer = null
    }
  }

  // 重新生成二维码
  const refreshQRCode = () => {
    stopPolling()
    generateQRCode()
  }

  // 登录后导航
  const navigateAfterLogin = async () => {
    const router = useRouter()
    const route = useRoute()

    // 获取重定向地址
    const redirect = (route.query.redirect as string) || '/'
    await router.push(redirect)
  }

  // 组件卸载时清理
  onUnmounted(() => {
    stopPolling()
  })

  return {
    qrCodeUrl,
    loginStatus,
    errorMessage,
    generateQRCode,
    refreshQRCode,
    stopPolling,
  }
}
```

### 4.3 移动端微信登录实现

#### 4.3.1 微信登录处理

```typescript
// composables/useWechatLogin.ts
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { authApi } from '@live/core'
import type { CustomerWechatLoginDTO } from '@live/core'
import { useAuthStore } from '@/stores/auth'

export function useWechatLogin() {
  const route = useRoute()
  const router = useRouter()
  const authStore = useAuthStore()

  const isLoading = ref(false)
  const errorMessage = ref('')

  // 检查是否在微信环境
  const isWechatBrowser = () => {
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('micromessenger')
  }

  // 获取微信授权URL
  const getWechatAuthUrl = (scanFlag?: string) => {
    const appId = import.meta.env.VITE_WECHAT_APPID
    const currentUrl = new URL(window.location.href)

    // 如果有scanFlag，添加到URL参数中
    if (scanFlag) {
      currentUrl.searchParams.set('scanFlag', scanFlag)
    }

    const redirectUri = encodeURIComponent(currentUrl.toString())
    const state = scanFlag ? `scanFlag_${scanFlag}` : 'direct_login'

    return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`
  }

  // 处理微信授权回调
  const handleWechatCallback = async () => {
    const code = route.query.code as string
    const state = route.query.state as string

    if (!code) {
      // 没有code，重定向到微信授权
      redirectToWechatAuth()
      return
    }

    try {
      isLoading.value = true
      errorMessage.value = ''

      // 解析scanFlag
      let scanFlag: string | undefined

      // 从URL参数中提取scanFlag
      const urlParams = new URLSearchParams(window.location.search)
      scanFlag = urlParams.get('scanFlag') || undefined

      // 调用登录接口
      const response = await authApi.wechatLogin({
        code,
        scanFlag,
      })

      const token = response.data

      // 获取用户信息
      const userInfo = await authApi.getLoginInfo()

      // 存储登录信息
      authStore.setLoginInfo(token, userInfo.data.customerUserInfo)
      authStore.loginType = 'wechat'

      // 如果是扫码登录，显示成功提示后关闭页面
      if (scanFlag) {
        showSuccessMessage('登录成功，请返回PC端继续操作')
        setTimeout(() => {
          // 尝试关闭当前页面
          window.close()
        }, 2000)
      } else {
        // 直接登录，跳转到首页
        await router.push('/')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      errorMessage.value = '登录失败，请重试'

      // 清除URL中的code参数
      const newUrl = window.location.pathname
      window.history.replaceState({}, '', newUrl)
    } finally {
      isLoading.value = false
    }
  }

  // 重定向到微信授权
  const redirectToWechatAuth = () => {
    const scanFlag = route.query.scanFlag as string
    const authUrl = getWechatAuthUrl(scanFlag)
    window.location.href = authUrl
  }

  // 直接微信登录（非扫码场景）
  const startWechatLogin = () => {
    if (!isWechatBrowser()) {
      errorMessage.value = '请在微信中打开'
      return
    }

    redirectToWechatAuth()
  }

  // 显示成功消息
  const showSuccessMessage = (message: string) => {
    // 这里可以使用UI库的消息提示组件
    alert(message)
  }

  return {
    isLoading,
    errorMessage,
    isWechatBrowser,
    handleWechatCallback,
    startWechatLogin,
  }
}
```

### 4.4 HTTP拦截器配置

#### 4.4.1 请求拦截器

```typescript
// lib/http.ts
import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { useAuthStore } from '@/stores/auth'
import { toast } from '@live/ui'

// 创建axios实例
const http = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
http.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const authStore = useAuthStore()

    // 添加Token到请求头
    if (authStore.token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    // 添加请求时间戳
    config.headers['X-Request-Time'] = Date.now().toString()

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, msg, data } = response.data

    // 统一处理业务状态码
    if (code === 0) {
      return response
    } else {
      console.error('业务错误:', code, msg)
      toast.error(msg || '请求失败')
      return Promise.reject(new Error(msg))
    }
  },
  async (error) => {
    const authStore = useAuthStore()

    if (error.response) {
      const { status } = error.response

      switch (status) {
        case 401:
          // Token过期或无效
          const refreshSuccess = await authStore.refreshAccessToken()
          if (refreshSuccess) {
            // 重试原请求
            return http.request(error.config)
          } else {
            // 刷新失败，跳转登录页
            authStore.clearLoginInfo()
            window.location.href = '/login'
          }
          break

        case 403:
          toast.error('没有权限访问该资源')
          break

        case 404:
          toast.error('请求的资源不存在')
          break

        case 500:
          toast.error('服务器内部错误')
          break

        default:
          toast.error('网络请求失败')
      }
    } else if (error.code === 'NETWORK_ERROR') {
      toast.error('网络连接失败，请检查网络设置')
    } else if (error.code === 'ECONNABORTED') {
      toast.error('请求超时，请重试')
    } else {
      toast.error('请求失败，请重试')
    }

    return Promise.reject(error)
  }
)

export default http
```

## 5. 登录流程详细说明

### 5.1 PC端扫码登录流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant PC as PC端浏览器
    participant Mobile as 手机微信
    participant BE as 后端服务
    participant WX as 微信服务

    Note over U, WX: PC端扫码登录完整流程

    U->>PC: 访问登录页面
    PC->>BE: POST /customer-login/generatePCScanUrl
    BE-->>PC: 返回微信授权URL
    PC->>PC: 生成二维码并显示
    PC->>PC: 开始轮询扫码状态

    U->>Mobile: 扫描二维码
    Mobile->>WX: 跳转微信授权页面
    U->>Mobile: 确认授权
    WX-->>Mobile: 返回授权码(code)
    Mobile->>BE: POST /customer-login/wechat-login
    BE-->>Mobile: 返回登录成功

    loop 轮询检查
        PC->>BE: GET /customer-login/checkScanResult/{scanFlag}
        alt 用户已扫码
            BE-->>PC: scanStatus=1 (已扫码等待确认)
            PC->>PC: 更新状态为"已扫码"
        else 用户确认登录
            BE-->>PC: scanStatus=2 + token
            PC->>PC: 停止轮询
            PC->>BE: GET /login-info/getLoginInfo
            BE-->>PC: 返回用户信息
            PC->>PC: 存储登录状态
            PC-->>U: 跳转到首页
        else 二维码过期
            BE-->>PC: scanStatus=3
            PC->>PC: 提示二维码过期
        end
    end
```

### 5.2 移动端微信登录流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant H5 as 移动端H5
    participant WX as 微信服务
    participant BE as 后端服务

    Note over U, BE: 移动端微信登录流程

    alt 直接登录场景
        U->>H5: 访问登录页面
        H5->>H5: 检查是否在微信内
        H5->>WX: 重定向到微信授权页面
        U->>WX: 确认授权
        WX-->>H5: 返回授权码(code)
        H5->>BE: POST /customer-login/wechat-login
        BE-->>H5: 返回token和用户信息
        H5->>H5: 存储登录状态
        H5-->>U: 跳转到首页

    else 扫码登录场景
        U->>H5: 扫码访问 /authorize?scanFlag=xxx
        H5->>H5: 检查URL中的code参数
        alt 没有code
            H5->>WX: 重定向微信授权(带scanFlag)
            U->>WX: 确认授权
            WX-->>H5: 返回授权码(code)
        end
        H5->>BE: POST /customer-login/wechat-login {code, scanFlag}
        BE-->>H5: 返回登录成功
        H5-->>U: 显示"登录成功，请返回PC端"
        H5->>H5: 尝试关闭当前页面
    end
```

## 6. 安全机制和错误处理

### 6.1 安全防护措施

#### 6.1.1 Token安全

- **Token加密存储**：敏感Token信息加密后存储在LocalStorage `[🔧 需要实现]`
- **Token过期处理**：自动检测Token过期并尝试刷新 `[✅ 已实现在HTTP拦截器中]`
- **HTTPS传输**：所有认证相关请求强制使用HTTPS `[🔧 需要配置]`
- **域名验证**：验证请求来源域名的合法性 `[🔧 需要实现]`

```typescript
// utils/security.ts
import CryptoJS from 'crypto-js'

const SECRET_KEY = 'your-secret-key' // 实际应用中应该从环境变量获取

// 加密Token
export function encryptToken(token: string): string {
  return CryptoJS.AES.encrypt(token, SECRET_KEY).toString()
}

// 解密Token
export function decryptToken(encryptedToken: string): string {
  const bytes = CryptoJS.AES.decrypt(encryptedToken, SECRET_KEY)
  return bytes.toString(CryptoJS.enc.Utf8)
}

// 安全存储Token
export function setSecureToken(key: string, token: string) {
  const encryptedToken = encryptToken(token)
  localStorage.setItem(key, encryptedToken)
}

// 安全获取Token
export function getSecureToken(key: string): string | null {
  const encryptedToken = localStorage.getItem(key)
  if (!encryptedToken) return null

  try {
    return decryptToken(encryptedToken)
  } catch (error) {
    console.error('Token解密失败:', error)
    localStorage.removeItem(key)
    return null
  }
}
```

#### 6.1.2 防重放攻击

- **请求时间戳**：每个请求添加时间戳，防止重放攻击 `[🔧 需要实现]`
- **请求签名**：关键接口添加签名验证 `[🔧 需要实现]`
- **随机数**：使用随机数增加请求的唯一性 `[🔧 需要实现]`

#### 6.1.3 二维码安全

- **时效控制**：二维码有效期限制为2分钟 `[✅ 已实现在后端]`
- **一次性使用**：每个scanFlag只能使用一次 `[✅ 已实现在后端]`
- **状态验证**：严格验证扫码状态的变更流程 `[✅ 已实现在接口中]`

### 6.2 错误处理机制

#### 6.2.1 网络错误处理 `[✅ 已实现在 apps/web/src/utils/error-handler.ts]`

```typescript
// apps/web/src/utils/error-handler.ts (实际实现)
export interface ErrorInfo {
  type: 'javascript' | 'resource' | 'network' | 'business'
  message: string
  stack?: string
  url?: string
  line?: number
  column?: number
  timestamp: number
  userAgent: string
  userId?: string
}

class ErrorHandler {
  // 手动报告业务错误
  reportError(message: string, extra?: any) {
    const errorInfo: ErrorInfo = {
      type: 'business',
      message,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      ...extra,
    }
    this.logError(errorInfo)
  }

  // 显示用户提示（自适应PC/移动端）
  private async showUserMessage(message: string) {
    const uiStore = useUIStore()
    if (uiStore.deviceType === 'mobile') {
      const { showFailToast } = await import('vant')
      showFailToast(message)
    } else {
      const { ElMessage } = await import('element-plus')
      ElMessage.error(message)
    }
  }

  // 上报到服务器
  private async reportToServer(errorInfo: ErrorInfo) {
    try {
      await fetch('/api/error-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorInfo),
      })
    } catch {
      // 静默处理上报失败
    }
  }
}
```

#### 6.2.2 登录异常处理 `[🔧 需要补充登录专用错误处理]`

```typescript
// composables/useLoginErrorHandler.ts [🔧 建议新增]
import { ref } from 'vue'
import { errorHandler } from '@/utils/error-handler'

export function useLoginErrorHandler() {
  const isRetrying = ref(false)
  const retryCount = ref(0)
  const maxRetryCount = 3

  // 处理登录错误
  const handleLoginError = (
    error: any,
    context?: { action: string; scanFlag?: string }
  ) => {
    // 使用全局错误处理器记录错误
    errorHandler.reportError(`登录失败: ${error.message}`, {
      type: 'network',
      action: context?.action || 'unknown',
      scanFlag: context?.scanFlag,
      userId: getCurrentUserId(), // [🔧 需要实现]
    })

    // 根据错误类型显示不同提示
    if (error.response) {
      const { status } = error.response
      switch (status) {
        case 401:
          showLoginExpiredDialog() // [🔧 需要实现]
          break
        case 403:
          showPermissionDeniedDialog() // [🔧 需要实现]
          break
        case 429:
          showRateLimitDialog() // [🔧 需要实现]
          break
        default:
          showGenericErrorDialog(error.message)
      }
    } else if (error.code === 'NETWORK_ERROR') {
      showNetworkErrorDialog()
    } else {
      showGenericErrorDialog(error.message)
    }
  }

  // 处理二维码相关错误
  const handleQRCodeError = (error: any, scanFlag: string) => {
    errorHandler.reportError(`二维码登录失败: ${error.message}`, {
      type: 'business',
      action: 'qrcode_login',
      scanFlag,
    })

    if (retryCount.value < maxRetryCount) {
      retryCount.value++
      return true // 可以重试
    }
    return false
  }

  // 处理微信登录错误
  const handleWechatError = (error: any, code?: string) => {
    errorHandler.reportError(`微信登录失败: ${error.message}`, {
      type: 'business',
      action: 'wechat_login',
      wechatCode: code,
    })
  }

  return {
    isRetrying,
    retryCount,
    handleLoginError,
    handleQRCodeError,
    handleWechatError,
    resetRetry: () => {
      retryCount.value = 0
      isRetrying.value = false
    },
  }
}

// [🔧 需要实现的辅助函数]
function getCurrentUserId(): string | undefined {
  // 从认证store获取当前用户ID
  return undefined
}

function showLoginExpiredDialog() {
  // 显示登录过期对话框
}

function showPermissionDeniedDialog() {
  // 显示权限不足对话框
}

function showRateLimitDialog() {
  // 显示请求频率限制对话框
}

function showNetworkErrorDialog() {
  // 显示网络错误对话框
}

function showGenericErrorDialog(message: string) {
  // 显示通用错误对话框
}
```

#### 6.2.3 二维码相关错误处理 `[🔧 需要集成到error-handler.ts]`

- **生成失败**：网络错误时提供重试机制，使用 `errorHandler.reportError()` 记录
- **轮询超时**：超过最大轮询次数时提示二维码过期，记录超时事件
- **状态异常**：处理扫码状态不一致的情况，上报异常状态

```typescript
// 在二维码登录组合函数中集成错误处理 [🔧 需要更新现有代码]
import { errorHandler } from '@/utils/error-handler'

export function useQRCodeLogin() {
  // ... 现有代码

  const generateQRCode = async () => {
    try {
      // ... 现有二维码生成逻辑
    } catch (error) {
      // 集成全局错误处理
      errorHandler.reportError('二维码生成失败', {
        type: 'network',
        action: 'generate_qrcode',
        error: error.message,
      })

      errorMessage.value = '生成二维码失败，请重试'
      loginStatus.value = 'idle'
    }
  }

  const checkScanResult = async () => {
    try {
      // ... 现有轮询逻辑
    } catch (error) {
      // 记录轮询错误但不中断
      errorHandler.reportError('扫码状态检查失败', {
        type: 'network',
        action: 'check_scan_result',
        scanFlag: scanFlag.value,
        pollCount: pollCount,
      })
    }
  }
}
```

## 7. 用户会话管理 `[🔧 需要实现 - 无对应API接口]`

### 7.1 会话状态设计

```typescript
// types/session.ts
export interface SessionInfo {
  sessionId: string // 会话ID
  userId: string // 用户ID
  deviceId: string // 设备ID
  deviceType: 'pc' | 'mobile' // 设备类型
  loginTime: number // 登录时间
  lastActiveTime: number // 最后活跃时间
  ipAddress: string // IP地址
  userAgent: string // 用户代理
  location?: {
    // 地理位置
    country: string
    province: string
    city: string
  }
}

export interface SessionState {
  current: SessionInfo | null // 当前会话
  history: SessionInfo[] // 历史会话
  isActive: boolean // 会话是否活跃
  heartbeatInterval: number // 心跳间隔
}
```

### 7.2 会话保活机制 `[🔧 需要实现心跳接口]`

```typescript
// composables/useSession.ts
import { ref, onMounted, onUnmounted } from 'vue'

export function useSession() {
  const sessionState = ref<SessionState>({
    current: null,
    history: [],
    isActive: false,
    heartbeatInterval: 30000, // 30秒心跳
  })

  let heartbeatTimer: number | null = null

  // 开始会话
  const startSession = (sessionInfo: SessionInfo) => {
    sessionState.value.current = sessionInfo
    sessionState.value.isActive = true

    // 开始心跳
    startHeartbeat()

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 监听页面卸载
    window.addEventListener('beforeunload', handleBeforeUnload)
  }

  // 结束会话
  const endSession = () => {
    if (sessionState.value.current) {
      // 添加到历史记录
      sessionState.value.history.push(sessionState.value.current)
      sessionState.value.current = null
    }

    sessionState.value.isActive = false
    stopHeartbeat()

    // 移除事件监听
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('beforeunload', handleBeforeUnload)
  }

  // 开始心跳
  const startHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
    }

    heartbeatTimer = setInterval(async () => {
      try {
        await sendHeartbeat()
        updateLastActiveTime()
      } catch (error) {
        console.error('心跳失败:', error)
        // 心跳失败可能表示会话已失效
        handleSessionExpired()
      }
    }, sessionState.value.heartbeatInterval)
  }

  // 停止心跳
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  // 发送心跳请求
  const sendHeartbeat = async () => {
    // 发送心跳请求到服务器 [🔧 需要实现heartbeat接口]
    await authApi.heartbeat()
  }

  // 更新最后活跃时间
  const updateLastActiveTime = () => {
    if (sessionState.value.current) {
      sessionState.value.current.lastActiveTime = Date.now()
    }
  }

  // 处理页面可见性变化
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // 页面隐藏，停止心跳
      stopHeartbeat()
    } else {
      // 页面可见，恢复心跳
      if (sessionState.value.isActive) {
        startHeartbeat()
      }
    }
  }

  // 处理页面卸载
  const handleBeforeUnload = () => {
    // 发送会话结束通知
    navigator.sendBeacon(
      '/api/session/end',
      JSON.stringify({
        sessionId: sessionState.value.current?.sessionId,
      })
    )
  }

  // 处理会话过期
  const handleSessionExpired = () => {
    endSession()
    const authStore = useAuthStore()
    authStore.clearLoginInfo()

    // 跳转到登录页
    window.location.href = '/login?reason=session_expired'
  }

  onMounted(() => {
    // 恢复会话状态
    const savedSession = localStorage.getItem('session_info')
    if (savedSession) {
      try {
        const sessionInfo = JSON.parse(savedSession)
        startSession(sessionInfo)
      } catch (error) {
        console.error('恢复会话失败:', error)
      }
    }
  })

  onUnmounted(() => {
    endSession()
  })

  return {
    sessionState: readonly(sessionState),
    startSession,
    endSession,
    updateLastActiveTime,
  }
}
```

### 7.3 多端登录管理 `[🔧 需要实现 - 无对应API接口]`

```typescript
// stores/multiDevice.ts
export interface DeviceSession {
  deviceId: string
  deviceType: 'pc' | 'mobile' | 'tablet'
  deviceName: string
  loginTime: number
  lastActiveTime: number
  ipAddress: string
  location: string
  isCurrent: boolean
}

export const useMultiDeviceStore = defineStore('multiDevice', {
  state: () => ({
    sessions: [] as DeviceSession[],
    maxDeviceCount: 3, // 最大同时登录设备数
  }),

  actions: {
    // 获取所有会话 [🔧 需要实现getSessions接口]
    async fetchSessions() {
      const response = await authApi.getSessions()
      this.sessions = response.data
    },

    // 踢出指定设备 [🔧 需要实现kickoutDevice接口]
    async kickoutDevice(deviceId: string) {
      await authApi.kickoutDevice(deviceId)
      this.sessions = this.sessions.filter((s) => s.deviceId !== deviceId)
    },

    // 踢出所有其他设备 [🔧 需要实现kickoutAllOthers接口]
    async kickoutAllOthers() {
      await authApi.kickoutAllOthers()
      this.sessions = this.sessions.filter((s) => s.isCurrent)
    },

    // 检查设备数量限制
    checkDeviceLimit() {
      const activeDevices = this.sessions.filter(
        (s) => Date.now() - s.lastActiveTime < 30 * 60 * 1000 // 30分钟内活跃
      )

      return activeDevices.length < this.maxDeviceCount
    },
  },
})
```

## 8. 开发指南

### 8.1 本地开发环境搭建

#### 8.1.1 环境要求

- Node.js >= 16.0.0
- pnpm >= 7.0.0
- 现代浏览器（Chrome、Firefox、Safari、Edge）

#### 8.1.2 安装和启动

```bash
# 克隆项目
git clone <repository-url>
cd live-ui-client

# 安装依赖
pnpm install

# 复制环境变量文件
cp apps/web/.env.example apps/web/.env.local

# 配置环境变量
# 编辑 apps/web/.env.local 文件，设置以下变量：
# VITE_API_BASE_URL=http://localhost:8080
# VITE_WECHAT_APPID=your_wechat_appid

# 启动开发服务器
pnpm dev
```

#### 8.1.3 开发服务器配置

```typescript
// vite.config.ts
export default defineConfig({
  server: {
    port: 5173,
    proxy: {
      '/customer-apis': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/customer-apis/, ''),
      },
    },
  },
})
```

### 8.2 组件开发规范

#### 8.2.1 登录组件示例

```vue
<!-- components/LoginModal.vue -->
<template>
  <div class="login-modal" v-if="visible">
    <div class="login-modal-content">
      <div class="login-header">
        <h2>登录花粉直播</h2>
        <button @click="close" class="close-btn">×</button>
      </div>

      <div class="login-body">
        <!-- PC端显示二维码 -->
        <div v-if="!isMobile" class="qrcode-login">
          <div class="qrcode-container">
            <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="登录二维码" />
            <div v-else class="qrcode-loading">生成中...</div>

            <!-- 扫码状态提示 -->
            <div class="scan-status" :class="loginStatus">
              <div v-if="loginStatus === 'idle'">请使用微信扫描二维码登录</div>
              <div v-else-if="loginStatus === 'scanned'">
                扫码成功，请在手机上确认登录
              </div>
              <div v-else-if="loginStatus === 'expired'">
                二维码已过期
                <button @click="refreshQRCode" class="refresh-btn">
                  重新获取
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 移动端显示微信登录按钮 -->
        <div v-else class="mobile-login">
          <button
            @click="startWechatLogin"
            :disabled="isLoading"
            class="wechat-login-btn"
          >
            <span v-if="!isLoading">微信登录</span>
            <span v-else>登录中...</span>
          </button>
        </div>

        <!-- 错误提示 -->
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useQRCodeLogin } from '@/composables/useLogin'
import { useWechatLogin } from '@/composables/useWechatLogin'
import { useUIStore } from '@/stores/ui'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'login-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const uiStore = useUIStore()
const isMobile = computed(() => uiStore.isMobile)

// PC端二维码登录
const {
  qrCodeUrl,
  loginStatus,
  errorMessage: qrErrorMessage,
  generateQRCode,
  refreshQRCode,
} = useQRCodeLogin()

// 移动端微信登录
const {
  isLoading,
  errorMessage: wechatErrorMessage,
  startWechatLogin,
} = useWechatLogin()

// 合并错误消息
const errorMessage = computed(
  () => qrErrorMessage.value || wechatErrorMessage.value
)

// 关闭弹窗
const close = () => {
  emit('update:visible', false)
}

// 监听登录状态
watch(loginStatus, (status) => {
  if (status === 'success') {
    emit('login-success')
    close()
  }
})

// 组件挂载时初始化
onMounted(() => {
  if (props.visible && !isMobile.value) {
    generateQRCode()
  }
})

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && !isMobile.value) {
      generateQRCode()
    }
  }
)
</script>

<style scoped lang="scss">
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.login-modal-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  min-width: 400px;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    min-width: auto;
    width: 90%;
    max-width: 400px;
  }
}

.login-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;

    &:hover {
      color: #333;
    }
  }
}

.qrcode-container {
  text-align: center;

  img {
    width: 200px;
    height: 200px;
    border: 1px solid #eee;
    border-radius: 8px;
  }

  .qrcode-loading {
    width: 200px;
    height: 200px;
    border: 1px solid #eee;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    margin: 0 auto;
  }
}

.scan-status {
  margin-top: 16px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;

  &.idle {
    background: #f0f9ff;
    color: #0369a1;
  }

  &.scanned {
    background: #f0fdf4;
    color: #166534;
  }

  &.expired {
    background: #fef2f2;
    color: #dc2626;
  }

  .refresh-btn {
    margin-left: 8px;
    padding: 4px 8px;
    background: #dc2626;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      background: #b91c1c;
    }
  }
}

.mobile-login {
  text-align: center;

  .wechat-login-btn {
    width: 100%;
    padding: 16px;
    background: #07c160;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;

    &:hover:not(:disabled) {
      background: #06ad56;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.error-message {
  margin-top: 16px;
  padding: 12px;
  background: #fef2f2;
  color: #dc2626;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
}
</style>
```

### 8.3 API集成示例

#### 8.3.1 核心API封装

```typescript
// packages/core/src/api/auth.ts (实际实现)
import { request } from '../utils/request'

// 认证相关接口类型定义
export interface CustomerWechatLoginDTO {
  code: string
  scanFlag?: string
}

export interface CustomerParamItemDTO {
  paramKey: string
  paramValue: string
}

export interface CustomerParamDTO {
  items: CustomerParamItemDTO[]
}

export interface ScanFlagCheckResultVO {
  scanStatus: number // 0：未扫码 1：已扫码-未登录 2：已扫码-已登录 3：二维码已失效
  token?: string
}

export interface LoginUser {
  loginType: number
  userId: number
  token: string
  loginTime: number
  expireTime: number
  ipaddr: string
  loginLocation: string
  browser: string
  os: string
  permissions: string[]
  customerUserInfo?: {
    id: number
    wechatOpenId: string
    wechatUnionId: string
    userName: string
    mobile: string
    email: string
    gender: number
    avatar: string
    loginIp: string
    loginDate: string
    status: number
  }
}

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 手机端微信登录/PC扫码登录/注册三合一
   */
  wechatLogin: (data: CustomerWechatLoginDTO) =>
    request.post<string>('/customer-login/wechat-login', data),

  /**
   * 生成PC端二维码链接（有效期120秒）
   */
  generatePCScanUrl: (data: CustomerParamDTO) =>
    request.post<string>('/customer-login/generatePCScanUrl', data),

  /**
   * 检查C端用户扫描结果
   */
  checkScanResult: (scanFlag: string) =>
    request.get<ScanFlagCheckResultVO>(
      `/customer-login/checkScanResult/${scanFlag}`
    ),

  /**
   * 获取登录用户信息
   */
  getLoginInfo: () => request.get<LoginUser>('/login-info/getLoginInfo'),

  /**
   * 获取路由信息
   */
  getRouters: () => request.get<any>('/login-info/getRouters'),

  /**
   * 直播间登录
   */
  liveRoomLogin: (data: LiveRoomLoginDTO) =>
    request.post<string>('/anchor-login/live-room-login', data),

  /**
   * 获取微信用户openId、unionId、accessToken等信息
   */
  getMpOpenId: (code: string) =>
    request.get<WxOAuth2AccessToken>(`/mp/open/getMpOpenId?code=${code}`),

  /**
   * 获取微信用户信息（含授权信息）
   */
  getMpUserInfo: (code: string) =>
    request.get<MpUserInfoVO>(`/mp/open/getMpUserInfo?code=${code}`),
}
```

**使用示例**：

```typescript
// 导入API
import { authApi } from '@live/core'

// 生成二维码URL（不带自定义参数）
const qrResponse1 = await authApi.generatePCScanUrl({ items: [] })
// 返回: "https://live-test.fanspvt.com?scanFlag=123"

// 生成二维码URL（带自定义参数）
const qrResponse2 = await authApi.generatePCScanUrl({
  items: [
    { paramKey: 'source', paramValue: 'pc' },
    { paramKey: 'roomId', paramValue: '456' },
  ],
})
// 返回: "https://live-test.fanspvt.com?scanFlag=123&source=pc&roomId=456"

// 检查扫码状态
const scanResult = await authApi.checkScanResult('scanFlag123')

// 微信登录
const loginResponse = await authApi.wechatLogin({
  code: 'wx_auth_code',
  scanFlag: 'optional_scan_flag',
})
```

### 8.4 测试用例说明

#### 8.4.1 单元测试

```typescript
// tests/unit/auth.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import { useQRCodeLogin } from '@/composables/useLogin'
import LoginModal from '@/components/LoginModal.vue'

describe('登录认证模块', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('AuthStore', () => {
    it('应该正确设置登录信息', () => {
      const authStore = useAuthStore()
      const mockUser = {
        userId: '123',
        nickname: '测试用户',
        avatar: 'avatar.jpg',
      }
      const mockToken = 'mock-token'

      authStore.setLoginInfo(mockToken, mockUser)

      expect(authStore.token).toBe(mockToken)
      expect(authStore.user).toEqual(mockUser)
      expect(authStore.isLoggedIn).toBe(true)
    })

    it('应该正确清除登录信息', () => {
      const authStore = useAuthStore()

      // 先设置登录信息
      authStore.setLoginInfo('token', {} as any)

      // 然后清除
      authStore.clearLoginInfo()

      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
      expect(authStore.isLoggedIn).toBe(false)
    })
  })

  describe('QRCode Login', () => {
    it('应该能够生成二维码', async () => {
      // Mock API调用
      const mockApiResponse = {
        code: 200,
        data: 'https://open.weixin.qq.com/connect/oauth2/authorize?state=scanFlag_123',
        message: 'success',
        timestamp: Date.now(),
      }

      vi.spyOn(window, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockApiResponse),
      } as Response)

      const { generateQRCode, qrCodeUrl } = useQRCodeLogin()
      await generateQRCode()

      expect(qrCodeUrl.value).toBeTruthy()
    })

    it('应该能够轮询扫码状态', async () => {
      // Mock轮询响应
      const mockCheckResponse = {
        code: 200,
        data: { scanStatus: 1 },
        message: 'success',
        timestamp: Date.now(),
      }

      vi.spyOn(window, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockCheckResponse),
      } as Response)

      const { generateQRCode, loginStatus } = useQRCodeLogin()
      await generateQRCode()

      // 等待轮询开始
      await new Promise((resolve) => setTimeout(resolve, 100))

      expect(loginStatus.value).toBe('idle')
    })
  })

  describe('LoginModal Component', () => {
    it('应该在PC端显示二维码', () => {
      const wrapper = mount(LoginModal, {
        props: {
          visible: true,
        },
        global: {
          plugins: [createPinia()],
        },
      })

      expect(wrapper.find('.qrcode-login').exists()).toBe(true)
    })

    it('应该在移动端显示微信登录按钮', async () => {
      // Mock移动端环境
      const uiStore = useUIStore()
      uiStore.setDevice('mobile')

      const wrapper = mount(LoginModal, {
        props: {
          visible: true,
        },
        global: {
          plugins: [createPinia()],
        },
      })

      expect(wrapper.find('.mobile-login').exists()).toBe(true)
      expect(wrapper.find('.wechat-login-btn').exists()).toBe(true)
    })
  })
})
```

#### 8.4.2 集成测试

```typescript
// tests/integration/login-flow.test.ts
import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import App from '@/App.vue'

describe('登录流程集成测试', () => {
  let router: any
  let pinia: any

  beforeEach(() => {
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/login', component: { template: '<div>Login</div>' } },
      ],
    })
    pinia = createPinia()
  })

  it('应该完成PC端扫码登录流程', async () => {
    // 模拟完整的扫码登录流程
    const wrapper = mount(App, {
      global: {
        plugins: [router, pinia],
      },
    })

    // 1. 访问需要登录的页面，应该跳转到登录页
    await router.push('/protected')
    await wrapper.vm.$nextTick()

    // 2. 生成二维码
    // 3. 模拟扫码
    // 4. 验证登录成功
    // 5. 验证跳转到目标页面

    // 这里需要根据实际的组件结构来编写测试逻辑
  })

  it('应该完成移动端微信登录流程', async () => {
    // 模拟移动端微信登录流程
    // 类似上面的测试，但针对移动端场景
  })
})
```

#### 8.4.3 E2E测试

```typescript
// tests/e2e/login.spec.ts
import { test, expect } from '@playwright/test'

test.describe('登录功能E2E测试', () => {
  test('PC端扫码登录', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login')

    // 等待二维码加载
    await page.waitForSelector('.qrcode-container img')

    // 验证二维码存在
    const qrcode = page.locator('.qrcode-container img')
    await expect(qrcode).toBeVisible()

    // 验证扫码提示文字
    await expect(page.locator('.scan-status')).toContainText(
      '请使用微信扫描二维码登录'
    )

    // 这里可以进一步测试扫码流程，但需要模拟微信扫码
  })

  test('移动端微信登录', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })

    // 访问登录页面
    await page.goto('/login')

    // 验证微信登录按钮存在
    const wechatBtn = page.locator('.wechat-login-btn')
    await expect(wechatBtn).toBeVisible()
    await expect(wechatBtn).toContainText('微信登录')

    // 点击微信登录按钮
    await wechatBtn.click()

    // 验证跳转到微信授权页面（实际测试中可能需要mock）
    // await expect(page).toHaveURL(/open\.weixin\.qq\.com/)
  })

  test('登录状态持久化', async ({ page }) => {
    // 模拟已登录状态
    await page.addInitScript(() => {
      localStorage.setItem('access_token', 'mock-token')
      localStorage.setItem(
        'user_info',
        JSON.stringify({
          userId: '123',
          nickname: '测试用户',
          avatar: 'avatar.jpg',
        })
      )
    })

    // 访问首页
    await page.goto('/')

    // 验证登录状态
    await expect(page.locator('.user-info')).toContainText('测试用户')

    // 刷新页面，验证状态保持
    await page.reload()
    await expect(page.locator('.user-info')).toContainText('测试用户')
  })

  test('Token过期处理', async ({ page }) => {
    // 设置过期的Token
    await page.addInitScript(() => {
      localStorage.setItem('access_token', 'expired-token')
    })

    // 模拟API返回401错误
    await page.route('**/api/**', (route) => {
      route.fulfill({
        status: 401,
        body: JSON.stringify({ code: 401, message: 'Token expired' }),
      })
    })

    // 访问需要认证的页面
    await page.goto('/profile')

    // 验证跳转到登录页面
    await expect(page).toHaveURL(/\/login/)
  })
})
```

## 9. 部署和配置

### 9.1 生产环境配置

#### 9.1.1 环境变量配置

```bash
# .env.production
VITE_APP_TITLE=花粉直播
VITE_APP_BASE_API=/customer-apis
VITE_API_BASE_URL=https://test.fanspvt.com
VITE_WECHAT_APPID=wxef0ed36332a50cf1
VITE_ALIYUN_IM_APP_ID=your_im_app_id
VITE_ALIYUN_IM_APP_KEY=your_im_app_key

# 安全配置
VITE_ENCRYPT_SECRET=your-production-secret
VITE_API_TIMEOUT=30000

# 监控配置
VITE_SENTRY_DSN=https://your-sentry-dsn
VITE_GA_TRACKING_ID=GA-XXXXXXXX
```

#### 9.1.2 构建配置

```typescript
// vite.config.prod.ts
import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
        manualChunks: {
          vue: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus', 'vant'],
          utils: ['axios', 'qrcode'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
  define: {
    __VUE_PROD_DEVTOOLS__: false,
  },
})
```

### 9.2 CDN和缓存策略

#### 9.2.1 静态资源CDN配置

```nginx
# nginx.conf
server {
    listen 80;
    server_name fanspvt.com;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }

    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # API代理
    location /customer-apis/ {
        proxy_pass http://backend-service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 9.3 监控和日志 `[🔧 需要实现 - 第三方服务配置]`

#### 9.3.1 错误监控配置 `[🔧 需要配置Sentry]`

```typescript
// utils/monitor.ts
import * as Sentry from '@sentry/vue'
import { Integrations } from '@sentry/tracing'

export function initMonitoring(app: App) {
  if (import.meta.env.PROD) {
    Sentry.init({
      app,
      dsn: import.meta.env.VITE_SENTRY_DSN,
      integrations: [new Integrations.BrowserTracing()],
      tracesSampleRate: 0.1,
      environment: import.meta.env.MODE,
      beforeSend(event) {
        // 过滤敏感信息
        if (event.user) {
          delete event.user.email
          delete event.user.ip_address
        }
        return event
      },
    })
  }
}

// 自定义错误上报
export function reportError(error: Error, context?: Record<string, any>) {
  console.error('登录模块错误:', error)

  if (import.meta.env.PROD) {
    Sentry.captureException(error, {
      tags: {
        module: 'auth',
      },
      extra: context,
    })
  }
}
```

#### 9.3.2 用户行为追踪 `[🔧 需要配置Google Analytics]`

```typescript
// utils/analytics.ts
import { gtag } from 'gtag'

export function trackLoginEvent(method: 'qrcode' | 'wechat', success: boolean) {
  gtag('event', 'login', {
    method,
    success,
    event_category: 'auth',
  })
}

export function trackQRCodeGenerate() {
  gtag('event', 'qrcode_generate', {
    event_category: 'auth',
  })
}

export function trackScanStatus(status: number) {
  gtag('event', 'scan_status', {
    status,
    event_category: 'auth',
  })
}
```

## 10. 总结

花粉直播登录认证模块采用现代化的技术栈和架构设计，提供了完整的PC端扫码登录和移动端微信登录解决方案。该模块具有以下特点：

### 10.1 技术特色

- **统一架构**：采用Vue3 + TypeScript + Monorepo架构，代码复用性高
- **响应式设计**：同一套代码同时支持PC和移动端，自动适配
- **模块化设计**：清晰的包结构，便于维护和扩展
- **类型安全**：完整的TypeScript类型定义，减少运行时错误

### 10.2 用户体验

- **简化流程**：PC端扫码登录，移动端一键授权
- **实时反馈**：登录状态实时更新，用户体验流畅
- **错误处理**：完善的错误提示和重试机制
- **状态同步**：多端登录状态实时同步

### 10.3 安全保障

- **Token安全**：加密存储，自动刷新机制
- **会话管理**：完整的会话生命周期管理
- **防护机制**：防重放攻击，时效控制
- **多端管理**：支持多设备登录管理和控制

### 10.4 可维护性

- **代码规范**：统一的编码规范和提交规范
- **测试覆盖**：完整的单元测试、集成测试和E2E测试
- **文档完善**：详细的API文档和开发指南
- **监控告警**：生产环境监控和错误追踪

## 11. 接口实现说明

### 11.1 实际代码结构

登录认证模块的接口实现位于 `packages/core/src/api/auth.ts`，包含以下核心功能：

- **基础认证接口**：微信登录、PC扫码、用户信息获取
- **扩展接口**：微信OAuth信息获取、直播间登录
- **类型定义**：完整的TypeScript类型支持

### 11.2 关键变更说明

1. **生成二维码接口**：需要传入 `CustomerParamDTO` 参数，支持自定义参数
2. **响应格式统一**：所有接口使用 `{code, msg, data}` 格式
3. **类型安全**：提供完整的TypeScript类型定义
4. **扩展性设计**：预留了微信OAuth和直播间登录等扩展接口

### 11.3 开发建议

- 严格按照已定义的接口类型进行开发
- 统一使用 `authApi` 对象调用认证相关接口
- 注意处理异步操作和错误边界情况
- 遵循现有的代码风格和命名规范

这套登录认证模块为花粉直播项目提供了稳定可靠的用户认证基础，支持项目的快速迭代和长期发展。通过良好的架构设计和完善的功能实现，确保了用户的登录体验和系统的安全性。

## 12. 功能实现状态总览

### 12.1 已实现功能 ✅

- **核心认证接口**：微信登录、PC扫码、用户信息获取
- **二维码安全**：时效控制、一次性使用、状态验证
- **Token过期处理**：HTTP拦截器自动处理401错误
- **基础错误处理**：网络异常、业务错误统一处理
- **微信OAuth扩展**：获取微信用户信息和授权信息

### 12.2 需要实现的功能 🔧

#### 12.2.1 安全增强功能

- **Token加密存储**：敏感信息加密存储到LocalStorage
- **防重放攻击**：请求时间戳、签名验证、随机数
- **域名验证**：验证请求来源的合法性
- **HTTPS配置**：强制所有认证请求使用HTTPS

#### 12.2.2 会话管理功能（无API支持）

- **心跳接口**：`/api/auth/heartbeat` - 保持会话活跃
- **会话管理**：会话状态跟踪、自动续期、页面可见性处理
- **会话结束**：`/api/session/end` - 主动结束会话通知

#### 12.2.3 多设备管理功能（无API支持）

- **会话列表**：`/api/auth/sessions` - 获取用户所有登录会话
- **设备踢出**：`/api/auth/kickout/{deviceId}` - 踢出指定设备
- **批量踢出**：`/api/auth/kickout-others` - 踢出所有其他设备

#### 12.2.4 监控和分析功能（第三方服务）

- **错误监控**：Sentry配置和错误上报
- **用户行为追踪**：Google Analytics事件追踪
- **性能监控**：页面加载、接口响应时间监控

### 12.3 开发优先级建议

1. **高优先级**：Token加密存储、HTTPS配置
2. **中优先级**：防重放攻击、错误监控配置
3. **低优先级**：会话管理、多设备管理（需要后端API支持）

### 12.4 错误上报接口说明 `[🔧 需要实现后端接口]`

当前 `error-handler.ts` 会向 `/api/error-report` 接口上报错误信息，但该接口尚未实现。建议后端实现以下错误上报接口：

```typescript
// 错误上报接口定义
interface ErrorReportDTO {
  message: string        // 错误描述
  type: 'network' | 'business' | 'ui' | 'security'  // 错误类型
  level: 'info' | 'warn' | 'error' | 'fatal'        // 错误级别
  context?: {           // 错误上下文信息
    action?: string     // 操作名称（如：wechat_login, generate_qrcode）
    url?: string        // 请求URL
    status?: number     // HTTP状态码
    code?: string       // 业务错误码
    scanFlag?: string   // 二维码标识
    userId?: string     // 用户ID
    timestamp: number   // 错误发生时间
    userAgent: string   // 用户代理
    platform: string   // 平台信息（PC/Mobile）
  }
}

// 推荐的错误上报接口
POST /api/error-report
Content-Type: application/json

{
  "message": "微信登录失败",
  "type": "business",
  "level": "error",
  "context": {
    "action": "wechat_login",
    "code": "40001",
    "userId": "user123",
    "timestamp": 1634567890123,
    "userAgent": "Mozilla/5.0...",
    "platform": "PC"
  }
}
```

**登录相关错误上报示例**：

1. **网络错误上报**：

```typescript
errorHandler.reportError('登录请求超时', {
  type: 'network',
  action: 'wechat_login',
  url: '/customer-login/wechat-login',
  code: 'ECONNABORTED',
})
```

2. **业务错误上报**：

```typescript
errorHandler.reportError('微信授权码已过期', {
  type: 'business',
  action: 'wechat_login',
  code: '40001',
  scanFlag: 'scan123',
})
```

3. **二维码相关错误**：

```typescript
errorHandler.reportError('二维码生成失败', {
  type: 'network',
  action: 'generate_qrcode',
  status: 500,
})
```

### 12.5 注意事项

- 带有 `[🔧 需要实现]` 标注的功能需要开发团队实现
- 带有 `[✅ 已实现]` 标注的功能可以直接使用
- 会话管理和多设备管理功能需要后端提供相应的API接口
- 监控功能需要配置第三方服务（Sentry、Google Analytics等）
- 错误上报接口 `/api/error-report` 需要后端团队实现，用于统一收集前端错误信息

### TODO

优先级 2：

- 防重放攻击机制
- 错误监控配置（Sentry）
- 用户行为追踪（Google Analytics）
- Token加密存储优化
