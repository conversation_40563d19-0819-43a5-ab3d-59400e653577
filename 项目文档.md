# 花粉直播后台管理系统 - 项目文档

## 📋 项目概述

**项目名称**: liveui (花粉直播后台管理系统)  
**版本**: 3.8.5  
**作者**: 花粉  
**许可证**: MIT  
**描述**: 基于 Vue.js 的推广服务管理系统，专注于直播行业的后台管理功能

## 🚀 技术栈

### 前端框架

- **Vue.js**: ^2.7.16 - 核心前端框架
- **Vue Router**: 3.6.5 - 前端路由管理
- **Vuex**: 3.6.0 - 状态管理

### UI 组件库

- **Element UI**: 2.15.13 - 主要 UI 组件库
- **VXE Table**: ^3.7.10 - 高性能表格组件
- **Vant**: ^2.13.2 - 移动端组件库
- **UmyUI**: ^1.1.6 - 自定义 UI 组件

### 开发工具

- **Vue CLI**: ^4.5.18 - 项目构建工具
- **Webpack**: 通过 Vue CLI 配置
- **Babel**: ^7.22.9 - JavaScript 编译器
- **ESLint**: 7.15.0 - 代码规范检查

### 核心依赖

- **Axios**: 0.24.0 - HTTP 客户端
- **dayjs**: ^1.11.7 - 日期时间处理
- **ECharts**: 5.4.0 - 数据可视化
- **CryptoJS**: ^4.1.1 - 加密解密
- **JSEncrypt**: 3.0.0-rc.1 - RSA 加密

## 📁 项目结构

```
live-ui/
├── public/                     # 静态资源
│   ├── html/                  # HTML页面
│   ├── js/                    # 公共JS文件
│   └── logo/                  # 项目logo
├── src/
│   ├── api/                   # API接口层
│   │   ├── account/           # 账户相关接口
│   │   ├── data/             # 数据相关接口
│   │   ├── form/             # 表单相关接口
│   │   ├── monitor/          # 监控相关接口
│   │   ├── promotion/        # 推广相关接口
│   │   ├── system/           # 系统管理接口
│   │   └── wx/               # 微信相关接口
│   ├── assets/               # 静态资源
│   │   ├── icons/            # SVG图标
│   │   ├── images/           # 图片资源
│   │   ├── login/            # 登录页面资源
│   │   └── styles/           # 样式文件
│   ├── components/           # 公共组件
│   │   ├── Advertising/      # 广告组件
│   │   ├── FileUpload/       # 文件上传组件
│   │   ├── ImageUpload/      # 图片上传组件
│   │   ├── Pagination/       # 分页组件
│   │   └── ...              # 其他公共组件
│   ├── hooks/                # Vue组合式API钩子
│   ├── layout/               # 布局组件
│   ├── plugins/              # 插件配置
│   ├── router/               # 路由配置
│   ├── store/                # Vuex状态管理
│   ├── utils/                # 工具函数
│   └── views/                # 页面组件
│       ├── dashboard/        # 仪表盘
│       ├── login/            # 登录页面
│       ├── monitor/          # 监控管理
│       ├── promotion/        # 推广管理
│       ├── system/           # 系统管理
│       └── wx/               # 微信管理
├── babel.config.js           # Babel配置
├── vue.config.js            # Vue CLI配置
└── package.json             # 项目依赖配置
```

## 🔧 核心功能模块

### 1. 用户认证与权限管理

- 用户登录/注册
- JWT 令牌认证
- 角色权限控制
- 菜单权限管理

### 2. 推广管理模块

- **媒体管理**: 巨量引擎、快手、腾讯等媒体平台管理
- **商品管理**: 商品信息维护和管理
- **评论管理**: 评论数据统计和管理
- **公司管理**: 公司信息管理
- **人群管理**: 目标人群分析
- **落地页管理**: 推广落地页配置

### 3. 微信生态模块

- **企业微信授权**: 企业微信应用授权管理
- **客户链接管理**: 微信客户链接生成和管理
- **客户群组管理**: 微信群组管理
- **小程序账户**: 小程序账户信息管理
- **数据统计**: 微信相关数据统计分析

### 4. 监控与统计

- **缓存监控**: Redis 缓存状态监控
- **域名管理**: 域名配置和监控
- **任务调度**: 定时任务管理
- **负载均衡**: 负载均衡配置
- **系统监控**: 服务器性能监控

### 5. 系统管理

- **用户管理**: 系统用户管理
- **角色管理**: 角色权限配置
- **菜单管理**: 系统菜单配置
- **字典管理**: 系统字典数据
- **部门管理**: 组织架构管理
- **配置管理**: 系统参数配置

### 6. 表单管理

- **表单配置**: 动态表单创建和配置
- **表单统计**: 表单提交数据统计

## 🛠️ 开发环境配置

### 环境要求

- **Node.js**: >= 8.9
- **npm**: >= 3.0.0
- **浏览器兼容性**: > 1%, last 2 versions

### 安装依赖

```bash
# 克隆项目
git clone <项目地址>

# 进入项目目录
cd live-ui

# 安装依赖（推荐使用淘宝镜像）
npm install --registry=https://registry.npmmirror.com --legacy-peer-deps
```

### 开发命令

```bash
# 启动开发服务器
npm run dev
# 或者
npm run serve

# Mac系统启动
npm run serve-mac

# 代码规范检查
npm run lint
```

### 构建命令

```bash
# 构建生产环境
npm run build:prod

# 构建测试环境
npm run build:stage
```

## ⚙️ 配置说明

### 开发服务器配置

- **端口**: 80
- **代理地址**: http://192.168.31.133:9080
- **热更新**: 支持
- **主机**: 0.0.0.0

### 主要配置项

- **侧边栏主题**: 浅色主题
- **顶部导航**: 启用
- **标签页视图**: 启用
- **固定头部**: 启用
- **侧边栏 Logo**: 启用
- **动态标题**: 启用

## 🎨 UI 特性

### 主题定制

- 支持深色/浅色主题切换
- Element UI 主题定制
- 响应式布局设计

### 组件特色

- 丰富的表格组件（支持虚拟滚动）
- 文件上传组件（支持多种文件类型）
- 图表组件（基于 ECharts）
- 表单构建器
- 图片预览组件

## 🔐 安全特性

### 身份认证

- JWT 令牌认证
- 权限路由控制
- 接口权限验证

### 数据安全

- 请求数据加密
- 敏感信息脱敏
- XSS 攻击防护

## 📊 性能优化

### 构建优化

- Gzip 压缩
- 代码分割
- 图片优化
- 路由懒加载

### 运行时优化

- 组件缓存
- 虚拟滚动
- 防抖节流
- 内存泄漏防护

## 🌐 浏览器支持

支持现代浏览器及 IE11+：

- Chrome ≥ 60
- Firefox ≥ 60
- Safari ≥ 10
- Edge ≥ 15
- IE ≥ 11

## 📝 开发规范

### 代码规范

- ESLint 代码检查
- Vue 风格指南
- 组件命名规范
- 接口命名规范

### Git 规范

- 分支管理规范
- 提交信息规范
- 代码审查流程

## 🐛 常见问题

### 开发环境问题

1. **Node 版本问题**: 确保使用 Node.js 8.9+
2. **依赖安装失败**: 使用 `--legacy-peer-deps` 参数
3. **端口冲突**: 修改 vue.config.js 中的端口配置

### 构建部署问题

1. **内存不足**: 增加 Node.js 内存限制
2. **路径问题**: 检查 publicPath 配置
3. **跨域问题**: 配置正确的代理地址
