package com.panda.pollen.tencent.service.V3;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import com.panda.pollen.tencent.model.V3.TencentDynamicCreativeUpdateResponse;
import com.panda.pollen.common.annotation.RequestLimitWarning;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.StringUtils;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.common.utils.spring.SelfBaseService;
import com.panda.pollen.framework.ratelimiter.core.annotation.RateRedisLimiter;
import com.tencent.ads.ApiException;
import com.tencent.ads.exception.TencentAdsResponseException;
import com.tencent.ads.model.v3.AdgroupsUpdateConfiguredStatusRequest;
import com.tencent.ads.model.v3.AdgroupsUpdateConfiguredStatusResponseData;
import com.tencent.ads.model.v3.BatchRequestSpecStruct;
import com.tencent.ads.model.v3.BatchRequestsAddRequest;
import com.tencent.ads.model.v3.BatchRequestsAddResponseData;
import com.tencent.ads.model.v3.BrandComponent;
import com.tencent.ads.model.v3.ConfiguredStatus;
import com.tencent.ads.model.v3.CreativeComponents;
import com.tencent.ads.model.v3.DynamicCreativesGetListStruct;
import com.tencent.ads.model.v3.DynamicCreativesGetResponseData;
import com.tencent.ads.model.v3.DynamicCreativesUpdateRequest;
import com.tencent.ads.model.v3.DynamicCreativesUpdateResponseData;
import com.tencent.ads.model.v3.FilterOperator;
import com.tencent.ads.model.v3.FilteringStruct;
import com.tencent.ads.model.v3.JumpinfoComponent;
import com.tencent.ads.model.v3.JumpinfoStruct;
import com.tencent.ads.model.v3.PageInfo;
import com.tencent.ads.model.v3.PageSpec;
import com.tencent.ads.model.v3.ResponseStruct;
import com.tencent.ads.model.v3.TextLinkComponent;
import com.tencent.ads.model.v3.TextLinkStruct;
import com.tencent.ads.model.v3.UpdateConfiguredStatusStruct;
import com.tencent.ads.model.v3.VideoComponent;
import com.tencent.ads.model.v3.WechatMiniProgramPageSpec;
import com.tencent.ads.v3.TencentAds;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.panda.pollen.tencent.constant.V3.TencentConstantV3.ADGROUPS_UPDATE_CONFIGURED_STATUS_V3;
import static com.panda.pollen.tencent.constant.V3.TencentConstantV3.BATCH_REQUESTS_ADD_RELATIVE_PATH;
import static com.panda.pollen.tencent.constant.V3.TencentConstantV3.BATCH_REQUESTS_ADD_V3;
import static com.panda.pollen.tencent.constant.V3.TencentConstantV3.DYNAMIC_CREATIVES_GET_V3;
import static com.panda.pollen.tencent.constant.V3.TencentConstantV3.DYNAMIC_CREATIVES_UPDATE_V3;

@Service
@Slf4j
public class TencentDynamicCreativeV3Service extends
        SelfBaseService<TencentDynamicCreativeV3Service> implements TencentSdkV3BaseService {


    private static final Long PAGE_SIZE = 100L;

    private static TencentDynamicCreativeUpdateResponse getDynamicCreatives(DynamicCreativesGetListStruct report) {
        //封装对象
        TencentDynamicCreativeUpdateResponse tencentDynamicCreativeUpdateResponse = new TencentDynamicCreativeUpdateResponse();
        tencentDynamicCreativeUpdateResponse.setDynamicCreativeId(String.valueOf(report.getDynamicCreativeId()));
        tencentDynamicCreativeUpdateResponse.setConfiguredStatus(report.getConfiguredStatus().getValue());
        tencentDynamicCreativeUpdateResponse.setDynamicCreativeName(report.getDynamicCreativeName());
        tencentDynamicCreativeUpdateResponse.setClickTrackingUrl(report.getClickTrackingUrl());
        //获取创意主件
        CreativeComponents creativeComponents = report.getCreativeComponents();
        if (ObjectUtil.isNotEmpty(creativeComponents)) {
            //获取跳转主件
            List<JumpinfoComponent> mainJumpInfo = creativeComponents.getMainJumpInfo();
            //遍历
            for (JumpinfoComponent jumpinfoComponent : mainJumpInfo) {
                //获取落地页结构
                JumpinfoStruct value = jumpinfoComponent.getValue();
                if (ObjectUtil.isNotEmpty(value)) {
                    //获取落地页内容
                    PageSpec pageSpec = value.getPageSpec();
                    if (ObjectUtil.isNotEmpty(pageSpec)) {
                        //获取小程序落地页
                        WechatMiniProgramPageSpec wechatMiniProgramSpec = pageSpec.getWechatMiniProgramSpec();
                        if (ObjectUtil.isNotEmpty(wechatMiniProgramSpec)) {
                            String miniProgramPath = wechatMiniProgramSpec.getMiniProgramPath();
                            //落地页链接获取
                            tencentDynamicCreativeUpdateResponse.setMiniProgramPath(miniProgramPath);
                            break;
                        }
                    }
                }
            }
        }


        return tencentDynamicCreativeUpdateResponse;
    }

    /**
     * 按账户更新新版本动态创意，返回是以创意维度的替换结果
     * 因为3.0版本动态创意中的商品跳转信息结构很深，且因为依赖冲突无法在其他工程中引用sdk中的对象，所以只有在这里封装方法实现先查询动态创意列表再更新
     * https://developers.e.qq.com/v3.0/docs/api/dynamic_creatives/get
     *
     * @param accessToken
     * @param accountId
     * @param goodsId
     * @param dynamicCreativeIds 创意ID集合
     * @return
     */
    public ConcurrentHashMap<String, TencentDynamicCreativeUpdateResponse> updateDynamicCreativesByAccount(String path, String accessToken, String accountId, String goodsId, Boolean isMedia, List<String> dynamicCreativeIds) {
        long i = 1;
        long total = 0;
        ConcurrentHashMap<String, TencentDynamicCreativeUpdateResponse> list = new ConcurrentHashMap<>();
        List<String> fields = Arrays.asList("dynamic_creative_id", "dynamic_creative_name", "creative_components");
        DynamicCreativesGetResponseData response = null;
        //过滤动态创意
        List<FilteringStruct> filtering = new ArrayList<>();
        if (ListUtils.isNotEmpty(dynamicCreativeIds)) {
            FilteringStruct struct = new FilteringStruct();
            struct.setField("dynamic_creative_id");
            struct.setOperator(FilterOperator.IN);
            struct.setValues(dynamicCreativeIds);
            filtering.add(struct);
        }
        do {
            try {
                response = getSelf().getDynamicCreatives(accessToken, accountId, i, PAGE_SIZE, fields, filtering);
                if (response != null) {
                    if (ListUtils.isNotEmpty(response.getList())) {
                        List<BatchRequestSpecStruct> batchRequestSpecList = new ArrayList<>();
                        for (DynamicCreativesGetListStruct dynamicCreative : response.getList()) {
                            doUpdate(dynamicCreative, accountId, path, goodsId, isMedia, accessToken, list);
//                            BatchRequestSpecStruct batchRequestSpecStruct = buildUpdate(dynamicCreative, accountId, path, goodsId, isMedia, list);
//                            if (batchRequestSpecStruct != null) {
//                                batchRequestSpecList.add(batchRequestSpecStruct);
//                            }
                        }
//                        //批量更新
//                        doBatchUpdate(accessToken, batchRequestSpecList, list);
                    }
                    PageInfo pageInfo = response.getPageInfo();
                    if (pageInfo != null) {
                        total = pageInfo.getTotalPage();
                    }
                }
            } catch (TencentAdsResponseException e) {
                log.error("拉取腾讯动态创意失败:{}-{}", accountId, i, e);
            } catch (ApiException e) {
                log.error("拉取腾讯动态创意请求失败:{}-{}", accountId, i, e);
            }
            i++;
        } while (i <= total);
        return list;
    }

    private void doBatchUpdate(String accessToken, List<BatchRequestSpecStruct> batchRequestSpecList, ConcurrentHashMap<String, TencentDynamicCreativeUpdateResponse> list) throws ApiException {
        if (ListUtils.isNotEmpty(batchRequestSpecList)) {
            TencentAds tencentAds = init(accessToken);
            BatchRequestsAddRequest batchRequestsAddRequest = new BatchRequestsAddRequest();
            batchRequestsAddRequest.setBatchRequestSpec(batchRequestSpecList);
            BatchRequestsAddResponseData responseData = null;
            //批量更新
            try {
                //todo试一下只改path的情况
                long start = System.currentTimeMillis();
                responseData = getSelf().batchRequestsDynamicCreativeUpdateStatusResult(tencentAds, batchRequestsAddRequest);
                log.error("批量更新耗时：{},广告数：{}", System.currentTimeMillis() - start, batchRequestSpecList.size());
                if (ListUtils.isNotEmpty(responseData.getList())) {
                    //{"code":11019,"code_type":"BusinessException","message":"Your request is much too frequent.","message_cn":"您的请求过于频繁或主体/开发者的频控已达到上限","trace_id":"f9f9acae68964afb3617fddcb13636a4"}
                    //{"code":18001,"code_type":"BusinessException","message":"Your request is missing a required parameter. The required parameter is 'account_id'.","message_cn":"缺失必填参数account_id，请传入必填参数","trace_id":"5a47498e1eff2b82c9fa6e547010cf9a"}
                    //{"code":0,"data":{"dynamic_creative_id":**********},"message":"","message_cn":""}
                    for (int i = 0; i < responseData.getList().size(); i++) {
                        //请求数据
                        BatchRequestSpecStruct requestStruct = batchRequestSpecList.get(i);
                        DynamicCreativesUpdateRequest request = new Gson().fromJson(requestStruct.getBody(), DynamicCreativesUpdateRequest.class);
                        //返回数据
                        ResponseStruct responseStruct = responseData.getList().get(i);
                        JSONObject response = JSONObject.parseObject(responseStruct.getBody());
                        //获取创意日志对象
                        TencentDynamicCreativeUpdateResponse forLog = list.get(String.valueOf(request.getDynamicCreativeId()));
                        if (forLog == null) {
                            log.error("更新腾讯动态创意失败1:{}-{}", requestStruct.getBody(), responseStruct.getBody());
                            continue;
                        }
                        //错误码
                        long code = response.getLongValue("code", -1);
                        if (code != 0) {
                            forLog.setCode(code);
                            forLog.setMessage(response.getString("message_cn"));
                            log.error("更新腾讯动态创意失败2:{}-{}", requestStruct.getBody(), responseStruct.getBody());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("更新腾讯动态创意失败0", e);
                for (Map.Entry<String, TencentDynamicCreativeUpdateResponse> entry : list.entrySet()) {
                    TencentDynamicCreativeUpdateResponse value = entry.getValue();
                    value.setCode(500L);
                    value.setMessage("batchRequestsAdd调用失败");
                }
            }


        }
    }

    /**
     * 执行替换动态创意
     *
     * @param dynamicCreative
     * @param accountId
     * @param path
     * @param goodsId
     * @param isMedia
     * @param list
     */
    public void doUpdate(DynamicCreativesGetListStruct dynamicCreative, String accountId, String path, String goodsId, Boolean isMedia, String accessToken, ConcurrentHashMap<String, TencentDynamicCreativeUpdateResponse> list) {

        TencentDynamicCreativeUpdateResponse updateResp = new TencentDynamicCreativeUpdateResponse();
        String dynamicCreativeId = String.valueOf(dynamicCreative.getDynamicCreativeId());
        try {
            CreativeComponents creativeComponents = dynamicCreative.getCreativeComponents();
            //替换mainjumpinfo的链接
            List<JumpinfoComponent> mainJumpInfo = creativeComponents.getMainJumpInfo();
            updateResp.setAccountId(accountId);
            boolean needUpdate = false;
            if (ListUtils.isNotEmpty(mainJumpInfo)) {
                for (JumpinfoComponent jumpinfoComponent : mainJumpInfo) {
                    JumpinfoStruct value = jumpinfoComponent.getValue();
                    if (value != null) {
                        needUpdate = updateLink(value.getPageSpec(), path, goodsId, isMedia);
                    }
                }
            }
            //替换text_link的链接
            List<TextLinkComponent> textLink = creativeComponents.getTextLink();
            if (ListUtils.isNotEmpty(textLink)) {
                for (TextLinkComponent textLinkComponent : textLink) {
                    TextLinkStruct value = textLinkComponent.getValue();
                    if (value != null) {
                        JumpinfoStruct jumpinfoStruct = value.getJumpInfo();
                        if (jumpinfoStruct != null) {
                            needUpdate = updateLink(jumpinfoStruct.getPageSpec(), path, goodsId, isMedia) || needUpdate;
                        }
                    }
                }
            }

            updateResp.setDynamicCreativeId(dynamicCreativeId);
            updateResp.setDynamicCreativeName(dynamicCreative.getDynamicCreativeName());
            if (needUpdate) {
                handleRepeatCreativeComponents(creativeComponents);
                handleBrandImageIdIsNull(creativeComponents);
                //更新动态创意
                updateDynamicCreative(accessToken, accountId, dynamicCreativeId, creativeComponents);

                updateResp.setMessage("3.0动态创意替换成功");
                updateResp.setCode(0L);
            } else {
                updateResp.setMessage("该动态创意下没有需要替换的跳转信息");
                updateResp.setCode(0L);
            }
        } catch (TencentAdsResponseException e) {
            log.error("更新腾讯动态创意失败:{}", accountId, e);
            updateResp.setCode(e.getCode());
            updateResp.setMessage(e.getMessageCn());
        } catch (ApiException e) {
            log.error("更新腾讯动态创意请求失败:{}", accountId, e);
            updateResp.setCode((long) e.getCode());
            updateResp.setMessage(StringUtils.substring(e.getMessage(), 0, 1000));
        } catch (Exception e) {
            log.error("更新腾讯动态创意请求失败:{}", accountId, e);
            updateResp.setCode(500L);
            updateResp.setMessage(StringUtils.substring(e.getMessage(), 0, 1000));
        }
        updateResp.setOperateTime(new Date());
        list.put(dynamicCreativeId, updateResp);
    }

    /**
     * 执行替换动态创意
     *
     * @param dynamicCreative
     * @param accountId
     * @param path
     * @param goodsId
     * @param isMedia
     * @param list
     */
    public BatchRequestSpecStruct buildUpdate(DynamicCreativesGetListStruct dynamicCreative, String accountId, String path, String goodsId, Boolean isMedia, ConcurrentHashMap<String, TencentDynamicCreativeUpdateResponse> list) {

        TencentDynamicCreativeUpdateResponse updateResp = new TencentDynamicCreativeUpdateResponse();
        String dynamicCreativeId = String.valueOf(dynamicCreative.getDynamicCreativeId());
        CreativeComponents creativeComponents = dynamicCreative.getCreativeComponents();
        //替换mainjumpinfo的链接
        List<JumpinfoComponent> mainJumpInfo = creativeComponents.getMainJumpInfo();
        updateResp.setAccountId(accountId);
        boolean needUpdate = false;
        if (ListUtils.isNotEmpty(mainJumpInfo)) {
            for (JumpinfoComponent jumpinfoComponent : mainJumpInfo) {
                JumpinfoStruct value = jumpinfoComponent.getValue();
                if (value != null) {
                    needUpdate = updateLink(value.getPageSpec(), path, goodsId, isMedia);
                }
            }
        }
        //替换text_link的链接
        List<TextLinkComponent> textLink = creativeComponents.getTextLink();
        if (ListUtils.isNotEmpty(textLink)) {
            for (TextLinkComponent textLinkComponent : textLink) {
                TextLinkStruct value = textLinkComponent.getValue();
                if (value != null) {
                    JumpinfoStruct jumpinfoStruct = value.getJumpInfo();
                    if (jumpinfoStruct != null) {
                        needUpdate = updateLink(jumpinfoStruct.getPageSpec(), path, goodsId, isMedia) || needUpdate;
                    }
                }
            }
        }

        updateResp.setDynamicCreativeId(dynamicCreativeId);
        updateResp.setDynamicCreativeName(dynamicCreative.getDynamicCreativeName());
        updateResp.setOperateTime(new Date());
        updateResp.setCode(0L);
        list.put(dynamicCreativeId, updateResp);
        if (needUpdate) {
            handleRepeatCreativeComponents(creativeComponents);
            handleBrandImageIdIsNull(creativeComponents);

            updateResp.setMessage("3.0动态创意替换成功");
            //构建更新动态创意
            DynamicCreativesUpdateRequest dynamicCreativeUpdateRequest = new DynamicCreativesUpdateRequest();
            dynamicCreativeUpdateRequest.setAccountId(NumberUtil.parseLong(accountId));
            dynamicCreativeUpdateRequest.setDynamicCreativeId(NumberUtil.parseLong(dynamicCreativeId));
            dynamicCreativeUpdateRequest.setCreativeComponents(creativeComponents);
            BatchRequestSpecStruct struct = new BatchRequestSpecStruct();
            struct.setRelativePath(BATCH_REQUESTS_ADD_RELATIVE_PATH);
            struct.setBody(new Gson().toJson(dynamicCreativeUpdateRequest));

            return struct;
        } else {
            updateResp.setMessage("该动态创意下没有需要替换的跳转信息");
        }
        return null;
    }

    /**
     * 腾讯替换链接通用放法
     *
     * @param pageSpec 链接的对象
     * @param path     替换的地址
     * @param goodsId  商品id
     * @param isMedia  true媒体替换false商品替换
     * @return
     */
    private boolean updateLink(PageSpec pageSpec, String path, String goodsId, boolean isMedia) {
        if (pageSpec != null) {
            WechatMiniProgramPageSpec wechatMiniProgramSpec = pageSpec.getWechatMiniProgramSpec();
            if (wechatMiniProgramSpec != null) {
                String miniProgramPath = wechatMiniProgramSpec.getMiniProgramPath();
                if (ObjectUtil.isNotEmpty(miniProgramPath)) {
                    // 替换商品链接
                    if (StringUtils.contains(miniProgramPath, "=" + goodsId) || StringUtils.contains(miniProgramPath.toLowerCase(), "\\u003d" + goodsId) || isMedia) {
                        wechatMiniProgramSpec.setMiniProgramPath(path);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * @param creativeComponents
     * @Description 处理腾讯重复元素的问题
     * <AUTHOR>
     * @param:
     * @Date : 2024/5/21 10:27
     * @return: void
     */
    private void handleRepeatCreativeComponents(CreativeComponents creativeComponents) {
        List<VideoComponent> videoList = creativeComponents.getVideo();
        List<VideoComponent> newVideoList = new ArrayList<VideoComponent>();
        for (VideoComponent videoComponent : videoList) {
            boolean isAdd = true;
            for (VideoComponent component : newVideoList) {
                if (ObjectUtil.equals(videoComponent.getComponentId(), component.getComponentId())
                        && videoComponent.getValue().getVideoId().equals(component.getValue().getVideoId())
                        && videoComponent.getValue().getCoverId().equals(component.getValue().getCoverId())) {
                    isAdd = false;
                }
            }
            if (isAdd) {
                newVideoList.add(videoComponent);
            }
        }
        creativeComponents.setVideo(newVideoList);
    }

    /**
     * @param creativeComponents
     * @Description 处理腾讯brand_image_id为空的时候
     * <AUTHOR>
     * @param:
     * @Date : 2024/7/3 00:44
     * @return: void
     */
    private void handleBrandImageIdIsNull(CreativeComponents creativeComponents) {
        List<BrandComponent> brandComponents = creativeComponents.getBrand();
        if (ListUtils.isNotEmpty(brandComponents)) {
            for (BrandComponent brandComponent : brandComponents) {
                if (StringUtils.isBlank(brandComponent.getValue().getBrandImageId())) {
                    //品牌图片id为空替换会报错所以随便赋值后不报错
                    brandComponent.getValue().brandImageId("1");
                }
            }
        }
    }

    @RateRedisLimiter(count = 1000, timeUnit = TimeUnit.MINUTES, keyArg = DYNAMIC_CREATIVES_GET_V3+"key")
    @RequestLimitWarning(mediaTypeEnum = MediaTypeEnum.TENCENT)
    public DynamicCreativesGetResponseData getDynamicCreatives(String accessToken, String accountId, Long page, Long pageSize,
                                                               List<String> fields, List<FilteringStruct> filtering) throws TencentAdsResponseException, ApiException {
        TencentAds tencentAds = init(accessToken);
        return tencentAds.dynamicCreatives().dynamicCreativesGet(NumberUtil.parseLong(accountId),
                filtering, page, pageSize, fields, false, null, null, null);
    }

    /**
     * 更新新版本动态创意
     *
     * @param accessToken
     * @param accountId
     * @param dynamicCreativeId  从获取动态创意接口获得
     * @param creativeComponents 从获取动态创意接口获得
     * @return
     * @throws TencentAdsResponseException
     * @throws ApiException
     */
    public DynamicCreativesUpdateResponseData updateDynamicCreative(String accessToken, String accountId, String dynamicCreativeId,
                                                                    CreativeComponents creativeComponents) throws TencentAdsResponseException, ApiException {
        TencentAds tencentAds = init(accessToken);
        DynamicCreativesUpdateRequest request = new DynamicCreativesUpdateRequest();
        request.setAccountId(NumberUtil.parseLong(accountId));
        request.setDynamicCreativeId(NumberUtil.parseLong(dynamicCreativeId));
        request.setCreativeComponents(creativeComponents);
        return getSelf().dynamicCreativesUpdate(tencentAds, request);
    }

    /**
     * @param accessToken       token
     * @param accountId         账户id
     * @param dynamicCreativeId 创意ID
     * @param adgroupIds        计划ID或广告组ID
     * @Description 获取创意接口列表
     * <AUTHOR>
     * https://developers.e.qq.com/v3.0/docs/api/dynamic_creatives/get
     * @Date : 2024/4/12 10:25
     * @return: java.util.List<com.panda.tencent.model.V3.TencentDynamicCreativeUpdateResponse>
     */
    public List<TencentDynamicCreativeUpdateResponse> getDynamicCreatives(String accountId, String accessToken, List<String> dynamicCreativeId, List<String> adgroupIds) {
        long i = 1;
        long total = 0;
        List<TencentDynamicCreativeUpdateResponse> list = new ArrayList<>();
        List<FilteringStruct> filteringStructs = new ArrayList<>();
        //创意ID集合是否为空
        if (ListUtils.isNotEmpty(dynamicCreativeId)) {
            FilteringStruct filteringStruct = new FilteringStruct();
            filteringStruct.setField("dynamic_creative_id");
            filteringStruct.setOperator(FilterOperator.IN);
            filteringStruct.setValues(dynamicCreativeId);
            filteringStructs.add(filteringStruct);
        }
        //广告组ID是否为空
        if (ListUtils.isNotEmpty(adgroupIds)) {
            FilteringStruct filteringStruct = new FilteringStruct();
            filteringStruct.setField("adgroup_id");
            filteringStruct.setOperator(FilterOperator.IN);
            filteringStruct.setValues(adgroupIds);
            filteringStructs.add(filteringStruct);
        }
        //过滤条件 广告组状态为有效
        FilteringStruct filteringStruct = new FilteringStruct();
        filteringStruct.setField("configured_status");
        filteringStruct.setOperator(FilterOperator.EQUALS);
        filteringStruct.setValues(ListUtils.newArrayList("AD_STATUS_NORMAL"));
        filteringStructs.add(filteringStruct);
        List<String> fields = Arrays.asList("dynamic_creative_id", "dynamic_creative_name", "creative_components", "configured_status", "click_tracking_url", "impression_tracking_url");
        DynamicCreativesGetResponseData response = null;
        do {
            try {
                response = getSelf().getDynamicCreatives(accessToken, accountId, i, PAGE_SIZE, fields, filteringStructs);
                if (ObjectUtil.isNotEmpty(response)) {
                    if (ListUtils.isNotEmpty(response.getList())) {
                        for (DynamicCreativesGetListStruct report : response.getList()) {
                            list.add(getDynamicCreatives(report));
                        }
                        PageInfo pageInfo = response.getPageInfo();
                        if (ObjectUtil.isNotEmpty(pageInfo)) {
                            total = pageInfo.getTotalPage();
                        }
                    }
                }
            } catch (TencentAdsResponseException | ApiException e) {
                log.error("拉取腾讯创意失败:账户ID{}--失败原因{}", accountId, e.getMessage());
            }
            i++;
        } while (i <= total);

        return list;
    }

    /**
     * @param accountId         账户ID
     * @param accessToken       token
     * @param dynamicCreativeId 创意ID
     * @param configuredStatus  状态(AD_STATUS_NORMAL:有效 AD_STATUS_SUSPEND:暂停)
     * @Description 修改创意状态
     * <AUTHOR>
     * https://developers.e.qq.com/v3.0/docs/api/dynamic_creatives/update
     * @Date : 2024/4/12 10:55
     * @return: com.tencent.ads.model.v3.DynamicCreativesUpdateResponseData
     */
    public DynamicCreativesUpdateResponseData updateDynamicCreativeStatus(String accountId, String accessToken, String dynamicCreativeId, ConfiguredStatus configuredStatus) throws TencentAdsResponseException, ApiException {
        TencentAds tencentAds = init(accessToken);
        DynamicCreativesUpdateRequest request = new DynamicCreativesUpdateRequest();
        request.setAccountId(NumberUtil.parseLong(accountId));
        request.setDynamicCreativeId(NumberUtil.parseLong(dynamicCreativeId));
        request.setConfiguredStatus(configuredStatus);
        return getSelf().dynamicCreativesUpdate(tencentAds, request);
    }

    @RateRedisLimiter(count = 1000, timeUnit = TimeUnit.MINUTES, keyArg = DYNAMIC_CREATIVES_UPDATE_V3+"key", rate = 0.6)
    @RequestLimitWarning(mediaTypeEnum = MediaTypeEnum.TENCENT)
    public DynamicCreativesUpdateResponseData dynamicCreativesUpdate(TencentAds tencentAds, DynamicCreativesUpdateRequest dynamicCreativeUpdateRequest) throws TencentAdsResponseException, ApiException {
        log.error("tencentAds.dynamicCreatives().dynamicCreativesUpdate:{}", Thread.currentThread().getName());
        try {
            return tencentAds.dynamicCreatives().dynamicCreativesUpdate(dynamicCreativeUpdateRequest);
        } catch (TencentAdsResponseException e) {
            //不是超过限制错误
            if (!MediaTypeEnum.TENCENT.isLimitCode(e.getCode())) {
                throw e;
            }

        }
        //超过限制，休眠60秒再重试
        try {
            Thread.sleep(60000);
        } catch (InterruptedException ex) {
            throw new RuntimeException(ex);
        }

        return tencentAds.dynamicCreatives().dynamicCreativesUpdate(dynamicCreativeUpdateRequest);
    }


    @RateRedisLimiter(count = 1000, timeUnit = TimeUnit.MINUTES, keyArg = ADGROUPS_UPDATE_CONFIGURED_STATUS_V3)
    @RequestLimitWarning(mediaTypeEnum = MediaTypeEnum.TENCENT)
    public AdgroupsUpdateConfiguredStatusResponseData adGroupsUpdateConfiguredStatusResult(TencentAds tencentAds, AdgroupsUpdateConfiguredStatusRequest adgroupsUpdateConfiguredStatusRequest) throws ApiException {
        return tencentAds.adgroups().adgroupsUpdateConfiguredStatus(adgroupsUpdateConfiguredStatusRequest);
    }


    /**
     * 广告组状态更新
     *
     * @param accountId
     * @param accessToken
     * @param adgroupIds
     * @param configuredStatus
     * @return
     * @throws TencentAdsResponseException
     * @throws ApiException
     */
    public AdgroupsUpdateConfiguredStatusResponseData adGroupsUpdateConfiguredStatus(Long accountId, String accessToken, List<Long> adgroupIds, ConfiguredStatus configuredStatus) {
        TencentAds tencentAds = init(accessToken);
        Gson gson = new Gson();
        List<UpdateConfiguredStatusStruct> updateConfiguredStatusSpecList = new ArrayList<>();
        for (Long adgroupId : adgroupIds) {
            UpdateConfiguredStatusStruct updateConfiguredStatusStruct = new UpdateConfiguredStatusStruct();
            updateConfiguredStatusStruct.setAdgroupId(adgroupId);
            updateConfiguredStatusStruct.setConfiguredStatus(configuredStatus);
            updateConfiguredStatusSpecList.add(updateConfiguredStatusStruct);
        }
        AdgroupsUpdateConfiguredStatusRequest adgroupsUpdateConfiguredStatusRequest = new AdgroupsUpdateConfiguredStatusRequest();
        adgroupsUpdateConfiguredStatusRequest.setAccountId(accountId);
        adgroupsUpdateConfiguredStatusRequest.setUpdateConfiguredStatusSpec(updateConfiguredStatusSpecList);
        AdgroupsUpdateConfiguredStatusResponseData responseData;
        try {
            responseData = getSelf().adGroupsUpdateConfiguredStatusResult(tencentAds, adgroupsUpdateConfiguredStatusRequest);
            log.info("【腾讯批量更新广告启用状态成功】:广告组账户advertiserId={},更新日志：{}", accountId, gson.toJson(responseData));
            return responseData;
        } catch (TencentAdsResponseException | ApiException e) {
            //日志输出
            log.error("【腾讯批量更新广告启用状态】:广告组账户advertiserId={},失败原因{}", accountId, e.getMessage());
            throw new ServiceException("【腾讯批量更新广告启用状态失败】:广告组账户advertiserId={},失败原因{}", accountId, e.getMessage());
        }
    }

    /**
     * @param tencentAds
     * @param batchRequestsAddRequest
     * @return {@link BatchRequestsAddResponseData }
     * @throws ApiException https://developers.e.qq.com/v3.0/docs/api/batch_requests/add
     */
    @RateRedisLimiter(count = 1000, timeUnit = TimeUnit.MINUTES, keyArg = BATCH_REQUESTS_ADD_V3)
    @RequestLimitWarning(mediaTypeEnum = MediaTypeEnum.TENCENT)
    public BatchRequestsAddResponseData batchRequestsDynamicCreativeUpdateStatusResult(TencentAds tencentAds, BatchRequestsAddRequest batchRequestsAddRequest) throws ApiException {
        return tencentAds.batchRequests().batchRequestsAdd(batchRequestsAddRequest);
    }


    /**
     * 广告创意状态批量更新
     *
     * @param accountId
     * @param accessToken
     * @param dynamicCreativeIds
     * @param configuredStatus
     * @return
     * @throws TencentAdsResponseException
     * @throws ApiException
     */
    public BatchRequestsAddResponseData batchRequestsDynamicCreativeUpdateStatus(Long accountId, String accessToken, List<Long> dynamicCreativeIds, ConfiguredStatus configuredStatus) {
        TencentAds tencentAds = init(accessToken);
        Gson gson = new Gson();
        BatchRequestsAddRequest batchRequestsAddRequest = new BatchRequestsAddRequest();
        List<BatchRequestSpecStruct> batchRequestSpecList = new ArrayList<>();
        for (Long dynamicCreativeId : dynamicCreativeIds) {
            DynamicCreativesUpdateRequest dynamicCreativeUpdateRequest = new DynamicCreativesUpdateRequest();
            dynamicCreativeUpdateRequest.setAccountId(accountId);
            dynamicCreativeUpdateRequest.setDynamicCreativeId(dynamicCreativeId);
            dynamicCreativeUpdateRequest.setConfiguredStatus(configuredStatus);
            BatchRequestSpecStruct struct = new BatchRequestSpecStruct();
            struct.setRelativePath(BATCH_REQUESTS_ADD_RELATIVE_PATH);
            struct.setBody(gson.toJson(dynamicCreativeUpdateRequest));
            batchRequestSpecList.add(struct);
        }
        batchRequestsAddRequest.setBatchRequestSpec(batchRequestSpecList);
        BatchRequestsAddResponseData responseData;
        try {
            responseData = getSelf().batchRequestsDynamicCreativeUpdateStatusResult(tencentAds, batchRequestsAddRequest);
            log.info("【腾讯批量更新广告启用状态成功】:创意账户advertiserId={},更新日志：{}", accountId, gson.toJson(responseData));
            return responseData;
        } catch (TencentAdsResponseException | ApiException e) {
            //日志输出
            log.error("【腾讯批量更新广告启用状态】:创意账户advertiserId={},失败原因{}", accountId, e.getMessage());
            throw new ServiceException("【腾讯批量更新广告启用状态失败】:创意账户advertiserId={},失败原因{}", accountId, e.getMessage());
        }
    }


}
