package com.panda.pollen.tencent.service.V3;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.panda.pollen.common.exception.MediaReportException;
import com.panda.pollen.tencent.constant.V3.TencentConstantV3;
import com.panda.pollen.tencent.model.V3.TencentAdgroupReport;
import com.panda.pollen.tencent.model.V3.TencentReport;
import com.panda.pollen.common.annotation.RequestLimitWarning;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.common.utils.spring.SelfBaseService;
import com.panda.pollen.framework.ratelimiter.core.annotation.RateRedisLimiter;
import com.tencent.ads.ApiException;
import com.tencent.ads.exception.TencentAdsResponseException;
import com.tencent.ads.model.v3.*;
import com.tencent.ads.v3.TencentAds;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TencentReportV3Service extends SelfBaseService<TencentReportV3Service> implements TencentSdkV3BaseService {


    /**
     * 报表级别-广告组级别报表
     */
    private static final String REPORT_LEVEL_ADGROUP = "REPORT_LEVEL_ADGROUP";

    private static final List<String> ADGROUP_REPORT_FIELDS = CollUtil.newArrayList("date", "adgroup_id", "adgroup_name", "account_id", "view_count", "view_user_count", "avg_view_per_user",
            "valid_click_count", "click_user_count", "cpc", "ctr", "cost", "acquisition_cost", "thousand_display_price", "conversions_count", "conversions_rate", "conversions_cost", "deep_conversions_count",
            "deep_conversions_rate", "deep_conversions_cost", "video_outer_play_count", "video_outer_play_user_count", "avg_user_play_count", "video_outer_play_time_count", "video_outer_play_time_avg_rate",
            "video_outer_play_rate", "video_outer_play_cost", "video_outer_play10_count", "video_outer_play25_count", "video_outer_play50_count", "video_outer_play75_count", "video_outer_play90_count",
            "video_outer_play95_count", "video_outer_play100_count", "video_outer_play3s_count", "video_outer_play5s_count", "video_outer_play7s_count", "comment_count", "comment_cost", "praise_count",
             "praise_cost", "forward_count", "forward_cost", "no_interest_count");


    /**
     * 查询日报表通用方法
     * https://developers.e.qq.com/v3.0/docs/api/daily_reports/get
     * @param tencentAds        TencentAds请求对象
     * @param accountId         媒体账户id
     * @param reportLevel       报表级别（REPORT_LEVEL_ADVERTISER-账户报表 REPORT_LEVEL_ADGROUP-广告报表）
     * @param dateRange         日期范围，最早支持查询 1 年内（365 天）的数据
     * @param groupBy           聚合参数
     * @param fields            指定返回的字段列表
     * @param page              页码
     * @param pageSize          每页数量
     * @return
     * @throws TencentAdsResponseException
     * @throws ApiException
     */
    @RateRedisLimiter(count = 2000, timeUnit = TimeUnit.MINUTES, keyArg = TencentConstantV3.DAILY_REPORT_URL_V3)
    @RequestLimitWarning(mediaTypeEnum = MediaTypeEnum.TENCENT)
    public DailyReportsGetResponseData getDailyReport(TencentAds tencentAds, String accountId, String reportLevel, ReportDateRange dateRange,
                                                      List<String> groupBy, List<String> fields, Long page, Long pageSize) throws TencentAdsResponseException, ApiException {
        return tencentAds.dailyReports().dailyReportsGet(NumberUtil.parseLong(accountId), reportLevel,
                dateRange, groupBy, fields, null, null, null, page, pageSize);
    }



    /**
     * 获取广告组报表数据
     * @param accessToken
     * @param advertiserId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<TencentAdgroupReport> getAdgroupReportList(String accessToken, String advertiserId, String startDate, String endDate) {
        // 构建查询条件
        TencentAds tencentAds = init(accessToken);
        ReportDateRange dateRange = new ReportDateRange().startDate(startDate).endDate(endDate);
        List<String> groupBy = CollUtil.newArrayList("date", "adgroup_id");
        Long pageIndex = 1L;
        Long pageSize = 1000L;
        List<TencentAdgroupReport> dataList = new ArrayList<>();
        while (true) {
            try {
                DailyReportsGetResponseData responseData = getSelf().getDailyReport(tencentAds, advertiserId, REPORT_LEVEL_ADGROUP, dateRange, groupBy, ADGROUP_REPORT_FIELDS, pageIndex, pageSize);
                if (responseData != null && CollUtil.isNotEmpty(responseData.getList())) {
                    List<TencentAdgroupReport> reportList = convertTencentAdgroupReport(responseData.getList());
                    dataList.addAll(reportList);
                    // 判断是否是最后一页
                    PageInfo pageInfo = responseData.getPageInfo();
                    if (pageInfo == null || ObjectUtil.equals(pageIndex, pageInfo.getTotalPage()) || ObjectUtil.equals(0L, pageInfo.getTotalPage())) {
                        break;
                    }
                    pageIndex++;
                } else {
                    break;
                }
            } catch (ApiException e) {
                log.error("获取巨量广告报错出错，失败原因：{}", e.getMessage());
                throw new MediaReportException(e.getMessage(), e.getCode());
            }
        }
        return dataList;
    }

    private List<TencentAdgroupReport> convertTencentAdgroupReport(List<DailyReportApiListStruct> list) {
        List<TencentAdgroupReport> reportList = new ArrayList<>();
        for (DailyReportApiListStruct struct : list) {
            TencentAdgroupReport adgroupReport = new TencentAdgroupReport();
            adgroupReport.setStatDate(LocalDate.parse(struct.getDate()));
            adgroupReport.setAdgroupId(struct.getAdgroupId().toString());
            adgroupReport.setAdgroupName(struct.getAdgroupName());
            adgroupReport.setViewCount(new BigDecimal(struct.getViewCount()));
            adgroupReport.setViewUserCount(new BigDecimal(struct.getViewUserCount()));
            adgroupReport.setAvgViewPerUser(BigDecimal.valueOf(struct.getAvgViewPerUser()));
            adgroupReport.setValidClickCount(BigDecimal.valueOf(struct.getValidClickCount()));
            adgroupReport.setClickUserCount(BigDecimal.valueOf(struct.getClickUserCount()));
            adgroupReport.setCpc(BigDecimal.valueOf(struct.getCpc()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setCtr(BigDecimal.valueOf(struct.getCtr()));
            adgroupReport.setCost(BigDecimal.valueOf(struct.getCost()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setAcquisitionCost(BigDecimal.valueOf(struct.getAcquisitionCost()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setThousandDisplayPrice(BigDecimal.valueOf(struct.getThousandDisplayPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setConversionsCount(BigDecimal.valueOf(struct.getConversionsCount()));
            adgroupReport.setConversionsRate(BigDecimal.valueOf(struct.getConversionsRate()));
            adgroupReport.setConversionsCost(BigDecimal.valueOf(struct.getConversionsCost()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setDeepConversionsCount(BigDecimal.valueOf(struct.getDeepConversionsCount()));
            adgroupReport.setDeepConversionsRate(BigDecimal.valueOf(struct.getDeepConversionsRate()));
            adgroupReport.setDeepConversionsCost(BigDecimal.valueOf(struct.getDeepConversionsCost()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setVideoOuterPlayCount(BigDecimal.valueOf(struct.getVideoOuterPlayCount()));
            adgroupReport.setVideoOuterPlayUserCount(BigDecimal.valueOf(struct.getVideoOuterPlayUserCount()));
            adgroupReport.setAvgUserPlayCount(BigDecimal.valueOf(struct.getAvgUserPlayCount()));
            adgroupReport.setVideoOuterPlayTimeCount(BigDecimal.valueOf(struct.getVideoOuterPlayTimeCount()));
            adgroupReport.setVideoOuterPlayTimeAvgRate(BigDecimal.valueOf(struct.getVideoOuterPlayTimeAvgRate()));
            adgroupReport.setVideoOuterPlayRate(BigDecimal.valueOf(struct.getVideoOuterPlayRate()));
            adgroupReport.setVideoOuterPlayCost(BigDecimal.valueOf(struct.getVideoOuterPlayCost()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setVideoOuterPlay10Count(BigDecimal.valueOf(struct.getVideoOuterPlay10Count()));
            adgroupReport.setVideoOuterPlay25Count(BigDecimal.valueOf(struct.getVideoOuterPlay25Count()));
            adgroupReport.setVideoOuterPlay50Count(BigDecimal.valueOf(struct.getVideoOuterPlay50Count()));
            adgroupReport.setVideoOuterPlay75Count(BigDecimal.valueOf(struct.getVideoOuterPlay75Count()));
            adgroupReport.setVideoOuterPlay90Count(BigDecimal.valueOf(struct.getVideoOuterPlay90Count()));
            adgroupReport.setVideoOuterPlay95Count(BigDecimal.valueOf(struct.getVideoOuterPlay95Count()));
            adgroupReport.setVideoOuterPlay100Count(BigDecimal.valueOf(struct.getVideoOuterPlay100Count()));
            adgroupReport.setVideoOuterPlay3sCount(BigDecimal.valueOf(struct.getVideoOuterPlay3sCount()));
            adgroupReport.setVideoOuterPlay5sCount(BigDecimal.valueOf(struct.getVideoOuterPlay5sCount()));
            adgroupReport.setVideoOuterPlay7sCount(BigDecimal.valueOf(struct.getVideoOuterPlay7sCount()));
            adgroupReport.setCommentCount(BigDecimal.valueOf(struct.getCommentCount()));
            adgroupReport.setCommentCost(BigDecimal.valueOf(struct.getCommentCost()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setPraiseCount(BigDecimal.valueOf(struct.getPraiseCount()));
            adgroupReport.setPraiseCost(BigDecimal.valueOf(struct.getPraiseCost()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setForwardCount(BigDecimal.valueOf(struct.getForwardCount()));
            adgroupReport.setForwardCost(BigDecimal.valueOf(struct.getForwardCost()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            adgroupReport.setNoInterestCount(BigDecimal.valueOf(struct.getNoInterestCount()));
            reportList.add(adgroupReport);
        }
        return reportList;
    }

}
