import { useMediaQuery, usePreferredDark } from '@vueuse/core'
import { defineStore } from 'pinia'
import { computed, readonly, ref } from 'vue'

export const useUIStore = defineStore('ui', () => {
  // 状态
  const loading = ref(false)
  const theme = ref<'light' | 'dark' | 'auto'>('auto')

  // 设备检测
  const isMobile = useMediaQuery('(max-width: 768px)')
  const isTablet = useMediaQuery('(min-width: 769px) and (max-width: 1024px)')
  const isDesktop = useMediaQuery('(min-width: 1025px)')
  const prefersDark = usePreferredDark()

  // 计算属性
  const deviceType = computed(() => {
    if (isMobile.value)
      return 'mobile'
    if (isTablet.value)
      return 'tablet'
    return 'desktop'
  })

  const isDark = computed(() => {
    if (theme.value === 'auto') {
      return prefersDark.value
    }
    return theme.value === 'dark'
  })

  const layoutConfig = computed(() => ({
    showHeader: !isMobile.value,
    showFooter: isDesktop.value,
    showBottomNav: isMobile.value,
    sidebarCollapsed: !isDesktop.value,
  }))

  // 方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
  }

  const initTheme = () => {
    const storedTheme = localStorage.getItem('theme') as
      | 'light'
      | 'dark'
      | 'auto'
    if (storedTheme) {
      theme.value = storedTheme
    }
  }

  return {
    // 状态
    loading: readonly(loading),
    theme: readonly(theme),

    // 设备检测
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    isDesktop: readonly(isDesktop),

    // 计算属性
    deviceType,
    isDark,
    layoutConfig,

    // 方法
    setLoading,
    setTheme,
    initTheme,
  }
})
