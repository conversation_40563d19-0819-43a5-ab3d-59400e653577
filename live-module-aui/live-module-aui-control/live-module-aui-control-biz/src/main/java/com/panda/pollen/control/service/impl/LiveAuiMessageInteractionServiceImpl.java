package com.panda.pollen.control.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.panda.pollen.aui.model.AliResponse;
import com.panda.pollen.aui.model.cons.ConsLiveAui;
import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.model.req.DeleteMessageRequest;
import com.panda.pollen.aui.service.AliCloudBizInterfaceService;
import com.panda.pollen.common.utils.uuid.IdUtils;
import com.panda.pollen.control.service.LiveAuiMessageInteractionService;

import lombok.extern.slf4j.Slf4j;


/**
 * <p> 消息互动实现类<p/>
 * ClassName com.panda.pollen.service.impl.LiveAuiMessageInteractionServiceImpl 
 * <AUTHOR> 
 * @date 2025年8月15日 下午8:42:56 
 * @version v1.0
 */
@Slf4j
@Service
public class LiveAuiMessageInteractionServiceImpl implements LiveAuiMessageInteractionService {

    @Resource
    private AliCloudBizInterfaceService aliCloudBizService;

    @Override
    public boolean sendMessage(AliSendLiveMessageUserRequest param) {
        param.setMsgTid(IdUtils.simpleUUID());
        param.setDataCenter(ConsLiveAui.DATA_CENTER);
        param.setStorage(Boolean.TRUE);
        AliResponse res = aliCloudBizService.sendMessage(param);
        return res.isSuccess();
    }

    @Override
    public boolean removeMessage(DeleteMessageRequest param) {
        param.setDataCenter(ConsLiveAui.DATA_CENTER);
        AliResponse res = aliCloudBizService.removeMessage(param);
        return res.isSuccess();
    }
 
}
