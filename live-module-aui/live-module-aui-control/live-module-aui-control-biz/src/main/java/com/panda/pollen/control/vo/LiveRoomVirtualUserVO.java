package com.panda.pollen.control.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p> 助教直播间虚拟用户VO<p/>
 * ClassName com.panda.pollen.control.vo.LiveRoomVirtualUserVO 
 * <AUTHOR> 
 * @date 2025年8月13日 09:51:43 
 * @version v1.0   
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LiveRoomVirtualUserVO {

    
	private Long id;
	
	/** 机器人ID */
	private Long robotId;
	
	/** 编号 */
	private String robotCode;
	
	/** 昵称 */
	private String nickName;
	
	/** 头像地址 */
	private String avatarUrl;
	
}
