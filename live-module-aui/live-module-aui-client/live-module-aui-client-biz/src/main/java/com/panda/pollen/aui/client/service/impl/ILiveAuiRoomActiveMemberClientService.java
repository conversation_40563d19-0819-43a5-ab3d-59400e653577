package com.panda.pollen.aui.client.service.impl;


import com.panda.pollen.aui.system.domain.LiveAuiRoomActiveMember;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025年08月15日 17:35
 */
public interface ILiveAuiRoomActiveMemberClientService {

    /**
     * 更新直播间临时成员的customerId
     * @param externalUserId        企微客户id
     * @param customerId            C端用户id
     */
    void updateLiveRoomMemberCustomerId(String externalUserId, Long customerId);

    /**
     * 更新直播间成员的externalUserId
     * @param customerId
     * @param externalUserId
     */
    void updateLiveRoomMemberExternalUserId(Long customerId, String externalUserId);

    /**
     * 根据C端用户id查询直播间成员
     * @param customerId
     * @return
     */
    List<LiveAuiRoomActiveMember> selectListByCustomerId(Long customerId);

    /**
     * 更新直播间成员数据
     * @param liveAuiRoomActiveMember
     */
    void saveLiveAuiRoomActiveMember(LiveAuiRoomActiveMember liveAuiRoomActiveMember);

}
