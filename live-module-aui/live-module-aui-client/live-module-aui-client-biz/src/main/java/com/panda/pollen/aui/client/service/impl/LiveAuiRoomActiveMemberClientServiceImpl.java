package com.panda.pollen.aui.client.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.panda.pollen.aui.system.domain.LiveAuiRoomActiveMember;
import com.panda.pollen.aui.system.service.ILiveAuiRoomActiveMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025年08月15日 17:36
 */
@Service
public class LiveAuiRoomActiveMemberClientServiceImpl implements ILiveAuiRoomActiveMemberClientService {

    @Autowired
    private ILiveAuiRoomActiveMemberService liveAuiRoomActiveMemberService;

    /**
     * 更新直播间临时成员的customerId
     * @param externalUserId        企微客户id
     * @param customerId            C端用户id
     */
    @Override
    public void updateLiveRoomMemberCustomerId(String externalUserId, Long customerId) {
        if (StrUtil.isBlank(externalUserId)) {
            return;
        }
        LambdaUpdateWrapper<LiveAuiRoomActiveMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LiveAuiRoomActiveMember::getExternalUserId, externalUserId);
        updateWrapper.set(LiveAuiRoomActiveMember::getCustomerId, customerId);
        liveAuiRoomActiveMemberService.update(updateWrapper);
    }

    /**
     * 更新直播间成员的externalUserId
     * @param customerId
     * @param externalUserId
     */
    @Override
    public void updateLiveRoomMemberExternalUserId(Long customerId, String externalUserId) {
        if (StrUtil.isBlank(externalUserId)) {
            return;
        }
        LambdaUpdateWrapper<LiveAuiRoomActiveMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LiveAuiRoomActiveMember::getCustomerId, customerId);
        updateWrapper.set(LiveAuiRoomActiveMember::getExternalUserId, externalUserId);
        liveAuiRoomActiveMemberService.update(updateWrapper);
    }

    /**
     * 根据C端用户id查询直播间成员
     * @param customerId
     * @return
     */
    @Override
    public List<LiveAuiRoomActiveMember> selectListByCustomerId(Long customerId) {
        LambdaQueryWrapper<LiveAuiRoomActiveMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LiveAuiRoomActiveMember::getCustomerId, customerId);
        queryWrapper.eq(LiveAuiRoomActiveMember::getDeleted, 0);
        return liveAuiRoomActiveMemberService.list(queryWrapper);
    }

    /**
     * 新增数据
     * @param liveAuiRoomActiveMember
     */
    @Override
    public void saveLiveAuiRoomActiveMember(LiveAuiRoomActiveMember liveAuiRoomActiveMember) {
        liveAuiRoomActiveMemberService.saveOrUpdate(liveAuiRoomActiveMember);
    }
}
