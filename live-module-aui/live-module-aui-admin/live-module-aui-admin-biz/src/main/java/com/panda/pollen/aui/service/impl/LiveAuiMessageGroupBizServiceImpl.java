package com.panda.pollen.aui.service.impl;

import cn.hutool.core.util.IdUtil;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.live.model.v20161101.*;
import com.aliyuncs.profile.DefaultProfile;
import com.panda.pollen.aui.mapstruct.MsLiveAuiMessageGroupBiz;
import com.panda.pollen.aui.model.dto.LiveAuiMessageGroupRequestDto;
import com.panda.pollen.aui.model.enums.Enum4LiveDataCenter;
import com.panda.pollen.aui.model.req.AliCreateLiveMessageGroupRequest;
import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.service.ILiveAuiMessageGroupBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageGroup;
import com.panda.pollen.aui.system.service.ILiveAuiMessageGroupService;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveAuiMessageGroupBizServiceImpl implements ILiveAuiMessageGroupBizService {

    @Resource
    private IAliCloudBizService iAliCloudBizService;

    @Resource
    private ILiveAuiMessageGroupService iLiveAuiMessageGroupService;

    private MsLiveAuiMessageGroupBiz msLiveAuiMessageGroupBiz;

    @Override
    public LiveAuiMessageGroup createMessageGroup(LiveAuiMessageGroupRequestDto liveAuiMessageGroupRequestDto) {
        this.checkExist(liveAuiMessageGroupRequestDto);
        LiveAuiMessageGroup entity = this.createAliMessageGroup(liveAuiMessageGroupRequestDto);
        //保存数据
        iLiveAuiMessageGroupService.save(entity);
        return entity;
    }

    /**
     * 根据名称查询该互动消息群组是否已经存在
     * @param dto 创建群组参数
     */
    private void checkExist(LiveAuiMessageGroupRequestDto dto) {
        LiveAuiMessageGroup liveAuiMessageGroup = iLiveAuiMessageGroupService.lambdaQuery().eq(LiveAuiMessageGroup::getGroupName, dto.getGroupName()).one();
        if (null != liveAuiMessageGroup) {
            throw new ServiceException("该群组【{}】已经存在！", dto.getGroupName());
        }
    }

    @Override
    public LiveAuiMessageGroup createAliMessageGroup(LiveAuiMessageGroupRequestDto dto) {
        LiveAuiMessageGroup result = null;
        //已存在并删除
        boolean exsitedAndDeleted;

        String id = IdUtil.getSnowflakeNextIdStr();
        AliCreateLiveMessageGroupRequest request = new AliCreateLiveMessageGroupRequest();
        request.setGroupId(id);
        request.setGroupName(dto.getGroupName());
        request.setGroupInfo(dto.getGroupInfo());
        request.setAdministrators(StringUtils.str2List(dto.getAdministrators(), ",", true, true));
        request.setDataCenter(Enum4LiveDataCenter.SHANGHAI.getCenter());

        try {
            CreateLiveMessageGroupResponse response = iAliCloudBizService.createLiveMessageGroup(request);
            boolean alreadyExists = response.getAlreadyExists();
            boolean alreadyDelete = response.getAlreadyDelete();

            exsitedAndDeleted = (alreadyDelete && alreadyExists);

            if (exsitedAndDeleted) {
                while (exsitedAndDeleted) {
                    request.setGroupId(IdUtil.getSnowflakeNextIdStr());
                    dto.setGroupId(id);

                    CreateLiveMessageGroupResponse response2 = iAliCloudBizService.createLiveMessageGroup(request);
                    boolean alreadyExists2 = response2.getAlreadyExists();
                    boolean alreadyDelete2 = response2.getAlreadyDelete();

                    exsitedAndDeleted = (alreadyExists2 && alreadyDelete2);

                    if (!exsitedAndDeleted) {
                        dto.setRequestId(response2.getRequestId());

                        result = buildEntity(dto);
                    }
                }
            } else {
                dto.setRequestId(response.getRequestId());
                dto.setGroupId(response.getGroupId());
                result = buildEntity(dto);
            }
        } catch (Exception e) {
            log.error("创建互动消息群组失败！", e);
        }
        return result;
    }

    private LiveAuiMessageGroup buildEntity(LiveAuiMessageGroupRequestDto liveAuiMessageGroupRequestDto) {
        LiveAuiMessageGroup entity = new LiveAuiMessageGroup();

        entity.setAppid(liveAuiMessageGroupRequestDto.getAppid());
        entity.setGroupId(liveAuiMessageGroupRequestDto.getGroupId());
        entity.setGroupName(liveAuiMessageGroupRequestDto.getGroupName());
        entity.setGroupInfo(liveAuiMessageGroupRequestDto.getGroupInfo());
        entity.setDataCenter(liveAuiMessageGroupRequestDto.getDataCenter());
        entity.setAdministrators(liveAuiMessageGroupRequestDto.getAdministrators());
        entity.setRequestId(liveAuiMessageGroupRequestDto.getRequestId());
        entity.setStatus(1L);
        entity.setCreateTime(new Date());

        return entity;
    }

    @Override
    public boolean updateMessageGroup(LiveAuiMessageGroupRequestDto dto) {
        boolean flag = false;
        LiveAuiMessageGroup entityDb = iLiveAuiMessageGroupService.getById(dto.getId());
        boolean updateAdmin = (!entityDb.getAdministrators().equals(dto.getAdministrators()));
        boolean updateInfo = (!entityDb.getGroupInfo().equals(dto.getGroupInfo()));

        boolean updateAliGroupResult = updateAliMessageGroup(dto, updateAdmin, updateInfo);
        if (updateAliGroupResult) {
            LiveAuiMessageGroup entity = msLiveAuiMessageGroupBiz.dto2po(dto);
            return iLiveAuiMessageGroupService.updateById(entity);
        }
        return flag;
    }

    private boolean updateAliMessageGroup(LiveAuiMessageGroupRequestDto liveAuiMessageGroupRequestDto, boolean updateAdmin, boolean updateInfo) {
        boolean updateResult;

        DefaultProfile profile = DefaultProfile.getProfile("cn-qingdao", System.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID"), System.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET"));

        IAcsClient client = new DefaultAcsClient(profile);

        ModifyLiveMessageGroupRequest request = new ModifyLiveMessageGroupRequest();
        request.setAppId(liveAuiMessageGroupRequestDto.getAppid());
        request.setGroupId(liveAuiMessageGroupRequestDto.getGroupId());
        request.setGroupInfo(liveAuiMessageGroupRequestDto.getGroupInfo());
        request.setModifyAdmin(updateAdmin);
        request.setAdminList(StringUtils.str2List(liveAuiMessageGroupRequestDto.getAdministrators(), ",", true, true));
        request.setModifyInfo(updateInfo);
        request.setGroupInfo(liveAuiMessageGroupRequestDto.getGroupInfo());
        request.setDataCenter(liveAuiMessageGroupRequestDto.getDataCenter());

        try {
            ModifyLiveMessageGroupResponse response = client.getAcsResponse(request);
            updateResult = true;

        } catch (ClientException e) {
            updateResult = false;
            log.error("修改阿里云直播互动消息群组失败！", e);
        }

        return updateResult;
    }

    @Override
    public boolean deleteMessageGroup(Long id) {
        LiveAuiMessageGroup liveAuiMessageGroup = iLiveAuiMessageGroupService.getById(id);
        deleteAliMessageGroup(liveAuiMessageGroup);
        return iLiveAuiMessageGroupService.removeById(id);
    }

    @Override
    public LiveAuiMessageGroup getById(Long id) {
        return iLiveAuiMessageGroupService.getById(id);
    }

    private void deleteAliMessageGroup(LiveAuiMessageGroup liveAuiMessageGroup) {
        DefaultProfile profile = DefaultProfile.getProfile("cn-qingdao", System.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID"), System.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET"));

        IAcsClient client = new DefaultAcsClient(profile);

        DeleteLiveMessageGroupRequest request = new DeleteLiveMessageGroupRequest();
        request.setAppId(liveAuiMessageGroup.getAppid());
        request.setGroupId(liveAuiMessageGroup.getGroupId());
        request.setDataCenter(liveAuiMessageGroup.getDataCenter());

        try {
            DeleteLiveMessageGroupResponse response = client.getAcsResponse(request);

        } catch (ClientException e) {
            log.error("删除阿里云消息互动群组失败！", e);
            throw new ServiceException("删除阿里云消息互动群组失败！");
        }
    }
}
