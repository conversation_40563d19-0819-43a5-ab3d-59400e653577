package com.panda.pollen.aui.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyuncs.live.model.v20161101.*;
import com.github.pagehelper.PageHelper;
import com.panda.pollen.aui.mapper.LiveAuiLiveBizMapper;
import com.panda.pollen.aui.mapstruct.MsLiveAuiAssistantBiz;
import com.panda.pollen.aui.mapstruct.MsLiveAuiLiveBiz;
import com.panda.pollen.aui.mapstruct.MsLiveAuiRoomInfoBiz;
import com.panda.pollen.aui.model.PullLiveInfo;
import com.panda.pollen.aui.model.PushLiveInfo;
import com.panda.pollen.aui.model.cons.ConsLiveAui;
import com.panda.pollen.aui.model.dto.*;
import com.panda.pollen.aui.model.enums.EnumListedStatus;
import com.panda.pollen.aui.model.enums.EnumLiveStatus;
import com.panda.pollen.aui.model.req.*;
import com.panda.pollen.aui.model.vo.*;
import com.panda.pollen.aui.model.vo.LiveRoomMsgVo;
import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.service.ILiveAuiCourseCategoryBizService;
import com.panda.pollen.aui.service.ILiveAuiLiveBizService;
import com.panda.pollen.aui.system.domain.*;
import com.panda.pollen.aui.system.service.*;
import com.panda.pollen.aui.util.LocalDateTimeUtils;
import com.panda.pollen.common.annotation.DataScope;
import com.panda.pollen.common.core.domain.entity.SysUser;
import com.panda.pollen.common.core.service.ISysUserService;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.exception.base.BaseException;
import com.panda.pollen.common.utils.PageUtils;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.scrm.domain.AuthCorpUserCustomer;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveAuiLiveBizServiceImpl implements ILiveAuiLiveBizService {
    @Resource
    private LiveAuiLiveBizMapper liveAuiLiveBizMapper;
    @Resource
    private ILiveAuiRoomInfoService iLiveAuiRoomInfoService;
    @Resource
    private ILiveAuiRoomInfoExtService iLiveAuiRoomInfoExtService;
    @Resource
    private ILiveAuiRoomAssistantService iLiveAuiRoomAssistantService;
    @Resource
    private MsLiveAuiRoomInfoBiz msLiveAuiRoomInfoBiz;
    @Resource
    private ILiveAuiCourseCategoryBizService iLiveAuiCourseCategoryBizService;
    @Resource
    private MsLiveAuiAssistantBiz msLiveAuiAssistantBiz;
    @Resource
    private ILiveAuiRoomStreamUrlInfoService iLiveAuiRoomStreamUrlInfoService;
    @Resource
    private IAliCloudBizService iAliCloudBizService;
    @Resource
    private MsLiveAuiLiveBiz msLiveAuiLiveBiz;
    @Resource
    private ILiveAuiLiveRetweetService iLiveAuiLiveRetweetService;
    @Resource
    private ILiveAuiLiveRetweetOriginService iLiveAuiLiveRetweetOriginService;
    @Resource
    private ILiveAuiMessageTemplateService iLiveAuiMessageTemplateService;
    @Resource
    private ILiveAuiChatScriptService iLiveAuiChatScriptService;
    @Resource
    private ISysUserService iSysUserService;
    @Resource
    private ILiveAuiMessageGroupService iLiveAuiMessageGroupService;
    @Resource
    private ILiveAuiCourseCategoryService iLiveAuiCourseCategoryService;
    @Resource
    private ILiveAuiRoomRobotService iLiveAuiRoomRobotService;
    @Resource
    private ILiveAuiImageFileService iLiveAuiImageFileService;
    @Resource
    private ILiveAuiVideoFileService iLiveAuiVideoFileService;
    @Resource
    private ILiveAuiRoomActiveMemberService iLiveAuiRoomActiveMemberService;
    @Resource
    private ILiveAuiCloudTranscodeTaskService iLiveAuiCloudTranscodeTaskService;

    /**
     * 创建阿里云消息组
     *
     * @param request 创建直播间参数
     * @return 创建消息组结果
     */
    private CreateLiveMessageGroupResponse createLiveMessageGroup(AliCreateLiveMessageGroupRequest request) {
        // 远程创建消息组
        CreateLiveMessageGroupResponse response = iAliCloudBizService.createLiveMessageGroup(request);
        if (response == null) {
            throw new BaseException("创建远程消息组失败");
        }
        // 消息组存在
        boolean alreadyExists = response.getAlreadyExists();
        // 消息组被删除
        boolean alreadyDelete = response.getAlreadyDelete();
        // 先要存在才有后面的是否删除
        boolean existedAndDeleted = (alreadyExists && alreadyDelete);
        if (existedAndDeleted) {
            response = this.createLiveMessageGroup(request);
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(LiveRoomCreateDto dto) {
        // 检查所有的参数情况
        this.checkAndImproveParams(dto);
        // 获取消息组
        AliCreateLiveMessageGroupRequest request = msLiveAuiLiveBiz.dto2req(dto);
        CreateLiveMessageGroupResponse response = this.createLiveMessageGroup(request);
        // 1. 创建基础直播间
        LiveAuiRoomInfo liveAuiRoomInfo = msLiveAuiLiveBiz.dto2po(dto);
        liveAuiRoomInfo.setChatId(response.getGroupId());
        boolean riSaved = iLiveAuiRoomInfoService.save(liveAuiRoomInfo);
        if (!riSaved) {
            throw new BaseException("保存直播间数据失败");
        }
        // 2. 消息组数据
        LiveAuiMessageGroup liveAuiMessageGroup = msLiveAuiLiveBiz.resAndDto2Po(response, request);
        liveAuiMessageGroup.setRoomInfoId(liveAuiRoomInfo.getId());
        boolean mgSaved = iLiveAuiMessageGroupService.save(liveAuiMessageGroup);
        if (!mgSaved) {
            throw new BaseException("保存消息组数据失败");
        }
        // 3. 创建直播间扩展信息
        LiveAuiRoomInfoExt roomInfoExt = msLiveAuiRoomInfoBiz.dto2po(liveAuiRoomInfo.getId(), dto);
        roomInfoExt.setMessageGroupId(liveAuiMessageGroup.getId());
        if (dto.getListedStatus() != null && EnumListedStatus.LISTED_STATUS_ONLINE.getVal() == dto.getListedStatus() || EnumListedStatus.LISTED_STATUS_OFFLINE.getVal() == dto.getListedStatus()) {
            roomInfoExt.setListedTime(new Date());
        }
        boolean rieSaved = iLiveAuiRoomInfoExtService.save(roomInfoExt);
        if (!rieSaved) {
            throw new BaseException("保存直播间扩展信息失败");
        }
        // 4. 保存流URL信息
        List<LiveAuiRoomStreamUrlInfo> liveAuiRoomStreamUrlInfos = this.parseStreamUrlInfo(liveAuiRoomInfo.getId(), liveAuiMessageGroup.getGroupId());
        boolean rsuiSaved = iLiveAuiRoomStreamUrlInfoService.saveBatch(liveAuiRoomStreamUrlInfos);
        if (!rsuiSaved) {
            throw new BaseException("保存流URL信息失败");
        }
        // 5. 创建助教关联信息
        List<Long> assistantIds = dto.getAssistantIds();
        List<LiveAuiRoomAssistant> liveAuiRoomAssistants = this.getLiveAuiRoomAssistants(assistantIds, liveAuiRoomInfo);
        boolean assistantSaved = iLiveAuiRoomAssistantService.saveBatch(liveAuiRoomAssistants);
        if (!assistantSaved) {
            throw new BaseException("保存助教关联信息失败");
        }
        // 6、创建拉流转推任务
        this.createCreateLivePullToPush(dto, liveAuiRoomStreamUrlInfos, liveAuiRoomInfo.getId());
        // 7、自动选择10个机器人关联到当前直播间
        List<LiveAuiRoomRobot> liveAuiRoomRobotMsgRecords = this.getLiveAuiRoomRobots(liveAuiRoomInfo);
        boolean robotSaved = iLiveAuiRoomRobotService.saveBatch(liveAuiRoomRobotMsgRecords);
        if (!robotSaved) {
            throw new BaseException("保存机器人关联信息失败");
        }
        // 8、设置直播间的人员信息
        List<LiveAuiRoomActiveMember> liveAuiRoomActiveMembers = this.getLiveAuiRoomActiveMembers(liveAuiRoomInfo);
        boolean memberSaved = iLiveAuiRoomActiveMemberService.saveBatch(liveAuiRoomActiveMembers);
        if (!memberSaved) {
            throw new BaseException("保存直播间人员信息失败");
        }
        return true;
    }

    private List<LiveAuiRoomAssistant> getLiveAuiRoomAssistants(List<Long> assistantIds, LiveAuiRoomInfo liveAuiRoomInfo) {
        return assistantIds.stream().map(assistantId -> msLiveAuiLiveBiz.dto2po(liveAuiRoomInfo.getId(), assistantId)).toList();
    }

    /**
     * 获取直播间人员信息
     *
     * @param liveAuiRoomInfo 直播间信息
     * @return 直播间人员信息
     */
    private List<LiveAuiRoomActiveMember> getLiveAuiRoomActiveMembers(LiveAuiRoomInfo liveAuiRoomInfo) {
        List<AuthCorpUserCustomer> wxAuthCorpUserCustomers = liveAuiLiveBizMapper.getWxAuthCorpUserCustomers(10);
        // 获取所有企微客户
        List<String> externalUserIds = wxAuthCorpUserCustomers.stream().map(AuthCorpUserCustomer::getExternalUserId).toList();
        // 跟进人列表
        List<WxUserDto> wxUserDtos = liveAuiLiveBizMapper.getOwners(externalUserIds);
        // 会话id就是群组id
        return wxAuthCorpUserCustomers.stream().map(wxAuthCorpUserCustomer -> {
            LiveAuiRoomActiveMember liveAuiRoomActiveMember = new LiveAuiRoomActiveMember();
            liveAuiRoomActiveMember.setRoomId(liveAuiRoomInfo.getId());
            String externalUserId = wxAuthCorpUserCustomer.getExternalUserId();
            liveAuiRoomActiveMember.setExternalUserId(externalUserId);
            liveAuiRoomActiveMember.setAvatar(wxAuthCorpUserCustomer.getAvatar());
            liveAuiRoomActiveMember.setCustomerName(wxAuthCorpUserCustomer.getCustomerName());
            // 会话id就是群组id
            liveAuiRoomActiveMember.setGroupId(liveAuiRoomInfo.getChatId());
            wxUserDtos.stream()
                    .filter(wxUserDto -> wxUserDto.getExternalUserId().equals(externalUserId))
                    .findFirst()
                    .ifPresent(wxUserDto -> liveAuiRoomActiveMember.setOwnerId(wxUserDto.getSysUserId()));
            return liveAuiRoomActiveMember;
        }).toList();
    }

    /**
     * 校验与完善参数
     *
     * @param dto 创建直播间参数
     */
    private void checkAndImproveParams(LiveRoomCreateDto dto) {
        log.info("后台管理创建直播间请求参数: {}", dto);
        if (dto == null) {
            throw new BaseException("参数不能为空");
        }
        // 日期校验
        this.checkAndImproveDate(dto);
        // 主播id
        String anchorId = dto.getAnchorId();
        List<Long> anchorIds = Collections.singletonList(Long.valueOf(anchorId));
        List<SysUser> anchorSysUsers = liveAuiLiveBizMapper.getUserById(anchorIds);
        if (anchorSysUsers == null || anchorSysUsers.isEmpty()) {
            throw new BaseException("主播不存在");
        }
        String nickName = anchorSysUsers.stream().findFirst().get().getNickName();
        dto.setAnchorNick(nickName);
        String userName = anchorSysUsers.stream().findFirst().get().getUserName();
        dto.setAnchor(userName);

        // 图片判断
        String imageId = dto.getLiveImageId();
        if (imageId != null && !imageId.isEmpty()) {
            LiveAuiImageFile liveAuiImageFile = iLiveAuiImageFileService.getById(imageId);
            if (liveAuiImageFile == null) {
                throw new BaseException("图片不存在");
            }
        }

        // 暖场视频
        String warmUpVideoId = dto.getWarmUpVideoId();
        if (warmUpVideoId != null && !warmUpVideoId.isEmpty()) {
            LiveAuiVideoFile warmVideo = iLiveAuiVideoFileService.getById(warmUpVideoId);
            if (warmVideo == null) {
                throw new BaseException("暖场视频不存在");
            }
        }

        // 助教核对
        List<Long> assistantIds = dto.getAssistantIds();
        List<SysUser> assistantSysUsers = liveAuiLiveBizMapper.getUserById(assistantIds);
        if (assistantSysUsers == null || assistantSysUsers.isEmpty()) {
            throw new BaseException("助教不存在");
        }
    }

    /**
     * 日期校验
     *
     * @param dto 参数
     */
    private void checkAndImproveDate(LiveRoomCreateDto dto) {
        // 比较两个日期情况
        LocalDateTime startTime = dto.getStartTime();
        LocalDateTime stopTime = dto.getStopTime();
        // 参数验证 开始时间不能早于/等于当前时间
        if (startTime == null || !startTime.isAfter(LocalDateTime.now())) {
            throw new BaseException("开始时间不能早于/等于当前时间");
        }

        if (stopTime == null || stopTime.isBefore(startTime)) {
            throw new BaseException("结束时间不能早于开始时间");
        }
    }

    @NotNull
    private List<LiveAuiRoomRobot> getLiveAuiRoomRobots(LiveAuiRoomInfo roomInfo) {
        List<LiveAuiRobotPool> robots = liveAuiLiveBizMapper.getRobots(10);
        return robots.stream().map(liveAuiRobotPool -> {
            LiveAuiRoomRobot liveAuiRoomRobotMsgRecord = new LiveAuiRoomRobot();
            liveAuiRoomRobotMsgRecord.setRoomInfoId(roomInfo.getId());
            liveAuiRoomRobotMsgRecord.setRobotId(liveAuiRobotPool.getId());
            return liveAuiRoomRobotMsgRecord;
        }).toList();
    }

    /**
     * 创建拉流转推任务
     *
     * @param dto                       创建直播间参数
     * @param liveAuiRoomStreamUrlInfos 流URL信息
     */
    private void createCreateLivePullToPush(LiveRoomCreateDto dto, List<LiveAuiRoomStreamUrlInfo> liveAuiRoomStreamUrlInfos, Long roomInfoId) {
        if (dto.getCourseVideoId() == null && dto.getLiveTemplateId() == null) {
            throw new ServiceException("请选择内容来源");
        }
        String sourceUrl = null;

        if (dto.getCourseVideoId() != null) {
            sourceUrl = dto.getCourseVideoId();
        } else {
            LiveAuiMessageTemplate templateServiceById = iLiveAuiMessageTemplateService.getById(dto.getLiveTemplateId());
            if (templateServiceById == null) {
                throw new ServiceException("直播模板不存在");
            }
            sourceUrl = templateServiceById.getAppVideoId() != null ? templateServiceById.getAppVideoId().toString() : templateServiceById.getPcVideoId().toString();
        }

        AliCreateLivePullToPushRequest req = new AliCreateLivePullToPushRequest();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        req.setStartTime(dto.getStartTime().format(formatter));
        req.setEndTime(dto.getStopTime().format(formatter));
        req.setSourceType("vod");
        req.setSourceUrls(Collections.singletonList(sourceUrl));
        liveAuiRoomStreamUrlInfos.stream().filter(liveAuiRoomStreamUrlInfo -> "PUSH".equals(liveAuiRoomStreamUrlInfo.getUrlType()) && "RTMP".equalsIgnoreCase(liveAuiRoomStreamUrlInfo.getProtocol())).findAny().ifPresent(liveAuiRoomStreamUrlInfo -> req.setDstUrl(liveAuiRoomStreamUrlInfo.getUrl()));
        CreateLivePullToPushResponse response = iAliCloudBizService.createLivePullToPush(req);
        if (ObjectUtils.isEmpty(response)) {
            throw new BaseException("创建拉流转推任务失败");
        }
        LiveAuiCloudTranscodeTask transcodeTask = new LiveAuiCloudTranscodeTask();
        transcodeTask.setTaskId(response.getTaskId());
        transcodeTask.setRoomInfoId(roomInfoId);
        iLiveAuiCloudTranscodeTaskService.save(transcodeTask);
    }


    @Override
    public List<LiveRoomPageVo> page(LiveRoomPageQueryDto dto) {
        log.info("分页查询直播间列表，查询参数: {}", dto);
        // 设置分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 查询数据
        List<LiveRoomPageVo> voList = liveAuiLiveBizMapper.page(dto);
        if (CollUtil.isNotEmpty(voList)) {
            voList.forEach(vo -> {
                List<LiveAuiAssistantVo> liveAuiAssistantVos = generateAssistant(vo.getId());
                vo.setAssistantList(liveAuiAssistantVos);
                //消息群组
                String groupName = generateMessageGroup(vo.getMessageGroupId());
                vo.setMessageGroupName(groupName);
                //课程类目courseGroupId
                String categoryName = generateCourseName(vo.getCourseGroupId());
                vo.setCourseGroupName(categoryName);
                //直播模板liveTemplateId
                String templateName = generateLiveTemplateId(vo.getLiveTemplateId());
                vo.setTemplateName(templateName);
            });
        }
        return voList;
    }


    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<LiveRoomPageVo> historyPage(LiveRoomPageQueryDto dto) {
        PageUtils.startPage();
        return liveAuiLiveBizMapper.page(dto);
    }

    @Override
    public List<CourseCategoryDropdownVo> getDropdownList() {
        LiveAuiCourseCategoryRequestDto liveAuiCourseCategoryRequestDto = new LiveAuiCourseCategoryRequestDto().setCategoryType("LIVE").setStatus(1);
        List<LiveAuiCourseCategory> list = iLiveAuiCourseCategoryBizService.list(liveAuiCourseCategoryRequestDto);
        return msLiveAuiRoomInfoBiz.po2vo(list);
    }

    @Override
    public LiveRoomDetailVo getAdminLiveRoomDetail(String roomId) {
        LiveRoomDetailVo adminLiveRoomDetail = liveAuiLiveBizMapper.getAdminLiveRoomDetail(roomId);
        if (ObjectUtil.isNotEmpty(adminLiveRoomDetail)) {
            // 查询助教信息
            List<LiveAuiAssistantVo> liveAuiAssistantVos = generateAssistant(adminLiveRoomDetail.getId());
            adminLiveRoomDetail.setAssistantList(liveAuiAssistantVos);
            //课程类目courseGroupId
            String categoryName = generateCourseName(adminLiveRoomDetail.getCourseGroupId());
            adminLiveRoomDetail.setCourseGroupName(categoryName);
            //直播模板liveTemplateId
            String templateName = generateLiveTemplateId(adminLiveRoomDetail.getLiveTemplateId());
            adminLiveRoomDetail.setTemplateName(templateName);
        }
        return adminLiveRoomDetail;
    }

    @Override
    public boolean editAdminLiveRoom(LiveRoomUpdateDto dto) {
        // 检查所有的参数情况
        this.checkAndImproveParams(dto);
        // 1. 更新基础直播间主表
        LiveAuiRoomInfo liveAuiRoomInfo = msLiveAuiRoomInfoBiz.dto2poEntity(dto);
        boolean mainUpdated = iLiveAuiRoomInfoService.updateById(liveAuiRoomInfo);

        // 2. 更新扩展表
        LiveAuiRoomInfoExt liveAuiRoomInfoExt = msLiveAuiRoomInfoBiz.dto2poExt(dto);
        if (dto.getListedStatus() != null && EnumListedStatus.LISTED_STATUS_ONLINE.getVal() == dto.getListedStatus() || EnumListedStatus.LISTED_STATUS_OFFLINE.getVal() == dto.getListedStatus()) {
            liveAuiRoomInfoExt.setListedTime(new Date());
        }
        boolean extUpdated = iLiveAuiRoomInfoExtService.updateById(liveAuiRoomInfoExt);

        // 3. 更新助教：先删除原有再插入新助教
        updateRoomAssistant(dto);
        //4、更新直播间拉流转推任务
        updateLivePullToPushTask(dto);
        return mainUpdated && extUpdated;
    }

    private void updateLivePullToPushTask(LiveRoomUpdateDto dto) {
        //查询直播间拉流转推任务
        LiveAuiCloudTranscodeTask transcodeTask = iLiveAuiCloudTranscodeTaskService.lambdaQuery()
                .eq(LiveAuiCloudTranscodeTask::getRoomInfoId, Long.valueOf(dto.getId()))
                .one();
        if (ObjectUtil.isNotEmpty(transcodeTask)) {
            //更新任务
            AliDescribeLivePullToPushRequest toPushRequest = new AliDescribeLivePullToPushRequest();
            toPushRequest.setTaskId(transcodeTask.getTaskId());
            DescribeLivePullToPushResponse response = iAliCloudBizService.describeLivePullToPush(toPushRequest);
            if (ObjectUtils.isEmpty(response)) {
                throw new BaseException("推流任务查询失败");
            }
            AliUpdateLivePullToPushRequest req = new AliUpdateLivePullToPushRequest();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            req.setStartTime(dto.getStartTime().format(formatter));
            req.setEndTime(dto.getStopTime().format(formatter));
            req.setSourceUrls(Collections.singletonList(dto.getCourseVideoId()));
            req.setTaskId(transcodeTask.getTaskId());
            UpdateLivePullToPushResponse updateLivePullToPush = iAliCloudBizService.updateLivePullToPush(req);
            if (updateLivePullToPush == null) {
                throw new BaseException("推流任务更新失败");
            }
        }
    }

    @Override
    public List<LiveAuiRoomStreamUrlInfo> getLiveRoomUrl(String roomId) {
        if (CharSequenceUtil.isNotBlank(roomId)) {
            return iLiveAuiRoomStreamUrlInfoService.lambdaQuery().eq(LiveAuiRoomStreamUrlInfo::getRoomInfoId, roomId).list();
        }
        return List.of();
    }

    /**
     * 更新直播间助教
     */
    private void updateRoomAssistant(LiveRoomUpdateDto editDto) {
        Long roomInfoId = Long.valueOf(editDto.getId());
        // 删除原有助教
        iLiveAuiRoomAssistantService.lambdaUpdate().eq(LiveAuiRoomAssistant::getRoomInfoId, roomInfoId).remove();
        // 插入新助教
        if (editDto.getAssistantIds() != null && !editDto.getAssistantIds().isEmpty()) {
            List<Long> assistantIds = editDto.getAssistantIds();
            List<LiveAuiRoomAssistant> liveAuiRoomAssistants = assistantIds.stream().map(assistantId -> msLiveAuiAssistantBiz.dto2po(roomInfoId, assistantId)).toList();
            iLiveAuiRoomAssistantService.saveBatch(liveAuiRoomAssistants);
        }
    }

    /**
     * 保存直播间流URL信息（基于RoomInfoDto）
     *
     * @param roomInfoId 直播间ID
     * @param groupId    直播间信息DTO
     * @return 是否保存成功
     */
    private List<LiveAuiRoomStreamUrlInfo> parseStreamUrlInfo(Long roomInfoId, String groupId) {
        PushLiveInfo pushLiveInfo = iAliCloudBizService.getPushLiveInfo(groupId);
        PullLiveInfo pullLiveInfo = iAliCloudBizService.getPullLiveInfo(groupId);
        List<LiveAuiRoomStreamUrlInfo> streamUrlList = new ArrayList<>();
        // 处理推流URL信息
        if (pushLiveInfo != null) {
            List<LiveAuiRoomStreamUrlInfo> liveAuiRoomStreamUrlInfos = this.parsePushUrls(roomInfoId, pushLiveInfo);
            streamUrlList.addAll(liveAuiRoomStreamUrlInfos);
        }
        // 处理拉流URL信息
        if (pullLiveInfo != null) {
            List<LiveAuiRoomStreamUrlInfo> liveAuiRoomStreamUrlInfos = this.parsePullUrls(roomInfoId, pullLiveInfo);
            streamUrlList.addAll(liveAuiRoomStreamUrlInfos);
        }
        return streamUrlList;

    }

    /**
     * 解析推流URL信息（基于PushLiveInfo）
     */
    private List<LiveAuiRoomStreamUrlInfo> parsePushUrls(Long roomInfoId, PushLiveInfo pushLiveInfo) {
        List<LiveAuiRoomStreamUrlInfo> pushUrls = new ArrayList<>();
        // SRT推流
        addStreamUrl(pushUrls, roomInfoId, ConsLiveAui.URL_TYPE_PUSH, ConsLiveAui.PROTOCOL_SRT, pushLiveInfo.getSrtUrl(), ConsLiveAui.IS_ORI_AAC_NO);
        // RTMP推流
        addStreamUrl(pushUrls, roomInfoId, ConsLiveAui.URL_TYPE_PUSH, ConsLiveAui.PROTOCOL_RTMP, pushLiveInfo.getRtmpUrl(), ConsLiveAui.IS_ORI_AAC_NO);
        // RTS推流
        addStreamUrl(pushUrls, roomInfoId, ConsLiveAui.URL_TYPE_PUSH, ConsLiveAui.PROTOCOL_RTS, pushLiveInfo.getRtsUrl(), ConsLiveAui.IS_ORI_AAC_NO);
        return pushUrls;
    }

    /**
     * 解析拉流URL信息（基于PullLiveInfo）
     */
    private List<LiveAuiRoomStreamUrlInfo> parsePullUrls(Long roomInfoId, com.panda.pollen.aui.model.PullLiveInfo pullLiveInfo) {
        List<LiveAuiRoomStreamUrlInfo> pullUrls = new ArrayList<>();
        // FLV拉流
        addStreamUrl(pullUrls, roomInfoId, ConsLiveAui.URL_TYPE_PULL, ConsLiveAui.PROTOCOL_FLV, pullLiveInfo.getFlvUrl(), ConsLiveAui.IS_ORI_AAC_NO);
        addStreamUrl(pullUrls, roomInfoId, ConsLiveAui.URL_TYPE_PULL, ConsLiveAui.PROTOCOL_FLV, pullLiveInfo.getFlvOriaacUrl(), ConsLiveAui.IS_ORI_AAC_YES);
        // RTMP拉流
        addStreamUrl(pullUrls, roomInfoId, ConsLiveAui.URL_TYPE_PULL, ConsLiveAui.PROTOCOL_RTMP, pullLiveInfo.getRtmpUrl(), ConsLiveAui.IS_ORI_AAC_NO);
        addStreamUrl(pullUrls, roomInfoId, ConsLiveAui.URL_TYPE_PULL, ConsLiveAui.PROTOCOL_RTMP, pullLiveInfo.getRtmpOriaacUrl(), ConsLiveAui.IS_ORI_AAC_YES);
        // HLS拉流
        addStreamUrl(pullUrls, roomInfoId, ConsLiveAui.URL_TYPE_PULL, ConsLiveAui.PROTOCOL_HLS, pullLiveInfo.getHlsUrl(), ConsLiveAui.IS_ORI_AAC_NO);
        addStreamUrl(pullUrls, roomInfoId, ConsLiveAui.URL_TYPE_PULL, ConsLiveAui.PROTOCOL_HLS, pullLiveInfo.getHlsOriaacUrl(), ConsLiveAui.IS_ORI_AAC_YES);
        // RTS拉流
        addStreamUrl(pullUrls, roomInfoId, ConsLiveAui.URL_TYPE_PULL, ConsLiveAui.PROTOCOL_RTS, pullLiveInfo.getRtsUrl(), ConsLiveAui.IS_ORI_AAC_NO);
        addStreamUrl(pullUrls, roomInfoId, ConsLiveAui.URL_TYPE_PULL, ConsLiveAui.PROTOCOL_RTS, pullLiveInfo.getRtsOriaacUrl(), ConsLiveAui.IS_ORI_AAC_YES);

        return pullUrls;
    }

    /**
     * 添加流URL信息到列表（基于字符串）
     */
    private void addStreamUrl(List<LiveAuiRoomStreamUrlInfo> urlList, Long roomInfoId, String urlType, String protocol, String url, Integer isOriAac) {
        if (CharSequenceUtil.isNotBlank(url)) {
            LiveAuiRoomStreamUrlInfo streamUrl = new LiveAuiRoomStreamUrlInfo();
            streamUrl.setRoomInfoId(roomInfoId);
            streamUrl.setUrlType(urlType);
            streamUrl.setProtocol(protocol);
            streamUrl.setUrl(url);
            streamUrl.setIsOriginAac(isOriAac);
            streamUrl.setAuthKey(extractAuthKey(url));
            urlList.add(streamUrl);
        }
    }

    /**
     * 从URL中提取auth_key
     */
    private String extractAuthKey(String url) {
        if (CharSequenceUtil.isBlank(url)) {
            return null;
        }

        Matcher matcher = ConsLiveAui.AUTH_KEY_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreateLivePullToPushResponse createLivePullToPush(AliCreateLivePullToPushRequest dto) {
        this.checkDtoField(dto.getStartTime(), dto.getEndTime());
        String sourceType = dto.getSourceType();
        if ("vod".equalsIgnoreCase(sourceType) || "url".equalsIgnoreCase(sourceType)) {
            // 此参数仅当 source type 参数取值为 live 时必填，取值为 vod、url 时无效
            dto.setSourceProtocol(CharSequenceUtil.EMPTY);
        }
        String sourceProtocol = dto.getSourceProtocol();
        List<String> sourceUrls = dto.getSourceUrls();
        if ("live".equalsIgnoreCase(sourceType)) {
            if (sourceUrls.size() != 1) {
                log.error("live 类型仅支持填写 1 个完整直播播放 URL");
                throw new BaseException("live 类型仅支持填写 1 个完整直播播放 URL");
            }

            if (!CollUtil.newArrayList("rtmp", "rtsp", "srt", "http-flv").contains(sourceProtocol)) {
                log.error("live 类型支持：rtmp、rtsp、srt、http-flv 协议");
                throw new BaseException("live 类型支持：rtmp、rtsp、srt、http-flv 协议");
            }
        }
        if ("url".equalsIgnoreCase(sourceType) && !CollUtil.newArrayList("mp4", "http-flv").contains(sourceProtocol)) {
            log.error("url 类型支持：mp4、http-flv 协议");
            throw new BaseException("url 类型支持：mp4、http-flv 协议");
        }
        CreateLivePullToPushResponse response = iAliCloudBizService.createLivePullToPush(dto);
        if (ObjectUtil.isNotNull(response)) {
            LiveAuiLiveRetweet liveAuiLiveRetweet = msLiveAuiLiveBiz.dtoToPo(dto);
            msLiveAuiLiveBiz.resToPo(response, liveAuiLiveRetweet);
            boolean saved = iLiveAuiLiveRetweetService.save(liveAuiLiveRetweet);
            if (saved) {
                this.batchRetweetOrigin(dto.getSourceUrls(), liveAuiLiveRetweet);
            }
        }
        return response;
    }

    @Override
    public DeleteLivePullToPushResponse deleteLivePullToPush(AliDeleteLivePullToPushRequest dto) {
        // 查找是否有数据库记录
        LiveAuiLiveRetweet liveAuiLiveRetweet = iLiveAuiLiveRetweetService.lambdaQuery().eq(LiveAuiLiveRetweet::getTaskId, dto.getTaskId()).one();
        if (ObjectUtil.isNotNull(liveAuiLiveRetweet)) {
            // 删除数据源
            iLiveAuiLiveRetweetOriginService.lambdaUpdate().eq(LiveAuiLiveRetweetOrigin::getRetweetId, liveAuiLiveRetweet.getId()).remove();
            // 删除任务
            iLiveAuiLiveRetweetService.removeById(liveAuiLiveRetweet.getId());

        }
        return iAliCloudBizService.deleteLivePullToPush(dto);
    }

    @Override
    public UpdateLivePullToPushResponse updateLivePullToPush(AliUpdateLivePullToPushRequest dto) {
        // 校验字段
        this.checkDtoField(dto.getStartTime(), dto.getEndTime());
        // 远程更新
        UpdateLivePullToPushResponse response = iAliCloudBizService.updateLivePullToPush(dto);
        if (ObjectUtil.isNotNull(response)) {
            LiveAuiLiveRetweet liveAuiLiveRetweet = iLiveAuiLiveRetweetService.lambdaQuery().eq(LiveAuiLiveRetweet::getTaskId, dto.getTaskId()).one();
            msLiveAuiLiveBiz.dto2po(dto, liveAuiLiveRetweet);
            iLiveAuiLiveRetweetService.updateById(liveAuiLiveRetweet);
            boolean removed = iLiveAuiLiveRetweetOriginService.lambdaUpdate().eq(LiveAuiLiveRetweetOrigin::getRetweetId, liveAuiLiveRetweet.getId()).remove();
            if (removed) {
                this.batchRetweetOrigin(dto.getSourceUrls(), liveAuiLiveRetweet);
            }
        }
        return response;
    }

    @Override
    public List<LiveTemplateVo> retrieveLiveTemplateList() {
        List<LiveAuiMessageTemplate> auiMessageTemplates = iLiveAuiMessageTemplateService.lambdaQuery().eq(LiveAuiMessageTemplate::getStatus, 1).list();
        return msLiveAuiLiveBiz.poList2VoList(auiMessageTemplates);

    }

    @Override
    public Boolean deleteLiveRoomById(String liveId) {
        return iLiveAuiRoomInfoService.removeById(liveId);
    }

    @Override
    public List<DeptUserTreeVO> retrieveUserTreeVoList(UserTreeDto userTreeDto) {
        Long deptId = SecurityUtils.getDeptId();
        List<DeptTreeVo> deptTreeVos = liveAuiLiveBizMapper.retrieveDeptTreeVoList(deptId);
        List<Long> deptIds = deptTreeVos.stream().map(DeptTreeVo::getDeptId).toList();
        List<Long> userIds = userTreeDto.getUserIds();
        List<UserTreeVo> userTreeVos = liveAuiLiveBizMapper.retrieveUserToDept(deptIds);
        if (userIds != null && !userIds.isEmpty()) {
            userTreeVos = userTreeVos.stream().filter(userTreeVo -> !userIds.contains(userTreeVo.getUserId())).toList();
        }

        // 3. 构建部门Map
        Map<Long, DeptUserTreeVO> deptMap = new HashMap<>();
        for (DeptTreeVo deptTreeVo : deptTreeVos) {
            DeptUserTreeVO node = new DeptUserTreeVO();
            node.setId(deptTreeVo.getDeptId());
            node.setLabel(deptTreeVo.getDeptName());
            node.setType(0);
            node.setChildren(new ArrayList<>());
            deptMap.put(deptTreeVo.getDeptId(), node);
        }
        // 4. 挂载用户到部门
        for (UserTreeVo userTreeVo : userTreeVos) {
            DeptUserTreeVO userNode = new DeptUserTreeVO();
            userNode.setId(userTreeVo.getUserId());
            userNode.setLabel(userTreeVo.getUserName());
            userNode.setType(1);
            userNode.setAvatar(userTreeVo.getAvatar());
            userNode.setChildren(new ArrayList<>());
            DeptUserTreeVO deptNode = deptMap.get(userTreeVo.getDeptId());
            if (deptNode != null) {
                deptNode.getChildren().add(userNode);
            }
        }
        // 5. 组装树
        List<DeptUserTreeVO> tree = new ArrayList<>();
        for (DeptTreeVo deptTreeVo : deptTreeVos) {
            DeptUserTreeVO node = deptMap.get(deptTreeVo.getDeptId());
            if (deptTreeVo.getParentId() == 0 || !deptMap.containsKey(deptTreeVo.getParentId())) {
                // 根部门
                tree.add(node);
            } else {
                DeptUserTreeVO parent = deptMap.get(deptTreeVo.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }

        }
        return tree;
    }

    @Override
    public List<LiveRoomMessageVo> retrieveHisLiveRoomList(String videoId) {
        // 使用联表查询一次性获取所有需要的数据
        List<LiveAuiRoomInfoExt> liveAuiRoomInfoExts = iLiveAuiRoomInfoExtService.lambdaQuery().eq(LiveAuiRoomInfoExt::getCourseVideoId, videoId).list();

        if (CollUtil.isEmpty(liveAuiRoomInfoExts)) {
            return new ArrayList<>();
        }

        // 提取房间ID列表
        List<Long> roomIds = liveAuiRoomInfoExts.stream().map(LiveAuiRoomInfoExt::getRoomInfoId).distinct().toList();

        // 查询状态为已结束的直播间
        List<LiveAuiRoomInfo> liveAuiRoomInfos = iLiveAuiRoomInfoService.lambdaQuery().in(LiveAuiRoomInfo::getId, roomIds)
                // 已结束的直播间
                .eq(LiveAuiRoomInfo::getStatus, 2).list();

        if (CollUtil.isEmpty(liveAuiRoomInfos)) {
            return new ArrayList<>();
        }

        // 将已结束的房间ID放入Set中，便于快速查找
        Set<Long> finishedRoomIds = liveAuiRoomInfos.stream().map(LiveAuiRoomInfo::getId).collect(Collectors.toSet());

        // 构建返回结果
        return liveAuiRoomInfoExts.stream().filter(ext -> finishedRoomIds.contains(ext.getRoomInfoId())).map(ext -> {
            LiveRoomMessageVo liveRoomMessageVo = new LiveRoomMessageVo();
            liveRoomMessageVo.setLiveId(String.valueOf(ext.getRoomInfoId()));
            LiveAuiRoomInfo byId = iLiveAuiRoomInfoService.getById(ext.getRoomInfoId());
            liveRoomMessageVo.setRoomName(byId.getTitle());
            return liveRoomMessageVo;
        }).toList();
    }


    @Override
    public LiveAuiRoomInfo retrieveLiveRoomMsg(String liveId) {
        log.info("查询直播间消息：{}", liveId);
        return iLiveAuiRoomInfoService.lambdaQuery()
                .eq(LiveAuiRoomInfo::getId, liveId)
                .eq(LiveAuiRoomInfo::getStatus, EnumLiveStatus.LIVE_STATUS_OFF.getVal())
                .one();
    }

    @Override
    public List<LiveRoomMsgVo> downloadLiveRoomMsg(String liveId) {
        List<LiveAuiChatScript> auiChatScripts = iLiveAuiChatScriptService.lambdaQuery().eq(LiveAuiChatScript::getRoomInfoId, liveId).list();
        return msLiveAuiLiveBiz.chatPoList2VoList(auiChatScripts);
    }


    /**
     * 批量添加转推源
     *
     * @param sourceUrls         转推源
     * @param liveAuiLiveRetweet 转推
     */
    private void batchRetweetOrigin(List<String> sourceUrls, LiveAuiLiveRetweet liveAuiLiveRetweet) {
        List<LiveAuiLiveRetweetOrigin> liveAuiLiveRetweetOrigins = sourceUrls.stream().map(sourceUrl -> {
            LiveAuiLiveRetweetOrigin liveAuiLiveRetweetOrigin = new LiveAuiLiveRetweetOrigin();
            liveAuiLiveRetweetOrigin.setSourceUrl(sourceUrl);
            liveAuiLiveRetweetOrigin.setRetweetId(liveAuiLiveRetweet.getId());
            return liveAuiLiveRetweetOrigin;
        }).toList();
        iLiveAuiLiveRetweetOriginService.saveBatch(liveAuiLiveRetweetOrigins);
    }

    /**
     * 校验字段
     *
     * @param st 开始时间
     * @param et 结束时间
     */
    private void checkDtoField(String st, String et) {
        LocalDateTime startTime = LocalDateTimeUtils.parseYmdHms(st);
        LocalDateTime endTime = LocalDateTimeUtils.parseYmdHms(et);
        if (!startTime.isBefore(endTime)) {
            log.error("开始时间必须早于结束时间");
            throw new BaseException("开始时间必须早于结束时间");
        }
        long days = LocalDateTimeUtil.between(startTime, endTime, ChronoUnit.DAYS);
        if (days > 7) {
            log.error("时间间隔不能超过7天");
            throw new BaseException("时间间隔不能超过7天");
        }

    }

    private String generateLiveTemplateId(String liveTemplateId) {
        String templateName = StrUtil.EMPTY;
        if (ObjectUtil.isNotEmpty(liveTemplateId)) {
            LiveAuiMessageTemplate liveAuiMessageTemplate = iLiveAuiMessageTemplateService.getById(liveTemplateId);
            templateName = ObjectUtil.isNotEmpty(liveAuiMessageTemplate) ? liveAuiMessageTemplate.getTemplateName() : "";
        }
        return templateName;
    }

    private String generateCourseName(String courseGroupId) {
        String categoryName = StrUtil.EMPTY;
        if (ObjectUtil.isNotEmpty(courseGroupId)) {
            LiveAuiCourseCategory courseCategory = iLiveAuiCourseCategoryService.getById(courseGroupId);
            categoryName = ObjectUtil.isNotEmpty(courseCategory) ? courseCategory.getCategoryName() : "";
        }
        return categoryName;
    }

    private String generateMessageGroup(String messageGroupId) {
        String groupName = StrUtil.EMPTY;
        if (ObjectUtil.isNotEmpty(messageGroupId)) {
            LiveAuiMessageGroup messageGroup = iLiveAuiMessageGroupService.getById(messageGroupId);
            groupName = ObjectUtil.isNotEmpty(messageGroup) ? messageGroup.getGroupName() : StrUtil.EMPTY;
        }
        return groupName;
    }

    private List<LiveAuiAssistantVo> generateAssistant(String roomId) {
        List<LiveAuiRoomAssistant> roomAssistants = iLiveAuiRoomAssistantService.lambdaQuery().eq(LiveAuiRoomAssistant::getRoomInfoId, roomId).list();
        List<LiveAuiAssistantVo> assistantList = new ArrayList<>();
        if (CollUtil.isNotEmpty(roomAssistants)) {
            roomAssistants.forEach(roomAssistant -> {
                LiveAuiAssistantVo assistantVo = new LiveAuiAssistantVo();
                assistantVo.setAssistantId(String.valueOf(roomAssistant.getId()));
                //查询用户表
                SysUser user = iSysUserService.selectUserById(roomAssistant.getAssistantId());
                Optional.ofNullable(user).ifPresent(u -> assistantVo.setAssistantName(u.getUserName()));
                assistantList.add(assistantVo);
            });
        }
        return assistantList;
    }

    @Override
    public List<CourseCategoryDropdownVo> groupQuery(String categoryName) {
        List<LiveAuiCourseCategory> list = iLiveAuiCourseCategoryService.lambdaQuery()
                .like(StrUtil.isNotEmpty(categoryName), LiveAuiCourseCategory::getCategoryName, categoryName)
                .eq(LiveAuiCourseCategory::getStatus, 1)
                .list();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(item -> {
            CourseCategoryDropdownVo vo = new CourseCategoryDropdownVo();
            vo.setId(String.valueOf(item.getId()));
            vo.setCategoryName(item.getCategoryName());
            return vo;
        }).toList();
    }

    @Override
    public Boolean groupAdd(CourseCategoryAddDto courseCategoryAddDto) {
        LiveAuiCourseCategory courseCategory = new LiveAuiCourseCategory();
        courseCategory.setCategoryName(courseCategoryAddDto.getCategoryName());
        courseCategory.setSystemed(true);
        courseCategory.setStatus(1);
        courseCategory.setCategoryType("LIVE");
        return iLiveAuiCourseCategoryBizService.save(courseCategory);
    }

    @Override
    public Boolean groupUpdate(CourseCategoryUpdateDto courseCategoryUpdateDto) {
        LiveAuiCourseCategory courseCategory = new LiveAuiCourseCategory();
        courseCategory.setId(Long.valueOf(courseCategoryUpdateDto.getId()));
        courseCategory.setCategoryName(courseCategoryUpdateDto.getCategoryName());
        return iLiveAuiCourseCategoryBizService.update(courseCategory);
    }

    @Override
    public Boolean groupDelete(String categoryId) {
        if (StrUtil.isEmpty(categoryId)) {
            throw new ServiceException("参数错误");
        }
        return iLiveAuiCourseCategoryService.removeById(categoryId);
    }

    @Override
    public Boolean offShelf(String liveId) {
        if (StrUtil.isEmpty(liveId)) {
            throw new ServiceException("参数错误");
        }
        LiveAuiRoomInfoExt roomInfoExt = iLiveAuiRoomInfoExtService.lambdaQuery().eq(LiveAuiRoomInfoExt::getRoomInfoId, liveId).one();
        if (ObjectUtil.isNotEmpty(roomInfoExt)) {
            Integer status = roomInfoExt.getListedStatus() == EnumListedStatus.LISTED_STATUS_ONLINE.getVal() ? EnumListedStatus.LISTED_STATUS_OFFLINE.getVal() : EnumListedStatus.LISTED_STATUS_ONLINE.getVal();
            roomInfoExt.setListedStatus(status);
            roomInfoExt.setListedTime(new Date());
            return iLiveAuiRoomInfoExtService.updateById(roomInfoExt);
        }
        return false;
    }

}