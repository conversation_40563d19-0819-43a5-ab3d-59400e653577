package com.panda.pollen.aui.job.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.panda.pollen.aui.job.service.ILiveAuiLiveJobService;
import com.panda.pollen.aui.service.ILiveAuiLiveBizService;
import com.panda.pollen.aui.system.domain.LiveAuiLiveRetweet;
import com.panda.pollen.aui.system.service.ILiveAuiLiveRetweetService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RQueue;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 直播任务服务实现类，负责管理直播转发任务的延迟队列和执行。
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveAuiLiveJobServiceImpl implements ILiveAuiLiveJobService {
    // 注入直播转发服务
    @Resource
    private ILiveAuiLiveRetweetService iLiveAuiLiveRetweetService;

    @Resource
    private ILiveAuiLiveBizService iLiveAuiLiveBizService;

    // 注入 Redisson 客户端
    @Resource
    private RedissonClient redissonClient;

    // 延迟队列名称
    private static final String DELAYED_QUEUE_NAME = "liveAuiLiveRetweetsQueue";
    // 任务集合名称
    private static final String TASK_SET_NAME = "liveAuiLiveRetweetsTaskSet";
    // 线程池大小
    private static final int THREAD_POOL_SIZE = 5;
    // 延迟队列对象
    private RDelayedQueue<LiveAuiLiveRetweet> delayedQueue;
    // 定时任务执行器
    private ScheduledExecutorService executorService;
    
    /**
     * 初始化方法，在 Bean 创建后执行，用于初始化延迟队列和线程池。
     */
    @PostConstruct
    public void init() {
        // 获取普通队列
        RQueue<LiveAuiLiveRetweet> rQueue = redissonClient.getQueue(DELAYED_QUEUE_NAME);
        // 获取延迟队列
        delayedQueue = redissonClient.getDelayedQueue(rQueue);
        // 初始化定时任务线程池
        executorService = Executors.newScheduledThreadPool(THREAD_POOL_SIZE);
    }

    /**
     * 开始直播任务，获取未来的直播转发任务并添加到延迟队列，启动任务处理。
     */
    @Override
    public void startLive() {
        log.info("{}：开始进：{}", LocalDateTimeUtil.now(), "开始直播");
        // 获取未来的直播转发任务
        List<LiveAuiLiveRetweet> liveAuiLiveRetweets = this.getFutureLiveRetweets();
        // 获取任务集合
        RSet<String> taskSet = redissonClient.getSet(TASK_SET_NAME);
        // 将任务添加到延迟队列
        this.addTasksToDelayedQueue(liveAuiLiveRetweets, delayedQueue, taskSet);
        // 启动任务处理
        this.startProcessingTasks(delayedQueue, taskSet);
    }

    /**
     * 获取未来的直播转发任务。
     * @return 未来的直播转发任务列表
     */
    private List<LiveAuiLiveRetweet> getFutureLiveRetweets() {
        return iLiveAuiLiveRetweetService.lambdaQuery()
                // 查询开始时间大于当前时间的任务
                .gt(LiveAuiLiveRetweet::getStartTime, LocalDateTimeUtil.now())
                .list();
    }

    /**
     * 将直播转发任务添加到延迟队列。
     * @param liveAuiLiveRetweets 直播转发任务列表
     * @param delayedQueue 延迟队列
     * @param taskSet 任务集合
     */
    private void addTasksToDelayedQueue(List<LiveAuiLiveRetweet> liveAuiLiveRetweets, RDelayedQueue<LiveAuiLiveRetweet> delayedQueue, RSet<String> taskSet) {
        for (LiveAuiLiveRetweet liveAuiLiveRetweet : liveAuiLiveRetweets) {
            // 获取任务 ID
            String taskId = liveAuiLiveRetweet.getId().toString();
            // 解析任务开始时间
            LocalDateTime taskStartTime = LocalDateTime.parse(liveAuiLiveRetweet.getStartTime());
            
            // 如果任务已存在或时间已过，则跳过
            if (taskSet.contains(taskId) || taskStartTime.isBefore(LocalDateTime.now())) {
                log.info("任务 {} 已存在于队列中或时间已过，跳过添加", taskId);
                continue;
            }

            // 计算任务延迟时间
            long delay = LocalDateTimeUtil.between(LocalDateTime.now(), taskStartTime).toMillis();
            if (delay < 0) {
                log.info("任务 {} 延迟时间为负，跳过添加", taskId);
                continue;
            }
            
            // 将任务添加到延迟队列
            delayedQueue.offer(liveAuiLiveRetweet, delay, TimeUnit.MILLISECONDS);
            // 将任务 ID 添加到任务集合
            taskSet.add(taskId);
            
            log.info("任务 {} 已加入延迟队列，延迟时间: {} 毫秒", taskId, delay);
        }
    }

    /**
     * 开始处理延迟队列中的任务，定时从队列中取出任务执行。
     * @param delayedQueue 延迟队列
     * @param taskSet 任务集合
     */
    private void startProcessingTasks(RDelayedQueue<LiveAuiLiveRetweet> delayedQueue, RSet<String> taskSet) {
        // 每秒执行一次任务处理
        executorService.scheduleAtFixedRate(() -> {
            try {
                // 从延迟队列中取出任务
                LiveAuiLiveRetweet task = delayedQueue.poll();
                if (task != null) {
                    // 获取任务 ID
                    String taskId = task.getId().toString();
                    try {
                        // 执行直播任务
                        executeLiveTask(task);
                        // 从任务集合中移除任务 ID
                        taskSet.remove(taskId);
                        log.info("任务 {} 执行成功，已从队列中移除", taskId);
                    } catch (Exception e) {
                        log.error("任务 {} 执行失败: {}, 重新加入队列", taskId, e.getMessage(), e);
                        // 5 秒后重试
                        long retryDelay = 5000;
                        delayedQueue.offer(task, retryDelay, TimeUnit.MILLISECONDS);
                    }
                }
            } catch (Exception e) {
                log.error("处理任务时发生错误: ", e);
            }
        }, 0, 1, TimeUnit.SECONDS);
    }

    /**
     * 执行具体的直播任务逻辑，需根据实际需求实现。
     * @param task 直播任务
     */
    private void executeLiveTask(LiveAuiLiveRetweet task) {
        // 实现具体的直播任务逻辑
        log.info("云转推任务已经在执行了: {}", task);
    }
    
    /**
     * 销毁方法，在 Bean 销毁前执行，用于关闭线程池和延迟队列。
     */
    @PreDestroy
    public void shutdown() {
        // 关闭线程池
        executorService.shutdown();
        try {
            // 等待线程池关闭，最多等待 5 秒
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                // 强制关闭线程池
                executorService.shutdownNow();
            }
            // 关闭延迟队列
            delayedQueue.destroy();
        } catch (InterruptedException e) {
            // 强制关闭线程池
            executorService.shutdownNow();
        }
    }
}