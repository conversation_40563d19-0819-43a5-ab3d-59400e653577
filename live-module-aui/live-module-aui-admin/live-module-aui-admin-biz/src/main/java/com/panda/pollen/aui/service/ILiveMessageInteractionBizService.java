package com.panda.pollen.aui.service;

import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;

/**
 * <AUTHOR> yh
 * @Date : 2025/6/24 21:08
 * @Description :
 */

public interface ILiveMessageInteractionBizService {

    /**
     * 发送消息给指定用户
     * @param aliSendLiveMessageUserRequest 请求参数
     * @return 响应
     */

    String sendMessage2One(AliSendLiveMessageUserRequest aliSendLiveMessageUserRequest);

    /**
     * 发送消息到群组
     * @param aliSendLiveMessageUserRequest 请求参数
     * @return 响应
     */

    String sendMessage2All(AliSendLiveMessageUserRequest aliSendLiveMessageUserRequest);


    /**
     * 删除（撤回）某条群组消息
     * @param aliSendLiveMessageUserRequest 删除消息的请求参数
     * @return 响应
     */

    boolean deleteLiveMessageGroupMessage(AliSendLiveMessageUserRequest aliSendLiveMessageUserRequest);


    /**
     * 删除（撤回）某条单聊消息
     * @param aliSendLiveMessageUserRequest 删除消息的请求参数
     * @return 响应
     */

    boolean deleteLiveMessageUserMessage(AliSendLiveMessageUserRequest aliSendLiveMessageUserRequest);


}
