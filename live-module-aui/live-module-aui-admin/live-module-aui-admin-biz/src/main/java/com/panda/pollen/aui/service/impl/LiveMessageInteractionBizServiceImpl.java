package com.panda.pollen.aui.service.impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.live.model.v20161101.*;
import com.aliyuncs.profile.DefaultProfile;
import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.service.ILiveMessageInteractionBizService;
import com.panda.pollen.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR> yh
 * @Date : 2025/6/27 21:46
 * @Description :
 */
@Slf4j
@Service
public class LiveMessageInteractionBizServiceImpl implements ILiveMessageInteractionBizService {

    @Value("${biz.openapi.access.key}")
    private String accessKeyId;

    @Value("${biz.openapi.access.secret}")
    private String accessKeySecret;

    private IAcsClient client;

    @PostConstruct
    public void init() {

        DefaultProfile profile = DefaultProfile.getProfile("cn-shanghai", accessKeyId, accessKeySecret);
        client = new DefaultAcsClient(profile);
    }

    @Override
    public String sendMessage2One(AliSendLiveMessageUserRequest aliSendLiveMessageUserRequest) {
        String msgTid;
        SendLiveMessageUserRequest request = new SendLiveMessageUserRequest();

        try {
            SendLiveMessageUserResponse response = client.getAcsResponse(request);

            //消息唯一标识，可作为删除消息的依据。由大小写字母、数字组成，长度不超过 64 字节
            msgTid = response.getMsgTid();
        } catch (ClientException e) {
            log.error("发送消息给指定用户失败！", e);
            throw new ServiceException("发送消息给指定用户失败！");
        }
        return msgTid;
    }

    @Override
    public String sendMessage2All(AliSendLiveMessageUserRequest aliSendLiveMessageUserRequest) {
        String msgTid;

        SendLiveMessageGroupRequest request = new SendLiveMessageGroupRequest();
        request.setAppId(aliSendLiveMessageUserRequest.getAppid());
        request.setGroupId(aliSendLiveMessageUserRequest.getGroupId());
        request.setSenderId(aliSendLiveMessageUserRequest.getSenderId());
        request.setSenderMetaInfo(aliSendLiveMessageUserRequest.getSenderInfo());
        request.setBody(aliSendLiveMessageUserRequest.getBody());
        request.setMsgType(aliSendLiveMessageUserRequest.getMsgType());
        request.setMsgTid(aliSendLiveMessageUserRequest.getMsgTid());
        request.setStaticsIncrease(aliSendLiveMessageUserRequest.getStaticsIncrease());
        request.setWeight(aliSendLiveMessageUserRequest.getWeight());
        request.setNoStorage(!aliSendLiveMessageUserRequest.isStorage());
        request.setNoCache(aliSendLiveMessageUserRequest.isNoCache());
        request.setDataCenter(aliSendLiveMessageUserRequest.getDataCenter());

        try {
            SendLiveMessageGroupResponse response = client.getAcsResponse(request);
            //消息唯一标识，可作为删除消息的依据。由大小写字母、数字组成，长度不超过 64 字节
            msgTid = response.getMsgTid();

        } catch (ClientException e) {
            log.error("发送消息给群组失败！", e);
            throw new ServiceException("发送消息给群组失败！");
        }
        return msgTid;
    }

    @Override
    public boolean deleteLiveMessageGroupMessage(AliSendLiveMessageUserRequest aliSendLiveMessageUserRequest) {

        DeleteLiveMessageGroupMessageRequest request = new DeleteLiveMessageGroupMessageRequest();
        request.setAppId(aliSendLiveMessageUserRequest.getAppid());
        request.setGroupId(aliSendLiveMessageUserRequest.getGroupId());
        request.setMessageId(aliSendLiveMessageUserRequest.getMsgTid());
        request.setDataCenter(aliSendLiveMessageUserRequest.getDataCenter());

        try {
            DeleteLiveMessageGroupMessageResponse response = client.getAcsResponse(request);
        } catch (ClientException e) {
            log.error("删除（撤回）某条群组消息失败！", e);
            throw new ServiceException("删除（撤回）某条群组消息失败！");
        }
        return true;
    }

    @Override
    public boolean deleteLiveMessageUserMessage(AliSendLiveMessageUserRequest aliSendLiveMessageUserRequest) {
        DeleteLiveMessageUserMessageRequest request = new DeleteLiveMessageUserMessageRequest();
        try {
            DeleteLiveMessageUserMessageResponse response = client.getAcsResponse(request);
        } catch (ServerException e) {
            log.error("删除（撤回）某条群组消息失败！", e);
            throw new ServiceException("删除（撤回）某条群组消息失败！");
        } catch (ClientException e) {
            log.error("删除（撤回）某条群组消息失败！", e);
            throw new ServiceException(e.getMessage());
        }

        return true;
    }
}
