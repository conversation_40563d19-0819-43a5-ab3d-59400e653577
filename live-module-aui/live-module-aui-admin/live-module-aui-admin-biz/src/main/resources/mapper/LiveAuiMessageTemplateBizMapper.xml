<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.aui.mapper.LiveAuiMessageTemplateBizMapper">

    <resultMap id="voMap" type="com.panda.pollen.aui.model.vo.LiveAuiMessageTemplateVo">
        <id column="id" property="id" />
        <result column="dept_id" property="deptId" />
        <result column="user_id" property="userId" />
        <result column="template_name" property="templateName" />
        <result column="app_video_id" property="appVideoId" />
        <result column="app_video_name" property="appVideoName"/>
        <result column="pc_video_id" property="pcVideoId" />
        <result column="pc_video_name" property="pcVideoName" />
        <result column="status" property="status" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="pc_ali_video_id" property="pcAliVideoId" />
        <result column="app_ali_video_id" property="appAliVideoId" />
        <result column="app_video_duration" property="appVideoDuration" />
    </resultMap>

    <select id="getByName" resultType="com.panda.pollen.aui.system.domain.LiveAuiMessageTemplate">
        SELECT
            t.*
        FROM
            live_aui_message_template t
                LEFT JOIN sys_user u ON u.user_name = t.create_by AND u.del_flag = '0'
                LEFT JOIN sys_dept d ON d.dept_id = u.dept_id AND d.del_flag = '0'
        WHERE
            t.deleted = 0
          AND t.template_name = #{templateName}
          ${params.dataScope}
        limit 1
    </select>

    <select id="list" resultMap="voMap">
        SELECT
            t.*,
            t2.title app_video_name,
            t2.video_id app_ali_video_id,
            t2.duration app_video_duration,
            t3.title pc_video_name,
            t3.video_id pc_ali_video_id
        FROM
            live_aui_message_template t
        LEFT JOIN sys_user u ON u.user_name = t.create_by AND u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id AND d.del_flag = '0'
        LEFT JOIN live_aui_video_file t2 ON t.app_video_id = t2.id
        LEFT JOIN live_aui_video_file t3 ON t.pc_video_id = t3.id
        <where>
            t.`deleted` = #{deleted}
            <if test="null != templateName and '' != templateName">
                and template_name like concat('%',#{templateName},'%')
            </if>
            <if test="null != status">
                and t.`status` = #{status}
            </if>
            ${params.dataScope}
        </where>
            order by create_time desc
    </select>

    <select id="getById" resultMap="voMap" >
        SELECT
            t1.*,
            t2.title app_video_name,
            t2.video_id app_ali_video_id,
            t2.duration app_video_duration,
            t3.title pc_video_name,
            t3.video_id pc_ali_video_id,
            t3.duration pc_video_duration
        FROM
            live_aui_message_template t1
                LEFT JOIN live_aui_video_file t2 ON t1.app_video_id = t2.id
                LEFT JOIN live_aui_video_file t3 ON t1.pc_video_id = t3.id
        where
            t1.id = #{id}
        limit 1
    </select>

    <update id="closeById">
        update live_aui_message_template set status = #{openStatus} where id = #{id}
    </update>
    <update id="updateScriptId">
        update live_aui_message_template set message_script_id = #{scriptId} where id = #{templateId}
    </update>


</mapper>