package com.panda.pollen.aui.controller;

import com.panda.pollen.aui.model.req.AliModifyLiveMessageGroupBandRequest;
import com.panda.pollen.aui.service.ILiveAuiMessageControlBizService;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> yh
 * @Date : 2025/6/24 20:41
 * @Description : 群组消息控制器
 * 消息禁言 取消禁言 发送消息至群组/个人  删除消息
 */

@Api(tags = "直播场控")
@RestController
@RequestMapping("/liveAuiMessageControlBiz")
public class LiveAuiMessageControlBizController {

    @Resource
    private ILiveAuiMessageControlBizService liveAuiMessageControlService;


    @ApiOperation("单用户禁言")
    @PostMapping(value = "/bandOne")
    public AjaxResultV2<Boolean> bandOne(@RequestBody AliModifyLiveMessageGroupBandRequest aliModifyLiveMessageGroupBandRequest){

        return AjaxResultV2.success(liveAuiMessageControlService.bandOne(aliModifyLiveMessageGroupBandRequest));
    }

    @ApiOperation("多用户禁言")
    @PostMapping(value = "/bandMuti")
    public AjaxResultV2<Boolean> bandMuti(@RequestBody AliModifyLiveMessageGroupBandRequest aliModifyLiveMessageGroupBandRequest){
        return AjaxResultV2.success(liveAuiMessageControlService.bandMutiUsers(aliModifyLiveMessageGroupBandRequest));
    }

    @ApiOperation("全员禁言")
    @PostMapping(value = "/bandAll")
    public AjaxResultV2<Boolean> bandAll(@RequestBody AliModifyLiveMessageGroupBandRequest aliModifyLiveMessageGroupBandRequest){
        return AjaxResultV2.success(liveAuiMessageControlService.bandAllUsers(aliModifyLiveMessageGroupBandRequest));
    }

    @ApiOperation("取消全员禁言")
    @PostMapping(value = "/unbanLiveMessageGroup")
    public AjaxResultV2<Boolean> unbanLiveMessageGroup(@RequestBody AliModifyLiveMessageGroupBandRequest aliModifyLiveMessageGroupBandRequest){
        return AjaxResultV2.success(liveAuiMessageControlService.unbanLiveMessageGroup(aliModifyLiveMessageGroupBandRequest));
    }

    @ApiOperation("新增禁言用户")
    @PostMapping(value = "/addLiveMessageGroupBand")
    public AjaxResultV2<Boolean> addLiveMessageGroupBand(@RequestBody AliModifyLiveMessageGroupBandRequest aliModifyLiveMessageGroupBandRequest){
        return AjaxResultV2.success(liveAuiMessageControlService.addLiveMessageGroupBand(aliModifyLiveMessageGroupBandRequest));
    }

    @ApiOperation("解除禁言用户")
    @PostMapping(value = "/removeLiveMessageGroupBand")
    public AjaxResultV2<Boolean> removeLiveMessageGroupBand(@RequestBody AliModifyLiveMessageGroupBandRequest aliModifyLiveMessageGroupBandRequest){
        return AjaxResultV2.success(liveAuiMessageControlService.removeLiveMessageGroupBand(aliModifyLiveMessageGroupBandRequest));
    }

    @ApiOperation("将指定用户从群组中踢出去")
    @PostMapping(value = "/kickLiveMessageGroupUser")
    public AjaxResultV2<Boolean> kickLiveMessageGroupUser(@RequestBody AliModifyLiveMessageGroupBandRequest aliModifyLiveMessageGroupBandRequest){
        return AjaxResultV2.success(liveAuiMessageControlService.kickLiveMessageGroupUser(aliModifyLiveMessageGroupBandRequest));
    }


}
