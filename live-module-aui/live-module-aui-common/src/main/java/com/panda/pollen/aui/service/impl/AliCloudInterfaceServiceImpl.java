package com.panda.pollen.aui.service.impl;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.vod.upload.impl.UploadM3u8FileImpl;
import com.aliyun.vod.upload.impl.UploadVideoImpl;
import com.aliyun.vod.upload.req.UploadFileStreamRequest;
import com.aliyun.vod.upload.req.UploadLocalM3u8Request;
import com.aliyun.vod.upload.req.UploadStreamRequest;
import com.aliyun.vod.upload.req.UploadURLStreamRequest;
import com.aliyun.vod.upload.req.UploadVideoRequest;
import com.aliyun.vod.upload.req.UploadWebM3u8Request;
import com.aliyun.vod.upload.resp.UploadFileStreamResponse;
import com.aliyun.vod.upload.resp.UploadLocalM3u8Response;
import com.aliyun.vod.upload.resp.UploadStreamResponse;
import com.aliyun.vod.upload.resp.UploadURLStreamResponse;
import com.aliyun.vod.upload.resp.UploadVideoResponse;
import com.aliyun.vod.upload.resp.UploadWebM3u8Response;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.live.model.v20161101.AddLiveMessageGroupBandRequest;
import com.aliyuncs.live.model.v20161101.AddLiveMessageGroupBandResponse;
import com.aliyuncs.live.model.v20161101.CheckLiveMessageUsersOnlineRequest;
import com.aliyuncs.live.model.v20161101.CheckLiveMessageUsersOnlineResponse;
import com.aliyuncs.live.model.v20161101.CreateLiveMessageAppRequest;
import com.aliyuncs.live.model.v20161101.CreateLiveMessageAppResponse;
import com.aliyuncs.live.model.v20161101.CreateLiveMessageGroupRequest;
import com.aliyuncs.live.model.v20161101.CreateLiveMessageGroupResponse;
import com.aliyuncs.live.model.v20161101.CreateLivePullToPushRequest;
import com.aliyuncs.live.model.v20161101.CreateLivePullToPushResponse;
import com.aliyuncs.live.model.v20161101.CreateMessageGroupRequest;
import com.aliyuncs.live.model.v20161101.CreateMessageGroupResponse;
import com.aliyuncs.live.model.v20161101.DeleteLiveMessageGroupMessageRequest;
import com.aliyuncs.live.model.v20161101.DeleteLiveMessageGroupMessageResponse;
import com.aliyuncs.live.model.v20161101.DeleteLiveMessageUserMessageRequest;
import com.aliyuncs.live.model.v20161101.DeleteLiveMessageUserMessageResponse;
import com.aliyuncs.live.model.v20161101.DeleteLivePullToPushRequest;
import com.aliyuncs.live.model.v20161101.DeleteLivePullToPushResponse;
import com.aliyuncs.live.model.v20161101.DescribeLiveMessageGroupRequest;
import com.aliyuncs.live.model.v20161101.DescribeLiveMessageGroupResponse;
import com.aliyuncs.live.model.v20161101.GetMessageTokenRequest;
import com.aliyuncs.live.model.v20161101.GetMessageTokenResponse;
import com.aliyuncs.live.model.v20161101.KickLiveMessageGroupUserRequest;
import com.aliyuncs.live.model.v20161101.KickLiveMessageGroupUserResponse;
import com.aliyuncs.live.model.v20161101.ListLiveMessageGroupMessagesRequest;
import com.aliyuncs.live.model.v20161101.ListLiveMessageGroupMessagesResponse;
import com.aliyuncs.live.model.v20161101.ListMessageGroupUserByIdRequest;
import com.aliyuncs.live.model.v20161101.ListMessageGroupUserByIdResponse;
import com.aliyuncs.live.model.v20161101.ModifyLiveMessageAppCallbackRequest;
import com.aliyuncs.live.model.v20161101.ModifyLiveMessageAppCallbackResponse;
import com.aliyuncs.live.model.v20161101.ModifyLiveMessageGroupBandRequest;
import com.aliyuncs.live.model.v20161101.ModifyLiveMessageGroupBandResponse;
import com.aliyuncs.live.model.v20161101.RemoveLiveMessageGroupBandRequest;
import com.aliyuncs.live.model.v20161101.RemoveLiveMessageGroupBandResponse;
import com.aliyuncs.live.model.v20161101.SendLiveMessageGroupRequest;
import com.aliyuncs.live.model.v20161101.SendLiveMessageGroupResponse;
import com.aliyuncs.live.model.v20161101.SendLiveMessageUserRequest;
import com.aliyuncs.live.model.v20161101.SendLiveMessageUserResponse;
import com.aliyuncs.live.model.v20161101.UnbanLiveMessageGroupRequest;
import com.aliyuncs.live.model.v20161101.UnbanLiveMessageGroupResponse;
import com.aliyuncs.live.model.v20161101.UpdateLivePullToPushRequest;
import com.aliyuncs.live.model.v20161101.UpdateLivePullToPushResponse;
import com.aliyuncs.vod.model.v20170321.AddCategoryRequest;
import com.aliyuncs.vod.model.v20170321.AddCategoryResponse;
import com.aliyuncs.vod.model.v20170321.AddTranscodeTemplateGroupRequest;
import com.aliyuncs.vod.model.v20170321.AddTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.CreateAppInfoRequest;
import com.aliyuncs.vod.model.v20170321.CreateAppInfoResponse;
import com.aliyuncs.vod.model.v20170321.CreateUploadImageRequest;
import com.aliyuncs.vod.model.v20170321.CreateUploadImageResponse;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoRequest;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.DeleteCategoryRequest;
import com.aliyuncs.vod.model.v20170321.DeleteCategoryResponse;
import com.aliyuncs.vod.model.v20170321.DeleteImageRequest;
import com.aliyuncs.vod.model.v20170321.DeleteImageResponse;
import com.aliyuncs.vod.model.v20170321.DeleteTranscodeTemplateGroupRequest;
import com.aliyuncs.vod.model.v20170321.DeleteTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.DeleteVideoRequest;
import com.aliyuncs.vod.model.v20170321.DeleteVideoResponse;
import com.aliyuncs.vod.model.v20170321.GetCategoriesRequest;
import com.aliyuncs.vod.model.v20170321.GetCategoriesResponse;
import com.aliyuncs.vod.model.v20170321.GetImageInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetImageInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetImageInfosRequest;
import com.aliyuncs.vod.model.v20170321.GetImageInfosResponse;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetTranscodeTemplateGroupRequest;
import com.aliyuncs.vod.model.v20170321.GetTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.GetUploadDetailsRequest;
import com.aliyuncs.vod.model.v20170321.GetUploadDetailsResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetVideoInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoInfosRequest;
import com.aliyuncs.vod.model.v20170321.GetVideoInfosResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoListRequest;
import com.aliyuncs.vod.model.v20170321.GetVideoListResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoPlayAuthRequest;
import com.aliyuncs.vod.model.v20170321.GetVideoPlayAuthResponse;
import com.aliyuncs.vod.model.v20170321.ListAppInfoRequest;
import com.aliyuncs.vod.model.v20170321.ListAppInfoResponse;
import com.aliyuncs.vod.model.v20170321.ListTranscodeTemplateGroupRequest;
import com.aliyuncs.vod.model.v20170321.ListTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.RefreshUploadVideoRequest;
import com.aliyuncs.vod.model.v20170321.RefreshUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.SearchMediaRequest;
import com.aliyuncs.vod.model.v20170321.SearchMediaResponse;
import com.aliyuncs.vod.model.v20170321.SetDefaultTranscodeTemplateGroupRequest;
import com.aliyuncs.vod.model.v20170321.SetDefaultTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.SubmitTranscodeJobsRequest;
import com.aliyuncs.vod.model.v20170321.SubmitTranscodeJobsResponse;
import com.aliyuncs.vod.model.v20170321.UpdateCategoryRequest;
import com.aliyuncs.vod.model.v20170321.UpdateCategoryResponse;
import com.aliyuncs.vod.model.v20170321.UpdateImageInfosRequest;
import com.aliyuncs.vod.model.v20170321.UpdateImageInfosResponse;
import com.aliyuncs.vod.model.v20170321.UpdateTranscodeTemplateGroupRequest;
import com.aliyuncs.vod.model.v20170321.UpdateTranscodeTemplateGroupResponse;
import com.panda.pollen.aui.mapstruct.AliCloudReqConverter;
import com.panda.pollen.aui.model.AliResponse;
import com.panda.pollen.aui.model.LinkInfo;
import com.panda.pollen.aui.model.PullLiveInfo;
import com.panda.pollen.aui.model.PushLiveInfo;
import com.panda.pollen.aui.model.cons.ConsLiveAui;
import com.panda.pollen.aui.model.dto.NewImTokenResponseDto;
import com.panda.pollen.aui.model.dto.RoomInfoDto;
import com.panda.pollen.aui.model.enums.EnumCategoryType;
import com.panda.pollen.aui.model.enums.EnumDeleteImageType;
import com.panda.pollen.aui.model.enums.EnumImageTypeDelete;
import com.panda.pollen.aui.model.enums.EnumSortBy;
import com.panda.pollen.aui.model.enums.MediaStatus;
import com.panda.pollen.aui.model.req.AliAddCategoryRequest;
import com.panda.pollen.aui.model.req.AliAddTranscodeTemplateGroupRequest;
import com.panda.pollen.aui.model.req.AliCreateAppInfoRequest;
import com.panda.pollen.aui.model.req.AliCreateLiveMessageAppRequest;
import com.panda.pollen.aui.model.req.AliCreateLivePullToPushRequest;
import com.panda.pollen.aui.model.req.AliCreateUploadImageRequest;
import com.panda.pollen.aui.model.req.AliCreateUploadVideoRequest;
import com.panda.pollen.aui.model.req.AliDeleteImageRequest;
import com.panda.pollen.aui.model.req.AliDeleteLivePullToPushRequest;
import com.panda.pollen.aui.model.req.AliDeleteTranscodeTemplateGroupRequest;
import com.panda.pollen.aui.model.req.AliDeleteVideoRequest;
import com.panda.pollen.aui.model.req.AliGetCategoriesRequest;
import com.panda.pollen.aui.model.req.AliGetImageInfoRequest;
import com.panda.pollen.aui.model.req.AliGetImageInfosRequest;
import com.panda.pollen.aui.model.req.AliGetMessageTokenRequest;
import com.panda.pollen.aui.model.req.AliGetPlayInfoRequest;
import com.panda.pollen.aui.model.req.AliGetUploadDetailsRequest;
import com.panda.pollen.aui.model.req.AliGetVideoInfoRequest;
import com.panda.pollen.aui.model.req.AliGetVideoListRequest;
import com.panda.pollen.aui.model.req.AliGetVideoPlayAuthRequest;
import com.panda.pollen.aui.model.req.AliListAppInfoRequest;
import com.panda.pollen.aui.model.req.AliRefreshUploadVideoRequest;
import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.model.req.AliSetDefaultTranscodeTemplateGroupRequest;
import com.panda.pollen.aui.model.req.AliSubmitTranscodeJobsRequest;
import com.panda.pollen.aui.model.req.AliUpdateCategoryRequest;
import com.panda.pollen.aui.model.req.AliUpdateImageInfosRequest;
import com.panda.pollen.aui.model.req.AliUpdateLivePullToPushRequest;
import com.panda.pollen.aui.model.req.AliUpdateTranscodeTemplateGroupRequest;
import com.panda.pollen.aui.model.req.DeleteMessageRequest;
import com.panda.pollen.aui.model.req.LiveRoomRequest;
import com.panda.pollen.aui.service.AliCloudBizInterfaceService;
import com.panda.pollen.aui.service.AliCloudClientFacotry;
import com.panda.pollen.aui.service.AliCloudConfig;
import com.panda.pollen.aui.system.domain.LiveAuiMessageApp;
import com.panda.pollen.aui.util.LocalDateTimeUtils;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.exception.base.BaseException;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p> 阿里云对接实现 <p/>
 * ClassName com.panda.pollen.aui.service.impl.AliCloudInterfaceServiceImpl 
 * <AUTHOR> 
 * @date 2025年8月14日 下午11:54:07 
 * @version v1.0
 */
@Slf4j
@Service
public class AliCloudInterfaceServiceImpl implements AliCloudBizInterfaceService {

    private static final String LIVE_DOMAIN = "live.aliyun.com";

    private static final String LIVE_OPEN_API_DOMAIN = "live.aliyuncs.com";

    private static final Integer PAGE_SIZE = 50; 
    
    private static final AliCloudConfig config = AliCloudClientFacotry.getConfig();

    @Override
    public GetMessageTokenResponse getImToken(AliGetMessageTokenRequest req) {
        long start = System.currentTimeMillis();
        GetMessageTokenRequest request = new GetMessageTokenRequest();
        request.setAppId(config.getImAppId());
        request.setDeviceId(req.getDeviceId());
        request.setDeviceType(req.getDeviceType());
        request.setUserId(req.getUserId());
        log.info("getImToken, request:{}", JSON.toJSONString(req));
        GetMessageTokenResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
            log.info("getImToken, response:{}, consume:{}", JSON.toJSONString(response), (System.currentTimeMillis() - start));
        } catch (ServerException e) {
            log.error("getImToken ServerException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (ClientException e) {
            log.error("getImToken ClientException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (Exception e) {
            log.error("getImToken Exception. error:{}", e.getMessage());
        }
        return response;
    }

    @Override
    public NewImTokenResponseDto getNewImToken(AliGetMessageTokenRequest req) {
        String role = req.getRole();
        if (role == null) {
            role = "";
        }
        String nonce = UUID.randomUUID().toString();
        long timestamp = DateUtil.offsetDay(new Date(), 2).getTime() / 1000;
        String signContent = String.format("%s%s%s%s%s%s", config.getAppId(), config.getAppKey(), req.getUserId(), nonce, timestamp, role);
        String appToken = org.apache.commons.codec.digest.DigestUtils.sha256Hex(signContent);

        NewImTokenResponseDto newImTokenResponseDto = NewImTokenResponseDto.builder().appId(config.getAppId()).appSign(config.getAppSign()).appToken(appToken).auth(NewImTokenResponseDto.Auth.builder().userId(req.getUserId()).nonce(nonce).timestamp(timestamp).role(role).build()).build();

        log.info("getNewImToken. userId:{}, newImTokenResponseDto:{}", req.getUserId(), JSON.toJSONString(newImTokenResponseDto));
        return newImTokenResponseDto;
    }

    @Override
    public String createMessageGroup(String anchor) {
        long start = System.currentTimeMillis();
        CreateMessageGroupRequest request = new CreateMessageGroupRequest();
        request.setAppId(config.getImAppId());
        request.setCreatorId(anchor);
        log.info("createMessageGroup, request:{}", JSON.toJSONString(request));

        try {
            CreateMessageGroupResponse createMessageGroupResponse = AliCloudClientFacotry.getClient().getAcsResponse(request);
            log.info("createMessageGroup, response:{}, consume:{}", JSON.toJSONString(createMessageGroupResponse), (System.currentTimeMillis() - start));
            return createMessageGroupResponse.getResult().getGroupId();
        } catch (ServerException e) {
            log.error("createMessageGroup ServerException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (ClientException e) {
            log.error("createMessageGroup ClientException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (Exception e) {
            log.error("createMessageGroup Exception. error:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public CreateLiveMessageGroupResponse createNewImMessageGroup(CreateLiveMessageGroupRequest req) {
        long start = System.currentTimeMillis();
        req.setAppId(config.getAppId());
        log.info("createNewImMessageGroup, request:{}", JSON.toJSONString(req));
        try {
            CreateLiveMessageGroupResponse acsResponse = AliCloudClientFacotry.getClient().getAcsResponse(req);
            log.info("createNewImMessageGroup, response:{}, consume:{}", JSON.toJSONString(acsResponse), (System.currentTimeMillis() - start));
            return acsResponse;
        } catch (ServerException e) {
            log.error("createNewImMessageGroup ServerException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (ClientException e) {
            log.error("createNewImMessageGroup ClientException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (Exception e) {
            log.error("createNewImMessageGroup Exception. error:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public PushLiveInfo getPushLiveInfo(String streamName) {
        String protocolHost = "{}://{}";
        String pushAuthKey = this.getAuth(streamName, config.getLiveStreamPushAuthKey());
        String url = CharSequenceUtil.format("{}/{}/{}?auth_key={}", config.getLiveStreamPushUrl(), config.getLiveStreamAppName(), streamName, pushAuthKey);
        String rtmp = CharSequenceUtil.format(protocolHost, ConsLiveAui.RTMP, url);
        String artc = CharSequenceUtil.format(protocolHost, ConsLiveAui.ARTC, url);
        String srt = CharSequenceUtil.format(protocolHost, ConsLiveAui.SRT, url);
        PushLiveInfo pushLiveInfo = PushLiveInfo.builder().rtmpUrl(rtmp).rtsUrl(artc).srtUrl(srt).build();
        log.info("getPushLiveInfo. streamName:{}, pushLiveInfo:{}", streamName, JSON.toJSONString(pushLiveInfo));
        return pushLiveInfo;
    }

    @Override
    public PullLiveInfo getPullLiveInfo(String streamName) {

        return getPullLiveInfo(config.getLiveStreamAppName(), streamName);
    }

    @Override
    public LinkInfo getRtcInfo(String channelId, String userId, String anchorId) {

        // 24小时有效
        long timestamp = DateUtil.offsetDay(new Date(), 1).getTime() / 1000;
        String token = getRtcAuth(channelId, userId, timestamp);

        String rtcPushUrl = String.format("artc://%s/push/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s", LIVE_DOMAIN, channelId, config.getLiveMicAppId(), userId, timestamp, token);

        String rtcPullUrl = String.format("artc://%s/play/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s", LIVE_DOMAIN, channelId, config.getLiveMicAppId(), userId, timestamp, token);

        String streamName = String.format("%s_%s_%s_camera", config.getLiveMicAppId(), channelId, anchorId);

        PullLiveInfo rtcLinkCdnUrl = getPullLiveInfo("live", streamName);

        LinkInfo linkInfo = LinkInfo.builder().rtcPushUrl(rtcPushUrl).rtcPullUrl(rtcPullUrl).cdnPullInfo(rtcLinkCdnUrl).build();

        log.info("getRtcInfo. channelId:{}, userId:{}, linkInfo:{}", channelId, userId, JSON.toJSONString(linkInfo));

        return linkInfo;
    }

    @Override
    public String searchMediaByTitle(String title) {

        long start = System.currentTimeMillis();
        SearchMediaRequest request = new SearchMediaRequest();
        request.setMatch(String.format("Title='%s'", title));
        request.setAcceptFormat(FormatType.JSON);

        try {
            SearchMediaResponse acsResponse = AliCloudClientFacotry.getClient().getAcsResponse(request);
            log.info("searchMediaByTitle, title:{}, response:{}, consume:{}", title, JSON.toJSONString(acsResponse), (System.currentTimeMillis() - start));
            if (CollectionUtils.isNotEmpty(acsResponse.getMediaList())) {
                return acsResponse.getMediaList().get(0).getMediaId();
            }
        } catch (ServerException e) {
            log.error("searchMediaByTitle ServerException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (ClientException e) {
            log.error("searchMediaByTitle ClientException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (Exception e) {
            log.error("searchMediaByTitle Exception. error:{}", e.getMessage());
        }

        return null;
    }

    @Override
    public RoomInfoDto.VodInfo getPlayInfo(String mediaId) {

        long start = System.currentTimeMillis();
        GetPlayInfoRequest request = new GetPlayInfoRequest();
        request.setVideoId(mediaId);
        try {
            GetPlayInfoResponse acsResponse = AliCloudClientFacotry.getClient().getAcsResponse(request);
            List<RoomInfoDto.PlayInfo> playInfos = new ArrayList<>();
            RoomInfoDto.PlayInfo playInfoTmp;
            for (GetPlayInfoResponse.PlayInfo playInfo : acsResponse.getPlayInfoList()) {
                playInfoTmp = new RoomInfoDto.PlayInfo();
                BeanUtils.copyProperties(playInfo, playInfoTmp);
                playInfoTmp.setPlayUrl(playInfo.getPlayURL());
                playInfoTmp.setBitRate(playInfo.getBitrate());
                playInfos.add(playInfoTmp);
            }
            log.info("getPlayInfo, mediaId:{}, response:{}, consume:{}", mediaId, JSON.toJSONString(acsResponse), (System.currentTimeMillis() - start));
            return RoomInfoDto.VodInfo.builder().playInfos(playInfos).status(MediaStatus.VodStatusOK.getVal()).build();
        } catch (ServerException e) {
            log.error("getPlayInfo ServerException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (ClientException e) {
            log.error("getPlayInfo ClientException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (Exception e) {
            log.error("getPlayInfo Exception. error:{}", e.getMessage());
        }

        return null;
    }

    @Override
    public RoomInfoDto.Metrics getNewImGroupDetails(String groupId) {

        long start = System.currentTimeMillis();

        DescribeLiveMessageGroupRequest request = new DescribeLiveMessageGroupRequest();

        request.setAppId(config.getAppId());
        request.setGroupId(groupId);

        try {
            DescribeLiveMessageGroupResponse response = AliCloudClientFacotry.getClient().getAcsResponse(request);
            log.info("getNewImGroupDetails, response:{}, consume:{}", JSON.toJSONString(response), (System.currentTimeMillis() - start));

            if (response.getDelete() != null && response.getDelete()) {
                // 表示群已删除
                log.warn("getNewImGroupDetails, groupId:{} is deleted", groupId);
                return null;
            }
            return RoomInfoDto.Metrics.builder().pv(response.getTotalTimes()).onlineCount(response.getOnlineUserCounts()).build();
        } catch (ServerException e) {
            log.error("getNewImGroupDetails ServerException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (ClientException e) {
            log.error("getNewImGroupDetails ClientException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (Exception e) {
            log.error("getNewImGroupDetails Exception. error:{}", e.getMessage());
        }

        return null;
    }

    @Override
    public RoomInfoDto.Metrics getGroupDetails(String groupId) {

        long start = System.currentTimeMillis();

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);

        request.setSysVersion("2016-11-01");
        request.setSysAction("GetGroupStatistics");
        request.setSysDomain(LIVE_OPEN_API_DOMAIN);
        request.putQueryParameter("AppId", config.getImAppId());
        request.putQueryParameter("GroupId", groupId);

        try {
            CommonResponse response = AliCloudClientFacotry.getClient().getCommonResponse(request);
            log.info("getGroupDetails, response:{}, consume:{}", response.getData(), (System.currentTimeMillis() - start));
            JSONObject jsonObject = JSONObject.parseObject(response.getData());
            if (jsonObject.containsKey("Result")) {
                return JSON.parseObject(jsonObject.getJSONObject("Result").toJSONString(), RoomInfoDto.Metrics.class);
            }
        } catch (ServerException e) {
            log.error("getGroupDetails ServerException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (ClientException e) {
            log.error("getGroupDetails ClientException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (Exception e) {
            log.error("getGroupDetails Exception. error:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public RoomInfoDto.UserStatus getUserInfo(String groupId, String anchorId) {

        long start = System.currentTimeMillis();

        ListMessageGroupUserByIdRequest request = new ListMessageGroupUserByIdRequest();
        request.setAppId(config.getImAppId());
        request.setGroupId(groupId);
        request.setUserIdList(Collections.singletonList(anchorId));

        try {
            ListMessageGroupUserByIdResponse acsResponse = AliCloudClientFacotry.getClient().getAcsResponse(request);
            log.info("getUserInfo, response:{}, consume:{}", JSON.toJSONString(acsResponse), (System.currentTimeMillis() - start));
            if (CollectionUtils.isEmpty(acsResponse.getResult().getUserList())) {
                log.info("getUserInfo is empty. groupId:{}, anchorId:{}", groupId, anchorId);
                return null;
            }
            ListMessageGroupUserByIdResponse.Result.UserListItem userListItem = acsResponse.getResult().getUserList().get(0);
            RoomInfoDto.UserStatus userStatus = new RoomInfoDto.UserStatus();
            userStatus.setMute(userListItem.getIsMute());
            userStatus.setMuteSource(userListItem.getMuteBy());

            return userStatus;
        } catch (ServerException e) {
            log.error("getUserInfo ServerException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (ClientException e) {
            log.error("getUserInfo ClientException. ErrCode:{}, ErrMsg:{}, RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
        } catch (Exception e) {
            log.error("getUserInfo Exception. error:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public boolean validLiveCallbackSign(String liveSignature, String liveTimestamp) {

        String signContent = String.format("%s|%s|%s", config.getLiveStreamPushUrl(), liveTimestamp, config.getLiveCallbackAuthKey());
        String sum = DigestUtils.md5DigestAsHex(signContent.getBytes());
        if (!sum.equals(liveSignature)) {
            log.warn("validLiveCallbackSign sign invalid.signContent:{}", signContent);
            return false;
        }
        return true;
    }

    private PullLiveInfo getPullLiveInfo(String appName, String streamName) {

        String streamUrl = String.format("%s/%s/%s", config.getLiveStreamPullUrl(), appName, streamName);
        String streamUrlOfOriaac = String.format("%s/%s/%s_oriaac", config.getLiveStreamPullUrl(), appName, streamName);

        String pullAuthKey = getAuth(streamName, config.getLiveStreamPullAuthKey());
        String pullAuthKeyOfOriaac = getAuth(String.format("%s_oriaac", streamName), config.getLiveStreamPullAuthKey());
        String pullAuthKeyWithFlv = getAuth(String.format("%s%s", streamName, ".flv"), config.getLiveStreamPullAuthKey());
        String pullAuthKeyWithFlvOfOriaac = getAuth(String.format("%s_oriaac%s", streamName, ".flv"), config.getLiveStreamPullAuthKey());
        String pullAuthKeyWithM3u8 = getAuth(String.format("%s%s", streamName, ".m3u8"), config.getLiveStreamPullAuthKey());
        String pullAuthKeyWithM3u8OfOriaac = getAuth(String.format("%s_oriaac%s", streamName, ".m3u8"), config.getLiveStreamPullAuthKey());

        PullLiveInfo pullLiveInfo = PullLiveInfo.builder().rtmpUrl(String.format("%s://%s?auth_key=%s", "rtmp", streamUrl, pullAuthKey)).rtmpOriaacUrl(String.format("%s://%s?auth_key=%s", "rtmp", streamUrlOfOriaac, pullAuthKeyOfOriaac)).rtsUrl(String.format("%s://%s?auth_key=%s", "artc", streamUrl, pullAuthKey)).rtsOriaacUrl(String.format("%s://%s?auth_key=%s", "artc", streamUrlOfOriaac, pullAuthKeyOfOriaac)).flvUrl(String.format("https://%s.flv?auth_key=%s", streamUrl, pullAuthKeyWithFlv)).flvOriaacUrl(String.format("https://%s.flv?auth_key=%s", streamUrlOfOriaac, pullAuthKeyWithFlvOfOriaac)).hlsUrl(String.format("https://%s.m3u8?auth_key=%s", streamUrl, pullAuthKeyWithM3u8)).hlsOriaacUrl(String.format("https://%s.m3u8?auth_key=%s", streamUrlOfOriaac, pullAuthKeyWithM3u8OfOriaac)).build();

        log.info("getPullLiveInfo. streamName:{}, pullLiveInfo:{}", streamName, JSON.toJSONString(pullLiveInfo));
        return pullLiveInfo;
    }

    @Override
    public String getRtcAuth(String channelId, String userId, long timestamp) {

        String rtcAuthStr = String.format("%s%s%s%s%d", config.getLiveMicAppId(), config.getLiveMicAppKey(), channelId, userId, timestamp);
        String rtcAuth = getSha256(rtcAuthStr);
        log.info("getRtcAuth. rtcAuthStr:{}, rtcAuth:{}", rtcAuthStr, rtcAuth);
        return rtcAuth;
    }

    @Override
    public String getSpecialRtcAuth(String channelId, String userId, long timestamp) {
        String rtcAuthStr = String.format("%s%s%s%s%d", "79a51aa1-7127-4f32-90ce-cdfe618835d9", "181a27773a0f06f6042800ede171279e", channelId, userId, timestamp);
        String rtcAuth = getSha256(rtcAuthStr);
        log.info("getRtcAuth. rtcAuthStr:{}, rtcAuth:{}", rtcAuthStr, rtcAuth);
        return rtcAuth;
    }

    @Override
    public AliResponse checkUsersOnline(LiveRoomRequest param) {
        try {
            CheckLiveMessageUsersOnlineRequest request = AliCloudReqConverter.INSTANCE.toCheckUser(param);
            CheckLiveMessageUsersOnlineResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
            return AliResponse.builder().requestId(res.getRequestId()).users(res.getUserList()).build();
        } catch (ClientException e) {
            log.error("【CheckUser】查询用户是否在线失败。{}", e);
            throw new ServiceException("查询用户是否在线失败");
        }
    }

    @Override
    public AliResponse sendMessage(AliSendLiveMessageUserRequest param) {
        try {
            if(StrUtil.isNotEmpty(param.getReceiverId())) {
                SendLiveMessageUserRequest request = AliCloudReqConverter.INSTANCE.toMsgUser(param);
                SendLiveMessageUserResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
                return AliResponse.builder().requestId(res.getRequestId()).msgTid(res.getMsgTid()).build();
            } else {
                SendLiveMessageGroupRequest request = AliCloudReqConverter.INSTANCE.toMsgGroup(param);
                SendLiveMessageGroupResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
                return AliResponse.builder().requestId(res.getRequestId()).msgTid(res.getMsgTid()).build();
            }
        } catch (ClientException e) {
            log.error("【sendMsg】发送消息失败！--->>{}", e);
            throw new ServiceException("发送消息给指定用户失败！");
        }
    }

    @Override
    public AliResponse removeMessage(DeleteMessageRequest req) {
        try {
            if(StrUtil.isNotEmpty(req.getGroupId())) {
                DeleteLiveMessageGroupMessageRequest request = AliCloudReqConverter.INSTANCE.toMsgGroup(req);
                DeleteLiveMessageGroupMessageResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
                return AliResponse.builder().requestId(res.getRequestId()).build();
            } else {
                DeleteLiveMessageUserMessageRequest request = AliCloudReqConverter.INSTANCE.toMsgUser(req);
                DeleteLiveMessageUserMessageResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
                return AliResponse.builder().requestId(res.getRequestId()).build();
            }
        } catch (ClientException e) {
            String msg = "删除（撤回）消息失败！";
            log.error("【Delete Msg】{} -->> {}", msg, e);
            throw new ServiceException(msg);
        }
    }
    
    @Override
    public AliResponse banned(LiveRoomRequest req) {
        try {
            ModifyLiveMessageGroupBandRequest request = AliCloudReqConverter.INSTANCE.toBand(req);
            ModifyLiveMessageGroupBandResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
            return AliResponse.builder().requestId(res.getRequestId()).build();
        } catch (ClientException e) {
            log.error("【Banned】禁言操作失败！{}, {}", req, e);
            throw new ServiceException("禁言操作失败");
        }
    }
    
    @Override
    public AliResponse unban(LiveRoomRequest req) {
        try {
            UnbanLiveMessageGroupRequest request = AliCloudReqConverter.INSTANCE.toUnban(req);
            UnbanLiveMessageGroupResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
            return AliResponse.builder().requestId(res.getRequestId()).build();
        } catch (ClientException e) {
            log.error("【unban】解除群组全员禁言失败-->{}", e);
            throw new ServiceException("解除群组全员禁言失败");
        }
    }

    @Override
    public AliResponse addBand(LiveRoomRequest req) {
        try {
            AddLiveMessageGroupBandRequest request = AliCloudReqConverter.INSTANCE.toAddBand(req);
            AddLiveMessageGroupBandResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
            return AliResponse.builder().requestId(res.getRequestId()).build();
        } catch (ClientException e) {
            log.error("【AddBand】新增禁言用户失败！{}", e);
            throw new ServiceException("新增禁言用户失败");
        }
    }

    @Override
    public AliResponse removeBand(LiveRoomRequest req) {
        try {
            RemoveLiveMessageGroupBandRequest request = AliCloudReqConverter.INSTANCE.toRemoveBand(req);
            RemoveLiveMessageGroupBandResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
            return AliResponse.builder().requestId(res.getRequestId()).build();
        } catch (ClientException e) {
            log.error("【DelBand】移除禁言用户失败！{}", e);
            throw new ServiceException("移除禁言用户失败");
        }
    }

    @Override
    public AliResponse kickUser(LiveRoomRequest req) {
        try {
            KickLiveMessageGroupUserRequest request = AliCloudReqConverter.INSTANCE.toKickUser(req);
            KickLiveMessageGroupUserResponse res = AliCloudClientFacotry.getClient().getAcsResponse(request);
            return AliResponse.builder().requestId(res.getRequestId()).build();
        } catch (ClientException e) {
            log.error("【Kick】踢出用户失败！{}", e);
            throw new ServiceException("踢出用户失败");
        }
    }
 

    @Override
    public ListLiveMessageGroupMessagesResponse recordRoomMessageAfterPlay(String appId, String groupId, Integer sortType, String dataCenter, Long nextPageToken) {
        ListLiveMessageGroupMessagesResponse response;
        ListLiveMessageGroupMessagesRequest request = new ListLiveMessageGroupMessagesRequest();
        request.setAppId(appId);
        request.setGroupId(groupId);
        request.setSortType(sortType);
        request.setDataCenter(dataCenter);
        request.setPageSize(PAGE_SIZE);
        request.setNextPageToken(nextPageToken);

        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("查询群组消息列表失败-->{}", e);
            throw new ServiceException("查询群组消息列表失败！");
        }
        return response;
    }


    private static String getSha256(String str) {
        MessageDigest messageDigest;
        String encodestr;
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodestr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            log.error("异常！", e);
            throw new ServiceException("查询群组消息列表失败！");
        }
        return encodestr;
    }

    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    /**
     * 获取鉴权。文档见：<a href="https://help.aliyun.com/document_detail/199349.html">...</a>
     *
     * <AUTHOR>
     */
    private String getAuth(String streamName, String authKey) {
        String rand = "0";
        String uid = "0";
        String path = CharSequenceUtil.format("/{}/{}", config.getLiveStreamAppName(), streamName);
        log.info("应用与流组装的路径:{}", path);
        long exp = System.currentTimeMillis() / 1000 + config.getLiveStreamAuthExpires();
        log.info("当前应用流定义的过期时间:{}", exp);
        String signStr = CharSequenceUtil.format("{}-{}-{}-{}-{}", path, exp, rand, uid, authKey);
        log.info("签名串:{}", signStr);
        String hashValue = DigestUtils.md5DigestAsHex(signStr.getBytes());
        log.info("签名后并且计算hash后的值:{}", hashValue);
        String ak = CharSequenceUtil.format("{}-{}-{}-{}", exp, rand, uid, hashValue);
        log.info("最终鉴权字符串:{}", ak);
        log.info("getAuth. signStr:{}, hashValue:{}, ak:{}", signStr, hashValue, ak);
        return ak;
    }

    @Override
    public CreateUploadVideoResponse createUploadVideo(AliCreateUploadVideoRequest req) {
        CreateUploadVideoRequest request = new CreateUploadVideoRequest();
        BeanUtils.copyProperties(req, request);
        request.setCoverURL(req.getCoverUrl());
        log.info("创建上传视频, request:{}", request);
        CreateUploadVideoResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
            log.info("上传结果, response:{}", response);
        } catch (ServerException e) {
            log.error("创建上传视频失败！", e);
        } catch (ClientException e) {
            log.error("ErrCode:{},ErrMsg:{},RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId(), e);
        }
        return response;
    }

    @Override
    public CreateLivePullToPushResponse createLivePullToPush(AliCreateLivePullToPushRequest req) {
        CreateLivePullToPushRequest request = new CreateLivePullToPushRequest();
        // 地域 ID
        request.setSysRegionId(req.getRegionId());
        // 指定任务启动所在区域
        request.setRegion(req.getRegion());
        // 任务名称，用于支持模糊查询，默认值为''
        request.setTaskName(req.getTaskName());
        // 任务开始时间
        String startTime = LocalDateTimeUtils.ymdHms2YmdThmsZ(req.getStartTime());
        request.setStartTime(startTime);
        // 任务结束时间
        String endTime = LocalDateTimeUtils.ymdHms2YmdThmsZ(req.getEndTime());
        request.setEndTime(endTime);
        // 源流类型
        request.setSourceType(req.getSourceType());
        // 源流协议
        request.setSourceProtocol(req.getSourceProtocol());
        // 源流地址
        request.setSourceUrls(req.getSourceUrls());
        // 推流协议
        request.setDstUrl(req.getDstUrl());
        // 播放完后重复继续播放的次数
        request.setRepeatNumber(req.getRepeatNumber());
        // 文件索引，从第 n 个文件开始播放起
        request.setFileIndex(req.getFileIndex());
        // 启动偏移，视频文件起始偏移值。单位：秒。取值：大于 0
        request.setOffset(req.getOffset());
        // 推流回调地址
        request.setCallbackUrl(req.getCallbackUrl());
        // 重试间隔，单位秒，取值区间[60,300]，默认 60 秒
        request.setRetryInterval(req.getRetryInterval());
        // 重试次数，默认值为 3
        request.setRetryCount(req.getRetryCount());
        CreateLivePullToPushResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            this.printError(e);
            log.error("发生异常", e);
        }
        return response;
    }

    private void printError(ClientException e) {
        log.error("ErrCode:{},ErrMsg:{},RequestId:{}", e.getErrCode(), e.getErrMsg(), e.getRequestId());
    }

    @Override
    public DeleteLivePullToPushResponse deleteLivePullToPush(AliDeleteLivePullToPushRequest req) {
        DeleteLivePullToPushRequest request = new DeleteLivePullToPushRequest();
        request.setSysRegionId(req.getRegionId());
        request.setTaskId(req.getTaskId());
        request.setRegion(req.getRegion());
        DeleteLivePullToPushResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            this.printError(e);
            log.error("发生异常", e);
        }
        return response;
    }


    @Override
    public UploadVideoResponse uploadVideo(String title, String fileName) {
        UploadVideoRequest request = new UploadVideoRequest(config.getAccessKeyId(), config.getAccessKeySecret(), title, fileName);
        /* 可指定分片上传时每个分片的大小，默认为2M字节 */
        request.setPartSize(2 * 1024 * 1024L);
        /* 可指定分片上传时的并发线程数，默认为1，（注：该配置会占用服务器CPU资源，需根据服务器情况指定）*/
        request.setTaskNum(1);
        /* 是否开启断点续传, 默认断点续传功能关闭。当网络不稳定或者程序崩溃时，再次发起相同上传请求，可以继续未完成的上传任务，适用于超时3000秒仍不能上传完成的大文件。
        注意：断点续传开启后，会在上传过程中将上传位置写入本地磁盘文件，影响文件上传速度，请您根据实际情况选择是否开启*/
        //request.setEnableCheckpoint(false);
        /* OSS慢请求日志打印超时时间，是指每个分片上传时间超过该阈值时会打印debug日志，如果想屏蔽此日志，请调整该阈值。单位：毫秒，默认为300000毫秒*/
        //request.setSlowRequestsThreshold(300000L);
        /* 可指定每个分片慢请求时打印日志的时间阈值，默认为300s*/
        //request.setSlowRequestsThreshold(300000L);
        /* 是否显示水印（可选），指定模板组ID时，根据模板组配置确定是否显示水印*/
        //request.setIsShowWaterMark(true);
        /* 自定义消息回调设置及上传加速设置（可选）, Extend为自定义扩展设置，MessageCallback为消息回调设置，AccelerateConfig为上传加速设置（上传加速功能需要先申请开通后才能使用）*/
        //request.setUserData("{\"Extend\":{\"test\":\"www\",\"localId\":\"xxxx\"},\"MessageCallback\":{\"CallbackType\":\"http\",\"CallbackURL\":\"http://example.aliyundoc.com\"},\"AccelerateConfig\":{\"Type\":\"oss\",\"Domain\":\"****Bucket.oss-accelerate.aliyuncs.com\"}}");
        /* 视频分类ID（可选） */
        //request.setCateId(0);
        /* 视频标签，多个用逗号分隔（可选） */
        //request.setTags("标签1,标签2");
        /* 视频描述（可选）*/
        //request.setDescription("视频描述");
        /* 封面图片（可选）*/
        //request.setCoverURL("http://cover.example.com/image_01.jpg");
        /* 模板组ID（可选）*/
        //request.setTemplateGroupId("8c4792cbc8694e7084fd5330e5****");
        /* 工作流ID（可选）*/
        //request.setWorkflowId("d4430d07361f0*be1339577859b0****");
        /* 存储区域（可选）*/
        //request.setStorageLocation("in-201703232118266-5sejd****.oss-cn-shanghai.aliyuncs.com");
        /* 开启默认上传进度回调 */
        //request.setPrintProgress(false);
        /* 设置自定义上传进度回调（必须继承 VoDProgressListener）*/
        /*默认关闭。如果开启了这个功能，上传过程中服务端会在日志中返回上传详情。如果不需要接收此消息，需关闭此功能*/
        //request.setProgressListener(new PutObjectProgressListener());
        /* 设置您实现的生成STS信息的接口实现类*/
        // request.setVoDRefreshSTSTokenListener(new RefreshSTSTokenImpl());
        /* 设置应用ID*/
        //request.setAppId("app-100****");
        /* 点播服务接入点 */
        //request.setApiRegionId("cn-shanghai");
        /* ECS部署区域*/
        // request.setEcsRegionId("cn-shanghai");
        /* 配置代理访问（可选） */
        //OSSConfig ossConfig = new OSSConfig();
        /* <必填>设置代理服务器主机地址 */
        //ossConfig.setProxyHost("<yourProxyHost>");
        /* <必填>设置代理服务器端口 */
        //ossConfig.setProxyPort(-1);
        /* 设置连接OSS所使用的协议（HTTP或HTTPS），默认为HTTP */
        //ossConfig.setProtocol("HTTP");
        /* 设置用户代理，指HTTP的User-Agent头，默认为aliyun-sdk-java */
        //ossConfig.setUserAgent("<yourUserAgent>");
        /* 设置代理服务器验证的用户名，https协议时需要填 */
        //ossConfig.setProxyUsername("<yourProxyUserName>");
        /* 设置代理服务器验证的密码，https协议时需要填 */
        //ossConfig.setProxyPassword("<yourProxyPassword>");
        //request.setOssConfig(ossConfig);

        UploadVideoImpl uploader = new UploadVideoImpl();
        UploadVideoResponse response = uploader.uploadVideo(request);
        //请求视频点播服务的请求ID
        log(response.getRequestId(), response.isSuccess(), response.getVideoId(), response.getCode(), response.getMessage());
        return response;
    }

    private void log(String requestId, boolean success, String videoId, String code, String message) {
        log.info("RequestId={}\n", requestId);
        if (!success) {
            /* 如果设置回调URL无效，不影响视频上传，可以返回VideoId同时会返回错误码。其他情况上传失败时，VideoId为空，此时需要根据返回错误码分析具体错误原因 */
            log.info("ErrorCode={}\n", code);
            log.info("ErrorMessage={}\n", message);
        }
        log.info("VideoId={}\n", videoId);
    }

    @Override
    public void uploadUrlStream(String title, String url, String fileExtension) {
        UploadURLStreamRequest request = new UploadURLStreamRequest(config.getAccessKeyId(), config.getAccessKeySecret(), title, url);
        /* 文件扩展名*/
        request.setFileExtension(fileExtension);
        /* 网络文件下载连接超时，单位毫秒，0-表示不限制*/
        request.setDownloadConnectTimeout(1000);
        /* 网络文件下载读取超时，单位毫秒，0-表示不限制*/
        request.setDownloadReadTimeout(0);
        /* 网络文件下载后保存的本地目录*/
        request.setLocalDownloadFilePath("/Users/<USER>");
        /* 是否显示水印（可选），指定模板组ID时，根据模板组配置确定是否显示水印*/
        //request.setShowWaterMark(true);
        /* 自定义消息回调设置及上传加速设置（可选），Extend为自定义扩展设置，MessageCallback为消息回调设置，AccelerateConfig为上传加速设置（上传加速功能需要先申请开通后才能使用） */
        //request.setUserData("{\"Extend\":{\"test\":\"www\",\"localId\":\"xxxx\"},\"MessageCallback\":{\"CallbackType\":\"http\",\"CallbackURL\":\"http://example.aliyundoc.com\"},\"AccelerateConfig\":{\"Type\":\"oss\",\"Domain\":\"****Bucket.oss-accelerate.aliyuncs.com\"}}");
        /* 视频分类ID（可选） */
        //request.setCateId(0);
        /* 视频标签，多个用逗号分隔（可选） */
        //request.setTags("标签1,标签2");
        /* 视频描述（可选） */
        //request.setDescription("视频描述");
        /* 封面图片（可选）*/
        //request.setCoverURL("http://cover.example.com/image_01.jpg");
        /* 模板组ID（可选）*/
        //request.setTemplateGroupId("8c4792cbc8694e7084fd5330e56****");
        /* 工作流ID（可选）*/
        //request.setWorkflowId("d4430d07361f0*be1339577859b0****");
        /* 存储区域（可选）*/
        //request.setStorageLocation("in-201703232118266-5sejd****.oss-cn-shanghai.aliyuncs.com");
        /* 开启默认上传进度回调 */
        //request.setPrintProgress(true);
        /* 设置自定义上传进度回调 （必须继承 VoDProgressListener）*/
        /*默认关闭。如果开启了这个功能，上传过程中服务端会在日志中返回上传详情。如果不需要接收此消息，需关闭此功能*/
        //request.setProgressListener(new PutObjectProgressListener());
        /* 设置应用ID*/
        //request.setAppId("app-100****");
        /* 点播服务接入点 */
        //request.setApiRegionId("cn-shanghai");
        /* ECS部署区域*/
        // request.setEcsRegionId("cn-shanghai");

        /* 配置代理访问（可选） */
        //OSSConfig ossConfig = new OSSConfig();
        /* <必填>设置代理服务器主机地址 */
        //ossConfig.setProxyHost("<yourProxyHost>");
        /* <必填>设置代理服务器端口 */
        //ossConfig.setProxyPort(-1);
        /* 设置连接OSS所使用的协议（HTTP或HTTPS），默认为HTTP */
        //ossConfig.setProtocol("HTTP");
        /* 设置用户代理，指HTTP的User-Agent头，默认为aliyun-sdk-java */
        //ossConfig.setUserAgent("<yourUserAgent>");
        /* 设置代理服务器验证的用户名，https协议时需要填 */
        //ossConfig.setProxyUsername("<yourProxyUserName>");
        /* 设置代理服务器验证的密码，https协议时需要填 */
        //ossConfig.setProxyPassword("<yourProxyPassword>");
        //request.setOssConfig(ossConfig);

        UploadVideoImpl uploader = new UploadVideoImpl();
        UploadURLStreamResponse response = uploader.uploadURLStream(request);
        //请求视频点播服务的请求ID
        log(response.getRequestId(), response.isSuccess(), response.getVideoId(), response.getCode(), response.getMessage());
    }

    @Override
    public void uploadFileStream(String title, String fileName) {
        UploadFileStreamRequest request = new UploadFileStreamRequest(config.getAccessKeyId(), config.getAccessKeySecret(), title, fileName);
        /* 是否使用默认水印（可选），指定模板组ID时，根据模板组配置确定是否使用默认水印*/
        //request.setShowWaterMark(true);
        /* 自定义消息回调设置及上传加速设置（可选），Extend为自定义扩展设置，MessageCallback为消息回调设置，AccelerateConfig为上传加速设置（上传加速功能需要先申请开通后才能使用）*/
        //request.setUserData("{\"Extend\":{\"test\":\"www\",\"localId\":\"xxxx\"},\"MessageCallback\":{\"CallbackType\":\"http\",\"CallbackURL\":\"http://example.aliyundoc.com\"},\"AccelerateConfig\":{\"Type\":\"oss\",\"Domain\":\"****Bucket.oss-accelerate.aliyuncs.com\"}}");
        /* 视频分类ID（可选）*/
        //request.setCateId(0);
        /* 视频标签，多个用逗号分隔（可选） */
        //request.setTags("标签1,标签2");
        /* 视频描述（可选）*/
        //request.setDescription("视频描述");
        /* 封面图片（可选）*/
        //request.setCoverURL("http://cover.example.com/image_01.jpg");
        /* 模板组ID（可选）*/
        //request.setTemplateGroupId("8c4792cbc8694e7084fd5330e56****");
        /* 工作流ID（可选）*/
        //request.setWorkflowId("d4430d07361f0*be1339577859b0****");
        /* 存储区域（可选）*/
        //request.setStorageLocation("in-201703232118266-5sejd****.oss-cn-shanghai.aliyuncs.com");
        /* 开启默认上传进度回调 */
        //request.setPrintProgress(true);
        /* 设置自定义上传进度回调（必须继承 VoDProgressListener）*/
        /*默认关闭。如果开启了这个功能，上传过程中服务端会在日志中返回上传详情。如果不需要接收此消息，需关闭此功能*/
        //request.setProgressListener(new PutObjectProgressListener());
        /* 设置应用ID*/
        //request.setAppId("app-100****");
        /* 点播服务接入点 */
        //request.setApiRegionId("cn-shanghai");
        /* ECS部署区域*/
        // request.setEcsRegionId("cn-shanghai");

        /* 配置代理访问（可选） */
        //OSSConfig ossConfig = new OSSConfig();
        /* <必填>设置代理服务器主机地址 */
        //ossConfig.setProxyHost("<yourProxyHost>");
        /* <必填>设置代理服务器端口 */
        //ossConfig.setProxyPort(-1);
        /* 设置连接OSS所使用的协议（HTTP或HTTPS），默认为HTTP */
        //ossConfig.setProtocol("HTTP");
        /* 设置用户代理，指HTTP的User-Agent头，默认为aliyun-sdk-java */
        //ossConfig.setUserAgent("<yourUserAgent>");
        /* 设置代理服务器验证的用户名，https协议时需要填 */
        //ossConfig.setProxyUsername("<yourProxyUserName>");
        /* 设置代理服务器验证的密码，https协议时需要填 */
        //ossConfig.setProxyPassword("<yourProxyPassword>");
        //request.setOssConfig(ossConfig);

        UploadVideoImpl uploader = new UploadVideoImpl();
        UploadFileStreamResponse response = uploader.uploadFileStream(request);
        //请求视频点播服务的请求ID
        log(response.getRequestId(), response.isSuccess(), response.getVideoId(), response.getCode(), response.getMessage());
    }


    @Override
    public UploadStreamResponse uploadStream(String title, String fileName, InputStream inputStream) {
        UploadStreamRequest request = new UploadStreamRequest(config.getAccessKeyId(), config.getAccessKeySecret(), title, fileName, inputStream);
        /* 是否使用默认水印（可选），指定模板组ID时，根据模板组配置确定是否使用默认水印*/
        //request.setShowWaterMark(true);
        /* 自定义消息回调设置及上传加速设置（可选）, Extend为自定义扩展设置，MessageCallback为消息回调设置，AccelerateConfig为上传加速设置（上传加速功能需要先申请开通后才能使用）*/
        //request.setUserData("{\"Extend\":{\"test\":\"www\",\"localId\":\"xxxx\"},\"MessageCallback\":{\"CallbackType\":\"http\",\"CallbackURL\":\"http://example.aliyundoc.com\"},\"AccelerateConfig\":{\"Type\":\"oss\",\"Domain\":\"****Bucket.oss-accelerate.aliyuncs.com\"}}");
        /* 视频分类ID（可选） */
        //request.setCateId(0);
        /* 视频标签，多个用逗号分隔（可选） */
        //request.setTags("标签1,标签2");
        /* 视频描述（可选）*/
        //request.setDescription("视频描述");
        /* 封面图片（可选）*/
        //request.setCoverURL("http://cover.example.com/image_01.jpg");
        /* 模板组ID（可选）*/
        //request.setTemplateGroupId("8c4792cbc8694e7084fd5330e56****");
        /* 工作流ID（可选）*/
        //request.setWorkflowId("d4430d07361f0*be1339577859b0****");
        /* 存储区域（可选）*/
        //request.setStorageLocation("in-201703232118266-5sejd****.oss-cn-shanghai.aliyuncs.com");
        /* 开启默认上传进度回调 */
        // request.setPrintProgress(true);
        /* 设置自定义上传进度回调（必须继承 VoDProgressListener） */
        /*默认关闭。如果开启了这个功能，上传过程中服务端会在日志中返回上传详情。如果不需要接收此消息，需关闭此功能*/
        // request.setProgressListener(new PutObjectProgressListener());
        /* 设置应用ID*/
        //request.setAppId("app-100****");
        /* 点播服务接入点 */
        //request.setApiRegionId("cn-shanghai");
        /* ECS部署区域*/
        // request.setEcsRegionId("cn-shanghai");

        /* 配置代理访问（可选） */
        //OSSConfig ossConfig = new OSSConfig();
        /* <必填>设置代理服务器主机地址 */
        //ossConfig.setProxyHost("<yourProxyHost>");
        /* <必填>设置代理服务器端口 */
        //ossConfig.setProxyPort(-1);
        /* 设置连接OSS所使用的协议（HTTP或HTTPS），默认为HTTP */
        //ossConfig.setProtocol("HTTP");
        /* 设置用户代理，指HTTP的User-Agent头，默认为aliyun-sdk-java */
        //ossConfig.setUserAgent("<yourUserAgent>");
        /* 设置代理服务器验证的用户名，https协议时需要填 */
        //ossConfig.setProxyUsername("<yourProxyUserName>");
        /* 设置代理服务器验证的密码，https协议时需要填 */
        //ossConfig.setProxyPassword("<yourProxyPassword>");
        //request.setOssConfig(ossConfig);

        UploadVideoImpl uploader = new UploadVideoImpl();
        UploadStreamResponse response = uploader.uploadStream(request);
        //请求视频点播服务的请求ID
        this.log(response.getRequestId(), response.isSuccess(), response.getVideoId(), response.getCode(), response.getMessage());
        return response;
    }


    @Override
    public void uploadLocalM3u8(String title, String m3u8Filename, String[] sliceFilenames) {
        //String title = "test_upload_local_m3u8";
        //String m3u8Filename = "/Users/<USER>/0e9ecfc6da934d1887ed7bdfc925****/cc38da35c7b24de0abe58619cdd7****-6479a12446b994719838e0307f****-ld.m3u8";
        // String[] sliceFilenames = new String[]{
        //         "/Users/<USER>/0e9ecfc6da934d1887ed7bdfc925****/slices/cc38da35c7b24de0abe58619cdd7****-c45797a1ad6e75fbb9d1a8493703****-ld-00001.ts",
        //         "/Users/<USER>/0e9ecfc6da934d1887ed7bdfc925****/slices/cc38da35c7b24de0abe58619cdd7****-c45797a1ad6e75fbb9d1a8493703****-ld-00002.ts",
        //         "/Users/<USER>/0e9ecfc6da934d1887ed7bdfc925****/slices/cc38da35c7b24de0abe58619cdd7****-c45797a1ad6e75fbb9d1a8493703****-ld-00003.ts",
        //         "/Users/<USER>/0e9ecfc6da934d1887ed7bdfc925****/slices/cc38da35c7b24de0abe58619cdd7****-c45797a1ad6e75fbb9d1a8493703****-ld-00004.ts",
        //         "/Users/<USER>/0e9ecfc6da934d1887ed7bdfc925****/slices/cc38da35c7b24de0abe58619cdd7****-c45797a1ad6e75fbb9d1a8493703****-ld-00005.ts"
        // };
        UploadLocalM3u8Request request = new UploadLocalM3u8Request(config.getAccessKeyId(), config.getAccessKeySecret(), title, m3u8Filename);
        // ts分片文件列表，可选，不指定时，直接解析m3u8FileURL获取分片地址
        request.setSliceFilenames(sliceFilenames);
        /* 可指定分片上传时每个分片的大小，默认为2M字节 */
        request.setPartSize(2 * 1024 * 1024L);
        /* 可指定分片上传时的并发线程数，默认为1，（注：该配置会占用服务器CPU资源，需根据服务器情况指定）*/
        request.setTaskNum(1);
        /* 是否显示水印（可选），指定模板组ID时，根据模板组配置确定是否显示水印*/
        //request.setShowWaterMark(true);
        /* 自定义消息回调设置及上传加速设置（可选），Extend为自定义扩展设置，MessageCallback为消息回调设置，AccelerateConfig为上传加速设置（上传加速功能需要先申请开通后才能使用）*/
        //request.setUserData("{\"Extend\":{\"test\":\"www\",\"localId\":\"xxxx\"},\"MessageCallback\":{\"CallbackType\":\"http\",\"CallbackURL\":\"http://example.aliyundoc.com\"},\"AccelerateConfig\":{\"Type\":\"oss\",\"Domain\":\"****Bucket.oss-accelerate.aliyuncs.com\"}}");
        /* 视频分类ID（可选）*/
        //request.setCateId(-1L);
        /* 视频标签，多个用逗号分隔（可选）*/
        //request.setTags("标签1,标签2");
        /* 视频描述（可选）*/
        //request.setDescription("视频描述");
        /* 封面图片（可选）*/
        //request.setCoverURL("http://cover.sample.com/sample.jpg");
        /* 模板组ID（可选）*/
        //request.setTemplateGroupId("8c4792cbc8694e7084fd5330e56****");
        /* 工作流ID（可选）*/
        //request.setWorkflowId("d4430d07361f0*be1339577859b0****");
        /* 存储区域（可选）*/
        //request.setStorageLocation("in-201703232118266-5sejd****.oss-cn-shanghai.aliyuncs.com");
        /* 设置应用ID*/
        // request.setAppId("app-1000000");
        /* 点播服务接入点 */
        // request.setApiRegionId("cn-shanghai");
        /* ECS部署区域*/
        // request.setEcsRegionId("cn-shanghai");

        /* 配置代理访问（可选） */
        //OSSConfig ossConfig = new OSSConfig();
        /* <必填>设置代理服务器主机地址 */
        //ossConfig.setProxyHost("<yourProxyHost>");
        /* <必填>设置代理服务器端口 */
        //ossConfig.setProxyPort(-1);
        /* 设置连接OSS所使用的协议（HTTP或HTTPS），默认为HTTP */
        //ossConfig.setProtocol("HTTP");
        /* 设置用户代理，指HTTP的User-Agent头，默认为aliyun-sdk-java */
        //ossConfig.setUserAgent("<yourUserAgent>");
        /* 设置代理服务器验证的用户名，https协议时需要填 */
        //ossConfig.setProxyUsername("<yourProxyUserName>");
        /* 设置代理服务器验证的密码，https协议时需要填 */
        //ossConfig.setProxyPassword("<yourProxyPassword>");
        //request.setOssConfig(ossConfig);

        UploadM3u8FileImpl uploadM3u8File = new UploadM3u8FileImpl();
        UploadLocalM3u8Response uploadLocalM3u8Response = uploadM3u8File.uploadLocalM3u8(request);
        log(uploadLocalM3u8Response.getCode(), uploadLocalM3u8Response.getMessage(), uploadLocalM3u8Response.getVideoId(), uploadLocalM3u8Response.getRequestId());
    }

    private void log(String code, String message, String videoId, String requestId) {
        log.info("code = {},message = {},videoId ={},requestId = {}", code, message, videoId, requestId);
    }


    @Override
    public void uploadWebM3u8(String title, String m3u8FileUrl, String[] sliceFileUrls) {
        // String title = "test_upload_web_m3u8";
        // String m3u8FileUrl = "http://test.aliyun.com/f0d644abc547129e957b386f77****/a0e1e2817ab9425aa558fe67a90e****-538087dcf2c201c31ce4324bf76af69****.m3u8";
        // String[] sliceFileUrls = new String[]{
        //         "http://test.aliyun.com/f0d644abc547129e957b386f77****/a0e1e2817ab9425aa558fe67a90e****-822598b9c170a8c6dad985e20cd9c27d-ld-0****.ts",
        //         "http://test.aliyun.com/f0d644abc547129e957b386f77****/a0e1e2817ab9425aa558fe67a90e****-822598b9c170a8c6dad985e20cd9c27d-ld-0****.ts",
        //         "http://test.aliyun.com/f0d644abc547129e957b386f77****/a0e1e2817ab9425aa558fe67a90e****-822598b9c170a8c6dad985e20cd9c27d-ld-0****.ts",
        //         "http://test.aliyun.com/f0d644abc547129e957b386f77****/a0e1e2817ab9425aa558fe67a90e****-822598b9c170a8c6dad985e20cd9c27d-ld-0****.ts",
        //         "http://test.aliyun.com/f0d644abc547129e957b386f77****/a0e1e2817ab9425aa558fe67a90e****-822598b9c170a8c6dad985e20cd9c27d-ld-0****.ts"
        // };
        UploadWebM3u8Request request = new UploadWebM3u8Request(config.getAccessKeyId(), config.getAccessKeySecret(), title, m3u8FileUrl);
        // ts分片地址，可选，不指定时，直接解析m3u8FileURL获取分片地址
        request.setSliceFileURLs(sliceFileUrls);
        /* 下载文件的临时存储目录，可自定义，如不指定则保存到程序所运行的目录下*/
        // request.setGlobalLocalFilePath("/User/download/");
        /* 可指定分片上传时每个分片的大小，默认为2M字节 */
        request.setPartSize(2 * 1024 * 1024L);
        /* 可指定分片上传时的并发线程数，默认为1，（注：该配置会占用服务器CPU资源，需根据服务器情况指定）*/
        request.setTaskNum(1);
        /* 是否显示水印（可选），指定模板组ID时，根据模板组配置确定是否显示水印*/
        //request.setShowWaterMark(true);
        /* 自定义消息回调设置及上传加速设置（可选），Extend为自定义扩展设置，MessageCallback为消息回调设置，AccelerateConfig为上传加速设置（上传加速功能需要先申请开通后才能使用）*/
        //request.setUserData("{\"Extend\":{\"test\":\"www\",\"localId\":\"xxxx\"},\"MessageCallback\":{\"CallbackType\":\"http\",\"CallbackURL\":\"http://example.aliyundoc.com\"},\"AccelerateConfig\":{\"Type\":\"oss\",\"Domain\":\"****Bucket.oss-accelerate.aliyuncs.com\"}}");
        /* 视频分类ID（可选）*/
        //request.setCateId(-1L);
        /* 视频标签，多个用逗号分隔（可选）*/
        //request.setTags("标签1,标签2");
        /* 视频描述（可选）*/
        //request.setDescription("视频描述");
        /* 封面图片（可选）*/
        //request.setCoverURL("http://cover.example.com/sample.jpg");
        /* 模板组ID（可选）*/
        //request.setTemplateGroupId("8c4792cbc8694e7084fd5330e56****");
        /* 工作流ID（可选）*/
        //request.setWorkflowId("d4430d07361f0*be1339577859b0****");
        /* 存储区域（可选）*/
        //request.setStorageLocation("in-2017032321****-5sejdln9o.oss-cn-shanghai.aliyuncs.com");
        /* 设置应用ID*/
        //request.setAppId("app-100****");
        /* 点播服务接入点 */
        //request.setApiRegionId("cn-shanghai");
        /* ECS部署区域*/
        // request.setEcsRegionId("cn-shanghai");

        /* 配置代理访问（可选） */
        //OSSConfig ossConfig = new OSSConfig();
        /* <必填>设置代理服务器主机地址 */
        //ossConfig.setProxyHost("<yourProxyHost>");
        /* <必填>设置代理服务器端口 */
        //ossConfig.setProxyPort(-1);
        /* 设置连接OSS所使用的协议（HTTP或HTTPS），默认为HTTP */
        //ossConfig.setProtocol("HTTP");
        /* 设置用户代理，指HTTP的User-Agent头，默认为aliyun-sdk-java */
        //ossConfig.setUserAgent("<yourUserAgent>");
        /* 设置代理服务器验证的用户名，https协议时需要填 */
        //ossConfig.setProxyUsername("<yourProxyUserName>");
        /* 设置代理服务器验证的密码，https协议时需要填 */
        //ossConfig.setProxyPassword("<yourProxyPassword>");
        //request.setOssConfig(ossConfig);

        UploadM3u8FileImpl uploadM3u8File = new UploadM3u8FileImpl();
        UploadWebM3u8Response uploadWebM3u8Response = uploadM3u8File.uploadWebM3u8(request);
        log(uploadWebM3u8Response.getCode(), uploadWebM3u8Response.getMessage(), uploadWebM3u8Response.getVideoId(), uploadWebM3u8Response.getRequestId());
    }

    @Override
    public GetVideoInfoResponse getVideoInfo(AliGetVideoInfoRequest req) {
        GetVideoInfoRequest request = new GetVideoInfoRequest();
        request.setVideoId(req.getVideoId());
        GetVideoInfoResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
            return response;
        } catch (ClientException e) {
            log.error("查询视频信息失败！", e);
        }
        return response;
    }

    @Override
    public GetVideoListResponse getVideoList(AliGetVideoListRequest req) {
        GetVideoListRequest request = new GetVideoListRequest();
        request.setCateId(req.getCateId());
        request.setStatus(req.getStatus());
        request.setPageNo(req.getPageNo());
        request.setPageSize(req.getPageSize());
        request.setSortBy(req.getSortBy());
        request.setStartTime(req.getStartTime());
        request.setEndTime(req.getEndTime());
        request.setStorageLocation(req.getStorageLocation());
        log.info("远程请求参数:{}", JSONObject.toJSONString(request));
        GetVideoListResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("分页查询视频列表失败！", e);
        }
        return response;
    }

    @Override
    public AddCategoryResponse addCategory(AliAddCategoryRequest req) {
        AddCategoryRequest request = new AddCategoryRequest();
        request.setCateName(req.getCateName());
        request.setType(req.getType());
        AddCategoryResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("添加分类失败！", e);
        }
        return response;
    }

    @Override
    public GetCategoriesResponse getCategories(Long cateId) {
        AliGetCategoriesRequest req = new AliGetCategoriesRequest()
                .setCateId(cateId)
                .setCategoryType(EnumCategoryType.DEFAULT.getVal())
                .setSortBy(EnumSortBy.CREATION_TIME_DESC.getVal())
                .setPageNo(1L)
                .setPageSize(100L);
        return this.getCategories(req);
    }

    @Override
    public GetCategoriesResponse getCategories(AliGetCategoriesRequest req) {
        GetCategoriesRequest request = new GetCategoriesRequest();
        request.setCateId(req.getCateId());
        request.setType(req.getCategoryType());
        request.setSortBy(req.getSortBy());
        request.setPageNo(req.getPageNo());
        request.setPageSize(req.getPageSize());
        log.info("远程请求参数[获取分类]:{}", JSON.toJSONString(request));
        GetCategoriesResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
            log.info("远程响应参数[获取分类]:{}", JSON.toJSONString(response));
        } catch (ClientException e) {
            log.error("获取分类列表失败！", e);
        }
        return response;
    }

    @Override
    public DeleteCategoryResponse deleteCategory(Long cateId) {
        DeleteCategoryRequest request = new DeleteCategoryRequest();
        request.setCateId(cateId);
        DeleteCategoryResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("删除分类列表失败！", e);
        }
        return response;
    }

    @Override
    public CreateUploadImageResponse createUploadImage(AliCreateUploadImageRequest req) {
        CreateUploadImageRequest request = new CreateUploadImageRequest();
        BeanUtils.copyProperties(req, request);
        request.setImageType(req.getImageType());
        request.setImageExt(req.getImageExt());
        CreateUploadImageResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取图片上传地址和凭证！", e);
        }
        return response;
    }

    @Override
    public UpdateCategoryResponse updateCategory(AliUpdateCategoryRequest req) {
        UpdateCategoryRequest request = new UpdateCategoryRequest();
        request.setCateId(req.getCateId());
        request.setCateName(req.getCateName());
        UpdateCategoryResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("更新分类失败！", e);
        }

        return response;
    }

    @Override
    public RefreshUploadVideoResponse refreshUploadVideo(AliRefreshUploadVideoRequest req) {
        RefreshUploadVideoRequest request = new RefreshUploadVideoRequest();
        BeanUtils.copyProperties(req, request);
        RefreshUploadVideoResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("刷新视频上传凭证失败！", e);
        }
        return response;
    }

    @Override
    public ListTranscodeTemplateGroupResponse listTranscodeTemplateGroup(String appId) {
        ListTranscodeTemplateGroupRequest request = new ListTranscodeTemplateGroupRequest();
        request.setAppId(appId);
        ListTranscodeTemplateGroupResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取转码模板列表失败！", e);
        }
        return response;
    }

    @Override
    public ListAppInfoResponse listAppInfo(AliListAppInfoRequest req) {
        ListAppInfoRequest request = new ListAppInfoRequest();
        BeanUtils.copyProperties(req, request);
        ListAppInfoResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取应用列表失败！", e);
        }
        return response;
    }

    @Override
    public GetTranscodeTemplateGroupResponse getTranscodeTemplateGroup(String transcodeTemplateGroupId) {
        GetTranscodeTemplateGroupRequest request = new GetTranscodeTemplateGroupRequest();
        request.setTranscodeTemplateGroupId(transcodeTemplateGroupId);
        GetTranscodeTemplateGroupResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取转码模板失败！", e);
        }
        return response;
    }

    @Override
    public GetImageInfoResponse getImageInfo(AliGetImageInfoRequest req) {
        GetImageInfoRequest request = new GetImageInfoRequest();
        request.setImageId(req.getImageId());
        GetImageInfoResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取图片信息失败！", e);
        }
        return response;
    }

    @Override
    public GetImageInfosResponse getImageInfos(AliGetImageInfosRequest req) {
        GetImageInfosRequest request = new GetImageInfosRequest();
        request.setImageIds(String.join(StrPool.COMMA, req.getImageIds()));
        GetImageInfosResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取图片信息失败！", e);
        }
        return response;
    }

    @Override
    public DeleteImageResponse deleteImage(AliDeleteImageRequest req) {
        // 判断传入值是否正确
        String deleteImageType = req.getDeleteImageType();
        EnumDeleteImageType enumDeleteImageType = EnumDeleteImageType.of(deleteImageType);
        switch (enumDeleteImageType) {
            case IMAGE_ID:
                String imageIds = req.getImageIds();
                if (StrUtil.isBlank(imageIds)) {
                    throw new BaseException("图片ID不能为空");
                }
                break;
            case IMAGE_URL:
                String imageUrls = req.getImageUrls();
                if (StrUtil.isBlank(imageUrls)) {
                    throw new BaseException("图片URL不能为空");
                }
                break;
            case VIDEO_ID:
                String videoId = req.getVideoId();
                if (StrUtil.isBlank(videoId)) {
                    throw new BaseException("视频ID不能为空");
                }
                String imageType = req.getImageType();
                if (StrUtil.isBlank(imageType)) {
                    throw new BaseException("图片类型不能为空");
                }
                EnumImageTypeDelete.of(imageType);
                break;
            default:
                throw new BaseException("删除图片类型错误");
        }
        // 远程调用
        DeleteImageRequest request = new DeleteImageRequest();
        request.setDeleteImageType(req.getDeleteImageType());
        request.setImageURLs(req.getImageUrls());
        request.setImageIds(req.getImageIds());
        request.setVideoId(req.getVideoId());
        request.setImageType(req.getImageType());
        DeleteImageResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("删除图片失败！", e);
        }
        return response;
    }

    @Override
    public SubmitTranscodeJobsResponse submitTranscodeJobs(AliSubmitTranscodeJobsRequest req) {
        SubmitTranscodeJobsRequest request = new SubmitTranscodeJobsRequest();

        SubmitTranscodeJobsResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("提交转码任务失败！", e);
        }
        return response;
    }

    @Override
    public GetUploadDetailsResponse getUploadDetails(AliGetUploadDetailsRequest req) {
        GetUploadDetailsRequest request = new GetUploadDetailsRequest();
        request.setMediaIds(String.join(StrPool.COMMA, req.getMediaIds()));
        request.setMediaType(req.getMediaType());
        GetUploadDetailsResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取上传信息失败！", e);
        }
        return response;
    }

    @Override
    public UpdateLivePullToPushResponse updateLivePullToPush(AliUpdateLivePullToPushRequest req) {
        UpdateLivePullToPushRequest request = new UpdateLivePullToPushRequest();
        request.setSysRegionId(req.getRegionId());
        request.setTaskId(req.getTaskId());
        request.setRegion(req.getRegion());
        // 任务开始时间
        String startTime = LocalDateTimeUtils.ymdHms2YmdThmsZ(req.getStartTime());
        request.setStartTime(startTime);
        // 任务结束时间
        String endTime = LocalDateTimeUtils.ymdHms2YmdThmsZ(req.getEndTime());
        request.setEndTime(endTime);
        request.setSourceUrls(req.getSourceUrls());
        request.setRepeatNumber(req.getRepeatNumber());
        request.setOffset(req.getOffset());
        request.setCallbackUrl(req.getCallbackUrl());
        request.setFileIndex(req.getFileIndex());
        UpdateLivePullToPushResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("更新拉流转推任务失败！", e);
        }
        return response;
    }

    @Override
    public GetVideoInfosResponse getVideoInfos(List<String> mediaIds) {
        GetVideoInfosRequest getVideoInfosRequest = new GetVideoInfosRequest();
        getVideoInfosRequest.setVideoIds(String.join(StrPool.COMMA, mediaIds));
        GetVideoInfosResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(getVideoInfosRequest);
        } catch (ClientException e) {
            log.error("获取视频信息失败！", e);
        }
        return response;
    }

    @Override
    public GetPlayInfoResponse getPlayInfo(AliGetPlayInfoRequest req) {
        GetPlayInfoRequest request = new GetPlayInfoRequest();
        request.setVideoId(req.getVideoId());
        request.setOutputType(req.getOutputType());
        request.setDefinition(req.getDefinition());
        request.setAuthTimeout(req.getAuthTimeout());
        request.setStreamType(req.getStreamType());
        request.setResultType(req.getResultType());
        request.setAdditionType(req.getAdditionType());
        request.setPlayConfig(req.getPlayConfig());
        request.setReAuthInfo(req.getReAuthInfo());
        request.setFormats(req.getFormats());
        GetPlayInfoResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取视频播放地址失败！", e);
        }

        return response;
    }

    @Override
    public GetVideoPlayAuthResponse getVideoPlayAuth(AliGetVideoPlayAuthRequest req) {
        GetVideoPlayAuthRequest request = new GetVideoPlayAuthRequest();
        request.setVideoId(req.getVideoId());
        request.setAuthInfoTimeout(req.getAuthInfoTimeout());
        request.setApiVersion(req.getApiVersion());
        GetVideoPlayAuthResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("获取音视频播放凭证异常！", e);
        }
        return response;
    }

    @Override
    public UpdateImageInfosResponse updateImageInfos(AliUpdateImageInfosRequest req) {
        UpdateImageInfosRequest request = new UpdateImageInfosRequest();
        request.setUpdateContent(JSONObject.toJSONString(req.getUpdateContent()));
        UpdateImageInfosResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("批量更新图片信息失败！", e);
        }
        return response;
    }

    @Override
    public CreateAppInfoResponse createAppInfo(AliCreateAppInfoRequest req) {
        CreateAppInfoRequest request = new CreateAppInfoRequest();
        request.setAppName(req.getAppName());
        request.setDescription(req.getDescription());
        CreateAppInfoResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("创建应用失败！", e);
        }
        return response;
    }

    @Override
    public AddTranscodeTemplateGroupResponse addTranscodeTemplateGroup(AliAddTranscodeTemplateGroupRequest req) {
        AddTranscodeTemplateGroupRequest request = new AddTranscodeTemplateGroupRequest();
        request.setName(req.getName());
        request.setTranscodeTemplateList(req.getTranscodeTemplateList());
        request.setTranscodeTemplateGroupId(req.getTranscodeTemplateGroupId());
        request.setAppId(req.getAppId());
        AddTranscodeTemplateGroupResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("创建转码模板组失败！", e);
        }
        return response;
    }

    @Override
    public UpdateTranscodeTemplateGroupResponse updateTranscodeTemplateGroup(AliUpdateTranscodeTemplateGroupRequest req) {
        UpdateTranscodeTemplateGroupRequest request = new UpdateTranscodeTemplateGroupRequest();
        request.setTranscodeTemplateGroupId(req.getTranscodeTemplateGroupId());
        request.setName(req.getName());
        request.setTranscodeTemplateList(req.getTranscodeTemplateList());
        request.setLocked(req.getLocked());
        UpdateTranscodeTemplateGroupResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("修改转码配置！", e);
        }
        return response;
    }

    @Override
    public SetDefaultTranscodeTemplateGroupResponse setDefaultTranscodeTemplateGroup(AliSetDefaultTranscodeTemplateGroupRequest req) {
        SetDefaultTranscodeTemplateGroupRequest request = new SetDefaultTranscodeTemplateGroupRequest();
        request.setTranscodeTemplateGroupId(req.getTranscodeTemplateGroupId());
        SetDefaultTranscodeTemplateGroupResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("修改转码配置！", e);
        }
        return response;
    }

    @Override
    public DeleteTranscodeTemplateGroupResponse deleteTranscodeTemplateGroup(AliDeleteTranscodeTemplateGroupRequest req) {
        DeleteTranscodeTemplateGroupRequest request = new DeleteTranscodeTemplateGroupRequest();
        request.setTranscodeTemplateGroupId(req.getTranscodeTemplateGroupId());
        request.setForceDelGroup(req.getForceDelGroup());
        request.setTranscodeTemplateIds(req.getTranscodeTemplateIds());
        DeleteTranscodeTemplateGroupResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("删除转码配置失败！", e);
        }
        return response;
    }

    @Override
    public LiveAuiMessageApp createLiveMessageApp(AliCreateLiveMessageAppRequest req) {
        CreateLiveMessageAppRequest request = new CreateLiveMessageAppRequest();
        request.setAppName(req.getAppName());
        request.setAuditType(0);
        request.setAuditUrl(req.getAuditUrl());
        request.setDataCenter(ConsLiveAui.DATA_CENTER);
        request.setEventCallbackUrl(req.getEventCallbackUrl());

        try {
            CreateLiveMessageAppResponse response = AliCloudClientFacotry.getClient().getAcsResponse(request);
            return AliCloudReqConverter.INSTANCE.reqRes2Po(req, response);

        } catch (Exception e) {
            log.error("创建互动消息应用失败！", e);
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public LiveAuiMessageApp updateLiveMessageApp(String eventCallbackUrl) {
        LiveAuiMessageApp response = new LiveAuiMessageApp();
        ModifyLiveMessageAppCallbackRequest request = new ModifyLiveMessageAppCallbackRequest();
        request.setAppId(config.getAppId());
        request.setDataCenter(ConsLiveAui.DATA_CENTER);
        request.setEventCallbackUrl(eventCallbackUrl);

        try {
            ModifyLiveMessageAppCallbackResponse aliResponse = AliCloudClientFacotry.getClient().getAcsResponse(request);

            response.setAppId(aliResponse.getAppId());
            response.setAppSign(aliResponse.getAppSign());
            response.setEventCallbackUrl(aliResponse.getEventCallbackUrl());
            response.setEventCallbackNeedAuthentication(aliResponse.getEventCallbackNeedAuthentication());

            return response;
        } catch (ClientException e) {
            log.error("创建互动消息应用失败！", e);
            throw new ServiceException("创建互动消息应用失败!");
        }
    }

    @Override
    public DeleteVideoResponse deleteVideo(AliDeleteVideoRequest req) {
        DeleteVideoRequest request = new DeleteVideoRequest();
        request.setVideoIds(String.join(StrUtil.COMMA, req.getVideoIds()));
        DeleteVideoResponse response = null;
        try {
            response = AliCloudClientFacotry.getClient().getAcsResponse(request);
        } catch (ClientException e) {
            log.error("删除视频失败！", e);
        }
        return response;

    }

}
