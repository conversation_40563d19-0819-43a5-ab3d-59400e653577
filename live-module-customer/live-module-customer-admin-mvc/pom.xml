<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.panda.pollen</groupId>
        <artifactId>live-module-customer</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>live-module-customer-admin-mvc</artifactId>
    <name>live-module-customer-admin-mvc</name>
    <description>
        C端用户在admin后台管理服务中相关的mvc功能模块
    </description>

    <dependencies>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-customer-biz</artifactId>
        </dependency>
    </dependencies>

</project>
