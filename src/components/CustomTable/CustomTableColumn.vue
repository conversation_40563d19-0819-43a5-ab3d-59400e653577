<script setup>
import RenderComponent from '@/components/RenderComponent/index.vue'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import { RateSort } from '@/utils/number'

defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  renderMap: {
    type: Object,
    default: () => ({})
  },
  sortable: {
    type: Boolean,
    default: true
  }
})

</script>

<template>
  <el-table-column :label="data.label" :sortable="sortable" align="center" :prop="data.prop" :width="data.width" :min-width="data.minWidth" :show-overflow-tooltip="data.overflow" :sort-method="(a,b) => RateSort(a ,b , data.prop)">
    <template #header="scope">
      <span>{{ scope.column.label }}</span>
      <el-tooltip v-if="data.tooltip" effect="dark" :content="data.tooltip" placement="top">
        <i class="el-icon-question color-primary" />
      </el-tooltip>
    </template>
    <template #default="scope">
      <RenderComponent v-if="data.render" :render="renderMap[data.prop](scope.row)" />
      <TableColumnSet v-else-if="data.set" :set="data.set" :row="scope.row" :render-map="renderMap" :label-width="data.labelWidth" />
      <template v-else-if="data.format">{{ data.format(scope.row[data.prop]) }}</template>
      <template v-else>{{ scope.row[data.prop] }}</template>
    </template>
  </el-table-column>
</template>

<style scoped lang="scss">

</style>
