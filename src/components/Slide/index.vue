<template>
  <div
    v-show="images.length > 0"
    class="slideshow"
    @mouseenter="handleSlideMouseEnter"
    @mouseleave="handleSlideMouseLeave"
  >
    <div ref="slidesRef" class="slides">
      <img
        v-for="(image, index) in images"
        :key="index"
        class="slides-img"
        :src="image"
        :class="{ active: index === currentIndex }"
        @load="handleSlideLoad"
      >
    </div>
    <a class="prev" href="#" @click.prevent="showPrevSlide" />
    <a class="next" href="#" @click.prevent="showNextSlide" />
  </div>
</template>

<script>
export default {
  props: {
    images: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentIndex: 0,
      slideWidth: 0,
      slideTimeout: null,
      autoPlayInterval: null
    }
  },
  mounted() {
    window.addEventListener('resize', () => {
      this.showSlide(this.currentIndex)
    })

    this.$nextTick(() => {
      this.slideWidth = this.$refs.slidesRef.offsetWidth
    })

    this.startAutoPlay()
    this.showSlide(this.currentIndex)
  },
  beforeDestroy() {
    this.stopAutoPlay()
  },
  methods: {
    showSlide(index) {
      if (index < 0) {
        index = this.images.length - 1
      } else if (index >= this.images.length) {
        index = 0
      }

      if (this.$refs.slidesRef) this.$refs.slidesRef.style.transform = `translateX(-${index}00%)`
      this.currentIndex = index
    },
    showNextSlide() {
      this.showSlide(this.currentIndex + 1)
    },
    showPrevSlide() {
      this.showSlide(this.currentIndex - 1)
    },
    handleSlideLoad(event) {
      clearTimeout(this.slideTimeout)

      this.slideTimeout = setTimeout(() => {
        if (!event.target) return
        this.slideWidth = event.target.offsetWidth
        this.showSlide(this.currentIndex)
      }, 100)
    },
    startAutoPlay() {
      if (this.autoPlayInterval !== null) return

      this.autoPlayInterval = setInterval(() => {
        this.showNextSlide()
      }, 3000)
    },
    stopAutoPlay() {
      clearInterval(this.autoPlayInterval)
      this.autoPlayInterval = null
    },
    handleSlideMouseEnter() {
      this.stopAutoPlay()
    },
    handleSlideMouseLeave() {
      this.startAutoPlay()
    }
  }
}
</script>

<style lang="scss" scoped>
.slideshow {
  position: relative;
  cursor: grab;
}

.slideshow:hover {
  cursor: pointer;
}

.slideshow:hover .prev,
.slideshow:hover .next {
  display: block;
}

.slides {
  display: flex;
  transition: transform 0.5s ease;
}

.slides::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.slides-img {
  scroll-snap-align: start;
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.prev,
.next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  display: block;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-size: 24px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.prev:hover,
.next:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.prev {
  left: 10px;
}

.next {
  right: 10px;
}

.active {
  border: 2px solid #333;
}

.slideshow:active {
  cursor: grabbing;
}
</style>
