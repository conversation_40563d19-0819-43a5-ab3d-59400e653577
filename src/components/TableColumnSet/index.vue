<script setup>
import ClipboardButton from '@/components/ClipboardButton/index.vue'
import RenderComponent from '@/components/RenderComponent/index.vue'

defineProps({
  set: {
    type: Array,
    default: () => []
  },
  row: {
    type: Object,
    default: () => ({})
  },
  renderMap: {
    type: Object,
    default: () => ({})
  },
  labelWidth: {
    type: [Number, String],
    default: 80
  }
})

</script>

<template>
  <div class="set-wrap">
    <div v-for="s in set" :key="s.prop" class="set-item">
      <div class="set-label" :style="{width: `${labelWidth}px`}">{{ s.label }}:</div>
      <div class="set-value">
        <template v-if="row[s.prop] !== undefined ">
          <RenderComponent v-if="s.render && typeof s.render === 'function'" :render="s.render(row[s.prop])" />
          <RenderComponent v-else-if="s.render" :render="renderMap[s.prop](row[s.prop])" />
          <span v-else class="overflow-text" :title="row[s.prop]">{{ row[s.prop] }}</span>
        </template>
        <span v-else>--</span>
        <ClipboardButton v-if="s.copy" class="copy-btn" :value="s.render ? s.render(row[s.prop]) : row[s.prop]" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.set-wrap {
  .set-item {
    display: flex;
    align-items: center;
    .set-label {
      text-align: left;
      margin-right: 5px;
    }
    .set-value {
      flex: 1;
      display: flex;
      align-items: center;
      .copy-btn {
        margin-left: 5px;
      }
    }
  }
}
</style>
