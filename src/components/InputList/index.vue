<template>
  <div>
    <ul class="list-group">
      <li
        v-for="(_, i) in innerList"
        :key="i"
        class="list-item"
      >
        <el-input v-model.trim="innerList[i]" type="text" class="form-control flex1" :placeholder="placeholder" clearable>
          <template slot="prepend">http(s)://</template>
        </el-input>
        <el-button size="mini" class="close-btn" :disabled="innerList.length === 0" type="danger" plain @click="remove(i)">
          <i class="el-icon-close close" />
        </el-button>
      </li>
    </ul>

    <div class="action-btns">
      <el-button class="flex1 add-btn" @click="add">+ 新增</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InputList',
  model: {
    prop: 'list',
    event: 'change'
  },
  props: {
    list: {
      type: Array,
      default: () => ([])
    },
    placeholder: {
      type: String,
      default: '请输入地址'
    }
  },
  data: () => ({
    innerList: []
  }),
  watch: {
    list: {
      handler(val) {
        this.innerList = val
      },
      deep: true
    },
    innerList: {
      handler(val) {
        this.$emit('change', val)
      },
      deep: true
    }
  },
  mounted() {
    this.innerList = this.list
  },
  methods: {
    add() {
      this.innerList.push('')
    },
    remove(i) {
      this.innerList.splice(i, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.action-btns {
  display: flex;
}
.list-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 10px;
  &:last-of-type {
    margin-bottom: 0px;
  }
  .close-btn {
    display: none;
  }
  &:hover {
    .close-btn {
      display: block;
    }
  }
}
</style>
