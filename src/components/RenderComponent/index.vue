<script>
import { h, unref } from 'vue'

export default {
  props: {
    render: {
      type: [Function, Object, String, Number]
    }
  },
  setup(props) {
    return () => {
      let render = ''
      if (!props.render) return render
      if (typeof props.render === 'string' || typeof props.render === 'number') {
        render = h('span', {}, props.render)
      } else if (typeof props.render === 'function') {
        render = props.render()
      } else if (typeof props.render === 'object') {
        render = unref(props.render)
      }

      return render
    }
  }
}
</script>

<style scoped lang="scss">

</style>
