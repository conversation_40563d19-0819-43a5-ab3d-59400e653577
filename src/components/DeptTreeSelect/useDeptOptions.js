import { nextTick, ref } from 'vue'
import { deptTreeSelect } from '@/api/system/user'
import { checkRole } from '@/utils/permission'
import { getDeptIds } from '@/utils'

const deptOptions = ref([])
let loading = false
let callbacks = []
export default function(callback) {
  if (callback) callbacks.push(callback)
  if (!loading) {
    if (!deptOptions.value.length) {
      loading = true
      deptTreeSelect().then(response => {
        deptOptions.value = response.data
        deptOptions.value.forEach(item => {
          if (item.children) {
            item.children = item.children.sort((a, b) => {
              return a.label.localeCompare(b.label, 'zh')
            })
          }
        })
      }).finally(() => {
        loading = false
        callbacks.forEach(cb => cb(deptOptions))
        callbacks = []
      })
    } else {
      nextTick(() => {
        callbacks.forEach(cb => cb(deptOptions))
        callbacks = []
      })
    }
  }
  return deptOptions
}

let defaultIds = []
export function getDefaultIds() {
  // 部门选项长度为一且非商务角色
  if (defaultIds.length) return defaultIds
  if (deptOptions.value.length === 1 && !checkRole(['business'])) {
  // if (deptOptions.value.length > 1) {
    //  遍历获取所有 id
    defaultIds = getDeptIds(deptOptions.value, deptOptions.value[0].id)
  }
  return defaultIds
}

export function setDefaultIds(self) {
  if (getDefaultIds().length) {
    const fun = self.getList
    self.getList = () => {
      if (!self.queryParams.deptIds?.length) {
        self.queryParams.deptIds = getDefaultIds()
      }
      fun()
    }
  }
}
