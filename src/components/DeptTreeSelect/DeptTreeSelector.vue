<script setup>
import { CTreeDrop } from '@wsfe/ctree'
import { computed } from 'vue'
import useDeptOptions from '@/components/DeptTreeSelect/useDeptOptions'

const props = defineProps({
  deptIds: {
    type: [Array, String],
    default: () => []
  },
  isSearch: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: true
  },
  callback: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['select', 'update:deptIds'])

const innerDeptIds = computed({
  get() {
    return props.deptIds ?? []
  },
  set(val) {
    emit('update:deptIds', val)
    emit('select')
  }
})

const deptOptions = useDeptOptions((deptOptions) => props.callback && props.callback(deptOptions))
</script>
<template>
  <el-form-item prop="deptIds">
    <CTreeDrop
      v-model="innerDeptIds"
      style="width: 190px"
      :dropdown-min-width="200"
      :data="deptOptions"
      drop-placeholder="部门"
      title-field="label"
      :selectable="!multiple"
      clearable
      default-expand-all
      :checkable="multiple"
    >
      <template #display="scope">
        <div
          v-if="multiple"
          :class="[scope.checkedNodes.length ? 'c-tree-overflow': isSearch? 'c-tree-placeholder' : '']"
        >
          {{ scope.checkedNodes.length? scope.checkedNodes.map((node) => node.label).join('、') : '部门' }}
        </div>
        <div
          v-else
          :class="[scope.selectedNode ? 'c-tree-overflow': isSearch? 'c-tree-placeholder' : '']"
        >
          {{ scope.selectedNode? scope.selectedNode.label : '部门' }}
        </div>
      </template>
    </CTreeDrop>

  </el-form-item>
</template>
