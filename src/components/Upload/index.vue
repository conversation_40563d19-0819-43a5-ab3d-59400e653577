<template>
  <el-upload
    ref="fileUpload"
    :data="data"
    :multiple="multiple"
    :action="uploadFileUrl"
    :before-upload="handleBeforeUpload"
    :on-error="handleUploadError"
    :on-success="handleUploadSuccess"
    :on-exceed="handleExceed"
    :show-file-list="false"
    :headers="headers"
    class="upload-file-uploader"
    :limit="limit"
    :disabled="disabled"
  >
    <!-- 上传按钮 -->
    <slot name="trigger">
      <el-button :disabled="disabled" :size="size" :type="buttonType">
        <slot>上传</slot>
      </el-button>
    </slot>
  </el-upload>

</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'Upload',
  props: {
    action: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    },
    fileSize: {
      type: Number,
      default: 5
    },
    size: {
      type: String,
      default: 'medium'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 10
    },
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg']
    },
    multiple: {
      type: Boolean,
      default: true
    },
    filterName: {
      type: Boolean,
      default: false
    },
    buttonType: {
      type: String,
      default: ''
    },
    beforeUpload: {
      type: Function,
      default: null
    }
  },
  emits: ['success', 'successResult'],
  data: () => ({
    number: 0,
    uploadFileUrl: import.meta.env.VITE_APP_BASE_API + '/common/upload', // 上传文件服务器地址
    headers: {
      Authorization: 'Bearer ' + getToken()
    },
    uploadList: []
  }),
  mounted() {
    if (this.action) {
      this.uploadFileUrl = this.action
      if (this.uploadFileUrl.indexOf('/') < 0) {
        this.uploadFileUrl = '/' + this.uploadFileUrl
      }
      if (this.uploadFileUrl.indexOf(import.meta.env.VITE_APP_BASE_API) < 0) {
        this.uploadFileUrl = import.meta.env.VITE_APP_BASE_API + this.uploadFileUrl
      }
    }
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      if (this.beforeUpload && !this.beforeUpload()) return false
      const fileName = file.name.split('.')
      // 校检文件类型
      if (this.fileType) {
        const fileExt = fileName[fileName.length - 1]
        const isTypeOk = this.fileType.indexOf(fileExt) >= 0
        if (!isTypeOk) {
          this.$message({
            showClose: true,
            message: `${file.name} 文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`,
            type: 'error'
          })
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message({
            showClose: true,
            message: `${file.name} 文件大小不能超过 ${this.fileSize} MB!`,
            type: 'error'
          })
          return false
        }
      }
      // 校验特殊字符
      if (this.filterName) {
        const specialCharacters = /[\!\@\#\$\%\^\&*\+=\\|\'\"\:\;\"\'\,\/\?]/
        if (specialCharacters.test(file.name)) {
          this.$message({
            showClose: true,
            message: `${file.name} 文件名包含特殊字符，请修改后再上传。`,
            type: 'error'
          })
          return false
        }
      }

      this.$modal.loading('正在上传文件，请稍候...')
      this.number++
      return true
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError('上传文件失败，请重试')
      this.$modal.closeLoading()
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ name: res.fileName, url: res.fileName })
        this.uploadedSuccessfully(res, file)
      } else {
        this.number--
        this.$modal.closeLoading()
        this.$modal.msgError(res.msg)
        this.$refs.fileUpload.handleRemove(file)
        this.uploadedSuccessfully(res, file)
      }
    },
    // 上传结束处理
    uploadedSuccessfully(res, file) {
      if (this.number > 0 && this.uploadList.length === this.number) {
        const fileList = [...this.uploadList]
        this.uploadList = []
        this.number = 0
        this.$emit('success', fileList)
        this.$emit('successResult', res, file)
        this.$refs.fileUpload.clearFiles()
        this.$modal.closeLoading()
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
