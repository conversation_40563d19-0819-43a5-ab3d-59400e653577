import { reactive, ref } from 'vue'
import useConfigs from '@/hooks/useConfigs'

const monitorLinkMap = reactive({
  1: '',
  2: '',
  3: '',
  4: '',
  5: '',
  6: '',
  7: '',
  8: '',
  9: '',
  10: '',
  11: '',
  12: '',
  13: '',
  14: ''
})
const centralMonitorDomain = ref('')
const linkMap = {
  1: 'monitor_link',
  2: 'ks_monitor_link',
  3: 'wb_monitor_link',
  4: 'tx_monitor_link',
  5: 'bl_monitor_link',
  6: 'zfb_monitor_link',
  7: 'uc_monitor_link',
  8: 'bd_monitor_link',
  9: '360_monitor_link',
  10: 'xhs_monitor_link',
  12: 'soul_monitor_link',
  13: 'zh_monitor_link',
  14: 'yk_monitor_link'
}
useConfigs(Object.values(linkMap), (configs) => {
  Object.entries(linkMap).forEach(([key, value]) => {
    monitorLinkMap[key] = configs[value]
  })
})
useConfigs('central_monitor_domain', (configs) => {
  centralMonitorDomain.value = configs['central_monitor_domain']
})

const suffixMap = {
  1: 'landing_page_suffix_jl',
  2: 'landing_page_suffix_ks',
  3: 'landing_page_suffix_wb',
  4: 'landing_page_suffix_tx',
  5: 'landing_page_suffix_bl',
  6: 'landing_page_suffix_zfb',
  7: 'landing_page_suffix_uc',
  8: 'landing_page_suffix_bd',
  9: 'landing_page_suffix_360',
  10: 'landing_page_suffix_xhs',
  12: 'landing_page_suffix_soul',
  13: 'landing_page_suffix_zh',
  14: 'landing_page_suffix_yk'
}

const landingPageSuffixMap = reactive({
  1: '',
  2: '',
  3: '',
  4: '',
  7: '',
  8: '',
  9: '',
  10: '',
  12: '',
  13: '',
  14: ''
})
useConfigs(Object.values(suffixMap), (configs) => {
  Object.entries(suffixMap).forEach(([key, value]) => {
    landingPageSuffixMap[key] = configs[value]
  })
})

export default function useUrlConfig() {
  return {
    landingPageSuffixMap,
    monitorLinkMap,
    centralMonitorDomain
  }
}
