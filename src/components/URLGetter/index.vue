<script setup>
import UrlClipboard from '@/views/promotion/goods/components/UrlClipboard.vue'
import { addUrlParams, parseParams } from '@/utils'
import { computed, onMounted, reactive, ref } from 'vue'
import { listLanding } from '@/api/promotion/landing'
import { generateTxUrl, getTxQrCode, generateTxUrlPddCashgift } from '@/api/promotion/goods'
import { Message } from 'element-ui'
import useUrlConfig from '@/components/URLGetter/useUrlConfig'
import useAccountSelect from '@/hooks/mediaAction/useAccountSelect'
import { is360, isTx, isWb, isXhs, isZh, isKs, isBd } from '@/utils/judge/media'
import personalCopy from '@/views/promotion/goods/components/personalCopy.vue'
import { isDjk, isUDSmart, isTbPro, isPdd, isJd, isJdPro } from '@/utils/judge/platform'
import useDicts from '@/hooks/useDicts'

const emit = defineEmits(['openLanding'])

const tooltipMap = {
  3: '微博',
  9: '360',
  13: '知乎'
}

const props = defineProps({
  goods: {
    type: Object,
    default: null
  },
  // 是否显示个人化复制按钮
  showPersonalBtn: {
    type: Boolean,
    default: false
  },
  // 是否为拼多多礼金
  isPddCashGift: {
    type: Boolean,
    default: false
  }

})

const { dicts } = useDicts(['report_event_baidu'])

const advertiserId = ref('')
const media = computed(() => props.goods.mediaPlatformType)
const { options: mediaAccountOptions, requestRemote, selectLoading } = useAccountSelect(media.value)
const adSelectedVisible = computed(() => (isZh(media.value) || isWb(media.value) || is360(media.value)))
const isAdSelected = computed(() => {
  if (isBd(media.value)) {
    return reportEvents.value.length > 0
  }
  return !adSelectedVisible.value || advertiserId.value !== ''
})
const adTooltip = computed(() => {
  if (isBd(media.value)) {
    return '获取二跳落地页链接、直达链接和一跳落地页时需要选择回传事件.'
  }
  return tooltipMap[media.value] + '获取二跳落地页链接、直达链接和一跳落地页时需要选择媒体账户.'
})

const handleAdIdChange = () => {
  requestRemote('')
  initUrlMap()

  let landing = ``
  if (isWb(media.value) || is360(media.value) || isZh(media.value)) {
    pageList.value.forEach(item => {
      item.advertiserId = advertiserId.value
      item.filePath = addUrlParams(item.filePath, { advertiserId: advertiserId.value })
      landing += `
链接: ${item.filePath}
          `
    })
    urlMap.monitorUrl = urlMap.monitorUrl && addUrlParams(urlMap.monitorUrl, { advertiserId: advertiserId.value })
  }

  urlMap.landingUrl = landing
  urlMap.dpUrl = urlMap.dpUrl && urlMap.dpUrl.replace('__ADVERTISER_ID__', advertiserId.value)
  urlMap.oneJumpUrl = urlMap.oneJumpUrl && urlMap.oneJumpUrl.replace('__ADVERTISER_ID__', advertiserId.value)

  copyAllUrl()
}

const reportEvents = ref([])
const handleReportEventChange = () => {
  initUrlMap()

  let landing = ``
  const cEvents = reportEvents.value.join(',')
  if (isBd(media.value)) {
    pageList.value.forEach(item => {
      item.filePath = addUrlParams(item.filePath, { c_events: cEvents })
      landing += `
链接: ${item.filePath}
        `
    })
    urlMap.monitorUrl = urlMap.monitorUrl && addUrlParams(urlMap.monitorUrl, { c_events: cEvents })
  }

  urlMap.landingUrl = landing
  urlMap.dpUrl = urlMap.dpUrl && urlMap.dpUrl.replace('__C__E__', encodeURIComponent(cEvents))
  urlMap.oneJumpUrl = urlMap.oneJumpUrl && urlMap.oneJumpUrl.replace('__C__E__', encodeURIComponent(cEvents))

  urlMap.weAppUrl = (props.goods.weAppUrl ? props.goods.weAppUrl : '').replace('__C__E__', encodeURIComponent(cEvents))
  copyAllUrl()
}

const activeUrlType = ref('1')
const { landingPageSuffixMap, monitorLinkMap, centralMonitorDomain } = useUrlConfig()

const pageList = ref([])
const showGoodsUrl = async(goods) => {
  pageList.value = []
  if (!props.isPddCashGift) {
    getLandingPageList(goods)
  }

  // if (goods.mediaPlatformType === 4) fetchTxUrl(goods)
  if (goods.platform === '4' || isTx(goods.mediaPlatformType)) {
    if (props.isPddCashGift) {
      await fetchTxUrlPddCashgift(goods)
    } else {
      await fetchTxUrl(goods)
      await getQrCode(goods)
    }
  }
}

function getLandingPageList(goods) {
  const postData = {
    goodsId: isJd(props.goods.platform) ? goods.goodsSign : goods.goodsId,
    mediaType: goods.mediaPlatformType,
    platform: goods.platform,
    pageNum: 1,
    pageSize: 20
  }
  listLanding(postData).then(res => {
    if (res.code === 200) {
      pageList.value = res.rows.map(item => {
        let filePath = item.filePath
        if (isTbPro(goods.platform) || isJdPro(goods.platform) || isDjk(goods.platform)) {
          filePath = item.filePath ? item.filePath + new URL(goods.url).search : ''
        } else {
          filePath = item.filePath ? item.filePath + (landingPageSuffixMap[media.value] || '') : ''
        }
        return {
          ...item,
          filePath
        }
      })
      let landing = ''
      pageList.value.forEach(item => {
        landing += `
链接: ${item.filePath}
          `
      })
      urlMap.landingUrl = landing

      copyAllUrl()
    }
  })
}

const txOriginalUrlVO = ref('')
const txAppletUrlVO = ref('')
const txAppletUrlOldVO = ref('')
const weappQrcodeUrl = ref('')
function fetchTxUrl(goods) {
  generateTxUrl(goods.id).then(res => {
    txOriginalUrlVO.value = res.data.txOriginalUrlVO
    txAppletUrlVO.value = res.data.txAppletUrlVO
    txAppletUrlOldVO.value = res.data.txAppletUrlOldVO
  })
}
function fetchTxUrlPddCashgift(goods) {
  generateTxUrlPddCashgift(goods.id).then(res => {
    txOriginalUrlVO.value = res.data.txOriginalUrlVO
    txAppletUrlVO.value = res.data.txAppletUrlVO
    txAppletUrlOldVO.value = res.data.txAppletUrlOldVO
  })
}
// 获取二维码
function getQrCode(goods) {
  getTxQrCode(goods.id).then(res => {
    if (res.code === 200) {
      weappQrcodeUrl.value = res.msg || ''
    }
  })
}
function addTimestamp(url) {
  if (!url) return ''
  return parseParams(url, {
    t: new Date().getTime()
  })
}

function clipboardSuccess() {
  Message.success('复制成功')
}

const urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/
function replaceMonitorLink(url) {
  if (props.goods.platform === '1' && isXhs(media.value)) return url
  if (url && props.goods.url && props.goods.url.includes('%22ckId%22%3A') && centralMonitorDomain.value) {
    const host = urlReg.exec(url)
    return url.replace(host[0], centralMonitorDomain.value)
  }
  return url
}

const urlMap = reactive({
  landingUrl: '',
  dpUrl: '',
  monitorUrl: '',
  oneJumpUrl: '',
  weAppUrl: ''
})
const initUrlMap = () => {
  const goods = props.goods
  let landing = ''
  pageList.value.forEach(item => {
    landing += `
链接: ${item.filePath}
          `
  })
  urlMap.landingUrl = landing
  urlMap.dpUrl = goods.dpUrl
  urlMap.monitorUrl = parseParams(monitorLinkMap[media.value], { goodsId: goods.goodsId, platform: goods.platform })
  if (hasCkID.value) { urlMap.monitorUrl = replaceMonitorLink(urlMap.monitorUrl) }

  urlMap.oneJumpUrl = goods.url
  urlMap.weAppUrl = goods.weAppUrl ? goods.weAppUrl : ''
  urlMap.exposureMonitorUrl = goods.exposureMonitorUrl
  urlMap.clickMonitorUrl = goods.clickMonitorUrl
}

const hasCkID = computed(() => {
  return props.goods.platform === '3' || (props.goods.url && props.goods.url.includes('%22ckId%22%3A'))
})

const showMonitorTooltip = computed(() => {
  if (isJd(props.goods.platform) && isKs(media.value)) return true
  return !hasCkID.value
})

const allUrl = ref('')
function copyAllUrl() {
  const platform = props.goods.platform
  if (isUDSmart(platform) || isTbPro(platform) || isJdPro(platform)) {
    allUrl.value = `商品名称：${props.goods.goodsName}
商品ID：${props.goods.goodsId}

二跳落地页: ${urlMap.landingUrl}

直达链接: ${urlMap.dpUrl}

一跳落地页: ${urlMap.oneJumpUrl}

微信直达链接: ${urlMap.weAppUrl}

曝光监测链接: ${urlMap.exposureMonitorUrl}

点击监测链接: ${urlMap.clickMonitorUrl}`
  } else {
    allUrl.value = `商品名称：${props.goods.goodsName}
商品ID：${props.goods.goodsId}

二跳落地页: ${urlMap.landingUrl}

直达链接: ${urlMap.dpUrl}

一跳落地页: ${urlMap.oneJumpUrl}

${(isPdd(platform) || isJd(platform)) ? `
微信直达链接: ${urlMap.weAppUrl}
` : ''}

触点监测链接: ${urlMap.monitorUrl}`
  }
}

const openLanding = () => {
  emit('openLanding', props.goods)
}

onMounted(() => {
  showGoodsUrl(props.goods)
  initUrlMap()
})
// 个人化复制
const personalCopyVisible = ref(false)
function openPersonalCopy() {
  personalCopyVisible.value = true
}

function replaceDjkWechat(url) {
  return url.replace('&site_set=__SITE_SET__', '').replace('&android_id=__HASH_ANDROID_ID__', '')
}

function replaceDjkWeb(url) {
  return url.replace('&site_set_name=__SITE_SET_NAME__', '')
    .replace('&oaidMd5=__HASH_OAID__', '')
    .replace('&site_set=__SITE_SET__', '')
    .replace('&muid=__MUID__', '')
    .replace('&android_id=__HASH_ANDROID_ID__', '')
    .replace('&mac=__QAID_CAA__', '')
}
// 下载文本文件
function downloadUrlAsText() {
  const fileName = `${props.goods.goodsName + '商品链接'}.txt`
  // 创建Blob对象
  const blob = new Blob([allUrl.value], { type: 'text/plain;charset=utf-8' })
  // 创建下载链接
  const downloadLink = document.createElement('a')
  downloadLink.href = URL.createObjectURL(blob)
  downloadLink.download = fileName.replace(/[\\/:*?"<>|]/g, '_') // 替换文件名中的非法字符
  // 添加到DOM并触发点击
  document.body.appendChild(downloadLink)
  downloadLink.click()
  // 清理
  document.body.removeChild(downloadLink)
  URL.revokeObjectURL(downloadLink.href)
  Message.success('下载成功')
}
</script>

<template>
  <div class="mb20">
    <el-tooltip class="item" effect="dark" :content="adTooltip" placement="top" :disabled="isAdSelected">
      <el-button v-if="showPersonalBtn&&goods.platform === '4' " class="personalBtn" size="mini" type="primary" @click="openPersonalCopy">
        个人化复制
      </el-button>
    </el-tooltip>

    <div style="border: 1px solid #DCDFE6;padding: 10px;margin-bottom: 10px">商品名：{{ goods.goodsName }}</div>

    <el-tabs v-if="isTx(media)" v-model="activeUrlType">
      <el-tab-pane label="原始推广页 " name="1">
        <el-form label-width="110px">
          <div style="padding: 0 10px">
            <el-form-item label="一跳落地页">
              <url-clipboard :url="txOriginalUrlVO.landingPageUrl" />
            </el-form-item>
            <el-form-item label="苹果ID">
              <url-clipboard :url="txOriginalUrlVO.iosId" not-redirect />
            </el-form-item>
            <el-form-item label="安卓ID">
              <url-clipboard :url="txOriginalUrlVO.androidId" not-redirect />
            </el-form-item>
            <el-form-item label="直达链接">
              <url-clipboard :url="txOriginalUrlVO.dpUrl" />
            </el-form-item>
            <el-form-item label="小程序监测链接">
              <url-clipboard :url="parseParams(txOriginalUrlVO.monitorUrl, {goodsId: goods.goodsId})" />
            </el-form-item>
            <el-form-item label="网页监测链接">
              <url-clipboard :url="parseParams(txOriginalUrlVO.monitorUrl, {goodsId: goods.goodsId}) + '&csite=__SITE_SET_NAME__'" />
            </el-form-item>
            <el-form-item v-if="isTbPro(goods.platform) || isJdPro(goods.platform)" label="曝光监测链接">
              <url-clipboard :url="txOriginalUrlVO.exposureMonitorUrl" />
            </el-form-item>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="新版-转化" name="2">
        <el-form label-width="100px">
          <div style="padding: 0 10px">
            <el-form-item label="小程序ID">
              <url-clipboard :url="txAppletUrlVO.appletId" not-redirect />
            </el-form-item>
            <el-form-item label="点击监测链接">
              <url-clipboard :url="parseParams(txAppletUrlVO.monitorUrl, {goodsId: goods.goodsId})" />
            </el-form-item>
            <el-form-item v-if="isJdPro(goods.platform)" label="曝光监测链接">
              <url-clipboard :url="txAppletUrlVO.exposureMonitorUrl" />
            </el-form-item>
            <el-form-item label="原始ID">
              <url-clipboard :url="txAppletUrlVO.originalId" not-redirect />
            </el-form-item>
            <el-form-item label="小程序URL">
              <url-clipboard :url="txAppletUrlVO.appletUrl" not-redirect />
            </el-form-item>
            <el-form-item v-if="weappQrcodeUrl" label="小程序预览码">
              <el-popover
                placement="right"
                :width="200"
                trigger="hover"
              >
                <div class="wxQrcode">
                  <div class="reference_title">扫码预览</div>
                  <div>
                    <el-image
                      style="width: 150px; height: 150px"
                      :src="weappQrcodeUrl"
                      :preview-src-list="[weappQrcodeUrl]"
                    />
                  </div>
                  <div class="reference_tips">手机微信扫码预览</div>
                </div>
                <template slot="reference">
                  <span class="wxQrcode_text">预览</span>
                </template>
              </el-popover>

            </el-form-item>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane v-if="!isPddCashGift" label="落地页" name="3">
        <!--        <el-form label-width="100px">-->
        <!--          <div style="padding: 0 10px">-->
        <!--            <el-form-item label="落地页链接">-->
        <!--              <url-clipboard :url="landingAddTimestamp(goods.landingPageUrl, goods.mediaPlatformType)" />-->
        <!--            </el-form-item>-->
        <!--          </div>-->
        <!--        </el-form>-->
        <el-table height="100%" :data="pageList" size="mini">
          <el-table-column v-if="adSelectedVisible" label="广告主ID" prop="advertiserId" width="100" show-overflow-tooltip />
          <el-table-column label="名称" prop="title" show-overflow-tooltip />
          <el-table-column label="备注" prop="remark" show-overflow-tooltip width="240" />
          <el-table-column label="地址" prop="filePath" width="80" align="center">
            <template #default="scope">
              <a class=" hover-text" style="color: #1890ff" target="_blank" :href="scope.row.filePath">预览</a>
              <el-tooltip class="item" effect="dark" content="非投放地址" placement="top"> <i class="el-icon-question" /></el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="90">
            <template #default="scope">
              <el-tooltip class="item" effect="dark" :content="adTooltip" placement="top" :disabled="isAdSelected">
                <el-button v-clipboard:copy="scope.row.filePath" v-clipboard:success="clipboardSuccess" :disabled="!isAdSelected" type="text" size="small">
                  <i class="el-icon-document-copy" />复制
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <template v-else>
      <div class="link-info">
        <div class="flex1 overflow-text">
          <el-form v-if="adSelectedVisible" ref="formRef" label-width="100px" size="mini">
            <el-form-item label="媒体账户" prop="advertiserId" :rules="[{ required: true, message: '请选择媒体账户', trigger: 'blur' }]">
              <el-select
                v-model="advertiserId"
                filterable
                remote
                clearable
                placeholder="请选择"
                :remote-method="requestRemote"
                style="width: 200px"
                :loading="selectLoading"
                @change="handleAdIdChange"
              >
                <el-option
                  v-for="item in mediaAccountOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-tooltip class="item" effect="dark" :content="adTooltip" placement="top">
                <i class="el-icon-question text-danger ml5" />
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-form v-if="isBd(media) && dicts.report_event_baidu" ref="formRef" label-width="100px" size="mini">
            <el-form-item label="回传事件" prop="advertiserId" :rules="[{ required: true, message: '请选择回传事件', trigger: 'blur' }]">
              <el-select
                v-model="reportEvents"
                clearable
                multiple
                collapse-tags
                placeholder="请选择"
                style="width: 200px"
                @change="handleReportEventChange"
              >
                <el-option
                  v-for="item in dicts.report_event_baidu"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex align-center">
          <el-tooltip class="item" effect="dark" content="复制的链接可能因聊天软件(如微信)的字数限制而不完整，可以选择下载文本文件进行传输" placement="top">
            <i class="el-icon-warning text-danger mr5" style="font-size: 18px" />
          </el-tooltip>
          <el-button icon="el-icon-download" size="mini" :disabled="!isAdSelected" @click="downloadUrlAsText">下载链接文件</el-button>
          <el-tooltip class="item" effect="dark" :disabled="isAdSelected" :content="isAdSelected ? '' : adTooltip" placement="top">
            <el-button v-if="!isZh(media)" v-clipboard:copy="allUrl" v-clipboard:success="clipboardSuccess" size="mini" type="primary" :disabled="!isAdSelected">
              复制全部链接
            </el-button>
          </el-tooltip>
        </div>

      </div>
      <el-form label-width="100px">
        <div style="padding: 0 10px">
          <div v-if="!isPddCashGift" style="font-weight: bold;" class="mb5">
            二跳落地页 <span v-if="goods.platform==='3'" class="color-danger" style="padding:0 5px">提示: 推荐做二跳，没有app时，可以丝滑打开小程序</span>
            <el-button type="text" size="mini" @click="openLanding">创建落地页</el-button>
          </div>
          <div v-if="!isPddCashGift" style="height: 250px">
            <el-table height="100%" :data="pageList" size="mini">
              <el-table-column v-if="adSelectedVisible" label="广告主ID" prop="advertiserId" width="100" show-overflow-tooltip />
              <el-table-column label="名称" prop="title" show-overflow-tooltip />
              <el-table-column label="备注" prop="remark" show-overflow-tooltip width="240" />
              <el-table-column label="地址" prop="filePath" width="80" align="center">
                <template #default="scope">
                  <a class=" hover-text" style="color: #1890ff" target="_blank" :href="scope.row.filePath">预览</a>
                  <el-tooltip class="item" effect="dark" content="非投放地址" placement="top"> <i class="el-icon-question" /></el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="90">
                <template #default="scope">
                  <el-tooltip class="item" effect="dark" :content="adTooltip" placement="top" :disabled="isAdSelected">
                    <el-button v-clipboard:copy="scope.row.filePath" v-clipboard:success="clipboardSuccess" :disabled="!isAdSelected" type="text" size="small">
                      <i class="el-icon-document-copy" />复制
                    </el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <template v-if="isDjk(goods.platform) ">
            <el-form>
              <el-form-item label="云台落地页">
                <url-clipboard :url="goods.url" />
              </el-form-item>
              <el-form-item label="直达链接">
                <el-tooltip class="item" effect="dark" :content="adTooltip" :disabled="isAdSelected" placement="top">
                  <url-clipboard :url="urlMap.dpUrl" :disabled="!isAdSelected" />
                </el-tooltip>
              </el-form-item>
              <el-form-item label="曝光监测链接">
                <url-clipboard :url="goods.exposureMonitorUrl" />
              </el-form-item>
              <template v-if="isTx(media)">
                <el-form-item label="小程序监测链接">
                  <url-clipboard :url="replaceDjkWechat(goods.clickMonitorUrl)" />
                </el-form-item>
                <el-form-item label="网页监测链接">
                  <url-clipboard :url="replaceDjkWeb(goods.clickMonitorUrl)" />
                </el-form-item>
              </template>
              <el-form-item v-else label="点击监测链接">
                <url-clipboard :url="goods.clickMonitorUrl" />
              </el-form-item>
            </el-form>
          </template>
          <div v-else-if="isUDSmart(goods.platform)">
            <el-form>
              <el-form-item label="一跳落地页">
                <url-clipboard :url="goods.url" />
              </el-form-item>
              <el-form-item label="直达链接">
                <el-tooltip class="item" effect="dark" :content="adTooltip" :disabled="isAdSelected" placement="top">
                  <url-clipboard :url="urlMap.dpUrl" :disabled="!isAdSelected" />
                </el-tooltip>
              </el-form-item>
              <el-form-item label="曝光监测链接">
                <url-clipboard :url="goods.exposureMonitorUrl" />
              </el-form-item>

              <el-form-item label="点击监测链接">
                <url-clipboard :url="goods.clickMonitorUrl" />
              </el-form-item>
            </el-form>
          </div>
          <template v-else-if="isTbPro(goods.platform) || isJdPro(goods.platform)">
            <el-form>
              <el-form-item label="一跳落地页">
                <url-clipboard :url="goods.url" />
              </el-form-item>
              <el-form-item label="直达链接">
                <el-tooltip class="item" effect="dark" :content="adTooltip" :disabled="isAdSelected" placement="top">
                  <url-clipboard :url="urlMap.dpUrl" :disabled="!isAdSelected" />
                </el-tooltip>
              </el-form-item>
              <el-form-item label="微信直达链接">
                <url-clipboard :url="urlMap.weAppUrl" />
              </el-form-item>
              <el-form-item label="点击监测链接">
                <div class="block-danger">
                  <url-clipboard :url="goods.clickMonitorUrl" />
                  <div class="color-danger">提示：当前商品广告必须配置该触点监测链接。</div>
                </div>
              </el-form-item>
              <el-form-item label="曝光监测链接">
                <url-clipboard :url="goods.exposureMonitorUrl" />
              </el-form-item>
            </el-form>
          </template>
          <div v-else-if="!isZh(media)">
            <el-form-item label="直达链接">
              <el-tooltip class="item" effect="dark" :content="adTooltip" :disabled="isAdSelected" placement="top">
                <url-clipboard :url="urlMap.dpUrl" :disabled="!isAdSelected" />
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="goods.platform==='1'||goods.platform==='3'" label="微信直达链接">
              <el-tooltip class="item" effect="dark" :content="adTooltip" :disabled="isAdSelected" placement="top">
                <url-clipboard :url="urlMap.weAppUrl" :disabled="!isAdSelected" />
              </el-tooltip>
              <div v-if="goods.platform==='1'" class="color-danger">此链接须搭配触点监测链接</div>
            </el-form-item>
            <el-form-item label="触点监测链接">
              <div :class="{'block-danger': showMonitorTooltip}">
                <el-tooltip class="item" effect="dark" :content="adTooltip" :disabled="isAdSelected" placement="top">
                  <url-clipboard :url="urlMap.monitorUrl" :disabled="!isAdSelected" />
                </el-tooltip>
                <div v-if="showMonitorTooltip" class="color-danger">提示：当前商品广告必须配置该触点监测链接。</div>
              </div>
            </el-form-item>
            <el-form-item label="一跳落地页">
              <el-tooltip class="item" effect="dark" :content="adTooltip" placement="top" :disabled="isAdSelected">
                <url-clipboard :url="urlMap.oneJumpUrl" :disabled="!isAdSelected" />
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="goods.platform==='2'" label="一跳过审链接" class="form-label-info">
              <div class="block-info">
                <url-clipboard font-color="#C0C4CC" :url="'https://mo.m.taobao.com/union/direct-sale/uland3_flow?itemId=' + goods.goodsId" />
                <div class="color-placeholder">提示：该链接无法回传。</div>
              </div>
            </el-form-item>
            <el-form-item v-if="goods.platform==='4'" label="商品链接">
              <url-clipboard :url="goods.goodsLink" />
            </el-form-item>
            <template v-if="goods.platform === '4'">
              <el-form-item label="原始ID">
                <url-clipboard :url="txAppletUrlOldVO.originalId" not-redirect />
              </el-form-item>
              <el-form-item label="小程序URL">
                <url-clipboard :url="txAppletUrlOldVO.appletUrl" not-redirect />
              </el-form-item>
            </template>
          </div>
        </div>
      </el-form>
    </template>
    <!-- 个人化复制 -->
    <personalCopy :visible.sync="personalCopyVisible" :one-jump-url="urlMap&&urlMap.oneJumpUrl" />
  </div>
</template>

<style scoped lang="scss">
.link-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
}
::v-deep .el-form-item {
  margin-bottom: 0;
}
.form-label-info ::v-deep .el-form-item__label{
  color: #C0C4CC;
}
.personalBtn{
  position: absolute;
  top: 18px;
  left: 110px;
}
.wxQrcode{
  min-height: 200px;
  width: 174px;
  text-align: center;
  .reference_title{
    font-size: 14px;
    color: #222;
    margin-bottom: 8px;
  }
  .reference_tips{
    margin-top: 8px;
    font-size: 14px;
    color: #b4b4b4;
  }
}
.wxQrcode_text{
  color: #1890ff;
  cursor: pointer;
}
</style>
