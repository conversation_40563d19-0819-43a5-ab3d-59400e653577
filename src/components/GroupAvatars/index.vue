<template>
  <div class="group-avatars" :class="{ [`size-${size}`]: size, [`layout-${layout}`]: layout }">
    <img
      v-for="(avatar, index) in displayAvatars"
      :key="index"
      :src="avatar"
      :alt="`群成员头像${index + 1}`"
      class="group-avatar"
      @error="handleImageError"
    >
    <div v-if="showMoreCount" class="avatar-more">
      +{{ moreCount }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import profileImg from '/src/assets/images/profile.png'

const props = defineProps({
  // 头像数组
  avatars: {
    type: Array,
    default: () => []
  },
  // 最大显示数量
  maxDisplay: {
    type: Number,
    default: 3
  },
  // 尺寸大小：small(20px), default(24px), medium(32px), large(40px)
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'medium', 'large'].includes(value)
  },
  // 布局模式：flex(默认横向排列), grid(网格布局)
  layout: {
    type: String,
    default: 'flex',
    validator: (value) => ['flex', 'grid'].includes(value)
  },
  // 默认头像
  defaultAvatar: {
    type: String,
    default: profileImg
  }
})

// 显示的头像列表
const displayAvatars = computed(() => {
  const validAvatars = props.avatars.map(img => img || props.defaultAvatar)
  return validAvatars.slice(0, props.maxDisplay)
})

// 是否显示更多数量
const showMoreCount = computed(() => {
  return props.avatars.length > props.maxDisplay
})

// 更多数量
const moreCount = computed(() => {
  return props.avatars.length - props.maxDisplay
})

// 图片加载错误处理
const handleImageError = (event) => {
  if (props.defaultAvatar) {
    event.target.src = props.defaultAvatar
  } else {
    // 使用默认的占位符
    event.target.style.display = 'none'
  }
}
</script>

<style lang="scss" scoped>
.group-avatars {
  display: flex;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;

  .group-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #e4e7ed;
    background: #f5f7fa;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
      z-index: 1;
      position: relative;
    }
  }

  .avatar-more {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #909399;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      background: #e4e7ed;
      color: #606266;
    }
  }

  // 不同尺寸
  &.size-small {
    .group-avatar,
    .avatar-more {
      width: 20px;
      height: 20px;
    }

    .avatar-more {
      font-size: 9px;
    }
  }

  &.size-medium {
    gap: 3px;

    .group-avatar,
    .avatar-more {
      width: 32px;
      height: 32px;
    }

    .avatar-more {
      font-size: 12px;
    }
  }

  &.size-large {
    gap: 4px;

    .group-avatar,
    .avatar-more {
      width: 40px;
      height: 40px;
    }

    .avatar-more {
      font-size: 14px;
    }
  }

  // 网格布局
  &.layout-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 2px;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid #e4e7ed;
    width: 80px;
    height: 80px;

    .group-avatar {
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 0;

      &:hover {
        transform: none;
      }
    }

    .avatar-more {
      background: rgba(0, 0, 0, 0.6);
      color: white;
      border: none;
      border-radius: 0;
      font-weight: 500;

      &:hover {
        background: rgba(0, 0, 0, 0.8);
        color: white;
      }
    }

    &.size-small {
      width: 60px;
      height: 60px;
    }

    &.size-medium {
      width: 100px;
      height: 100px;
    }

    &.size-large {
      width: 120px;
      height: 120px;
    }
  }
}
</style>
