<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { updateMediaProfit } from '@/api/promotion/media'
import { Message } from 'element-ui'
import { updateGoodsProfit } from '@/api/promotion/goods'
const props = defineProps({
  visible: Boolean,
  items: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'media' // goods
  }
})
const emit = defineEmits(['success'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

watch(() => props.visible, (val) => {
  if (val) {
    if (props.items.length > 1) {
      profitForm.profit = undefined
      profitForm.profitRoi = undefined
      profitForm.profitBackRate = undefined
    } else {
      const item = props.items[0]
      profitForm.profit = item.profit || undefined
      profitForm.profitRoi = item.profitRoi || undefined
      profitForm.profitBackRate = item.profitBackRate ? item.profitBackRate / 10 : undefined
    }
  }
})

const profitLoading = ref(false)

const profitForm = reactive({
  profit: undefined,
  profitRoi: undefined,
  profitBackRate: undefined
})

const submit = () => {
  let request = () => {}
  switch (props.type) {
    case 'media':
      request = updateMediaProfit
      break
    case 'goods':
      request = updateGoodsProfit
      break
  }
  profitLoading.value = true
  request({
    ids: props.items.map(item => item.id),
    profit: profitForm.profit,
    profitRoi: profitForm.profitRoi,
    profitBackRate: profitForm.profitBackRate * 10
  }).then(response => {
    Message.success('设置成功')
    emit('success')
    close()
  }).finally(() => {
    profitLoading.value = false
  })
}

const close = () => {
  open.value = false
}
</script>

<template>
  <el-dialog
    title="设置盈亏参数"
    :visible.sync="open"
    width="500px"
    top="10vh"
    append-to-body
  >
    <el-form ref="form" :model="profitForm" label-width="68px">
      <el-form-item label="名称" prop="name">
        <template v-if="items.length > 1">
          批量设置 <span class="color-primary">{{ items.length }}</span> 条数据
        </template>
        <template v-else-if="items.length === 1">
          {{ type === 'media' ? items[0].advertiserName : items[0].goodsName }}
        </template>
      </el-form-item>
      <el-form-item label="盈亏成本" prop="profit">
        <el-input-number v-model="profitForm.profit" :controls="false" :min="0" />
        <el-tooltip effect="dark" content="盈亏成本 = 拿货成本 + 运费 + 其它非广告消耗成本" placement="top">
          <i class="el-icon-question color-primary ml5" />
        </el-tooltip>
      </el-form-item>
      <el-form-item label="盈亏ROI" prop="profitRoi">
        <el-input-number v-model="profitForm.profitRoi" :controls="false" :min="1" />
        <el-tooltip effect="dark" content="设置预估的保本ROI" placement="top">
          <i class="el-icon-question color-primary ml5" />
        </el-tooltip>
      </el-form-item>
      <el-form-item label="充值返点" prop="profitBackRate">
        <el-input-number v-model="profitForm.profitBackRate" :controls="false" :min="0" :max="100" :precision="1" />
        <span class="ml5">%</span>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button v-loading="profitLoading" type="primary" @click="submit">确认</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
