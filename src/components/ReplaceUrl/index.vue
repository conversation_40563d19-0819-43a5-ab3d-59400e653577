<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { validURL } from '@/utils/validate'
import { upMonitor, batchChangeMonitorUrl } from '@/api/promotion/media'
import { Message } from 'element-ui'
import { generateTxUrl, listGoods } from '@/api/promotion/goods'
import { parseParams } from '@/utils'
import useUrlConfig from '@/components/URLGetter/useUrlConfig'
import { isDjk, isUDSmart, isTbPro, isJdPro } from '@/utils/judge/platform'

const { monitorLinkMap, centralMonitorDomain } = useUrlConfig()

const props = defineProps({
  visible: Boolean,
  form: {
    type: Object,
    default: () => ({})
  },
  selections: {
    type: Array,
    default: () => []
  },
  mediaType: {
    type: [Number, String],
    default: 1
  },
  batch: Boolean,
  // 是否来源于替换日志
  isFormLog: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['close'])
const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})
watch(() => props.visible, (val) => {
  if (val) {
    _mediaType.value = +props.mediaType
    reset()
  }
})

const goodsPlatform = computed(() => {
  return props.form.goodsPlatform
})

const _mediaType = ref(1)
const urlFormRef = ref(null)
const urlForm = reactive({
  url: [''],
  renewalUrl: '',
  exposureRenewalUrl: '',
  externalUrl: ''
})
const appletUrl = ref('')

const validateUrl = (rule, value, callback) => {
  if (value) {
    if (validURL(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的链接'))
    }
  } else {
    callback()
  }
}
const urlFormRules = {
  renewalUrl: [
    { validator: validateUrl, trigger: 'blur' }
  ]
  // externalUrl: [
  //   { validator: validateUrl, trigger: 'blur' }
  // ]
}

const replaceUrlLoading = ref(false)
const handleUpMonitor = (postData = {}) => {
  postData.url = postData.url.filter(i => i)

  if (props.isFormLog) {
    const data = {
      advertiserId: [props.selections[0].advertiserId],
      mediaPlatformType: _mediaType.value,
      ...postData,
      planIds: props.selections.map(item => item.businessId),
      traceId: props.selections[0].traceId
    }
    replaceUrlLoading.value = true
    batchChangeMonitorUrl(data).then(data => {
      if (data.code === 200) {
        Message.success(data.msg)
        emit('close', true)
        open.value = false
      }
    }).finally(() => {
      replaceUrlLoading.value = false
    })
  } else {
    const data = {
      advertiserId: props.batch ? props.selections.map(item => item.advertiserId) : [props.form.advertiserId],
      mediaPlatformType: _mediaType.value,
      ...postData
    }
    replaceUrlLoading.value = true
    upMonitor(data).then(data => {
      if (data.code === 200) {
        Message.success(data.msg)
        close()
      }
    }).finally(() => {
      replaceUrlLoading.value = false
    })
  }
}

const submit = () => {
  if (_mediaType.value === 1) {
    urlFormRef.value.validate(valid => {
      if (valid) {
        if (!urlForm.url.filter(i => i).length && !urlForm.renewalUrl && !urlForm.externalUrl && !urlForm.exposureRenewalUrl) {
          Message.error('请填写至少一项链接。')
          return
        }
        handleUpMonitor(urlForm)
      }
    })
  } else if (_mediaType.value === 4) {
    if (appletUrl.value === '') {
      Message.error('请填写小程序链接。')
      return
    }
    handleUpMonitor({ url: [appletUrl.value] })
  }
}

// 直达链接组
const addMore = () => {
  urlForm.url.push('')
}
const removeUrl = (i) => {
  urlForm.url.splice(i, 1)
}

const queryParams = reactive({
  goodsId: ''
})
const searchGoods = ref(null)
const searchLoading = ref(false)

const handleQuery = () => {
  if (!queryParams.goodsId) {
    Message.error('请输入商品ID')
    return
  }
  urlFormRef.value.clearValidate()
  searchLoading.value = true
  listGoods({ ...queryParams, mediaPlatformType: _mediaType.value }).then(response => {
    if (response.rows.length === 1) {
      const goods = response.rows[0]
      searchGoods.value = goods
      if (_mediaType.value === 1) {
        urlForm.url[0] = goods.dpUrl
        urlForm.externalUrl = goods.url
        if (goods.url && goods.url.includes('%22ckId%22%3A') && centralMonitorDomain.value) {
          urlForm.renewalUrl = replaceMonitorLink(parseParams(monitorLinkMap[goods.mediaPlatformType], { goodsId: goods.goodsId, platform: goods.platform }))
        } else {
          urlForm.renewalUrl = parseParams(monitorLinkMap[goods.mediaPlatformType], { goodsId: goods.goodsId, platform: goods.platform })
        }
        if (goods.exposureMonitorUrl) urlForm.exposureRenewalUrl = goods.exposureMonitorUrl
        searchLoading.value = false
      }
      if (_mediaType.value === 4) {
        generateTxUrl(goods.id).then(res => {
          appletUrl.value = res.data.txAppletUrlVO.appletUrl
          searchLoading.value = false
        })
      }
      if (isDjk(goods.platform) || isUDSmart(goods.platform) || isTbPro(goods.platform) || isJdPro(goods.platform)) {
        if (goods.clickMonitorUrl) urlForm.renewalUrl = goods.clickMonitorUrl
      }
    } else {
      searchGoods.value = null
      searchLoading.value = false
      Message.warning('未找到商品，请确认商品ID是否正确')
    }
  })
}

const urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/
function replaceMonitorLink(url) {
  const host = urlReg.exec(url)
  return url.replace(host[0], centralMonitorDomain.value)
}
const close = () => {
  emit('close')
  open.value = false
}

const reset = () => {
  searchGoods.value = null
  queryParams.goodsId = ''
  urlForm.url = ['']
  urlForm.renewalUrl = ''
  urlForm.exposureRenewalUrl = ''
  urlForm.externalUrl = ''
  appletUrl.value = ''
  if (urlFormRef.value) { urlFormRef.value.clearValidate() }
}

</script>

<template>
  <el-dialog
    title="替换链接"
    :visible.sync="open"
    width="50%"
    top="10vh"
    append-to-body
  >

    <el-form v-if="_mediaType ===1" ref="urlFormRef" :model="urlForm" :rules="urlFormRules" size="small" label-width="100px">
      <el-form-item label="一跳落地页" prop="externalUrl">
        <el-input
          v-model="urlForm.externalUrl"
          placeholder="请输入一跳落地页"
          clearable
        />
        <span style="color: #f4516c; display: flex; align-items: center;">
          <i class="el-icon-warning-outline" style="margin-right: 5px;" />
          注意：该操作将替换账户下所有广告的落地页！
        </span>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button :loading="replaceUrlLoading" type="primary" @click="submit">确 定</el-button>
      <el-button @click="close()">取 消</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">
.remove-icon{
  color: #ff4949;
  margin:0 10px;
  font-size: 16px;
  line-height: 16px;
  padding: 10px 0;
}
</style>
