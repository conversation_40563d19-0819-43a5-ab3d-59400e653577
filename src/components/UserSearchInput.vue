<script setup>
import TransferUser from '@/components/TransferUser/index.vue'
import { computed, ref } from 'vue'
import { getUserName } from '@/api/system/user'
import { checkPermi } from '@/utils/permission'

const props = defineProps({
  createBy: {
    type: String,
    default: ''
  },
  propsKey: {
    type: String,
    default: 'createBy'
  },
  placeholder: {
    type: String,
    default: '负责人'
  }
})
const emit = defineEmits(['query'])
const user = computed({
  get: () => props.createBy,
  set: (str) => emit('update:createBy', str)
})

const handleQuery = () => {
  emit('query')
}

const userVisible = ref(false)
const showUserSearch = () => {
  userVisible.value = true
}
const handleSelect = (userId) => {
  getUserName(userId).then(res => {
    user.value = res.msg
    userVisible.value = false
    handleQuery()
  })
}
</script>

<template>
  <div class="search-input">
    <el-form-item :prop="propsKey">
      <el-input
        v-model.trim="user"
        :placeholder="placeholder"
        clearable
        @keyup.enter.native="handleQuery"
        @clear="handleQuery"
      >
        <el-button v-if="checkPermi(['system:user:query'])" slot="append" icon="el-icon-search" @click="showUserSearch" />
      </el-input>
    </el-form-item>
    <el-dialog
      :title="'选择搜索'+placeholder"
      :visible.sync="userVisible"
      width="75%"
      top="10vh"
      append-to-body
    >
      <TransferUser :confirm="false" @select="handleSelect" />
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.search-input {
  display: inline-block;

}
</style>
