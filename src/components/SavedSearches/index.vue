<template>
  <div class="saved-searches">
    <el-dropdown split-button :size="size" type="primary" trigger="click" @click="handleSearch" @command="handleCommand">
      <i class="el-icon-search" /> 搜索
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in savedSearches"
          :key="item.id"
          :command="{type: 'load', search: item}"
        >
          <div class="search-item">
            <i
              v-if="item.isDefault"
              class="el-icon-star-on"
              style="color: #E6A23C; margin-right: 8px;"
              @click.stop="setDefault(item)"
            />
            <i
              v-else
              class="el-icon-star-off"
              style="margin-right: 8px;"
              @click.stop="setDefault(item)"
            />
            <span>{{ item.name }}</span>
            <div class="search-item-actions">

              <i
                class="el-icon-delete"
                style="margin-left: 8px;"
                @click.stop="handleDelete(item)"
              />
            </div>
          </div>
        </el-dropdown-item>
        <el-dropdown-item
          :divided="savedSearches.length > 0"
          :command="{type: 'save'}"
        >
          保存当前搜索
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <!-- Save Dialog -->
    <el-dialog
      title="保存搜索"
      :visible.sync="dialogVisible"
      append-to-body
      width="30%"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        @submit.native.prevent
      >
        <el-form-item label="搜索名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入搜索名称"
            @keyup.enter.native.prevent="saveSearch"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveSearch">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SavedSearches',
  props: {
    value: {
      type: Object,
      required: true
    },
    storageKey: {
      type: String,
      default() {
        return `${this.$route.name}`.toLowerCase()
      }
    },
    size: {
      type: String,
      default: 'small'
    }
  },
  data() {
    return {
      dialogVisible: false,
      savedSearches: [],
      form: {
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入搜索名称', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    extraParams() {
      const result = {}
      Object.keys(this.$attrs).forEach(key => {
        if (key.startsWith('extra-')) {
          const paramKey = key.replace('extra-', '')
          result[paramKey] = this.$attrs[key]
        }
      })
      return result
    },
    StorageKey() {
      return `${this.storageKey}_saved_searches`.toLowerCase()
    }
  },
  created() {
    this.loadSavedSearches()
  },
  methods: {
    handleSearch() {
      this.$emit('search')
    },
    loadSavedSearches() {
      const searches = localStorage.getItem(this.StorageKey)
      this.savedSearches = searches ? JSON.parse(searches) : []
      this.loadDefaultSearch()
    },
    handleCommand(command) {
      if (command.type === 'load') {
        this.loadParams(command)
        this.$emit('search')
      } else if (command.type === 'save') {
        this.dialogVisible = true
      }
    },
    loadParams(command) { // 合并默认参数和保存的参数
      const params = {
        ...this.value,
        ...command.search.params
      }
      // 加载额外参数
      if (command.search.extraParams) {
        Object.keys(command.search.extraParams).forEach(key => {
          this.$emit(`update:extra${key.charAt(0).toUpperCase() + key.slice(1)}`, command.search.extraParams[key])
        })
      }
      // 保持分页参数
      params.pageNum = this.value.pageNum || 1
      params.pageSize = this.value.pageSize || 10
      this.$emit('input', params)
    },
    saveSearch() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const search = {
            id: Date.now(),
            name: this.form.name,
            isDefault: false,
            params: this.processParams(this.value),
            extraParams: this.processParams(this.extraParams)
          }

          this.savedSearches.push(search)
          localStorage.setItem(this.StorageKey, JSON.stringify(this.savedSearches))

          this.dialogVisible = false
          this.form.name = ''

          this.$message.success('保存成功')
        }
      })
    },
    handleDelete(search) {
      this.$confirm('确认删除该搜索条件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.savedSearches.findIndex(item => item.id === search.id)
        this.savedSearches.splice(index, 1)
        localStorage.setItem(this.StorageKey, JSON.stringify(this.savedSearches))
        this.$message.success('删除成功')
      })
    },
    // 处理搜索参数，去除空值和不需要的字段
    processParams(params) {
      const result = {}
      Object.keys(params).forEach(key => {
        // 跳过分页参数
        if (['pageNum', 'pageSize'].includes(key)) {
          return
        }
        // 处理连字符参数，转换为驼峰
        const processedKey = key.split('-').map((part, index) => index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1)).join('')

        result[processedKey] = params[key]
      })
      return result
    },
    setDefault(search) {
      // 如果当前项已经是默认，则取消默认
      if (search.isDefault) {
        search.isDefault = false
      } else {
        // 否则清除其他默认项，设置当前项为默认
        this.savedSearches.forEach(item => {
          item.isDefault = item.id === search.id
        })
      }

      // 保存更新后的搜索列表
      localStorage.setItem(this.StorageKey, JSON.stringify(this.savedSearches))

      this.$message.success(search.isDefault ? '设置默认搜索成功' : '取消默认搜索成功')
    },
    loadDefaultSearch() {
      const defaultSearch = this.savedSearches.find(item => item.isDefault)
      if (defaultSearch) {
        this.loadParams({
          type: 'load',
          search: defaultSearch
        })
      }
    }
  }
}
</script>

<style scoped>
::v-deep .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
.saved-searches {
  display: inline-block;
  margin-right: 10px;
}

.search-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-item .el-icon-delete {
  cursor: pointer;
  color: #909399;
}

.search-item .el-icon-delete:hover {
  color: #f56c6c;
}

.search-item-actions {
  display: flex;
  align-items: center;
}

.search-item-actions i {
  cursor: pointer;
  color: #909399;
}

.search-item-actions i:hover {
  color: #409EFF;
}

.search-item-actions .el-icon-delete:hover {
  color: #f56c6c;
}
</style>
