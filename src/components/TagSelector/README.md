# TagSelector 组件

一个功能强大的标签选择组件，支持三面板布局：分组选择、标签选择和已选标签展示。组件已内化数据请求逻辑，使用更加简单。

## 特性

- ✅ 三面板布局：分组列表 + 标签列表 + 已选标签
- ✅ 支持 v-model 双向绑定
- ✅ 分组和标签的搜索功能
- ✅ 全选/反选功能
- ✅ 已选标签的快速移除
- ✅ 内化数据请求，只需提供企业 ID
- ✅ 自动加载分组和标签数据
- ✅ 响应式设计，适配不同屏幕

## 安装使用

```vue
<template>
  <div>
    <tag-selector
      v-model="selectedTags"
      :corp-id="corpId"
      @change="handleTagsChange"
    />
  </div>
</template>

<script>
import TagSelector from "@/components/TagSelector/index.vue";

export default {
  components: {
    TagSelector,
  },
  data() {
    return {
      selectedTags: [],
      corpId: "your-corp-id",
    };
  },
  methods: {
    handleTagsChange(tags) {
      console.log("选中的标签:", tags);
    },
  },
};
</script>
```

## Props

| 参数            | 说明                      | 类型          | 可选值 | 默认值 |
| --------------- | ------------------------- | ------------- | ------ | ------ |
| value / v-model | 选中的标签数组            | Array         | —      | []     |
| corpId          | 企业 ID，用于获取标签数据 | String/Number | —      | null   |

## Events

| 事件名 | 说明               | 回调参数       |
| ------ | ------------------ | -------------- |
| change | 选中标签变化时触发 | 选中的标签数组 |

## 方法

| 方法名 | 说明         | 参数 |
| ------ | ------------ | ---- |
| reset  | 重置组件状态 | —    |

## 标签对象结构

```javascript
{
  tagId: 'string',        // 标签ID
  tagName: 'string',      // 标签名称
  groupId: 'string',      // 所属分组ID
  groupName: 'string',    // 所属分组名称（可选）
  // ... 其他属性
}
```

## 完整示例

```vue
<template>
  <div class="tag-selector-demo">
    <h3>标签选择器演示</h3>

    <!-- 企业选择 -->
    <el-select v-model="selectedCorpId" placeholder="请选择企业">
      <el-option
        v-for="corp in corpList"
        :key="corp.id"
        :label="corp.name"
        :value="corp.id"
      />
    </el-select>

    <!-- 标签选择器 -->
    <tag-selector
      v-model="selectedTags"
      :corp-id="selectedCorpId"
      @change="handleTagsChange"
    />

    <!-- 已选标签展示 -->
    <div class="selected-tags-display">
      <h4>已选择的标签：</h4>
      <el-tag
        v-for="tag in selectedTags"
        :key="tag.tagId"
        closable
        @close="removeTag(tag)"
      >
        {{ tag.tagName }}
      </el-tag>
    </div>
  </div>
</template>

<script>
import TagSelector from "@/components/TagSelector/index.vue";

export default {
  name: "TagSelectorDemo",
  components: {
    TagSelector,
  },
  data() {
    return {
      selectedCorpId: null,
      corpList: [
        { id: "corp1", name: "企业1" },
        { id: "corp2", name: "企业2" },
      ],
      selectedTags: [],
    };
  },
  methods: {
    handleTagsChange(tags) {
      console.log("标签选择变化:", tags);
      // 可以在这里处理标签变化的业务逻辑
    },

    removeTag(tag) {
      this.selectedTags = this.selectedTags.filter(
        (t) => t.tagId !== tag.tagId
      );
    },
  },
};
</script>
```

## 样式自定义

组件提供了丰富的 CSS 类名，可以通过覆盖样式来自定义外观：

```scss
.tag-group-selector {
  // 整体容器
  .tag-selector-container {
    height: 400px; // 自定义高度
  }

  // 分组面板
  .groups-panel {
    width: 220px; // 自定义宽度
  }

  // 已选面板
  .selected-panel {
    width: 200px; // 自定义宽度
  }

  // 自定义选中状态
  .group-item.active {
    background: your-color;
  }
}
```

## 注意事项

1. 确保传入有效的 `corpId`，组件会根据该参数自动加载数据
2. 组件会自动处理加载状态和错误处理
3. 标签数据会被缓存，避免重复请求
4. 组件内部使用了 Element UI，确保项目中已引入

## 更新日志

### v2.0.0

- ✨ 内化数据请求逻辑，简化使用方式
- 🔥 移除 `availableGroups`、`loadingGroups`、`groupTagsLoading` props
- 🔥 移除 `load-group-tags` 事件
- ✨ 新增 `corpId` prop，用于自动加载数据
- 🐛 修复了一些加载状态的问题

### v1.0.0

- 🎉 初始版本发布
- ✨ 支持三面板标签选择
- ✨ 支持 v-model 双向绑定
- ✨ 支持搜索和全选功能
