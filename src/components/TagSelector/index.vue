<template>
  <div class="tag-group-selector">
    <div class="tag-selector-container">
      <!-- 左侧：分组列表 -->
      <div class="groups-panel">
        <div class="panel-header">
          <span class="panel-title">
            <i class="el-icon-folder" />
            标签分组
          </span>
        </div>
        <div class="search-box">
          <el-input
            v-model="groupSearchKeyword"
            placeholder="搜索分组"
            size="mini"
            prefix-icon="el-icon-search"
            clearable
            @input="handleGroupSearch"
          />
        </div>
        <div class="groups-list">
          <div v-if="loadingGroups" class="loading-tip">
            <i class="el-icon-loading" />
            <span>加载中...</span>
          </div>
          <div v-else-if="!filteredAvailableGroups.length" class="empty-tip">
            <i class="el-icon-folder" />
            <span>暂无可选分组</span>
          </div>
          <div v-else>
            <div
              v-for="group in filteredAvailableGroups"
              :key="group.groupId"
              :class="[
                'group-item',
                { 'active': currentSelectedGroupId === group.groupId }
              ]"
              @click="selectGroup(group.groupId)"
            >
              <div class="group-content">
                <div class="group-header">
                  <div class="group-name-row">
                    <i class="el-icon-folder group-icon" />
                    <span class="group-name">{{ group.groupName }}</span>
                    <div class="group-badges">
                      <!-- 已选标签数量徽章 -->
                      <el-tag
                        size="mini"
                        :type="getSelectedCountInGroup(group.groupId) > 0 ? 'success' : 'info'"
                        effect="plain"
                        class="selected-badge"
                      >
                        已选{{ getSelectedCountInGroup(group.groupId) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
                <div class="group-stats">
                  <div class="stat-item">
                    <i class="el-icon-price-tag" />
                    <span class="stat-text">{{ group.tagCount || 0 }}个标签</span>
                  </div>
                  <!-- 显示选中比例 -->
                  <div v-if="getSelectedCountInGroup(group.groupId) > 0 && group.tagCount > 0" class="stat-item selection-ratio">
                    <i class="el-icon-check" />
                    <span class="stat-text">{{ getSelectionRatio(group) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：标签列表 -->
      <div class="tags-panel">
        <div class="panel-header">
          <span class="panel-title">
            <i class="el-icon-price-tag" />
            标签列表
          </span>
          <span v-if="currentSelectedGroup" class="group-name">
            {{ currentSelectedGroup.groupName }}
          </span>
        </div>
        <div class="search-box">
          <el-input
            v-model="tagSearchKeyword"
            placeholder="搜索标签"
            size="mini"
            prefix-icon="el-icon-search"
            clearable
            @input="handleTagSearch"
          />
        </div>
        <div class="tags-list">
          <div v-if="!currentSelectedGroupId" class="empty-tip">
            <i class="el-icon-arrow-left" />
            <span>请先选择标签分组</span>
          </div>
          <div v-else-if="groupTagsLoading[currentSelectedGroupId]" class="loading-tip">
            <i class="el-icon-loading" />
            <span>加载标签中...</span>
          </div>
          <div v-else-if="!filteredCurrentGroupTags.length" class="empty-tip">
            <i class="el-icon-document" />
            <span>该分组下暂无标签</span>
          </div>
          <div v-else class="tags-container">
            <div class="select-all-bar">
              <el-checkbox
                v-model="checkAll"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
              >
                <span class="select-all-text">全选</span>
              </el-checkbox>
              <span class="selected-info">
                已选 <span class="selected-count">{{ selectedTagsInCurrentGroup.length }}</span> 项
              </span>
            </div>
            <div class="tag-checkbox-list">
              <div
                v-for="tag in filteredCurrentGroupTags"
                :key="tag.tagId"
                class="tag-checkbox-item"
              >
                <el-checkbox
                  :value="isTagSelected(tag)"
                  class="tag-checkbox"
                  @change="(checked) => handleTagChange(tag, checked)"
                >
                  <span class="tag-label">{{ tag.tagName }}</span>
                </el-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：已选标签 -->
      <div class="selected-panel">
        <div class="panel-header">
          <span class="panel-title">
            <i class="el-icon-collection-tag" />
            已选标签
          </span>
          <span class="selected-count-badge">{{ selectedTags.length }}</span>
        </div>
        <div class="selected-tags-list">
          <div v-if="!selectedTags.length" class="empty-tip">
            <i class="el-icon-tickets" />
            <span>暂未选择标签</span>
          </div>
          <div v-else class="selected-tags-container">
            <div class="clear-all-bar">
              <el-button
                type="text"
                size="small"
                icon="el-icon-delete"
                @click="clearAllSelectedTags"
              >
                清空所有
              </el-button>
            </div>
            <div class="selected-tags-scroll">
              <div
                v-for="tag in selectedTags"
                :key="tag.tagId"
                class="selected-tag-item"
              >
                <span class="tag-name" :title="tag.tagName">
                  <el-tooltip class="item" effect="dark" :content="tag.groupName" placement="top">
                    <i class="el-icon-more-outline" />
                  </el-tooltip>
                  {{ tag.tagName }}
                </span>
                <i
                  class="el-icon-close remove-tag"
                  @click="removeSelectedTag(tag)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getTagGroups, getTagsByGroupId } from '@/api/wx/authCorp'

export default {
  name: 'TagGroupSelector',
  props: {
    // v-model 绑定的值：tag对象数组
    value: {
      type: Array,
      default: () => []
    },
    // 企业ID，用于获取标签数据
    corpId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      groupSearchKeyword: '',
      tagSearchKeyword: '',
      currentSelectedGroupId: null,
      selectedTags: [], // 内部选中的标签对象数组
      groupTags: {}, // 缓存各分组的标签数据
      isInternalUpdate: false, // 标记是否为内部更新，避免循环
      // 内化的数据状态
      availableGroups: [], // 可用的分组列表
      loadingGroups: false, // 分组加载状态
      groupTagsLoading: {} // 各分组标签加载状态
    }
  },
  computed: {
    // 过滤后的可用分组
    filteredAvailableGroups() {
      if (!this.groupSearchKeyword) return this.availableGroups
      return this.availableGroups.filter(group =>
        group.groupName.toLowerCase().includes(this.groupSearchKeyword.toLowerCase())
      )
    },

    // 当前选中的分组
    currentSelectedGroup() {
      return this.availableGroups.find(group => group.groupId === this.currentSelectedGroupId)
    },

    // 当前分组的标签列表
    currentGroupTags() {
      return this.groupTags[this.currentSelectedGroupId] || []
    },

    // 过滤后的当前分组标签
    filteredCurrentGroupTags() {
      if (!this.tagSearchKeyword) return this.currentGroupTags
      return this.currentGroupTags.filter(tag =>
        tag.tagName.toLowerCase().includes(this.tagSearchKeyword.toLowerCase())
      )
    },

    // 当前分组中已选的标签
    selectedTagsInCurrentGroup() {
      return this.selectedTags.filter(selectedTag =>
        this.currentGroupTags.some(tag => tag.tagId === selectedTag.tagId)
      )
    },

    // 全选状态
    checkAll: {
      get() {
        return this.filteredCurrentGroupTags.length > 0 &&
               this.selectedTagsInCurrentGroup.length === this.filteredCurrentGroupTags.length
      },
      set(val) {
        // 由 handleCheckAllChange 处理
      }
    },

    // 半选状态
    isIndeterminate() {
      const selectedCount = this.selectedTagsInCurrentGroup.length
      const totalCount = this.filteredCurrentGroupTags.length
      return selectedCount > 0 && selectedCount < totalCount
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (this.isInternalUpdate) {
          this.isInternalUpdate = false
          return
        }
        // 从外部传入的值更新内部状态
        this.selectedTags = Array.isArray(newVal) ? [...newVal] : []
      }
    },

    selectedTags: {
      deep: true,
      handler() {
        this.emitChange()
      }
    },

    // 监听 corpId 变化
    corpId: {
      immediate: true,
      handler(newCorpId) {
        if (newCorpId) {
          this.loadTagGroups()
        } else {
          this.reset()
        }
      }
    }
  },
  methods: {
    // 发出变化事件
    emitChange() {
      if (this.isInternalUpdate) return

      this.isInternalUpdate = true
      this.$emit('input', [...this.selectedTags])
      this.$emit('change', [...this.selectedTags])
    },

    // 处理分组搜索
    handleGroupSearch() {
      // 搜索逻辑已在计算属性中处理
    },

    // 处理标签搜索
    handleTagSearch() {
      // 搜索逻辑已在计算属性中处理
    },

    // 加载标签分组列表
    async loadTagGroups() {
      if (!this.corpId) {
        this.availableGroups = []
        return
      }

      this.loadingGroups = true
      try {
        const response = await getTagGroups({
          corpId: this.corpId
        })
        this.availableGroups = (response.data || []).map(group => ({
          ...group,
          tagCount: group.tagCount || 0
        }))
      } catch (error) {
        console.error('获取标签分组失败:', error)
        this.availableGroups = []
        this.$message.error('获取标签分组失败')
      } finally {
        this.loadingGroups = false
      }
    },

    // 选择分组
    async selectGroup(groupId) {
      this.currentSelectedGroupId = groupId

      // 如果该分组的标签尚未加载，则加载
      if (!this.groupTags[groupId]) {
        await this.loadGroupTags(groupId)
      }
    },

    // 加载分组下的标签
    async loadGroupTags(groupId) {
      this.$set(this.groupTagsLoading, groupId, true)

      try {
        const response = await getTagsByGroupId({
          groupId: groupId
        })
        const tags = response.data || []

        // 缓存标签数据
        this.$set(this.groupTags, groupId, tags)

        // 更新分组的标签数量
        const groupIndex = this.availableGroups.findIndex(g => g.groupId === groupId)
        if (groupIndex > -1) {
          this.$set(this.availableGroups[groupIndex], 'tagCount', tags.length)
        }
      } catch (error) {
        console.error('获取分组标签失败:', error)
        this.$message.error('获取分组标签失败')
      } finally {
        this.$set(this.groupTagsLoading, groupId, false)
      }
    },

    // 设置分组标签数据（保留此方法以兼容外部调用，但现在主要用内部加载）
    setGroupTags(groupId, tags) {
      this.$set(this.groupTags, groupId, tags)
    },

    // 判断标签是否被选中
    isTagSelected(tag) {
      return this.selectedTags.some(selectedTag => selectedTag.tagId === tag.tagId)
    },

    // 处理单个标签选择变化
    handleTagChange(tag, checked) {
      if (checked) {
        // 添加标签
        if (!this.isTagSelected(tag)) {
          this.selectedTags.push({ ...tag })
        }
      } else {
        // 移除标签
        this.selectedTags = this.selectedTags.filter(selectedTag => selectedTag.tagId !== tag.tagId)
      }
    },

    // 获取某分组中已选的标签数量（改进版，支持未加载分组）
    getSelectedCountInGroup(groupId) {
      // 如果分组标签已加载，使用精确匹配
      if (this.groupTags[groupId] && this.groupTags[groupId].length > 0) {
        const groupTags = this.groupTags[groupId]
        return this.selectedTags.filter(selectedTag =>
          groupTags.some(tag => tag.tagId === selectedTag.tagId)
        ).length
      }

      // 如果分组标签未加载，通过标签的groupId属性来判断
      return this.selectedTags.filter(selectedTag =>
        selectedTag.groupId === groupId
      ).length
    },

    // 获取某分组中已选的标签数量（保留原方法以兼容）
    getSelectedTagsFromGroup(groupId) {
      const groupTags = this.groupTags[groupId] || []
      return this.selectedTags.filter(selectedTag =>
        groupTags.some(tag => tag.tagId === selectedTag.tagId)
      )
    },

    // 获取分组的选中比例
    getSelectionRatio(group) {
      const selectedCount = this.getSelectedCountInGroup(group.groupId)
      const totalCount = group.tagCount || 0

      if (totalCount === 0) return '0%'

      const percentage = Math.round((selectedCount / totalCount) * 100)
      return `${percentage}%`
    },

    // 处理全选变化
    handleCheckAllChange(val) {
      if (val) {
        // 全选：添加当前过滤后的所有标签
        const tagsToAdd = this.filteredCurrentGroupTags.filter(tag => !this.isTagSelected(tag))
        this.selectedTags = [...this.selectedTags, ...tagsToAdd.map(tag => ({ ...tag }))]
      } else {
        // 取消全选：移除当前过滤后的所有标签
        const tagsToRemove = this.filteredCurrentGroupTags.map(tag => tag.tagId)
        this.selectedTags = this.selectedTags.filter(selectedTag => !tagsToRemove.includes(selectedTag.tagId))
      }
    },

    // 移除已选标签
    removeSelectedTag(tag) {
      this.selectedTags = this.selectedTags.filter(selectedTag => selectedTag.tagId !== tag.tagId)
    },

    // 清空所有已选标签
    clearAllSelectedTags() {
      this.selectedTags = []
    },

    // 重置组件状态
    reset() {
      this.selectedTags = []
      this.currentSelectedGroupId = null
      this.groupSearchKeyword = ''
      this.tagSearchKeyword = ''
      this.groupTags = {}
      this.isInternalUpdate = false
      // this.availableGroups = []
      this.loadingGroups = false
      this.groupTagsLoading = {}
    }
  }
}
</script>

<style lang="scss" scoped>
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.tag-group-selector {
  .tag-selector-container {
    display: flex;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    height: 380px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .panel-header {
      padding: 10px 12px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e4e7ed;
      font-weight: 500;
      color: #303133;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .panel-title {
        display: flex;
        align-items: center;
        font-size: 13px;

        i {
          margin-right: 4px;
          color: #409eff;
          font-size: 12px;
        }
      }

      .group-name {
        font-size: 11px;
        color: #909399;
        font-weight: normal;
      }

      .selected-count-badge {
        background: #409eff;
        color: white;
        font-size: 10px;
        padding: 1px 5px;
        border-radius: 8px;
        min-width: 14px;
        text-align: center;
        line-height: 1.2;
        animation: fadeIn 0.3s ease;
      }
    }

    .search-box {
      padding: 6px 10px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafbfc;

      .el-input {
        :deep(.el-input__inner) {
          border-radius: 3px;
          border: 1px solid #e4e7ed;
          height: 28px;
          font-size: 12px;

          &:focus {
            border-color: #409eff;
          }
        }
      }
    }

    .loading-tip, .empty-tip {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 16px;
      color: #909399;
      font-size: 12px;
      text-align: center;

      i {
        font-size: 24px;
        margin-bottom: 8px;
        opacity: 0.5;
      }

      span {
        margin-top: 2px;
      }
    }

    // 左侧分组面板
    .groups-panel {
      width: 200px;
      border-right: 1px solid #e4e7ed;
      display: flex;
      flex-direction: column;
      background: #fff;

      .groups-list {
        flex: 1;
        overflow-y: auto;

        .group-item {
          display: flex;
          align-items: center;
          padding: 10px 12px;
          cursor: pointer;
          border-bottom: 1px solid #f5f5f5;
          transition: all 0.3s ease;
          position: relative;
          border-left: 3px solid transparent;
          animation: slideIn 0.3s ease;

          &:hover {
            background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left-color: #91d5ff;
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            .group-content .group-header .group-name-row .group-icon {
              color: #409eff;
              transform: scale(1.1);
            }
          }

          &.active {
            border-left-color: #1890ff;
            box-shadow: 0 2px 12px rgba(24, 144, 255, 0.12);

            .group-content {
              .group-header .group-name-row {
                .group-name {
                  color: #1890ff;
                  font-weight: 600;
                }

                .group-icon {
                  color: #1890ff;
                  animation: pulse 2s infinite;
                }
              }

              .group-stats .stat-item {
                .stat-text {
                  color: #1890ff;
                  font-weight: 500;
                }

                i {
                  color: #1890ff;
                }
              }
            }
          }

          .group-content {
            flex: 1;
            min-width: 0;

            .group-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 4px;

                              .group-name-row {
                  display: flex;
                  align-items: center;
                  flex: 1;
                  min-width: 0;

                  .group-icon {
                    margin-right: 6px;
                    font-size: 12px;
                    color: #606266;
                    flex-shrink: 0;
                    transition: all 0.3s ease;
                  }

                  .group-name {
                    font-size: 12px;
                    color: #303133;
                    font-weight: 500;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    flex: 1;
                    transition: all 0.3s ease;
                  }

                  .group-badges {
                    margin-left: 6px;
                    flex-shrink: 0;
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .selected-badge {
                      animation: fadeIn 0.3s ease;

                      :deep(.el-tag__content) {
                        font-size: 9px;
                        line-height: 1.2;
                      }
                    }
                  }
                }
            }

            .group-stats {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .stat-item {
                display: flex;
                align-items: center;

                i {
                  margin-right: 3px;
                  font-size: 9px;
                  color: #909399;
                  transition: all 0.3s ease;
                }

                .stat-text {
                  font-size: 10px;
                  color: #909399;
                  line-height: 1.2;
                  transition: all 0.3s ease;
                }

                &.selection-ratio {
                  margin-left: 8px;

                  i {
                    color: #67c23a;
                  }

                  .stat-text {
                    color: #67c23a;
                    font-weight: 500;
                  }
                }
              }
            }
          }
        }
      }
    }

    // 中间标签面板
    .tags-panel {
      flex: 1;
      border-right: 1px solid #e4e7ed;
      display: flex;
      flex-direction: column;
      background: #fff;

      .tags-list {
        flex: 1;
        overflow-y: auto;

        .tags-container {
          .select-all-bar {
            padding: 8px 12px;
            background: linear-gradient(90deg, #fafbfc 0%, #f5f6fa 100%);
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .select-all-text {
              color: #303133;
              font-weight: 500;
              font-size: 12px;
            }

            .selected-info {
              font-size: 11px;
              color: #909399;

              .selected-count {
                color: #409eff;
                font-weight: 500;
              }
            }
          }

          .tag-checkbox-list {
            padding: 6px;

            .tag-checkbox-item {
              margin-bottom: 4px;

              &:last-child {
                margin-bottom: 0;
              }

              .tag-checkbox {
                width: 100%;
                margin-right: 0;
                padding: 6px 8px;
                border-radius: 3px;
                transition: all 0.2s;

                &:hover {
                  background: #f0f9ff;
                }

                :deep(.el-checkbox__label) {
                  width: 100%;
                  padding-left: 4px;

                  .tag-label {
                    color: #606266;
                    font-size: 12px;
                  }
                }

                &:hover :deep(.el-checkbox__label .tag-label) {
                  color: #409eff;
                }
              }
            }
          }
        }
      }
    }

    // 右侧已选面板
    .selected-panel {
      width: 180px;
      display: flex;
      flex-direction: column;
      background: #fff;

      .selected-tags-list {
        flex: 1;
        overflow: hidden;

        .selected-tags-container {
          height: 100%;
          display: flex;
          flex-direction: column;

          .clear-all-bar {
            padding: 6px 10px;
            border-bottom: 1px solid #f0f0f0;
            text-align: right;
          }

          .selected-tags-scroll {
            flex: 1;
            overflow-y: auto;
            padding: 6px 10px;

            .selected-tag-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 6px 8px;
              margin-bottom: 4px;
              background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
              border: 1px solid #b3e5fc;
              border-radius: 4px;
              font-size: 11px;
              transition: all 0.2s;
              line-height: 1.3;
              animation: slideIn 0.3s ease;

              &:hover {
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                border-color: #64b5f6;
                transform: translateY(-1px);
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              }

              .tag-name {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #1976d2;
                font-weight: 500;
              }

              .remove-tag {
                margin-left: 4px;
                cursor: pointer;
                color: #bdbdbd;
                font-size: 12px;
                transition: color 0.2s;

                &:hover {
                  color: #f56c6c;
                  transform: scale(1.1);
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
