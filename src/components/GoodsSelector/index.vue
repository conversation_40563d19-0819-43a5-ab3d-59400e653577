<script>
export default {
  name: 'GoodsSelector',
  dicts: ['goods_link_type', 'platform_type', 'media_type', 'record_state', 'goods_link_type_tb', 'goods_link_type_pdd', 'duo_duo_jin_bao'],
  computed: {
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    },

    platformTypeMap() {
      return this.dict.type.platform_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    }
  }
}
</script>
<script setup>
import { computed, getCurrentInstance, nextTick, ref, watch } from 'vue'
import { listGoods, selectGoods } from '@/api/promotion/goods'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import DeptTreeSelect from '@/components/DeptTreeSelect/DeptTreeSelect.vue'
import useDeptOptions from '@/components/DeptTreeSelect/useDeptOptions'

const self = getCurrentInstance().proxy
const props = defineProps({
  visible: Boolean,
  defaultSelections: {
    type: Array,
    default: () => {
      return []
    }
  },
  defaultQueryParams: {
    type: Object,
    default: () => {
      return {}
    }
  },
  queryList: {
    type: Array,
    default: () => {
      return ['goodsName', 'goodsId']
    }
  },
  tableList: {
    type: Array,
    default: () => {
      return ['goodsName', 'remark']
    }
  },
  type: {
    type: String,
    default: null
  },
  autoSearch: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['close', 'select'])
const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})
watch(() => props.visible, (val) => {
  if (val) {
    selections.value = [...props.defaultSelections]
    if (props.defaultQueryParams) {
      nextTick(() => {
        Object.assign(query.value, props.defaultQueryParams)
      })
    }
    if (props.autoSearch) {
      resetQuery()
    } else {
      resetParams()
    }
  }
})

const close = () => {
  emit('close')
  open.value = false
}

const deptOptions = useDeptOptions()

const queryLoading = ref(false)
const list = ref([])
const query = ref({
  pageNum: 1,
  pageSize: 10
})
const total = ref(0)

const getList = () => {
  queryLoading.value = true
  let quest = listGoods
  if (props.type === 'pool') {
    quest = selectGoods
  }
  quest(query.value).then(res => {
    list.value = res.rows
    total.value = res.total
    queryLoading.value = false
  }).catch(() => {
    queryLoading.value = false
  })
}

const onQuery = () => {
  query.value.pageNum = 1
  getList()
}
const selections = ref([])
const select = (goods) => {
  if (props.type === 'single') {
    emit('select', goods)
    close()
  } else {
    selections.value.push(goods)
  }
}
const removeSelection = (goods) => {
  selections.value = selections.value.filter(item => item.id !== goods.id)
}
const isSelect = (goods) => {
  return selections.value.some(item => item.id === goods.id)
}
const submit = () => {
  emit('select', selections.value)
  close()
}

const resetParams = () => {
  list.value = []
  query.value = {
    pageNum: 1,
    pageSize: 10
  }
  self.resetForm('queryForm')
  if (props.defaultQueryParams) {
    Object.assign(query.value, props.defaultQueryParams)
  }
}

const resetQuery = () => {
  resetParams()
  getList()
}

const isSearch = (key) => {
  return props.queryList.includes(key)
}
const isTable = (key) => {
  return props.tableList.includes(key)
}
</script>

<template>
  <el-dialog
    title="选择商品"
    :visible.sync="open"
    width="80%"
    top="5vh"
    append-to-body
  >
    <el-row :gutter="20">
      <el-col :span="type === 'single' ? 24: 16">
        <el-form ref="queryForm" class="search-form" :model="query" size="small" :inline="true" label-width="100px">
          <el-form-item v-if="isSearch('platform')" prop="platform">
            <el-select v-model="query.platform" placeholder="平台类型" clearable @change="onQuery">
              <el-option
                v-for="dict in dict.type.platform_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              >
                <svg-icon :icon-class="dict.label" />
                <span> {{ dict.label }}</span></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="isSearch('mediaPlatformType')" prop="mediaPlatformType">
            <el-select v-model="query.mediaPlatformType" placeholder="媒体类型" clearable @change="onQuery">
              <el-option
                v-for="dict in dict.type.media_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              >
                <svg-icon :icon-class="dict.label" />
                <span> {{ dict.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="isSearch('goodsId')" prop="goodsId">
            <el-input
              v-model.trim="query.goodsId"
              placeholder="商品ID"
              clearable
              @keyup.enter.native="onQuery"
            />
          </el-form-item>
          <el-form-item v-if="isSearch('mallId')" prop="mallId">
            <el-input
              v-model.trim="query.mallId"
              placeholder="店铺ID"
              clearable
              @keyup.enter.native="onQuery"
            />
          </el-form-item>
          <el-form-item v-if="isSearch('goodsName')" prop="goodsName">
            <el-input
              v-model.trim="query.goodsName"
              placeholder="商品名称"
              clearable
              @keyup.enter.native="onQuery"
            />
          </el-form-item>
          <el-form-item v-if="isSearch('mallName')" prop="mallName">
            <el-input
              v-model.trim="query.mallName"
              placeholder="店铺名称"
              clearable
              @keyup.enter.native="onQuery"
            />
          </el-form-item>
          <UserSearchInput v-if="isSearch('createBy')" :create-by.sync="query.createBy" @query="onQuery" />
          <el-form-item v-if="isSearch('remark')" prop="remark">
            <el-input
              v-model.trim="query.remark"
              placeholder="备注"
              clearable
              @keyup.enter.native="onQuery"
            />
          </el-form-item>
          <el-form-item v-if="isSearch('putOnRecordState')" prop="putOnRecordState">
            <el-select v-model="query.putOnRecordState" placeholder="备案状态" clearable @change="onQuery">
              <el-option
                v-for="dict in dict.type.record_state"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="isSearch('currentLimitingState')" prop="currentLimitingState">
            <el-select v-model="query.currentLimitingState" placeholder="推广限制" clearable @change="onQuery">
              <el-option
                label="限制推广"
                :value="1"
              />
              <el-option
                label="未限制"
                :value="0"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="isSearch('deptIds')" prop="deptIds">
            <DeptTreeSelect v-model="query.deptIds" :options="deptOptions" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="onQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="tableRef"
          v-loading="queryLoading"
          :data="list"
          stripe
          border
          size="small"
        >
          <el-table-column v-if="isTable('mediaPlatformType')" label="类型" align="center" prop="mediaPlatformType" width="100">
            <template slot-scope="scope">
              <div class="icon-flex">
                <div class="type-icon-item">
                  <svg-icon v-if="mediaTypeMap[scope.row.mediaPlatformType]" class-name="type-icon" :icon-class="mediaTypeMap[scope.row.mediaPlatformType]" />
                  <div class="type-label">{{ mediaTypeMap[scope.row.mediaPlatformType] }}</div>
                </div>
                <div style="border: 1px solid #dfe6ec" />
                <div class="type-icon-item">
                  <svg-icon v-if="platformTypeMap[scope.row.platform]" class-name="type-icon" :icon-class="platformTypeMap[scope.row.platform]" />
                  <div class="type-label">{{ platformTypeMap[scope.row.platform] }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="isTable('goodsName')" label="商品信息" align="left" prop="goodsName">
            <template #default="scope">
              <div class="table-base-info">
                <svg-icon v-if="scope.row.platform === '2' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="taobao" />
                <div v-else class="mr10">
                  <image-preview :src="scope.row.goodsThumbnailUrl" :width="32" :height="32" />
                </div>
                <BaseInfoCell :id="scope.row.goodsId" class="info-wrap" style="flex:1" :name="scope.row.goodsName" />
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="isTable('mallName')" label="店铺信息" align="left" prop="mallName" width="200">
            <template #default="scope">
              <BaseInfoCell :id="scope.row.mallId" :name="scope.row.mallName" />
            </template>
          </el-table-column>
          <el-table-column v-if="isTable('remark')" label="备注" align="center" prop="remark" width="150" show-overflow-tooltip />
          <el-table-column v-if="isTable('nickName')" label="用户昵称" align="center" prop="nickName" width="120" show-overflow-tooltip />
          <el-table-column v-if="isTable('createBy')" label="负责人" align="center" prop="createBy" width="150" />
          <el-table-column v-if="isTable('deptName')" label="部门名称" align="center" prop="deptName" min-width="180" show-overflow-tooltip />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
            <template #default="scope">
              <el-button
                size="mini"
                type="text"
                :disabled="isSelect(scope.row)"
                @click="select(scope.row)"
              >选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="query.pageNum"
          :limit.sync="query.pageSize"
          size="mini"
          @pagination="getList"
        />
      </el-col>
      <el-col v-if="type !== 'single'" :span="8" class="selected">
        <div class="selected-title">已选择{{ selections.length ? `(${selections.length})`:'' }}</div>
        <el-table
          :data="selections"
          stripe
          border
          :height="selections.length > 10 ? 603 : null"
          size="small"
        >
          <el-table-column
            prop="name"
            label="名称"
          >
            <template #default="scope">
              <BaseInfoCell :id="scope.row.goodsId" :line-clamp="1" class="info-wrap" style="flex:1" :name="scope.row.goodsName" no-copy />
            </template>
          </el-table-column>
          <el-table-column
            prop="action"
            label="操作"
            width="50"
            align="center"
          ><template #default="scope">
            <el-button type="text" class="text-danger" icon="el-icon-delete" @click="removeSelection(scope.row)" />
          </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <span v-if="type !== 'single'" slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="submit">确 定</el-button>
      <el-button size="small" @click="close()">取 消</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">
.selected {
  max-height: 602px;
  .selected-title{
    font-size: 16px;
    font-weight: bold;
    margin: 5px 0 24px;

  }
}
</style>
