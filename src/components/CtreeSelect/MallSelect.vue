<script setup>
import { CTreeDrop } from '@wsfe/ctree'
</script>
<script>
import { getMallList } from '@/api/promotion/goods'
export default {
  model: {
    prop: 'modelValue',
    event: 'update:modelValue'
  },
  props: {
    modelValue: [Array, String],
    isSearch: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    goodsPlatform: {
      type: Number,
      default: 0
    },
    valueField: {
      type: String,
      default: 'mallId'
    },
    manual: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select'],
  data() {
    return {
      mallList: []
    }
  },
  computed: {
    innerValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
        this.$emit('select')
      }
    }
  },
  mounted() {
    if (!this.manual) {
      this.getList()
    }
  },
  methods: {
    handleSelect() {
      this.$emit('select')
    },
    getList(query = {}) {
      getMallList({
        goodsPlatform: this.goodsPlatform,
        ...query
      }).then(response => {
        const map = new Set()
        const list = []
        response.data.forEach(item => {
          if (map.has(item[this.valueField])) {
            return null
          }
          map.add(item[this.valueField])
          list.push({
            label: `${item.mallName} (ID:${item.mallId})`,
            id: item[this.valueField]
          })
        })
        this.mallList = list
      })
    }
  }
}
</script>
<template>
  <CTreeDrop
    v-model="innerValue"
    style="width: 190px"
    :dropdown-min-width="200"
    :data="mallList"
    drop-placeholder="店铺"
    title-field="label"
    :selectable="!multiple"
    clearable
    default-expand-all
    :checkable="multiple"
  >
    <template #display="scope">
      <div
        v-if="multiple"
        :class="[scope.checkedNodes.length ? 'c-tree-overflow': isSearch? 'c-tree-placeholder' : '']"
      >
        {{ scope.checkedNodes.length? scope.checkedNodes.map((node) => node.label).join('、') : '店铺' }}
      </div>
      <div
        v-else
        :class="[scope.selectedNode ? 'c-tree-overflow': isSearch? 'c-tree-placeholder' : '']"
      >
        {{ scope.selectedNode? scope.selectedNode.label : '店铺' }}
      </div>
    </template>
  </CTreeDrop>
</template>
