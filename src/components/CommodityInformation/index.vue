<script setup>
import { computed, ref, watch } from 'vue'
import { getCommodityInformation } from '@/api/promotion/order'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import UDSmartImage from '@/assets/images/UDSmart_img.png'
const props = defineProps({
  visible: Boolean,
  form: {
    type: Object,
    default: () => ({})
  },
  dateRange: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['update:visible'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

const loading = ref(false)
const list = ref([])
const fetchList = () => {
  list.value = []
  loading.value = true
  getCommodityInformation({
    advertiserId: props.form.advertiserId,
    planId: props.form.planId,
    startTime: props.dateRange[0],
    endTime: props.dateRange[1]
  }).then(response => {
    list.value = response.data
    if (props.form.mainProduct) {
      const index = list.value.findIndex(item => item.goodsId === props.form.mainProduct)
      if (index !== -1) {
        list.value.unshift(list.value.splice(index, 1)[0])
      }
    }
  }).finally(() => {
    loading.value = false
  })
}

const roi = (amount) => {
  return (amount / props.form.cost / 100).toFixed(2)
}

watch(() => props.visible, (val) => {
  if (val) {
    fetchList()
  }
})
</script>

<template>
  <el-dialog
    title="推广商品"
    :visible.sync="open"
    width="1000px"
    top="10vh"
    append-to-body
  >

    <el-table
      v-loading="loading"
      :data="list"
      style="width: 100%"
    >
      <el-table-column label="商品信息" align="left" prop="goodsName" min-width="200">
        <template #default="scope">
          <div class="table-base-info">
            <div style="width: 50px;height:50px;margin-right: 10px">
              <template v-if="scope.row.orderType==='UDS'">
                <image-preview v-if="scope.row.url" :src="scope.row.url" :width="50" :height="50" />
                <el-image v-else :src="UDSmartImage" :width="50" :height="50" />
              </template>
              <template v-else>
                <image-preview :src="scope.row.url" :width="50" :height="50" />
              </template>
            </div>
            <BaseInfoCell :id="scope.row.goodsId" class="info-wrap" style="flex:1" :name="scope.row.goodsName" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="店铺信息" align="left" prop="mallName" min-width="200">
        <template #default="scope">
          <BaseInfoCell :id="scope.row.mallId" class="info-wrap" style="flex:1" :name="scope.row.mallName" />
        </template>
      </el-table-column>
      <el-table-column label="订单信息" align="left" prop="order" min-width="200">
        <template #default="scope">
          <div><span class="set-label">订单金额：</span>{{ scope.row.orderTotalAmount / 100 }}</div>
          <div><span class="set-label">订单数：</span>{{ scope.row.orderTotalCount }}</div>
          <div><span class="set-label">下单ROI：</span>{{ roi(scope.row.orderTotalAmount) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="成交信息" align="left" prop="order" min-width="200">
        <template #default="scope">
          <div><span class="set-label">成交总金额：</span>{{ scope.row.realOrderAmount / 100 }}</div>
          <div><span class="set-label">成交订单数：</span>{{ scope.row.realOrderCount }}</div>
          <div><span class="set-label">成交ROI：</span>{{ roi(scope.row.realOrderAmount) }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="orderType"
        label="平台"
        width="120"
      />
    </el-table>
  </el-dialog>
</template>

<style scoped lang="scss">
.set-label {
  display: inline-block;
  width: 85px;
  color: #999;
}
</style>
