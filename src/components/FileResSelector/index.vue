<template>
  <div class="component-upload-image">
    <div :style="{width:imgWidth, height: imgHeight}">
      <div v-if="type ==='file'" class="flex  gap-10">
        <div v-if="value" class="overflow-text" :title="value.fileName">{{ value.fileName }}</div>
        <el-button
          type="primary"
          size="mini"
          @click="showRes"
        >选择文件</el-button>
      </div>
      <template v-else>
        <div v-if="!value" class="picture-card show-res-btn" @click="showRes">
          <i class="el-icon-plus" />
        </div>
        <div v-else class="picture-card with-img">
          <div class="action">
            <i v-if="type ==='image'" class="el-icon-view pointer" @click="handlePicturePreview" />
            <i v-if="type ==='file' && value" class="el-icon-view pointer" @click="handleVideoPreview" />
            <i v-if="type ==='video' && value" class="el-icon-view pointer" @click="handleVideoPreview" />
            <i v-if="edit" class="el-icon-delete pointer" @click="handleDelete" />
          </div>
          <el-image
            v-if="type === 'image'"
            :src="value"
            fit="cover"
          />
          <video
            v-else
            :src="value"
            style="width: 100%; height: 100%"
          />
        </div>
      </template>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      title="预览"
      width="800"
      append-to-body
    >
      <div style="padding-bottom: 10px">
        <img
          :src="value"
          style="display: block; max-width: 100%; margin: 0 auto 0"
        >
      </div>
    </el-dialog>

    <el-dialog
      title="选择文件"
      :visible.sync="fileVisible"
      width="90%"
      top="2vh"
      append-to-body
    >
      <FileRes :default-selected="defaultSelected" :img-file-size="imgFileSize" :upload-all="uploadAll" selector @select="handleFileSelect" />
    </el-dialog>

    <el-dialog
      :visible.sync="videoVisible"
      title="预览"
      width="800"
      top="5vh"
      append-to-body
      destroy-on-close
    >
      <video v-if="videoVisible" ref="videoRef" class="preview-video" style="width: 100%" :src="videoSrc" controls @canplay="canPlay" />
    </el-dialog>
  </div>
</template>

<script>
import FileRes from '@/views/promotion/fileres/index.vue'

export default {
  name: 'FileResSelector',
  components: { FileRes },
  props: {
    value: [String, Object],
    type: {
      type: String,
      default: 'image' // image, video, file
    },
    edit: {
      type: Boolean,
      default: true
    },
    uploadAll: {
      type: Boolean,
      default: false
    },
    defaultSelected: {
      type: String,
      default: ''
    },
    width: {
      type: [String, Number],
      default: '148px'
    },
    height: {
      type: [String, Number],
      default: '148px'
    },
    imgFileSize: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      dialogVisible: false,
      fileVisible: false,
      videoVisible: false,
      videoSrc: ''
    }
  },
  computed: {
    imgWidth() {
      return (this.width + '').includes('px') ? this.width : this.width + 'px'
    },
    imgHeight() {
      return (this.height + '').includes('px') ? this.height : this.height + 'px'
    }
  },
  methods: {
    // 删除图片
    handleDelete() {
      this.$emit('input', '')
    },
    // 预览
    handlePicturePreview() {
      this.dialogVisible = true
    },
    handleVideoPreview() {
      this.videoSrc = this.value
      this.videoVisible = true
    },
    showRes() {
      this.fileVisible = true
    },
    handleFileSelect(url, row) {
      this.fileVisible = false
      if (this.type === 'file') {
        this.$emit('input', row)
      } else {
        this.$emit('input', url)
      }
    },
    canPlay() {
      this.$nextTick(() => {
        this.$refs.videoRef.play().catch((err) => console.log('err:', err))
      })
    }
  }
}
</script>
<style scoped lang="scss">
.show-res-btn {
  display: flex;
  justify-content: center;
  align-items: center;
}
.picture-card {
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  cursor: pointer;
  line-height: 146px;
  vertical-align: top;
}
.with-img {
  position: relative;
  overflow: hidden;
  border: 1px solid #c0ccda;
  cursor: unset;
  .action {
    opacity: 0;
    position: absolute;
    top:0;
    bottom:0;
    right: 0;
    left: 0;
    z-index: 1;
    display: flex;
    color: #fff ;
    font-size: 24px;
    background-color: rgba(0, 0, 0, 0.4);
    justify-content: center;
    gap: 20px;
    align-items: center;
    transition: all .2s;
  }
  &:hover {
    .action {
      opacity: 1;
    }
  }
}
// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
    transition: all 0s;
}

::v-deep .el-list-enter, .el-list-leave-active {
    opacity: 0;
    transform: translateY(0);
}

::v-deep .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
.preview-video {
  width: 350px;
  height: 100%;
  max-height: 80vh;
}
</style>

