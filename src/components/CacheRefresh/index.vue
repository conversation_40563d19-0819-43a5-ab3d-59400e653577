<template>
  <div @click="refreshCache">
    <svg-icon icon-class="刷新" />
  </div>
</template>

<script>
export default {
  name: 'CacheRefresh',
  methods: {
    refreshCache() {
      this.$confirm('确定要刷新页面缓存吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const route = this.$route
        const query = { ...route.query }
        query._t = Date.now()
        this.$router.replace({ path: route.path, query })
        window.location.reload(true)
      }).catch(() => {})
    }
  }
}
</script>
