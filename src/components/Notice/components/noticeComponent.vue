<script setup>
import { computed, ref, getCurrentInstance, watch } from 'vue'
import noticeTable from './noticeTable.vue'
import { allRead, readReceiver } from '@/api/system/receiver'
import dayjs from 'dayjs'
const props = defineProps({
  visible: Boolean,
  unreadForm: {
    type: Object,
    default: () => {}
  }
})
const { proxy } = getCurrentInstance()

const activeName = ref('inform')
const emit = defineEmits(['update:visible', 'update:form', 'refresh'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})
const checked = ref(false)
const offlineList = ref(null)
if (localStorage.getItem('offineVisible')) {
  offlineList.value = JSON.parse(localStorage.getItem('offineVisible'))
}
if (offlineList.value) {
  checked.value = offlineList.value.state || false
}
watch(
  () => props.visible,
  (val) => {
    if (!val) {
      const list = {
        date: dayjs().valueOf(),
        state: checked.value
      }
      localStorage.setItem('offineVisible', JSON.stringify(list))
    }
  }
)
function handleRefresh() {
  proxy.$emit('refresh')
}
const informNoticeTableRef = ref()
const logNoticeTableRef = ref()
const bulletinCountRef = ref()
const popupLogRef = ref()
function handleRead() {
  allRead().then((res) => {
    // 刷新统计未读总数
    handleRefresh()
    // 刷新各个消息列表
    informNoticeTableRef.value.getList()
    logNoticeTableRef.value.getList()
    bulletinCountRef.value.getList()
    popupLogRef.value.getList()
    open.value = false
    proxy.$modal.msgSuccess('操作成功')
  })
}
const multiple = ref(false)
const ids = ref([])
function handleSelectTable(val) {
  ids.value = val.map((item) => item.id)
  multiple.value = val.length > 0
}
function handleMarkRead() {
  proxy
    .$confirm('确定要将选中消息标为已读吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      readReceiver({ ids: ids.value.toString() }).then((res) => {
        proxy.$modal.msgSuccess('操作成功')
        proxy.$emit('refresh')
        if (activeName.value === 'inform') {
          informNoticeTableRef.value.getList()
        } else if (activeName.value === 'log') {
          logNoticeTableRef.value.getList()
        } else if (activeName.value === 'notice') {
          bulletinCountRef.value.getList()
        } else if (activeName.value === 'popup') {
          popupLogRef.value.getList()
        }
      })
    })
    .catch(() => {})
}
function handleClick() {
  informNoticeTableRef.value.toggleSelection()
  logNoticeTableRef.value.toggleSelection()
  bulletinCountRef.value.toggleSelection()
  popupLogRef.value.toggleSelection()
  const arr = informNoticeTableRef.value.selectedList
  const arr2 = logNoticeTableRef.value.selectedList
  const arr3 = bulletinCountRef.value.selectedList
  const arr4 = popupLogRef.value.selectedList
  switch (activeName.value) {
    case 'inform':
      if (arr.length) {
        ids.value = arr.map((item) => item.id)
      } else {
        ids.value = []
      }
      multiple.value = arr.length > 0
      break
    case 'log':
      if (arr2.length) {
        ids.value = arr2.map((item) => item.id)
      } else {
        ids.value = []
      }
      multiple.value = arr2.length > 0
      break
    case 'notice':
      if (arr3.length) {
        ids.value = arr3.map((item) => item.id)
      } else {
        ids.value = []
      }
      multiple.value = arr3.length > 0
      break
    case 'popup':
      if (arr4.length) {
        ids.value = arr4.map((item) => item.id)
      } else {
        ids.value = []
      }
      break
    default:
      break
  }
}

defineExpose({
  setActive: (active) => {
    activeName.value = active
  }
})
</script>

<template>
  <el-dialog
    :visible.sync="open"
    title="消息通知"
    top="15vh"
    width="900px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="allRead">
      <el-button type="primary" plain size="mini" @click="handleRead">
        全部标为已读
      </el-button>
      <el-button
        type="primary"
        plain
        size="mini"
        :disabled="!multiple"
        @click="handleMarkRead"
      >
        标为已读
      </el-button>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        :label="`通知(${unreadForm.noticeCount || 0})`"
        name="inform"
      >
        <notice-table
          v-if="open"
          ref="informNoticeTableRef"
          :notice-type="1"
          @refresh="handleRefresh"
          @selectTable="handleSelectTable"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="`上线日志(${unreadForm.upgradeLogCount || 0})`"
        name="log"
      >
        <notice-table
          v-if="open"
          ref="logNoticeTableRef"
          :notice-type="3"
          @refresh="handleRefresh"
          @selectTable="handleSelectTable"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="`公告(${unreadForm.bulletinCount || 0})`"
        name="notice"
      >
        <notice-table
          v-if="open"
          ref="bulletinCountRef"
          :notice-type="2"
          @refresh="handleRefresh"
          @selectTable="handleSelectTable"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="`弹窗消息(${unreadForm.popupLogCount || 0})`"
        name="popup"
      >
        <notice-table
          v-if="open"
          ref="popupLogRef"
          :notice-type="4"
          @refresh="handleRefresh"
          @selectTable="handleSelectTable"
        />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <el-checkbox
        v-model="checked"
        class="footer-checkbox"
      >今日不再提醒</el-checkbox>
      <el-button size="medium" @click="open = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.sync-text {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  align-items: center;
  padding-top: 200px;
}

.footer-checkbox {
  padding-right: 20px;
}
.allRead {
  position: absolute;
  top: 20px;
  left: 120px;
}
</style>
