<script setup>
import { computed, ref, getCurrentInstance, onMounted } from 'vue'

import { listReceiver, readReceiver } from '@/api/system/receiver'
const props = defineProps({
  noticeType: {
    type: Number,
    default: 1
  },
  orderPayRange: {
    type: Array,
    default: () => []
  }
})
const tableLoading = ref(false)
const { proxy } = getCurrentInstance()
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 6
})
const recordList = ref([])
const emit = defineEmits([
  'update:visible',
  'update:form',
  'refresh',
  'selectTable'
])
function getList() {
  const data = {
    ...queryParams.value,
    noticeType: props.noticeType
  }
  tableLoading.value = true
  listReceiver(data)
    .then((res) => {
      recordList.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      tableLoading.value = false
    })
}
const selectedList = ref([])
const ids = ref([])
const multiple = ref(false)
const handleSelectionChange = (val) => {
  selectedList.value = val
  ids.value = val.map((item) => item.id)
  multiple.value = val.length > 0

  proxy.$emit('selectTable', selectedList.value)
}

const dialogVisible = ref(false)
const selectRow = ref()
function handleDetail(row) {
  selectRow.value = row
  dialogVisible.value = true
  if (row.status === 0) {
    readReceiver({ ids: row.id }).then((res) => {
      this.getList()
      proxy.$emit('refresh')
    })
  }
}
const tableRef = ref()
function toggleSelection() {
  tableRef.value.clearSelection()
}
// 暴露方法
defineExpose({
  getList,
  toggleSelection,
  selectedList
})
onMounted(() => {
  getList()
})
</script>

<template>
  <div style="position: relative">
    <div class="operate-table-wrap">
      <el-table
        ref="tableRef"
        v-loading="tableLoading"
        height="336px"
        :data="recordList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" :selectable="(row)=>{return row.status===0}" width="55" align="center" />
        <el-table-column
          label="标题"
          align="left"
          prop="noticeTitle"
          min-width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              class="text_color"
              :class="scope.row.status === 1 ? 'read_color' : 'unread_color'"
              @click="handleDetail(scope.row)"
            >{{ scope.row.noticeTitle }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          align="center"
          prop="status"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="info">已读</el-tag>
            <el-tag
              v-else-if="scope.row.status === 0"
              type="success"
            >未读</el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="接收时间"
          align="center"
          min-width="100"
          prop="receiverTime"
        >
          <template slot-scope="scope">
            <span
              :class="scope.row.status === 1 ? 'read_color' : 'unread_color'"
            >{{ scope.row.receiverTime }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[6, 10, 20]"
      @pagination="getList"
    />
    <!-- 消息详情 -->
    <el-drawer
      v-if="dialogVisible"
      :title="selectRow?.noticeTitle"
      :visible.sync="dialogVisible"
      append-to-body
    >
      <div class="drawer-wrap">
        <div v-html="selectRow?.noticeContent" />
      </div>
    </el-drawer>
  </div>
</template>

<style scoped lang="scss">
.text_color {
  cursor: pointer;
}
.text_color:hover {
  text-decoration: underline;
}
.unread_color {
  color: #000;
}
.read_color {
  color: #999;
}
.drawer-wrap {
  padding: 0 20px;
  overflow-y: auto;
}
.markRead {
  position: absolute;
  top: 0;
  left: 0;
}
</style>
