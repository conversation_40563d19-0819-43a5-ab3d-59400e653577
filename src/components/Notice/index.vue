<template>
  <div>
    <div @click="noticeVisible = true">
      <el-badge value="new" :hidden="noticeLen === 0" class="message-icon">
        <i class="el-icon-message" />
      </el-badge>
    </div>

    <notice-component
      ref="noticeComponentRef"
      :visible.sync="noticeVisible"
      :unread-form="unreadForm"
      @refresh="handleRefresh()"
    />
  </div>
</template>

<script>
import noticeComponent from './components/noticeComponent.vue'
import dayjs from 'dayjs'
export default {
  name: 'NoticeVisible',
  components: {
    noticeComponent
  }
}
</script>

<script setup>
import { onMounted, ref, reactive, onBeforeUnmount } from 'vue'
import useVisibilityChange from '@/hooks/useVisibilityChange'
import { getUnreadMessages } from '@/api/system/notice'
import { Notification } from 'element-ui'

const noticeLen = ref(0)
const noticeComponentRef = ref()

const unreadForm = reactive({
  bulletinCount: 0,
  noticeCount: 0,
  upgradeLogCount: 0
})
const offlineList = ref(null)
if (localStorage.getItem('offineVisible')) {
  offlineList.value = JSON.parse(localStorage.getItem('offineVisible'))
}
// 判断时间戳是否为今天的函数
function isToday(timestamp) {
  // / 获取今日的开始时间戳（以毫秒为单位）
  const startOfTodayTimestamp = dayjs().startOf('day').valueOf()
  // 获取今日的结束时间戳（以毫秒为单位）
  const endOfTodayTimestamp = dayjs().endOf('day').valueOf()
  if (timestamp >= startOfTodayTimestamp && timestamp <= endOfTodayTimestamp) {
    return true
  } else {
    return false
  }
}
const noticeVisible = ref(false)
const notificationRef = ref(false)
const fetchUnlist = (val) => {
  getUnreadMessages().then((res) => {
    const { bulletinCount, noticeCount, upgradeLogCount } = res.data
    unreadForm.bulletinCount = bulletinCount
    unreadForm.noticeCount = noticeCount
    unreadForm.upgradeLogCount = upgradeLogCount
    noticeLen.value =
      Number(bulletinCount) + Number(noticeCount) + Number(upgradeLogCount)
    if (val) {
      return
    }
    if (noticeLen.value > 0) {
      if (offlineList.value && offlineList.value.state) {
        const state = isToday(offlineList.value.date)
        if (state && offlineList.value.state) {
          noticeVisible.value = false
        } else {
          noticeVisible.value = true
        }
      } else {
        noticeVisible.value = true
      }
    }
    fetchPopupLog(res)
  })
}
const fetchPopupLog = async(res) => {
  if (!res) {
    res = await getUnreadMessages()
  }
  const { popupLogCount } = res.data
  unreadForm.popupLogCount = popupLogCount
  if (popupLogCount > 0) {
    notificationRef.value = Notification.warning({
      title: '有未读消息',
      message: `您有<span class="color-danger">${popupLogCount}</span>条未读消息，请及时<a class="color-primary" onclick="showPopupLog()">查看</a>`,
      position: 'bottom-right',
      customClass: 'shake-notification',
      dangerouslyUseHTMLString: true,
      offset: 50,
      duration: 59 * 1000
    })
  }
}

function handleRefresh() {
  fetchUnlist(true)
}

let timer = null
const clearTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

const loopPopupLog = () => {
  clearTimer()
  timer = setInterval(() => {
    fetchPopupLog()
  }, 1000 * 60)
}

useVisibilityChange({
  onVisible: loopPopupLog,
  onHidden: clearTimer
})

onMounted(() => {
  fetchUnlist()
  loopPopupLog()
  window.showPopupLog = () => {
    noticeVisible.value = true
    noticeComponentRef.value.setActive('popup')
    notificationRef.value.close()
  }
})

onBeforeUnmount(() => {
  clearTimer()
})

</script>

<style lang="scss" scoped>
::v-deep .el-badge__content {
  height: auto;
}
.message-icon {
  line-height: 22px;
  font-size: 22px;
  height: 26px;
}
.see-more {
  margin-top: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.info-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}
.drawer-wrap {
  padding: 0 20px;
  overflow-y: auto;
}

</style>
<style>

@keyframes horizontal-shaking {
  0% { transform: translateX(0) }
  30% { transform: translateX(0) }
  40% { transform: translateX(-5px) }
  50% { transform: translateX(5px) }
  60% { transform: translateX(-5px) }
  70% { transform: translateX(5px) }
  80% { transform: translateX(0) }
  100% { transform: translateX(0) }
}

.shake-notification {
  animation: horizontal-shaking 1s infinite;
}
</style>
