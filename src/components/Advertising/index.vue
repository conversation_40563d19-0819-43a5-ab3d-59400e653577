<template>
  <div class="advertising-wrap">
    <div class="sync-plan">
      <el-button
        icon="el-icon-refresh"
        size="mini"
        @click="handleQuery"
      >刷新</el-button>
      <el-button
        v-hasPermi="['promotion:advertising:synchronizationPlan']"
        icon="el-icon-refresh"
        size="mini"
        @click="syncAdvertising"
      >同步广告</el-button>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="广告列表" name="ad">
        <div class="files-table-wrap" style="height: 600px">
          <el-table v-loading="loading.ad" height="100%" :data="advertisingList">
            <el-table-column label="广告名称" prop="promotionName" width="200" show-overflow-tooltip />
            <el-table-column label="预览" align="center" prop="filePath">
              <template #default="scope">
                <div class="file-list">
                  <div v-for="m in scope.row.mediaMaterialListVOList" :key="m.id">
                    <div v-if="m.materialType === '2'" class="poster-wrap" @click="playVideo(m.url)">
                      <div class="poster-icon">
                        <i class="el-icon-video-play" />
                        <!--                        <i class="el-icon-download" @click="downloadVideo(scope.row.url)" />-->
                      </div>
                      <el-image :src="m.posterUrl" class="video-poster" fit="cover" />
                    </div>
                    <image-preview v-else :src="m.url" :width="60" :height="60" />
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="promotionCreateTime" width="180" />
            <el-table-column label="操作" align="center" prop="action" width="120">
              <template #default="scope">
                <ClipboardButton :value="getUrls(scope.row.mediaMaterialListVOList)">复制地址</ClipboardButton>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          :total="advertisingTotal"
          :page.sync="advertisingQuery.pageNum"
          :limit.sync="advertisingQuery.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="素材列表" name="mat">
        <div class="files-table-wrap" style="height: 600px">
          <el-table v-loading="loading.mat" height="100%" :data="materialList">
            <el-table-column label="预览" align="center" prop="filePath" width="100">
              <template #default="scope">
                <div v-if="scope.row.materialType === 2" class="poster-wrap" @click="playVideo(scope.row.url)">
                  <div class="poster-icon">
                    <i class="el-icon-video-play" />
                  </div>
                  <el-image :src="scope.row.posterUrl" class="video-poster" fit="cover" />
                </div>
                <image-preview v-else :src="scope.row.url" :width="60" :height="60" />
              </template>
            </el-table-column>
            <el-table-column label="文件名" prop="filename" show-overflow-tooltip />
            <el-table-column label="文件类型" align="center" prop="format" width="80" show-overflow-tooltip />
            <el-table-column label="文件大小" align="center" prop="fileSize" width="120">
              <template #default="scope">
                <span>{{ scope.row.size | bytes }}</span>
              </template>
            </el-table-column>
            <el-table-column label="素材标签" prop="tag" show-overflow-tooltip width="180" />
            <el-table-column label="上传时间" align="center" prop="uploadTime" width="160" />
            <el-table-column label="操作" align="center" prop="action" width="120">
              <template #default="scope">
                <ClipboardButton :value="scope.row.url">复制地址</ClipboardButton>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="bottom-operation">
          <div>
            <ClipboardButton :value="urls">
              复制素材
            </ClipboardButton>
          </div>
          <pagination
            :total="materialTotal"
            :page.sync="materialQuery.pageNum"
            :limit.sync="materialQuery.pageSize"
            @pagination="fetchMaterialList"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      :visible.sync="videoVisible"
      title="预览"
      width="700"
      top="5vh"
      append-to-body
      destroy-on-close
    >
      <video v-if="videoVisible" ref="videoRef" class="preview-video" :src="videoSrc" controls @canplay="canPlay">
        <meta name="referrer" content="never">
      </video>
    </el-dialog>
  </div>
</template>

<script>
import { getAdvertising, getMaterialList, syncPlan } from '@/api/promotion/fileres'
import ClipboardButton from '@/components/ClipboardButton/index.vue'

export default {
  name: 'Advertising',
  components: { ClipboardButton },
  filters: {
    bytes(value) {
      if (value === undefined || value === null || value === 0) return '-'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(value) / Math.log(k))
      return (value / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    }
  },
  props: {
    advertiserId: {
      type: String,
      default: ''
    },
    planId: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    activeName: 'ad',
    advertisingQuery: {
      pageNum: 1,
      pageSize: 10
    },
    advertisingTotal: 0,
    materialQuery: {
      pageNum: 1,
      pageSize: 10
    },
    materialTotal: 0,
    advertisingList: [],
    materialList: [],
    loading: {
      ad: false,
      mat: false
    },
    videoVisible: false,
    videoSrc: ''
  }),
  computed: {
    urls() {
      return this.materialList.map(item => item.url).join('\n')
    }
  },
  watch: {
    advertiserId() {
      this.reset()
      this.getList()
      this.fetchMaterialList()
    }
  },
  mounted() {
    this.getList()
    this.fetchMaterialList()
  },
  methods: {
    handleQuery() {
      if (this.activeName === 'ad') {
        this.getList()
      } else {
        this.fetchMaterialList()
      }
    },
    getList() {
      this.loading.ad = true
      if (this.planId) {
        this.advertisingQuery.promotionId = this.planId
      } else if (this.advertiserId) {
        this.advertisingQuery.advertiserId = this.advertiserId
      }
      // this.planId && (this.advertisingQuery.planId = this.planId)
      // this.advertiserId && (this.advertisingQuery.advertiserId = this.advertiserId)
      getAdvertising(this.advertisingQuery).then(response => {
        this.advertisingList = response.rows
        this.advertisingTotal = response.total
        this.loading.ad = false
      })
    },
    fetchMaterialList() {
      this.loading.mat = true
      this.materialQuery.advertiserId = this.advertiserId
      this.materialQuery.materialType = 2
      getMaterialList(this.materialQuery).then(response => {
        this.materialList = response.rows
        this.materialTotal = response.total
      }).finally(() => {
        this.loading.mat = false
      })
    },
    reset() {
      this.advertisingQuery = {
        pageNum: 1,
        pageSize: 10
      }
      this.materialQuery = {
        pageNum: 1,
        pageSize: 10
      }
    },
    playVideo(src) {
      this.videoSrc = src
      this.videoVisible = true
    },
    canPlay() {
      this.$nextTick(() => {
        this.$refs.videoRef.play().catch((err) => console.log('err:', err))
      })
    },
    syncAdvertising() {
      this.$confirm('确定同步广告吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        syncPlan({ advertiserId: this.advertiserId }).then(response => {
          if (response.code === 200) {
            this.$modal.msgSuccess(response.msg || '同步成功')
          } else {
            this.$modal.msgError('同步失败：' + response.msg)
          }
        })
      }).catch(() => {})
    }
  }
}
</script>

<script setup>
const getUrls = (list) => {
  if (!list) return ''
  return list.reduce((acc, cur) => {
    return (`${acc}
${cur.url}`)
  }, '')
}
</script>

<style lang="scss" scoped>
.advertising-wrap {
  position: relative;
  .sync-plan {
    position: absolute;
    right: 0;
    top:0;
    z-index: 999;
  }
}
.file-list {
  display: flex;
  overflow-y: auto;
}
.poster-wrap {
  position: relative;
  &:hover {
    .poster-icon {
      opacity: 1;
    }
  }
  .poster-icon {
    transition: opacity 0.3s ease-in-out;
    opacity: 0;
    position: absolute ;
    right: 5px;
    bottom: 5px;
    left:5px;
    top:5px;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 10;

    color: white;
    font-size: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3px;
    i {
      opacity: 0.6;
      cursor: pointer;
      &:hover {
        opacity: 1
      }
    }
  }
}
.video-poster {
  width: 60px;
  height: 60px;
  border-radius: 5px;
  margin:5px 5px 0;
}
.preview-video {
  width: 100%;
  max-height: calc(100vh - 200px)
}
.bottom-operation {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
