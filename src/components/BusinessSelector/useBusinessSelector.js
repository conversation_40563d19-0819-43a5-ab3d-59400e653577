import { ref } from 'vue'
import { checkPermi } from '@/utils/permission'
import { getConfigKey } from '@/api/system/config'
import { allocatedUserList } from '@/api/system/role'

const userList = ref([])
export default function useBusinessSelector() {
  if (checkPermi(['promotion:config:duoduo']) && !userList.value.length) {
    getConfigKey('BUSINESS_ROLE_ID').then((res) => {
      allocatedUserList({
        pageNum: 1,
        pageSize: 100,
        roleId: res.msg
      }).then((res) => {
        userList.value = res.rows
      })
    })
  }
  return userList
}
