<script setup>
import { computed } from 'vue'
import useBusinessSelector from './useBusinessSelector'

const userList = useBusinessSelector()
const props = defineProps({
  business: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '商务负责人'
  },
  label: {
    type: String,
    default: ''
  },
  formProp: {
    type: String,
    default: 'business'
  },
  permission: {
    type: Array,
    default: () => ['system:role:business']
  }
})
const emit = defineEmits(['query', 'update:business'])
const business = computed({
  get: () => props.business,
  set: (str) => {
    emit('update:business', str)
  }
})

const handleQuery = () => {
  emit('query')
}
</script>

<template>
  <el-form-item v-has-permi="permission" :label="label" :prop="formProp">
    <el-select
      v-model="business"
      :placeholder="placeholder"
      clearable
      filterable
      @change="handleQuery"
    >
      <el-option
        v-for="user in userList"
        :key="user.value"
        :label="`${user.nickName} (${user.userName})`"
        :value="user.userName"
      />
    </el-select>
  </el-form-item>
</template>

<style scoped lang="scss">

</style>
