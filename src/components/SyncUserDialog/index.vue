<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" append-to-body @close="handleClose">
    <el-form ref="syncUserForm" :model="syncUserForm" :rules="syncUserRules" label-width="100px">
      <el-form-item label="企业主体" prop="corpId">
        <el-select v-model="syncUserForm.corpId" placeholder="请选择企业微信" clearable filterable class="w-full" @change="handleCorpChange">
          <el-option
            v-for="corp in corpOptions"
            :key="corp.corpId"
            :label="`${corp.corpName} (${corp.corpId})`"
            :value="corp.corpId"
          >
            <span style="float: left">{{ corp.corpName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ corp.corpId }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择员工" prop="userIdList">
        <el-table
          ref="userTable"
          v-loading="userTableLoading"
          :data="corpUserList"
          border
          height="300"
          @selection-change="handleUserSelectionChange"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column prop="userName" label="员工姓名" min-width="120" />
          <el-table-column prop="userId" label="员工ID" min-width="150" />
          <el-table-column label="状态" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.robotStatus === 1 ? 'success' : 'danger'" size="mini">
                {{ scope.row.robotStatus === 1 ? '在线' : '离线' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <div class="footer-left">
        <span v-if="syncUserForm.userIdList.length > 0" class="selected-users-info">
          已选择 <span class="color-primary font-bold">{{ syncUserForm.userIdList.length }}</span> 个员工
        </span>
      </div>
      <div class="footer-right">
        <el-button type="primary" :loading="submitting" @click="confirmSync">确认同步</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getAuthCorpList } from '@/api/wx/authCorp'
import { getAllCorpUsers } from '@/api/wx/corpUsers'

export default {
  name: 'SyncUserDialog',
  props: {
    // 对话框标题
    title: {
      type: String,
      default: '同步指定员工'
    },
    // 同步API函数
    syncApi: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      // 同步指定员工表单参数
      syncUserForm: {
        corpId: null,
        userIdList: []
      },
      // 同步指定员工表单校验
      syncUserRules: {
        corpId: [
          { required: true, message: '请选择企业微信', trigger: 'change' }
        ],
        userIdList: [
          { required: true, message: '请至少选择一个员工', trigger: 'change' }
        ]
      },
      // 企业选项列表
      corpOptions: [],
      // 企业员工列表
      corpUserList: [],
      // 员工表格加载状态
      userTableLoading: false,
      // 提交状态
      submitting: false
    }
  },
  computed: {
    dialogTitle() {
      return this.title
    }
  },
  methods: {
    /** 打开对话框 */
    open() {
      this.loadCorpOptions()
      this.resetForm()
      this.dialogVisible = true
    },

    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },

    /** 重置表单 */
    resetForm() {
      this.syncUserForm.corpId = null
      this.syncUserForm.userIdList = []
      this.corpUserList = []
      if (this.$refs.syncUserForm) {
        this.$refs.syncUserForm.clearValidate()
      }
    },

    /** 加载企业选项 */
    loadCorpOptions() {
      getAuthCorpList().then(response => {
        this.corpOptions = response.rows || []
      }).catch(err => {
        console.error('获取企业列表失败:', err)
        this.$modal.msgError('获取企业列表失败')
      })
    },

    /** 处理企业变化 */
    async handleCorpChange(corpId) {
      this.syncUserForm.userIdList = []
      this.corpUserList = []

      if (corpId) {
        await this.loadCorpUsers(corpId)
      }
    },

    /** 加载企业员工列表 */
    async loadCorpUsers(corpId) {
      if (!corpId) return

      this.userTableLoading = true
      try {
        const response = await getAllCorpUsers({
          corpId: corpId,
          pageNum: 1,
          pageSize: 1000
        })
        this.corpUserList = response.data || []
      } catch (error) {
        console.error('获取企业员工失败：', error)
        this.$modal.msgError('获取企业员工失败，请稍后重试')
        this.corpUserList = []
      } finally {
        this.userTableLoading = false
      }
    },

    /** 处理员工选择变化 */
    handleUserSelectionChange(selection) {
      this.syncUserForm.userIdList = selection.map(user => user.userId)
    },

    /** 确认同步 */
    confirmSync() {
      this.$refs['syncUserForm'].validate(valid => {
        if (valid) {
          this.submitting = true
          const requestData = {
            corpId: this.syncUserForm.corpId,
            userIdList: this.syncUserForm.userIdList
          }

          this.syncApi(requestData).then(res => {
            if (res.code === 200) {
              this.$modal.msgSuccess(res.msg || '同步任务已提交，请耐心等待')
              this.handleClose()
              this.$emit('success')
            } else {
              this.$modal.msgError(res.msg || '同步失败')
            }
          }).catch(err => {
            console.error('同步失败:', err)
            this.$modal.msgError('同步失败，请稍后重试')
          }).finally(() => {
            this.submitting = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-left {
    flex: 1;

    .selected-users-info {
      color: #606266;
      font-size: 14px;
      margin-right: 10px;
    }
  }

  .footer-right {
    display: flex;
    gap: 10px;
  }
}

// 同步指定员工对话框样式
:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
    }
  }
}
</style>
