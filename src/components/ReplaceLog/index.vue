<script>
export default {
  name: 'ReplaceLog'
}
</script>
<script setup>
import { getCurrentInstance, ref } from 'vue'
import { getGoods, getRecordList } from '@/api/promotion/goods'
import useDialog from '@/hooks/useDialog'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'

const operateTypeMap = {
  1: '暂停计划',
  2: '替换链接',
  3: '更新监控链接',
  4: '更新出价',
  5: '修改回传',
  6: '修改锚点'
}

const operateStateOptions = [{
  label: '成功',
  value: 1
}, {
  label: '失败',
  value: 0
}]

const self = getCurrentInstance().proxy
const props = defineProps({
  visible: Boolean,
  queryParams: {
    type: Object,
    default: () => ({})
  },
  goods: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close'])

const { open: logOpen, close } = useDialog({ props, emit, onWatch: (val) => {
  if (val) {
    list.value = []
    resetQuery()
    getList()
    getReplaceGoods()
  }
} })

const queryLoading = ref(false)
const list = ref([])
const query = ref({
  pageNum: 1,
  pageSize: 10
})
const total = ref(0)

const getList = () => {
  queryLoading.value = true
  getRecordList({
    ...props.queryParams,
    ...query.value
  }).then(response => {
    list.value = response.rows
    total.value = response.total
  })
}

const replaceGoods = ref(null)
const getReplaceGoods = () => {
  replaceGoods.value = null
  if (props.goods.replaceGoodsId) {
    getGoods(props.goods.replaceGoodsId).then(response => {
      replaceGoods.value = response.data
    })
  }
}

const onQuery = () => {
  query.value.pageNum = 1
  getList()
}
const reset = () => {
  list.value = []
  resetQuery()
  getList()
}

const resetQuery = () => {
  query.value = {
    pageNum: 1,
    pageSize: 10
  }
  self.resetForm('queryForm')
}
</script>

<template>
  <el-dialog
    title="替换记录"
    :visible.sync="logOpen"
    top="5vh"
    width="80%"
    append-to-body
  >
    <el-form ref="operateForm" class="search-form" :model="query" size="small" :inline="true" label-width="80px">
      <template v-if="replaceGoods">
        <el-descriptions direction="vertical" :column="4" border size="small">
          <el-descriptions-item label="原商品">
            <BaseInfoCell :id="goods.goodsId" :name="goods.goodsName" no-copy />
          </el-descriptions-item>
          <el-descriptions-item label="替换商品">
            <BaseInfoCell :id="replaceGoods.goodsId" :name="replaceGoods.goodsName" no-copy />
          </el-descriptions-item>
        </el-descriptions>
        <el-divider />
      </template>
      <el-form-item prop="businessId">
        <el-input
          v-model.trim="query.businessId"
          placeholder="业务ID"
          clearable
          @keyup.enter.native="onQuery"
        />
      </el-form-item>
      <el-form-item prop="businessName">
        <el-input
          v-model.trim="query.businessName"
          placeholder="业务名称"
          clearable
          @keyup.enter.native="onQuery"
        />
      </el-form-item>
      <el-form-item prop="advertiserId">
        <el-input
          v-model.trim="query.advertiserId"
          placeholder="广告主ID"
          clearable
          @keyup.enter.native="onQuery"
        />
      </el-form-item>
      <el-form-item prop="operateState">
        <el-select v-model="query.operateState" placeholder="操作状态" clearable @change="onQuery">
          <el-option
            v-for="dict in operateStateOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="operateType">
        <el-select v-model="query.operateType" placeholder="操作类型" clearable @change="onQuery">
          <el-option label="暂停计划" value="1" />
          <el-option label="替换链接" value="2" />
          <el-option label="更新监控链接" value="3" />
          <el-option label="更新出价" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="onQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="operate-table-wrap">
      <el-table height="100%" :data="list">
        <el-table-column label="业务ID" align="center" prop="businessId" show-overflow-tooltip>
          <template #header>
            <span>业务ID</span>
            <el-tooltip effect="dark" content="巨量: 广告ID；腾讯: 创意ID" placement="top">
              <i class="el-icon-question color-primary" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="业务名称" align="center" prop="businessName" show-overflow-tooltip />
        <el-table-column label="广告主ID" align="center" prop="advertiserId" show-overflow-tooltip />
        <el-table-column label="操作类型" align="center" prop="operateType" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ operateTypeMap[scope.row.operateType] }}
          </template>
        </el-table-column>
        <el-table-column label="操作状态" align="center" prop="operateState" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.operateState === 1" type="success">成功</el-tag>
            <el-tag v-else-if="scope.row.operateState === 0" type="danger">失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="来源" align="center" prop="source" show-overflow-tooltip />
        <el-table-column label="消息" align="center" prop="operateErrorMessage" show-overflow-tooltip />
        <el-table-column label="操作时间" align="center" width="165" prop="operateTime">
          <template slot-scope="scope">
            <span>{{ scope.row.operateTime.replace('T', ' ') }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="query.pageNum"
      :limit.sync="query.pageSize"
      @pagination="getList"
    />
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="close">确 定</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">
.operate-table-wrap {
  height: calc(100vh - 450px);
}
</style>
