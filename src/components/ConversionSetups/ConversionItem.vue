<script setup>
import { PercentValue } from '@/utils/render'
import { computed, ref } from 'vue'
import { changeConversionSetups } from '@/api/promotion/media'
import { typeMap } from '@/components/ConversionSetups/config'
import { Message } from 'element-ui'
const props = defineProps({
  form: null,
  type: {
    type: String,
    default: 'media' // plan, goods. user
  },
  itemType: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:enable', 'setConversion', 'switchConversion'])

const defaultForm = {
  type: 1, // 类型(1媒体账户,2计划,3商品,4用户)
  businessId: '', // 业务id根据type对应不同id
  goodsPlatformType: null, // 商品的电商平台
  mediaPlatformType: null, // 媒体的媒体平台
  enableConversion: 0, // 是否开启回传限制(0:不开启;1:开启)
  conversionProportion: 100, // 回传比例
  conversionStartOrderCount: 0, // 回传比例开始扣的订单数
  deductAreaEnable: 0, // 扣回传区间开关（1-开 0-关）
  deductAreaMin: 0, // 扣回传区间最小值
  deductAreaMax: 0, // 扣回传区间最大值
  deductOutsideAreaEnable: 0, // 区间外的是否回传(0全回传1全不回传)
  newBid: 0, // 按比例计算后广告新的出价价格
  bid: 0, // 原始广告出价价格
  conversionDayOrHour: 0, // 按比例回传的生效失效(0按天,1按小时,2不重置)
  enableAmount: 0, // 是否开启回传金额控制(0:不开启;1:开启)
  amountProportion: 100, // 回传金额比例
  delayEnable: 0, // 延迟回传开关（1-开 0-关）
  delaySecond: 0, // 延迟回传设置的秒数
  bidEnable: false, // 是否开启修改出价
  conversionAmountType: 0, // 金额按比例回传类型(0按比例,1是按固定金额)
  conversionAmount: 0, // 金额按比例回传类型(0按比例,1是按固定金额)
  conversionDeductionCsite: false,
  conversionDeductionScope: 1 // 扣回传生效范围(默认0:账户,1计划)
}

const keyMap = {
  order: {
    enable: 'enableConversion',
    proportion: 'conversionProportion'
  },
  amount: {
    enable: 'enableAmount',
    proportion: 'amountProportion'
  }
}
// 回传类型
const conversionType = computed(() => {
  return typeMap[props.type].type
})

// 回传id
const businessIdKey = computed(() => {
  return typeMap[props.type].idKey
})

const loading = ref(false)
const fakeEnable = ref(false)
const enable = computed({
  get: () => {
    return props.form.conversionConfig ? props.form.conversionConfig[keyMap[props.itemType]?.enable] : fakeEnable.value
  },
  set: (val) => {
    if (props.form.conversionConfig) {
      const form = { ...props.form }
      form.conversionConfig[keyMap[props.itemType]?.enable] = val
      emit('update:form', form)
    } else {
      fakeEnable.value = val
    }
  }
})

// 数值展示
const proportion = computed(() => {
  if (props.itemType === 'amount' && props.form.conversionConfig?.conversionAmountType === 1) return props.form.conversionConfig.conversionAmount / 100 + '元'

  if (props.itemType === 'order' && props.form.conversionConfig?.batchConversionEnable) return '按批次'
  return PercentValue(props.form.conversionConfig?.[keyMap[props.itemType]?.proportion], '100%')
})

const handleUpdateConversion = () => {
  emit('setConversion', props.form, props.itemType)
}
const handleEnableConversion = () => {
  const postData = {
    ...defaultForm,
    type: conversionType.value,
    businessId: props.form[businessIdKey.value],
    mediaPlatformType: props.form.mediaType,
    enableConversion: false,
    enableAmount: false,
    conversionProportion: 100,
    amountProportion: 100,
    ...props.form.conversionConfig,
    bidEnable: false
  }

  switch (props.type) {
    case 'plan':
    case 'media':
      postData.mediaPlatformType = props.form.mediaType
      break
    case 'goods':
      postData.goodsPlatformType = props.form.platform
      postData.mediaPlatformType = props.form.mediaPlatformType
      break
  }

  if (props.itemType === 'amount' && !props.form.conversionConfig) {
    postData.enableAmount = !postData.enableAmount
  }
  if (props.itemType === 'order' && !props.form.conversionConfig) {
    postData.enableConversion = !postData.enableConversion
  }

  loading.value = true
  changeConversionSetups([postData]).then(response => {
    if (!props.form.conversionConfig.id) emit('switchConversion')
    Message.success('操作成功')
  }).finally(() => { loading.value = false })
}
</script>

<template>
  <div v-loading="loading" class="conversion-rate">
    <el-button type="text" :disabled="disabled" @click="handleUpdateConversion">
      {{ proportion }}
    </el-button>
    <el-switch
      v-model="enable"
      active-color="#13ce66"
      :disabled="disabled"
      @change="handleEnableConversion"
    />
  </div>
</template>

<style scoped lang="scss">
.conversion-rate {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}
</style>
