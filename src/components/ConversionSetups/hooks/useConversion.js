import { ref } from 'vue'
export default function({ type, onSave }) {
// 回传
  const conversionForm = ref([])
  const conversionDialogVisible = ref(false)
  const conversionFormType = ref('default')

  const showConversion = (row, type = 'default') => {
    conversionForm.value = [row]
    conversionDialogVisible.value = true
    conversionFormType.value = type
  }
  const batchUpdateConversion = (selections) => {
    conversionForm.value = selections
    conversionFormType.value = 'default'
    conversionDialogVisible.value = true
  }
  const handleConversionSave = () => {
    conversionDialogVisible.value = false
    onSave && onSave()
  }

  return {
    conversionType: type,
    conversionForm,
    conversionDialogVisible,
    conversionFormType,
    showConversion,
    batchUpdateConversion,
    handleConversionSave
  }
}
