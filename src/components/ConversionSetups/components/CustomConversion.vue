<script setup>
import { computed } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  value: {
    type: Array,
    default: () => []
  },
  type: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['input'])

const customList = computed({
  get() {
    if (props.value.length) {
      return props.value
    } else {
      const list = [{
        conversionProportion: 100,
        conversionStartOrderCount: 0,
        batchNum: 0,
        batchConversionNum: 0,
        startTime: '00:00',
        endTime: '23:59'
      }]
      emit('input', list)
      return list
    }
  },
  set(val) {
    emit('input', val.map(item => ({
      ...item
    })))
  }
})

const addItem = () => {
  customList.value.splice(customList.value.length - 1, 0, {
    conversionProportion: 100,
    conversionStartOrderCount: 0,
    batchNum: 0,
    batchConversionNum: 0,
    startTime: '00:00',
    endTime: '00:00'
  })
  handleListChange()
}

const removeItem = (i) => {
  customList.value.splice(i, 1)
  handleListChange()
}

const calcNewBatchConversionNum = (item) => {
  if (item.batchNum !== undefined) {
    if (item.batchNum < item.batchConversionNum) {
      item.batchConversionNum = item.batchNum
    }
  }
}

// item.startTime + ':00 - 23:59:00'
const handleTimeSelect = (item, i) => {
  handleListChange()
}

const handleListChange = () => {
  const len = customList.value.length
  if (len === 1) {
    Object.assign(customList.value[0], {
      startTime: '00:00',
      endTime: '23:59'
    })
    return
  }
  customList.value.forEach((item, i) => {
    if (i === 0) {
      item.startTime = '00:00'
    }
    if (isBeforeOrEqual(item.endTime, item.startTime)) {
      item.endTime = addOneMin(item.startTime)
    }
    const next = customList.value[i + 1]
    if (next) {
      next.startTime = addOneMin(item.endTime)
    }
    if (i === len - 1) {
      item.endTime = '23:59'
    }
    item.limit = addOneMin(item.startTime) + ':00 - 23:58:00'
  })
}

function addOneMin(time) {
  return dayjs('2000-01-01 ' + time).add(1, 'm').format('HH:mm')
}

function isBeforeOrEqual(start, end) {
  return dayjs('2000-01-01 ' + start).isBefore(dayjs('2000-01-01 ' + end)) || start === end
}

</script>

<template>
  <el-card class="mb10 mt10 mr10">
    <div v-for="(item, i) in customList" :key="i" class="custom-conversion-item">
      {{ item.startTime }} 至
      <el-time-picker
        v-if="customList.length-1 !== i"
        v-model="item.endTime"
        placeholder="结束时间"
        format="HH:mm"
        value-format="HH:mm"
        size="mini"
        :picker-options="{
          selectableRange: item.limit
        }"
        :clearable="false"
        style="width: 70px;margin:0 5px"
        @change="handleTimeSelect(item, i)"
      />
      <div v-else class="mr5 ml5"> {{ item.endTime }} </div>
      时，正常回传
      <el-input-number v-model="item.conversionStartOrderCount" size="mini" style="width: 60px;margin:0 5px" :min="0" :controls="false" /> 单后，
      <template v-if="type">
        每
        <el-input-number
          v-model="item.batchNum"
          size="mini"
          style="width: 50px;margin:0 5px"
          :min="1"
          :controls="false"
          @input="calcNewBatchConversionNum(item)"
        />单，回传前
        <el-input-number
          v-model="item.batchConversionNum"
          size="mini"
          style="width: 50px;margin:0 5px"
          :min="1"
          :max="item.batchNum"
          :controls="false"
        />单
      </template>
      <template v-else>
        以
        <el-input-number v-model="item.conversionProportion" size="mini" style="width: 60px;margin:0 5px" :min="0" :max="200" :controls="false" /> % 回传订单
      </template>

      <el-button v-show="customList.length-1 === i" icon="el-icon-plus" style="margin-left: 5px" size="mini" type="primary" @click="addItem">新增</el-button>
      <el-button v-show="customList.length-1 !== i" icon="el-icon-delete" style="margin-left: 5px;padding:5px" type="text" class="color-danger" @click="removeItem(i)" />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.custom-conversion-item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #d7dae2;
  padding-bottom: 5px;
  margin-bottom: 5px;
  &:last-of-type {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
  }
}
.end-divider {
  padding-bottom: 10px;
  border-bottom: 1px solid #d7dae2;
}
::v-deep(.el-date-editor .el-input__inner) {
  padding-right: 0px !important;
}
::v-deep(.el-input-number.is-without-controls .el-input__inner) {
  padding-right: 5px !important;
  padding-left: 5px !important;
}
</style>
