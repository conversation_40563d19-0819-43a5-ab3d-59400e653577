<template>
  <div>
    <BaseInfoCell :id="info.id" :name="info.name" no-copy class="mb10" />
    <div class="operate-table-wrap">
      <el-table
        height="100%"
        :data="list"
        @selection-change="handleSelectionChange"
      >
        <el-table-column label="状态" align="center" prop="operateState" min-width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.operateState === 1" type="success">成功</el-tag>
            <el-tag v-else-if="scope.row.operateState === 0" type="danger">失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center" min-width="120" prop="operateBy" />
        <el-table-column label="操作时间" align="center" min-width="165" prop="operateTime" />
        <el-table-column label="延迟回传" align="center" prop="delayEnable" show-overflow-tooltip min-width="100">
          <template slot-scope="{row}">
            <span v-if="row.delayEnable">
              {{ row.delaySecond }} s
            </span>
            <el-tag v-else type="danger">关闭</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column label="回传金额比例" align="center" prop="enableAmount" show-overflow-tooltip min-width="100">
          <template slot-scope="{row}">
            <span v-if="row.enableAmount">
              {{ row.amountProportion }} %
            </span>
            <el-tag v-else type="danger">关闭</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="回传配置" align="center" prop="enableConversion" show-overflow-tooltip min-width="200">
          <template slot-scope="{row}">
            <span v-if="row.enableConversion">
              {{ row.batchConversionEnable ? '按批次' : '按比例' }}
              {{ row.batchConversionEnable ? `每${row.batchNum}单，回传前${row.batchConversionNum}单，剩余${row.batchNum - row.batchConversionNum}单不上报` : `${row.conversionProportion} %` }}
            </span>
            <el-tag v-else type="danger">关闭</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="开始拦截订单数" align="center" prop="conversionStartOrderCount" min-width="120" />
        <el-table-column label="重置回传" align="center" prop="conversionDayOrHour" min-width="120">
          <template slot-scope="{row}">
            <template v-if="row.conversionDayOrHour!==undefined">
              <span v-if="row.conversionDayOrHour === 0">按天</span>
              <span v-else-if="row.conversionDayOrHour === 1">按小时</span>
              <span v-else-if="row.conversionDayOrHour === 2">不重置</span>
              <span v-else-if="row.conversionDayOrHour === 3">自定义
                <el-popover
                  placement="top-start"
                  width="400"
                  trigger="hover"
                >
                  <div v-for="(item, i) in row.timeSpanConversionProportionJson " :key="i">
                    <div v-if="row.batchConversionEnable">
                      {{ `${item.startTime} 至 ${item.endTime} 时，正常回传 ${item.conversionStartOrderCount} 单后，每 ${item.batchNum} 单，回传前 ${item.batchConversionNum} 单，剩余${item.batchNum - item.batchConversionNum}单不上报` }}
                    </div>
                    <div v-else>
                      {{ `${item.startTime} 至 ${item.endTime} 时，正常回传 ${item.conversionStartOrderCount} 单后，以${item.conversionProportion} % 回传订单` }}
                    </div>
                  </div>
                  <i slot="reference" class="el-icon-info color-primary" />
                </el-popover>
              </span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="生效区间" align="center" prop="deductAreaEnable" min-width="120">
          <template slot-scope="{row}">
            <template v-if="row.deductAreaEnable!==undefined">
              <span v-if="row.deductAreaEnable">
                {{ row.deductAreaMin /100 }} - {{ row.deductAreaMax/100 }}
              </span>
              <el-tag v-else type="danger">关闭</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="区间外扣回传" align="center" prop="deductOutsideAreaEnable" min-width="110">
          <template slot-scope="{row}">
            <template v-if="row.deductOutsideAreaEnable!==undefined">
              <el-tag v-if="row.deductOutsideAreaEnable" type="success">开启</el-tag>
              <el-tag v-else type="danger">关闭</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="扣回传粒度" align="center" prop="conversionDeductionScope" min-width="110">
          <template slot-scope="{row}">
            <template v-if="row.conversionDeductionScope!==undefined">
              <el-tag v-if="!row.conversionDeductionScope" type="primary">账户</el-tag>
              <el-tag v-else type="primary">计划</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="扣除低效流量" align="center" prop="conversionDeductionCsite" min-width="100">
          <template slot-scope="{row}">
            <template v-if="!row.conversionDeductionCsite">
              <el-tag type="danger">关闭</el-tag>
            </template>
            <el-popover
              placement="top-start"
              width="500"
              trigger="hover"
            >
              <el-tag v-for="(item, i) in row.conversionDeductionCsiteJson " :key="i" class="mr5 mb5">
                {{ item }}
              </el-tag>
              <template v-if="row.conversionDeductionCsite && row.conversionDeductionCsiteJson" slot="reference">
                <el-tag type="success">
                  开启
                  <i class="el-icon-info" />
                </el-tag>
              </template>
            </el-popover>
          </template>
        </el-table-column>

        <el-table-column label="防钓鱼配置" align="center" prop="entrapmentEnable" min-width="100">
          <template slot-scope="{row}">
            <template v-if="row.bidEnable!==undefined">
              <el-tag v-if="row.entrapmentEnable" type="success">开启</el-tag>
              <el-tag v-else type="danger">关闭</el-tag>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="fetchList"
    />
  </div>
</template>

<script>
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { selectModificationConversionRecord } from '@/api/promotion/media'
import { csiteMap } from '@/components/ConversionSetups/config'

export default {
  name: 'ConversionLog',
  components: { BaseInfoCell },
  props: {
    id: {
      type: String,
      default: ''
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  dicts: [
    'ocean_csite', 'kuaishou_csite', 'tencent_csite'
  ],
  data() {
    return {
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  mounted() {
    if (this.source) {
      this.queryParams.source = this.source
    }
    this.fetchList()
  },
  methods: {
    fetchList() {
      if (!this.id) {
        this.$message.warning('无修改日志')
        return
      }
      const postData = {
        businessId: this.id,
        ...this.queryParams
      }
      selectModificationConversionRecord(postData).then(response => {
        this.list = response.rows
        this.list.forEach(item => {
          Object.assign(item, {
            ...item,
            ...JSON.parse(item.operateData)
          })
          if (item.timeSpanConversionProportionJson) {
            item.timeSpanConversionProportionJson = JSON.parse(item.timeSpanConversionProportionJson)
          }
          if (item.conversionDeductionCsiteJson) {
            const res = JSON.parse(item.conversionDeductionCsiteJson)
            if (res?.length) {
              item.conversionDeductionCsiteJson = res[0].csiteInfo?.map(item => {
                return this.dict.label[csiteMap[res[0].mediaPlatformType]][item.code]
              })
            }
          }
        })
        this.total = response.total
      })
    },

    handleQuery() {
      this.fetchList()
    },

    handleSelectionChange(selection) {
      this.selections = selection
      this.multiple = !selection.length
    }

  }
}
</script>

<style scoped>
.operate-table-wrap {
  height: calc(100vh - 300px);
}
</style>
