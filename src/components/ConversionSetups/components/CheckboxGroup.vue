<template>
  <div>
    <el-checkbox-group
      v-model="localModelValue"
    >
      <el-checkbox
        v-for="item in options"
        :key="item.value"
        :label="item.value"
      >
        {{ item.label }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  value: {
    type: Array,
    required: true
  },
  options: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['input'])

const localModelValue = computed({
  get: () => props.value,
  set: (value) => emit('input', value)
})

</script>
