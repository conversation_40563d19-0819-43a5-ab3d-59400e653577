<script setup>
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue'
import { changeConversionSetups, getConversionConfig } from '@/api/promotion/media'
import { Message, MessageBox, Notification } from 'element-ui'
import ReplaceLog from '@/views/promotion/media/components/ReplaceLog.vue'
import ConversionLog from './components/ConversionLog.vue'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { typeMap } from '@/components/ConversionSetups/config'
import { useStore } from '@/store'
import { round, round2 } from '@/utils/number'
import CustomConversion from '@/components/ConversionSetups/components/CustomConversion.vue'
import useDicts from '@/hooks/useDicts'
import CheckboxGroup from './components/CheckboxGroup.vue'
import { csiteMap, csiteValueMap, csiteLabels, baseMedias } from './config'
import { isTx } from '@/utils/judge/media'

const self = getCurrentInstance().proxy
const store = useStore()
const userName = computed(() => store.getters.userInfo.userName)

const { dicts } = useDicts(['ocean_csite', 'kuaishou_csite', 'tencent_csite'])

const props = defineProps({
  form: {
    type: Array,
    default: () => ([])
  },
  type: {
    type: String,
    default: 'media' // plan, goods. user
  },
  formType: {
    type: String,
    default: 'default' // amount
  },
  hiddenOptions: {
    type: Array,
    default: () => []
  }

})

const emit = defineEmits(['save', 'cancel'])

// 显示表单项
const formItemShow = (formItem) => {
  if (props.hiddenOptions.includes(formItem)) return false
  // 不同按钮进入时展示
  if (props.formType === 'amount' || props.formType === 'order') return formItem === props.formType
  // if (props.formType === 'default') return true
  return true
}

// 显示信息
const info = computed(() => {
  if (props.form?.length !== 1) return false
  const form = props.form[0]
  switch (props.type) {
    case 'media':
    case 'plan':
      return {
        id: form.advertiserId,
        name: form.advertiserName
      }
    case 'goods':
      return {
        id: form.goodsId,
        name: form.goodsName
      }
    default:
      return false
  }
})
// 回传类型
const conversionType = computed(() => {
  return typeMap[props.type].type
})

// 回传id
const businessIdKey = computed(() => {
  return typeMap[props.type].idKey
})

const conversionRef = ref(null)
const conversionForm = reactive({
  type: 1, // 类型(1媒体账户,2计划,3商品,4用户)
  businessId: '', // 业务id根据type对应不同id
  goodsPlatformType: null, // 商品的电商平台
  mediaPlatformType: null, // 媒体的媒体平台
  enableConversion: 0, // 是否开启回传限制(0:不开启;1:开启)
  batchConversionEnable: 0, // 是否开启按批次回传(0:不开启;1:开启)
  batchNum: 1, // 按批次回传的每批次订单数
  batchConversionNum: 1, // 按批次回传的每批次的回传的个数
  conversionProportion: 100, // 回传比例
  conversionStartOrderCount: 0, // 回传比例开始扣的订单数
  deductAreaEnable: 0, // 扣回传区间开关（1-开 0-关）
  deductAreaMin: 0, // 扣回传区间最小值
  deductAreaMax: 0, // 扣回传区间最大值
  deductOutsideAreaEnable: 0, // 区间外的是否回传(0全回传1全不回传)
  newBid: 0, // 按比例计算后广告新的出价价格
  bid: 0, // 原始广告出价价格
  conversionDayOrHour: 0, // 按比例回传的生效失效(0按天,1按小时,2不重置)
  timeSpanConversionProportionJson: [],
  conversionDeductionCsiteJson: [],
  enableAmount: 0, // 是否开启回传金额控制(0:不开启;1:开启)
  amountProportion: 100, // 回传金额比例
  delayEnable: 0, // 延迟回传开关（1-开 0-关）
  delaySecond: 0, // 延迟回传设置的秒数
  bidEnable: false, // 是否开启修改出价
  conversionAmountType: 0, // 金额按比例回传类型(0按比例,1是按固定金额)
  conversionAmount: 0, // 金额按比例回传类型(0按比例,1是按固定金额)
  conversionDeductionCsite: false,
  conversionDeductionScope: 0, // 扣回传生效范围(默认0:账户,1计划)
  entrapmentEnable: 0 //  防钓鱼配置开关
})

const mediaPlatformTypes = computed(() => {
  return props.type === 'user' ? baseMedias : Array.from(new Set(props.form.map(item =>
    props.type === 'goods' ? item.mediaPlatformType : item.mediaType
  ))).filter(item => baseMedias.includes(item)).sort()
})

const txShow = computed(() => {
  return props.type === 'user' ? true : Array.from(new Set(props.form.map(item =>
    props.type === 'goods' ? item.mediaPlatformType : item.mediaType
  ))).some(item => isTx(item))
})

const csiteVisible = computed(() => {
  return mediaPlatformTypes.value.some(m => baseMedias.includes(m)) && conversionForm.conversionDeductionCsite
})

const conversionTypeStr = computed(() => {
  return conversionForm.batchConversionEnable ? '按批次' : '按比例'
})

const conversionCsite = ref({
  ocean_csite: [],
  kuaishou_csite: [],
  tencent_csite: []
})

const isCustomConversion = computed(() => conversionForm.conversionDayOrHour === 3)

const setDefaultConversion = () => {
  conversionForm.type = conversionType.value
  if (props.type === 'user') {
    conversionForm.businessId = userName.value
    getConversionConfig({
      type: conversionType.value,
      businessId: userName.value
    }).then(data => {
      if (data.data) {
        setConversionForm(data.data)
      }
    })
    return
  }
  if (props.form.length === 1 && props.form[0].conversionConfig) {
    setConversionForm(props.form[0].conversionConfig)
  }
}
function setConversionForm(data) {
  Object.assign(conversionForm, data)
  conversionForm.conversionAmount = round2(conversionForm.conversionAmount)
  conversionForm.deductAreaMin = round2(conversionForm.deductAreaMin)
  conversionForm.deductAreaMax = round2(conversionForm.deductAreaMax)
  conversionForm.timeSpanConversionProportionJson = data.timeSpanConversionProportionJson
    ? JSON.parse(data.timeSpanConversionProportionJson) : []
  conversionForm.bidEnable = false
  conversionForm.batchConversionEnable = conversionForm.batchConversionEnable || false
  conversionForm.batchNum = conversionForm.batchNum || 1
  conversionForm.batchConversionNum = conversionForm.batchConversionNum || 1

  conversionCsite.value = data.conversionDeductionCsiteJson
    ? JSON.parse(data.conversionDeductionCsiteJson).reduce((a, item) => {
      a[csiteMap[item.mediaPlatformType]] = item.csiteInfo.map(item => item.code)
      return a
    }, {}) : {
      ocean_csite: [],
      kuaishou_csite: [],
      tencent_csite: []
    }
  conversionCsite.value = {
    ocean_csite: [],
    kuaishou_csite: [],
    tencent_csite: [],
    ...conversionCsite.value
  }
}

const deductTips = () => {
  return isCustomConversion.value ? `<div class="conversion-deduction">${conversionForm.timeSpanConversionProportionJson.reduce((a, item) => {
    a += conversionForm.batchConversionEnable ? `<div class="mb5">${item.startTime} 至 ${item.endTime} 时，正常回传${item.conversionStartOrderCount} 单后，每 <span class="color-primary"> ${item.batchNum} </span>单，回传前 <span class="color-primary"> ${item.batchConversionNum} </span>单，剩余<span class="color-primary"> ${item.batchNum - item.batchConversionNum} </span>单不上报</div>` : `<div class="mb5">${item.startTime} 至 ${item.endTime} 时，正常回传${item.conversionStartOrderCount} 单后，以${item.conversionProportion} % 回传订单</div>`
    return a
  }, '')}` : `<div class="conversion-deduction">
    <div class="mb5"><span class="color-warning">每个${conversionForm.conversionDeductionScope ? '计划' : '账户'}</span> 
   ${conversionForm.batchConversionEnable ? `每 <span class="color-primary"> ${conversionForm.batchNum} </span>单，回传前 <span class="color-primary"> ${conversionForm.batchConversionNum} </span>单，剩余<span class="color-primary"> ${conversionForm.batchNum - conversionForm.batchConversionNum} </span>单不上报` : `以 <span class="color-primary"> ${conversionForm.conversionProportion}% </span>比例<span class="color-primary">回传订单</span>`}
    <div><span class="color-warning">每个${conversionForm.conversionDeductionScope ? '计划' : '账户'}</span> 在 <span class="color-primary"> ${conversionForm.conversionStartOrderCount} </span>单后开始<span class="color-primary">拦截订单</span></div>
  </div>`
}

const loading = ref(false)
const submit = () => {
  if (conversionForm.enableConversion) {
    Notification({
      title: '扣回传生效粒度',
      duration: 3000,
      dangerouslyUseHTMLString: true,
      message: deductTips()
    })
  }
  let postData = []
  const prepareFormData = (form) => {
    const preparedForm = { ...form }
    preparedForm.conversionAmount = round((preparedForm.conversionAmount ?? 0) * 100)
    preparedForm.deductAreaMin = round((preparedForm.deductAreaMin ?? 0) * 100)
    preparedForm.deductAreaMax = round((preparedForm.deductAreaMax ?? 0) * 100)
    preparedForm.timeSpanConversionProportionJson = JSON.stringify(preparedForm.timeSpanConversionProportionJson)

    return preparedForm
  }

  const csiteJson = {}
  Object.entries(conversionCsite.value).forEach(([key, value]) => {
    csiteJson[csiteValueMap[key]] = {
      mediaPlatformType: csiteValueMap[key],
      csiteInfo: value.map(item => ({ code: item }))
    }
  })

  let emptyWarning = false
  if (props.type === 'user') {
    postData = [prepareFormData(conversionForm)]
    const csiteJsonValues = Object.values(csiteJson)
    if (csiteJsonValues.every(item => item.csiteInfo.length === 0)) {
      emptyWarning = true
    }
    postData[0].conversionDeductionCsiteJson = JSON.stringify(csiteJsonValues)
  } else {
    postData = props.form.map(item => {
      const form = prepareFormData(conversionForm)
      form.businessId = item[businessIdKey.value]

      if (['plan', 'media'].includes(props.type)) {
        form.mediaPlatformType = item.mediaType
      } else if (props.type === 'goods') {
        form.goodsPlatformType = item.platform
        form.mediaPlatformType = item.mediaPlatformType
      }
      const csiteJsonValue = csiteJson[form.mediaPlatformType]
      if (csiteJsonValue) {
        if (csiteJsonValue.csiteInfo.length === 0) {
          emptyWarning = true
        }
        form.conversionDeductionCsiteJson = JSON.stringify([csiteJsonValue])
      } else {
        form.conversionDeductionCsiteJson = null
      }

      return form
    })
  }

  if (emptyWarning && conversionForm.conversionDeductionCsite) {
    Message.warning('请勾选需要扣除的版位')
    return
  }

  loading.value = true
  changeConversionSetups(postData).then(data => {
    Message.success('操作成功')
    emit('save')
  }).finally(() => {
    loading.value = false
  })
}

const calcNewBid = () => {
  if (conversionForm.bid !== undefined) {
    conversionForm.newBid = Math.round(conversionForm.bid * (1 + (1 - conversionForm.conversionProportion / 100)) * 0.95 * 100) / 100
  }
}
const calcNewBatchConversionNum = () => {
  if (conversionForm.batchNum !== undefined) {
    if (conversionForm.batchNum < conversionForm.batchConversionNum) {
      conversionForm.batchConversionNum = conversionForm.batchNum
    }
  }
}

const bidLogVisible = ref(false)
const changeLogVisible = ref(false)
const showBidLog = () => {
  bidLogVisible.value = true
}

const showChangeLog = () => {
  changeLogVisible.value = true
}

const cancel = () => {
  emit('cancel')
}

const close = () => {
  self.$tab.closePage()
}

const handleDeductAreaChange = (type) => {
  if (type === 'min') {
    if (conversionForm.deductAreaMin > conversionForm.deductAreaMax) {
      conversionForm.deductAreaMax = conversionForm.deductAreaMin + 0.01
    }
  } else {
    if (conversionForm.deductAreaMax < conversionForm.deductAreaMin) {
      conversionForm.deductAreaMin = conversionForm.deductAreaMax - 0.01
    }
  }
}

// Add this computed property
const availableCsites = computed(() => {
  return mediaPlatformTypes.value.map(item => csiteMap[item])
})

// Add these new variables
const activeCsiteTab = ref('ocean_csite')
const initCsiteTab = () => {
  if (props.type === 'user') return
  const csiteOrder = ['ocean_csite', 'kuaishou_csite', 'tencent_csite']
  activeCsiteTab.value = csiteOrder.find(csite => availableCsites.value.includes(csite)) || 'ocean_csite'
}

const checkAllCsite = ref({
  ocean_csite: false,
  kuaishou_csite: false,
  tencent_csite: false
})
const checkAllCsiteComputed = computed({
  get() {
    Object.keys(checkAllCsite.value).forEach(key => {
      checkAllCsite.value[key] = conversionCsite.value[key]?.length === dicts.value[key]?.length
    })
    return checkAllCsite.value
  },
  set(val) {
    checkAllCsite.value = val
  }
})

onMounted(() => {
  setDefaultConversion()
  initCsiteTab()
})

const checkCsiteAll = (csiteType) => {
  const csiteList = dicts.value[csiteType]
  const selectedCsite = conversionCsite.value[csiteType]
  if (selectedCsite.length === csiteList.length) {
    conversionCsite.value[csiteType] = []
  } else {
    conversionCsite.value[csiteType] = csiteList.map(item => item.value)
  }
}

function isIndeterminate(val, all) {
  return val > 0 && val < all
}
</script>

<template>
  <div>
    <div v-if="info" class="flex">
      <BaseInfoCell :id="info.id" :name="info.name" label="名称:" type="info" class="flex1" style="margin-bottom: 20px" no-copy />
      <el-button size="mini" class="ml10" style="height: 30px" @click="showChangeLog">修改日志</el-button>
    </div>
    <el-form ref="conversionRef" :model="conversionForm" label-width="120px">
      <el-form-item
        v-if="formItemShow('delay')"
        label="延迟回传"
        prop="delay"
      >
        <el-switch
          v-model="conversionForm.delayEnable"
          active-color="#13ce66"
        />
        <template v-if="conversionForm.delayEnable">
          <el-input-number v-model="conversionForm.delaySecond" :min="60" :max="3600" :precision="0" label="请输入延迟回传时间（秒）" :controls="false" style="margin: 0 10px" />
          <span>秒（不小于60秒，不大于3600秒）</span>
        </template>
      </el-form-item>
      <el-form-item v-if="formItemShow('amount')" label="回传金额比例">
        <div class="conversion-item">
          <el-switch
            v-model="conversionForm.enableAmount"
            active-color="#13ce66"
          />
          <template v-if="conversionForm.enableAmount">
            <el-radio-group v-model="conversionForm.conversionAmountType" class="ml10" size="mini">
              <el-radio-button :label="0">按比例</el-radio-button>
              <el-radio-button :label="1">按固定金额</el-radio-button>
            </el-radio-group>
            <template v-if="conversionForm.conversionAmountType === 0">
              <el-slider
                v-model="conversionForm.amountProportion"
                class="flex1"
                style="width: 250px;margin: 0 10px"
                :max="200"
              />
              <el-input-number v-model="conversionForm.amountProportion" size="mini" style="width: 100px;margin-right: 10px" :min="0" :max="200" :controls="false" />%
            </template>
            <template v-if="conversionForm.conversionAmountType === 1">
              <el-input-number v-model="conversionForm.conversionAmount" size="mini" style="width: 100px;margin:0 10px" :min="0" :precision="2" :controls="false" />元
            </template>
          </template>
        </div>
      </el-form-item>
      <template v-if="formItemShow('order')">
        <el-form-item label="回传配置">
          <div class="conversion-item">
            <el-switch
              v-model="conversionForm.enableConversion"
              active-color="#13ce66"
            />
            <template v-if="conversionForm.enableConversion">
              <el-radio-group v-model="conversionForm.batchConversionEnable" class="ml10 mr10" size="mini">
                <el-radio-button :label="false">按比例</el-radio-button>
                <el-radio-button :label="true">按批次</el-radio-button>
              </el-radio-group>
              <template v-if="conversionForm.batchConversionEnable">
                每
                <el-input-number
                  v-model="conversionForm.batchNum"
                  :disabled="isCustomConversion"
                  size="mini"
                  style="width: 100px;margin:0 10px"
                  :min="1"
                  :controls="false"
                  @input="calcNewBatchConversionNum"
                />单，回传前
                <el-input-number
                  v-model="conversionForm.batchConversionNum"
                  :disabled="isCustomConversion"
                  size="mini"
                  style="width: 100px;margin:0 10px"
                  :min="1"
                  :max="conversionForm.batchNum"
                  :controls="false"
                />单，剩余<span class="color-primary">{{ conversionForm.batchNum - conversionForm.batchConversionNum }}</span>单不上报
              </template>
              <template v-else>
                <el-slider
                  v-model="conversionForm.conversionProportion"
                  style="width: 250px;margin-right: 10px"
                  :max="200"
                  :disabled="isCustomConversion"
                  @change="calcNewBid"
                />
                <el-input-number v-model="conversionForm.conversionProportion" :disabled="isCustomConversion" size="mini" style="width: 100px;margin-right: 10px" :min="0" :max="200" :controls="false" @input="calcNewBid" />
                %
              </template>
            </template>
          </div>
          <div v-if="conversionForm.enableConversion" style="margin-left: 0px">
            <div class="conversion-item">
              <div class="conversion-label">开始拦截订单数
                <el-tooltip class="item" effect="dark" :content="`账户订单数大于这个数量后开始${conversionTypeStr}拦截订单`" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
              </div>
              <el-input-number
                v-model="conversionForm.conversionStartOrderCount"
                :disabled="isCustomConversion"
                :min="0"
                :controls="false"
                size="mini"
              />
              <div class="color-info conversion-deduction">
                保存后会正常回传 <span class="color-primary">{{ isCustomConversion ? '自定义': conversionForm.conversionStartOrderCount }}</span> 单后再{{ conversionTypeStr }}扣取
              </div>
            </div>
            <div class="conversion-item">
              <div class="conversion-label">
                重置回传
                <el-tooltip class="item" effect="dark" content="默认按天重置，可选择按小时重置回传；当选择不重置时，则只对开始拦截订单数有效；选择自定义时可以按时间段自由设置回传比例" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
              </div>
              <el-radio-group v-model="conversionForm.conversionDayOrHour" size="mini">
                <el-radio-button :label="0">按天</el-radio-button>
                <el-radio-button :label="1">按小时</el-radio-button>
                <el-radio-button :label="2">不重置</el-radio-button>
                <el-radio-button :label="3">自定义时间段</el-radio-button>
              </el-radio-group>
            </div>
            <div v-if="isCustomConversion">
              <CustomConversion v-model="conversionForm.timeSpanConversionProportionJson" :type="conversionForm.batchConversionEnable" />
            </div>
            <div class="conversion-item">
              <div class="conversion-label">生效区间
                <el-tooltip class="item" effect="dark" :content="`设置扣回传生效区间并开启后，当订单的价格在设置区间内时，才会${conversionTypeStr}扣取回传`" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
              </div>
              <el-switch
                v-model="conversionForm.deductAreaEnable"
                active-color="#13ce66"
              />
              <template v-if="conversionForm.deductAreaEnable">
                <el-input-number v-model="conversionForm.deductAreaMin" size="mini" :min="0" :max="10000000" :precision="2" label="请输入最小值" :controls="false" style="margin: 0 10px;width: 150px" @change="handleDeductAreaChange('min')" />
                -
                <el-input-number v-model="conversionForm.deductAreaMax" size="mini" :min="0" :max="10000000" :precision="2" label="请输入最大值" :controls="false" style="margin: 0 10px;width: 150px" @change="handleDeductAreaChange('max')" />
                元
              </template>
            </div>
            <div v-show="conversionForm.deductAreaEnable" class="conversion-item">
              <div class="conversion-label">区间外扣回传
                <el-tooltip class="item" effect="dark" :content="conversionForm.deductOutsideAreaEnable?'开启时，订单的价格在生效区间外的部分会扣除回传。':'关闭时，订单的价格在生效区间外的部分都会回传。'" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
              </div>
              <el-switch
                v-model="conversionForm.deductOutsideAreaEnable"
                active-color="#13ce66"
              />
            </div>
            <div v-if="type !== 'plan'" class="conversion-item">
              <div class="conversion-label">扣回传生效粒度
                <el-tooltip class="item" effect="dark" :content="`选择账户时，将以账户为范围${conversionTypeStr}扣除回传, 在计划中随机扣除; 选择计划时, 在计划中${conversionTypeStr}扣除回传`" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
              </div>
              <el-radio-group v-model="conversionForm.conversionDeductionScope" size="mini">
                <el-radio-button :label="0">账户</el-radio-button>
                <el-radio-button :label="1">计划</el-radio-button>
              </el-radio-group>
              <div class="color-info conversion-deduction">
                <div>回传配置:
                  <span class="color-warning">每个{{ conversionForm.conversionDeductionScope ? '计划':'账户' }}</span>
                  <template v-if="conversionForm.batchConversionEnable && !isCustomConversion">
                    每 <span class="color-primary"> {{ conversionForm.batchNum }} </span>单，回传前 <span class="color-primary"> {{ conversionForm.batchConversionNum }} </span>单，剩余<span class="color-primary">{{ conversionForm.batchNum - conversionForm.batchConversionNum }}</span>单不上报
                  </template>
                  <template v-else>
                    以 <span class="color-primary"> {{ isCustomConversion ? '自定义':`${conversionForm.conversionProportion}%` }} </span>{{ conversionForm.batchConversionEnable ? '批次':`比例` }}回传订单
                  </template>
                </div>
                <div>开始拦截订单数:
                  <span class="color-warning">每个{{ conversionForm.conversionDeductionScope ? '计划':'账户' }}</span> 在 <span class="color-primary"> {{ isCustomConversion ? '自定义':conversionForm.conversionStartOrderCount }} </span>单后开始拦截订单数</div>
              </div>
            </div>

            <div v-if="txShow" class="conversion-item">
              <div class="conversion-label">防钓鱼配置开关</div>
              <el-switch
                v-model="conversionForm.entrapmentEnable"
                active-color="#13ce66"
              />
              <div class="color-info conversion-deduction">
                仅腾讯有效
              </div>
            </div>
          </div>
        </el-form-item>
      </template>
      <el-form-item v-if="formItemShow('default') && mediaPlatformTypes.length" label="扣除低效版位">
        <div class="conversion-item">
          <el-switch
            v-model="conversionForm.conversionDeductionCsite"
            class="mr10"
            active-color="#13ce66"
          />
          <span v-if="csiteVisible" class="color-danger">
            注意：请勾选需要扣除的版位
          </span>
        </div>
        <div v-if="csiteVisible">
          <el-tabs v-model="activeCsiteTab" type="border-card">
            <el-tab-pane
              v-for="csiteType in availableCsites"
              :key="csiteType"
              :name="csiteType"
            >
              <div slot="label">
                <el-checkbox
                  v-model="checkAllCsiteComputed[csiteType]"
                  :indeterminate="isIndeterminate(conversionCsite[csiteType].length ,dicts[csiteType].length)"
                  class="mr10"
                  @change="checkCsiteAll(csiteType)"
                />
                {{ csiteLabels[csiteType] }}
                <span class="color-info">
                  {{ conversionCsite[csiteType].length }}/{{ dicts[csiteType].length }}
                </span>
              </div>
              <CheckboxGroup
                v-model="conversionCsite[csiteType]"
                :options="dicts[csiteType]"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form-item>
      <el-form-item v-if="props.type === 'user'">
        <el-button type="primary" size="mini" @click="submit">保存</el-button>
        <el-button type="danger" size="mini" @click="close">关闭</el-button>
      </el-form-item>
    </el-form>
    <div v-if="props.type !== 'user'" slot="footer" class="dialog-footer">
      <el-button :loading="loading" type="primary" @click="submit">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>

    <el-dialog
      title="出价日志"
      :visible.sync="bidLogVisible"
      width="70%"
      top="5vh"
      append-to-body
    >
      <ReplaceLog
        v-if="bidLogVisible"
        :id="form[0].advertiserId"
        :media-type="form[0].mediaType +''"
        operate-type="4"
        :operate-row="false"
      />
    </el-dialog>
    <el-dialog
      title="修改日志"
      :visible.sync="changeLogVisible"
      width="90%"
      top="5vh"
      append-to-body
    >
      <ConversionLog
        v-if="changeLogVisible"
        :id="conversionForm.id"
        :info="info"
      />
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

.conversion-item {
  display: flex;
  align-items: center;
  height: 38px;
  margin-bottom: 5px;
  .conversion-label {
    width: 120px;
    text-align: right;
    margin-right: 10px;
  }
}
.conversion-deduction {
  font-size: 12px;
  line-height: 16px;
  margin-left: 10px;
  width: 320px;
}
.log-form {
  padding:0 20px 20px;
  .conversion-label {
    font-weight: bold;
  }
}
.dialog-footer {
  display: flex;
  justify-content: right;
}
.bid-tip {
  width: 300px;
  border: #E65D6E dashed 1px;
  .bid-tip-text {
    text-align: center;
    color: #E65D6E;
    line-height: 24px;
    font-size: 14px;
  }
}

.el-tabs {
  margin-top: 10px;
}

.el-tab-pane {
  padding: 10px 0;
}
</style>
