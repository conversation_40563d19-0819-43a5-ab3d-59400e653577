export const typeMap = {
  plan: {
    type: 1,
    idKey: 'planId'
  },
  media: {
    type: 2,
    idKey: 'advertiserId'
  },
  goods: {
    type: 3,
    idKey: 'goodsId'
  },
  user: {
    type: 4,
    idKey: 'createBy'
  }
}

export const baseMedias = [1, 2, 4]
export const csiteMap = {
  1: 'ocean_csite',
  2: 'kuaishou_csite',
  4: 'tencent_csite'
}
export const csiteValueMap = {
  ocean_csite: 1,
  kuaishou_csite: 2,
  tencent_csite: 4
}

export const csiteLabels = {
  ocean_csite: '头条',
  kuaishou_csite: '快手',
  tencent_csite: '腾讯'
}
