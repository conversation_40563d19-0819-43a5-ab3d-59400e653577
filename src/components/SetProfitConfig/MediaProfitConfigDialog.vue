<script setup>
import { computed, ref, watch } from 'vue'
import { Message } from 'element-ui'
import { v4 as uuidv4 } from 'uuid'
import useDicts from '@/hooks/useDicts'
import { queryMediaProfitConfig, saveMediaProfitConfig, getMediaOrderPrices, setMediaProfitGoods, getMediaProfitGoods } from '@/api/promotion/media'
// import { updateMediaProfit } from '@/api/promotion/media'
import dayjs from 'dayjs'
import GoodsSelector from '@/components/GoodsSelector/index.vue'

const props = defineProps({
  visible: Boolean,
  mediaList: {
    type: Array,
    default: () => []
  },
  dateRange: {
    type: Array,
    default: () => [
      dayjs().format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ]
  },
  showGoodsSelect: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

const open = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const { dictMap } = useDicts(['media_type'])

const loading = ref(false)
const saveLoading = ref(false)

// 重构数据结构：统一的SKU配置，不区分媒体账户
const skuConfigData = ref([])

// 媒体订单SKU单价信息
const orderPricesList = ref([])
const pricesLoading = ref(false)
const noOrderPriceData = ref(false)

// 本地日期范围，用于Popover
const localDateRange = ref([])
// 日期选择器Popover引用
const datePopover = ref(null)

// 本地媒体列表，用于支持删除操作
const localMediaList = ref([])

// 商品选择相关
const goodsVisible = ref(false)
const selectedGoods = ref(null)
const goodsLoading = ref(false)

// 初始化加载数据
watch(() => props.visible, (val) => {
  if (val && props.mediaList.length > 0) {
    // 初始化本地媒体列表
    localMediaList.value = [...props.mediaList]

    // 只有单个媒体账户时才进行查询操作
    if (props.mediaList.length === 1) {
      if (props.showGoodsSelect) {
        fetchSelectedGoods()
      }
      fetchData()
      // 初始化本地日期范围并使用它查询数据
      localDateRange.value = props.dateRange ? [...props.dateRange] : []
      fetchOrderPrices(localDateRange.value)
    } else {
      // 批量操作模式，清空数据，只显示空表格供用户添加配置
      skuConfigData.value = []
      orderPricesList.value = []
      noOrderPriceData.value = false
      selectedGoods.value = null
      localDateRange.value = props.dateRange ? [...props.dateRange] : []
    }
  }
})

// 查询商品盈亏配置列表
const fetchData = async() => {
  // 只有单个媒体账户时才查询现有配置
  if (!props.mediaList || props.mediaList.length !== 1) return

  loading.value = true
  try {
    const res = await queryMediaProfitConfig({
      advertiserId: props.mediaList[0].advertiserId,
      mediaPlatformType: props.mediaList[0].mediaType
    })
    if (res.code === 200 && res.data) {
      // 新接口返回的是sku配置数组，每个sku包含items数组
      const skuData = []
      res.data.forEach(sku => {
        const skuConfig = {
          id: sku.id,
          skuId: sku.skuId,
          skuName: sku.skuName,
          items: []
        }

        if (sku.items && sku.items.length > 0) {
          // 处理每个配置项
          sku.items.forEach(item => {
            skuConfig.items.push({
              id: item.id,
              // 将金额从分转换为元，比例从千分比转换为百分比
              goodsCost: item.goodsCost ? item.goodsCost / 100 : null,
              expressCost: item.expressCost ? item.expressCost / 100 : null,
              commissionRate: item.commissionRate ? item.commissionRate / 10 : null,
              otherCost: item.otherCost ? item.otherCost / 100 : null,
              skuPrice: item.skuPrice ? item.skuPrice / 100 : null,
              referSignRate: item.referSignRate || 100,
              estimateSignRate: item.estimateSignRate || 100,
              startDate: item.startDate,
              endDate: item.endDate
            })
          })
        } else {
          // 如果没有items，创建一个空的配置项
          skuConfig.items.push({
            goodsCost: null,
            expressCost: null,
            commissionRate: null,
            otherCost: null,
            skuPrice: null,
            referSignRate: 100,
            estimateSignRate: 100,
            startDate: dayjs().format('YYYY-MM-DD'), // 默认为当前日期
            endDate: null
          })
        }

        skuData.push(skuConfig)
      })
      skuConfigData.value = skuData
    } else {
      skuConfigData.value = []
    }
  } catch (error) {
    console.error('获取商品盈亏配置失败', error)
    Message.error('获取商品盈亏配置失败')
    skuConfigData.value = []
  } finally {
    loading.value = false
  }
}

// 查询媒体订单SKU单价信息
const fetchOrderPrices = async(customDateRange) => {
  // 只有单个媒体账户时才查询订单价格信息
  if (!props.mediaList || props.mediaList.length !== 1 || !props.mediaList[0].advertiserId) return

  // 使用传入的自定义日期范围或本地日期范围
  const dateRangeToUse = customDateRange || localDateRange.value
  if (!dateRangeToUse || dateRangeToUse.length !== 2) return

  pricesLoading.value = true
  noOrderPriceData.value = false
  try {
    const params = {
      advertiserId: props.mediaList[0].advertiserId,
      params: {
        beginTime: dateRangeToUse[0],
        endTime: dateRangeToUse[1]
      }
    }

    const res = await getMediaOrderPrices(params)
    if (res.code === 200 && res.data) {
      orderPricesList.value = res.data
      noOrderPriceData.value = orderPricesList.value.length === 0
    } else {
      orderPricesList.value = []
      noOrderPriceData.value = true
    }
  } catch (error) {
    console.error('获取媒体订单SKU单价信息失败', error)
    Message.error('获取媒体订单SKU单价信息失败')
    orderPricesList.value = []
    noOrderPriceData.value = true
  } finally {
    pricesLoading.value = false
  }
}

// 更新日期范围并重新加载数据
const updateDateRange = () => {
  if (!localDateRange.value || localDateRange.value.length !== 2) {
    Message.warning('请选择完整的时间范围')
    return
  }

  // 使用本地日期范围查询数据
  fetchOrderPrices(localDateRange.value)

  // 关闭日期选择器弹窗
  if (datePopover.value) {
    datePopover.value.doClose()
  }
}

// 查询媒体关联商品
const fetchSelectedGoods = async() => {
  // 只有单个媒体账户时才查询关联商品
  if (!props.mediaList || props.mediaList.length !== 1) return

  goodsLoading.value = true
  try {
    const res = await getMediaProfitGoods({
      advertiserId: props.mediaList[0].advertiserId,
      mediaPlatformType: props.mediaList[0].mediaType
    })
    if (res.code === 200 && res.data) {
      selectedGoods.value = res.data
    } else {
      selectedGoods.value = null
    }
  } catch (error) {
    console.error('查询媒体关联商品失败', error)
    Message.error('查询媒体关联商品失败')
    selectedGoods.value = null
  } finally {
    goodsLoading.value = false
  }
}

// 处理商品选择
const handleGoodsSelect = async(goods) => {
  // 只有单个媒体账户时才能设置关联商品
  if (!props.mediaList || props.mediaList.length !== 1) {
    Message.warning('批量模式下不支持商品选择')
    return
  }

  try {
    const res = await setMediaProfitGoods({
      advertiserId: props.mediaList[0].advertiserId,
      mediaPlatformType: props.mediaList[0].mediaType,
      goodsId: goods.goodsId
    })
    if (res.code === 200) {
      Message.success('设置媒体关联商品成功')
      selectedGoods.value = goods
      goodsVisible.value = false
    } else {
      Message.error(res.msg || '设置媒体关联商品失败')
    }
  } catch (error) {
    console.error('设置媒体关联商品失败', error)
    Message.error('设置媒体关联商品失败')
  }
}

// 添加新SKU
const addSku = () => {
  // 统一的SKU配置，不区分媒体账户
  const newSku = {
    skuId: `sku_${Date.now()}`, // 临时ID
    skuName: '',
    items: [{
      skuPrice: null,
      goodsCost: null,
      expressCost: null,
      commissionRate: null,
      otherCost: null,
      referSignRate: 100,
      estimateSignRate: 100,
      startDate: dayjs().format('YYYY-MM-DD'), // 默认为当前日期
      endDate: null
    }]
  }
  skuConfigData.value.push(newSku)
}

// 为指定SKU添加新的配置项
const addItemToSku = (skuIndex) => {
  if (skuConfigData.value[skuIndex]) {
    const lastItem = skuConfigData.value[skuIndex].items[skuConfigData.value[skuIndex].items.length - 1]
    skuConfigData.value[skuIndex].items.push({
      skuPrice: null,
      goodsCost: null,
      expressCost: null,
      commissionRate: null,
      otherCost: null,
      referSignRate: 100,
      estimateSignRate: 100,
      startDate: lastItem.startDate
        ? dayjs(lastItem.startDate).add(2, 'day').format('YYYY-MM-DD')
        : dayjs().format('YYYY-MM-DD'), // 默认为上一条开始时间加一天，如果没有上一条则为当前日期
      endDate: null
    })
  }
}

// 删除SKU
const deleteSku = (skuIndex) => {
  skuConfigData.value.splice(skuIndex, 1)
}

// 删除配置项
const deleteItem = (skuIndex, itemIndex) => {
  if (skuConfigData.value[skuIndex] && skuConfigData.value[skuIndex].items.length > 1) {
    skuConfigData.value[skuIndex].items.splice(itemIndex, 1)
  } else if (skuConfigData.value[skuIndex] && skuConfigData.value[skuIndex].items.length === 1) {
    // 如果是最后一个配置项，删除整个SKU
    deleteSku(skuIndex)
  }
}

// 保存所有配置
const submit = async() => {
  // 检查是否还有媒体账户
  if (localMediaList.value.length === 0) {
    Message.warning('请至少选择一个媒体账户')
    return
  }

  // 表单验证 - 根据新的数据结构调整验证逻辑
  const duplicateSkuNames = new Set()

  for (const sku of skuConfigData.value) {
    // 检查SKU名称是否为空
    if (!sku.skuName) {
      Message.warning('SKU名称不能为空')
      return
    }

    // 检查是否有重复的SKU名称
    if (duplicateSkuNames.has(sku.skuName)) {
      Message.warning(`存在重复的SKU名称：${sku.skuName}`)
      return
    }
    duplicateSkuNames.add(sku.skuName)

    // 检查每个配置项
    for (let i = 0; i < sku.items.length; i++) {
      const item = sku.items[i]

      // SKU单价为必填项
      if (!item.skuPrice || item.skuPrice <= 0) {
        Message.warning(`SKU "${sku.skuName}" 的第${i + 1}项单价不能为空且必须大于0`)
        return
      }

      // 开始时间为必填项
      if (!item.startDate) {
        Message.warning(`SKU "${sku.skuName}" 的第${i + 1}项开始时间不能为空`)
        return
      }

      // 验证开始时间顺序：后面的开始时间不能早于前面的开始时间
      if (i > 0) {
        const prevItem = sku.items[i - 1]
        if (prevItem.startDate && dayjs(item.startDate).isBefore(dayjs(prevItem.startDate))) {
          Message.warning(`SKU "${sku.skuName}" 的第${i + 1}项开始时间不能早于第${i}项的开始时间`)
          return
        }
      }

      // 验证签收率范围
      if (item.referSignRate && (item.referSignRate < 1 || item.referSignRate > 100)) {
        Message.warning(`SKU "${sku.skuName}" 的第${i + 1}项参考签收率必须在1-100%之间`)
        return
      }
      if (item.estimateSignRate && (item.estimateSignRate < 1 || item.estimateSignRate > 100)) {
        Message.warning(`SKU "${sku.skuName}" 的第${i + 1}项预估签收率必须在1-100%之间`)
        return
      }

      // 验证佣金率范围
      if (item.commissionRate && (item.commissionRate < 0 || item.commissionRate > 100)) {
        Message.warning(`SKU "${sku.skuName}" 的第${i + 1}项佣金率必须在0-100%之间`)
        return
      }
    }
  }

  saveLoading.value = true
  try {
    // 按照新的接口结构组织数据
    const advertiserList = []

    // 为每个媒体账户应用相同的配置
    localMediaList.value.forEach(media => {
      const advertiserConfig = {
        advertiserId: media.advertiserId,
        mediaPlatformType: media.mediaType,
        skuList: []
      }

      // 为当前媒体账户应用所有SKU配置
      skuConfigData.value.forEach(sku => {
        const skuId = uuidv4()

        // 处理SKU的所有配置项
        const items = sku.items.map((item, index) => {
          // 计算结束时间：使用下一条配置项的开始时间前一天作为结束时间
          let endDate = null
          if (index < sku.items.length - 1) {
            const nextItem = sku.items[index + 1]
            if (nextItem && nextItem.startDate) {
              endDate = dayjs(nextItem.startDate).subtract(1, 'day').format('YYYY-MM-DD')
            }
          }

          return {
            id: item.id,
            skuPrice: Math.round(item.skuPrice * 100), // 元转为分，必填字段
            goodsCost: item.goodsCost ? Math.round(item.goodsCost * 100) : null, // 元转为分
            expressCost: item.expressCost ? Math.round(item.expressCost * 100) : null, // 元转为分
            commissionRate: item.commissionRate ? Math.round(item.commissionRate * 10) : null, // 百分比转为千分比
            otherCost: item.otherCost ? Math.round(item.otherCost * 100) : null, // 元转为分
            referSignRate: item.referSignRate || 100, // 默认100%
            estimateSignRate: item.estimateSignRate || 100, // 默认100%
            startDate: item.startDate, // 开始时间，必填
            endDate: endDate // 结束时间，自动计算
          }
        })

        // 创建SKU配置
        advertiserConfig.skuList.push({
          id: sku.id,
          skuId: skuId, // SKU唯一标识，必填
          skuName: sku.skuName, // SKU名称，必填
          items: items
        })
      })

      advertiserList.push(advertiserConfig)
    })

    // 确保数据结构符合接口要求
    const requestData = {
      advertiserList: advertiserList
    }

    const res = await saveMediaProfitConfig(requestData)
    if (res.code === 200) {
      Message.success('保存成功')
      emit('success')
      close()
    } else {
      Message.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败', error)
    Message.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 关闭弹窗
const close = () => {
  open.value = false
  skuConfigData.value = []
  orderPricesList.value = []
  noOrderPriceData.value = false
  localMediaList.value = []
  selectedGoods.value = null
  goodsVisible.value = false
}

// 获取对话框标题
const dialogTitle = computed(() => {
  if (localMediaList.value.length === 1) {
    return '媒体盈亏配置录入：' + (localMediaList.value[0].advertiserName || '媒体配置')
  }
  return '媒体盈亏配置录入'
})

// 删除媒体账户
const removeMediaAccount = (index) => {
  localMediaList.value.splice(index, 1)

  // 如果删除后没有媒体账户了，清空配置数据
  if (localMediaList.value.length === 0) {
    skuConfigData.value = []
    selectedGoods.value = null
  }

  // 如果只剩一个媒体账户，切换到单个账户模式
  if (localMediaList.value.length === 1) {
    fetchData()
    fetchSelectedGoods()
    localDateRange.value = props.dateRange ? [...props.dateRange] : []
    fetchOrderPrices(localDateRange.value)
  }
}

// 获取结束时间
const getEndDate = (index, items) => {
  // 如果是最后一条配置项，返回"长期有效"
  if (index === items.length - 1) {
    return '长期有效'
  }

  // 获取下一条配置项的开始时间作为当前配置项的结束时间
  const nextItem = items[index + 1]
  if (nextItem && nextItem.startDate) {
    // 计算前一天作为结束时间
    const endDate = dayjs(nextItem.startDate).subtract(1, 'day')
    return endDate.format('YYYY-MM-DD')
  }

  return '长期有效'
}
</script>

<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="open"
    width="90%"
    top="8vh"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="goods-profit-config">
      <div class="profit-config-container">
        <!-- 左侧面板 -->
        <div class="left-panel">
          <!-- 单个媒体账户时显示订单SKU单价信息 -->
          <div v-if="mediaList.length === 1" class="order-prices-info">
            <!-- 选中的商品信息 -->
            <div v-if="showGoodsSelect" class="selected-goods-info">
              <div class="info-title">
                <span class="selected-goods-title">关联商品</span>
                <el-button
                  type="text"
                  size="mini"
                  :disabled="localMediaList.length !== 1"
                  style="color: #409EFF; padding: 0; margin-left: 8px;"
                  @click="goodsVisible = true"
                >
                  <i class="el-icon-plus" style="margin-right: 2px;" />选择商品
                </el-button>
              </div>
              <div v-loading="goodsLoading" class="selected-goods-container">
                <div v-if="selectedGoods" class="goods-item">
                  <div class="goods-info">
                    <div class="goods-image">
                      <image-preview :src="selectedGoods.goodsThumbnailUrl" :width="40" :height="40" />
                    </div>
                    <div class="goods-details">
                      <div class="goods-name" :title="selectedGoods.goodsName">{{ selectedGoods.goodsName }}</div>
                      <div class="goods-id">ID: {{ selectedGoods.goodsId }}</div>
                      <div v-if="selectedGoods.mallName" class="mall-name">店铺: {{ selectedGoods.mallName }}</div>
                    </div>
                  </div>
                </div>
                <div v-else class="no-goods">
                  <p>暂未关联商品</p>
                </div>
              </div>
            </div>

            <!-- 订单SKU单价信息 -->
            <div class="order-sku-prices">
              <div class="info-title">
                <span class="order-prices-info-title">订单SKU单价</span>
                <span class="order-prices-info-tip">数据仅供参考</span>
                <el-popover
                  ref="datePopover"
                  placement="bottom"
                  width="380"
                  trigger="click"
                  popper-class="date-popover"
                >
                  <div class="date-picker-container">
                    <el-date-picker
                      v-model="localDateRange"
                      type="daterange"
                      range-separator="-"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                      @change="updateDateRange"
                    />
                  </div>
                  <i slot="reference" class="el-icon-date time-icon" title="修改时间范围" />
                </el-popover>
              </div>
              <el-table
                v-loading="pricesLoading"
                :data="orderPricesList"
                border
                size="mini"
                style="width: 100%"
                class="price-table"
              >
                <el-table-column
                  prop="goodsPrice"
                  label="订单商品价格(元)"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{ (scope.row.goodsPrice / 100).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="orderCount"
                  label="数量"
                  align="center"
                  width="60"
                />
                <template slot="empty">
                  <div class="empty-text">
                    {{ noOrderPriceData ? '暂无数据' : '加载中...' }}
                  </div>
                </template>
              </el-table>
            </div>
          </div>

          <!-- 多个媒体账户时显示媒体账户列表 -->
          <div v-else class="media-list-info">
            <div class="info-title">
              <span class="media-list-title">选中的媒体账户</span>
              <span class="media-count-tip">共 {{ localMediaList.length }} 个账户</span>
            </div>
            <div class="media-list-container">
              <div
                v-for="(media, index) in localMediaList"
                :key="media.advertiserId"
                class="media-item"
              >
                <div class="media-info">
                  <div class="media-name">
                    <svg-icon :icon-class="dictMap.media_type[media.mediaType]" />
                    {{ media.advertiserName || media.advertiserId }}
                  </div>
                </div>
                <el-button
                  type="text"
                  size="mini"
                  style="color: #f56c6c;"
                  @click="removeMediaAccount(index)"
                >
                  <i class="el-icon-delete" />
                </el-button>
              </div>
              <div v-if="localMediaList.length === 0" class="empty-media">
                <p>没有选中的媒体账户</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧 配置表格 -->
        <div class="right-panel" :class="{ 'full-width': localMediaList.length > 1 }">

          <div class="profit-toolbar">
            <!-- <el-form label-width="68px" inline>
              <el-form-item label="充值返点" prop="profitBackRate" style="margin-bottom: 0;">
                <div class="flex">
                  <el-input-number v-model="profitForm.profitBackRate" :controls="false" :min="0" :max="100" :precision="1" />
                  <span class="ml5">%</span>
                </div>
              </el-form-item>
            </el-form> -->
            <div />
            <el-button type="primary" size="mini" @click="addSku">
              <i class="el-icon-plus" /> 添加SKU
            </el-button>
          </div>

          <!-- 二维数组表格显示 -->
          <div v-loading="loading" class="sku-config-container">
            <div v-if="skuConfigData.length === 0" class="empty-state">
              <p>暂无配置，请点击"添加SKU"开始配置</p>
            </div>

            <div v-for="(sku, skuIndex) in skuConfigData" :key="sku.skuId" class="sku-group">
              <!-- SKU头部信息 -->
              <div class="sku-header">
                <div class="sku-info">
                  <div class="sku-name-input">
                    <label>SKU名称：</label>
                    <el-input
                      v-model="sku.skuName"
                      placeholder="请输入SKU名称"
                      size="small"
                      style="width: 200px;"
                    />
                  </div>
                </div>
                <div class="sku-actions">
                  <el-button type="text" size="small" @click="addItemToSku(skuIndex)">
                    <i class="el-icon-plus" /> 添加配置项
                  </el-button>
                  <el-button type="text" size="small" style="color: #f56c6c;" @click="deleteSku(skuIndex)">
                    <i class="el-icon-delete" /> 删除SKU
                  </el-button>
                </div>
              </div>

              <!-- 配置项表格 -->
              <el-table
                :data="sku.items"
                border
                size="mini"
                class="items-table"
              >
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>

                <el-table-column label="开始时间" min-width="150">
                  <template slot="header">
                    开始时间
                    <span style="color: #f56c6c;">*</span>
                  </template>
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.startDate"
                      type="date"
                      placeholder="选择开始时间"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      size="mini"
                      style="width: 100%"
                      :clearable="false"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="结束时间" min-width="100">
                  <template slot-scope="scope">
                    <span>{{ getEndDate(scope.$index, sku.items) }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="SKU单价(元)" min-width="110">
                  <template slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.skuPrice"
                      :controls="false"
                      :min="0"
                      :precision="2"
                      placeholder="SKU单价"
                      size="mini"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="商品成本(元)" width="110">
                  <template slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.goodsCost"
                      :controls="false"
                      :min="0"
                      :precision="2"
                      placeholder="商品成本"
                      size="mini"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="物流成本(元)" width="110">
                  <template slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.expressCost"
                      :controls="false"
                      :min="0"
                      :precision="2"
                      placeholder="物流成本"
                      size="mini"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="其他成本(元)" width="110">
                  <template slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.otherCost"
                      :controls="false"
                      :min="0"
                      :precision="2"
                      placeholder="其他成本"
                      size="mini"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="平台佣金率(%)" width="120">
                  <template slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.commissionRate"
                      :controls="false"
                      :min="0"
                      :max="100"
                      :precision="1"
                      placeholder="平台佣金率"
                      size="mini"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="参考签收率(%)" width="140">
                  <template slot="header">
                    参考签收率(%)
                    <el-tooltip class="item" effect="dark" content="根据投放前历史经验填写的参考签收率" placement="top">
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.referSignRate"
                      :controls="false"
                      :min="1"
                      :max="100"
                      :precision="1"
                      placeholder="100"
                      size="mini"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="预估签收率(%)" width="140">
                  <template slot="header">
                    预估签收率(%)
                    <el-tooltip class="item" effect="dark" content="根据投放中修正的预估签收率" placement="top">
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <el-input-number
                      v-model="scope.row.estimateSignRate"
                      :controls="false"
                      :min="1"
                      :max="100"
                      :precision="1"
                      placeholder="100"
                      size="mini"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      size="mini"
                      style="color: #f56c6c;"
                      @click="deleteItem(skuIndex, scope.$index)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button :loading="saveLoading" type="primary" @click="submit">保存</el-button>
      <el-button @click="close">关闭</el-button>
    </div>

    <!-- 商品选择器 -->
    <GoodsSelector
      :visible.sync="goodsVisible"
      type="single"
      :query-list="['goodsName', 'goodsId', 'platform', 'mediaPlatformType']"
      @select="handleGoodsSelect"
      @close="goodsVisible = false"
    />
  </el-dialog>

</template>

<style scoped lang="scss">
.goods-profit-config {
  height: 65vh;
  overflow: hidden;

  .profit-config-container {
    display: flex;
    gap: 20px;
    height: 100%;

    .left-panel {
      flex: 0 0 220px; /* 固定宽度220px，不伸缩 */
      min-width: 0; /* 防止内容溢出 */
      height: 100%;
    }

    .right-panel {
      flex: 1; /* 占据剩余空间 */
      min-width: 0; /* 防止内容溢出 */
      height: 100%;
      display: flex;
      flex-direction: column;

      &.full-width {
        flex: 1;
        width: 100%;
      }
    }
  }

  .profit-toolbar {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
  }

  .batch-tip {
    margin-bottom: 15px;
    font-size: 14px;

    .color-primary {
      color: #409EFF;
    }
  }

  .el-input-number {
    width: 100%;
  }

  // 二维数组表格样式
  .sku-config-container {
    flex: 1;
    overflow-y: auto;
    min-height: 0;

    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #909399;
      font-size: 14px;
    }

    .sku-group {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .sku-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background-color: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-bottom: none;

        .sku-info {
          display: flex;
          align-items: center;
          gap: 20px;

          .sku-name-input {
            display: flex;
            align-items: center;
            gap: 8px;

            label {
              font-weight: 500;
              color: #303133;
              white-space: nowrap;
            }
          }
        }

        .sku-actions {
          display: flex;
          gap: 8px;
        }
      }

      .items-table {
        .el-table__body-wrapper {
          max-height: none;
        }

        .el-input-number {
          .el-input__inner {
            padding: 0 8px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }

  .order-prices-info {
    height: 100%;
    display: flex;
    flex-direction: column;

    .order-sku-prices {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;

      .price-table {
        flex: 1;

        .el-table__body-wrapper {
          max-height: none;
        }
      }
    }

    .info-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 4px 0;

      .order-prices-info-title {
        font-size: 14px;
        font-weight: bold;
      }

      .selected-goods-title {
        font-size: 14px;
        font-weight: bold;
      }

      .order-prices-info-tip {
        margin-left: 10px;
        font-size: 12px;
        color: #f56c6c;
      }

      .time-icon {
        margin-left: 10px;
        font-size: 16px;
        color: #409EFF;
        cursor: pointer;

        &:hover {
          color: #66b1ff;
        }
      }
    }

    .date-picker-container {
      padding: 5px;
    }

    .empty-text {
      color: #909399;
      font-size: 14px;
      padding: 10px 0;
    }
  }

  .selected-goods-info {
    margin-bottom: 8px;
    flex-shrink: 0;

    .selected-goods-container {
      border: 1px solid #e4e7ed;
      padding: 8px;
      min-height: 50px;

      .goods-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 8px;

        .goods-info {
          display: flex;
          align-items: flex-start;
          flex: 1;
          gap: 8px;
          min-width: 0;

          .goods-image {
            border: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .no-image {
              font-size: 10px;
              color: #909399;
            }
          }

          .goods-details {
            flex: 1;
            min-width: 0;

            .goods-name {
              font-size: 12px;
              font-weight: 500;
              color: #303133;
              margin-bottom: 2px;
              line-height: 1.2;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              max-height: 28px;
            }

            .goods-id {
              font-size: 10px;
              color: #909399;
              margin-bottom: 2px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .mall-name {
              font-size: 10px;
              color: #409EFF;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }

        .el-button {
          flex-shrink: 0;
          margin-top: 2px;
        }
      }

      .no-goods {
        text-align: center;
        color: #909399;
        font-size: 12px;
        padding: 15px 0;
      }
    }
  }

  .media-list-info {
    height: 100%;

    .info-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 4px 0;

      .media-list-title {
        font-size: 14px;
        font-weight: bold;
      }

      .media-count-tip {
        margin-left: 10px;
        font-size: 12px;
        color: #409EFF;
      }
    }

    .media-list-container {
      max-height: 450px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      .media-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #f5f7fa;
        }

        .media-info {
          flex: 1;

          .media-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }

          .media-type {
            font-size: 12px;
            color: #909399;
          }
        }
      }

      .empty-media {
        text-align: center;
        padding: 40px 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

::v-deep(.el-input-number.is-without-controls .el-input__inner) {
  padding-right: 5px !important;
  padding-left: 5px !important;
}
</style>
