<template>
  <div class="top-right-btn" :style="style">
    <div class="flex">
      <!--      <template>-->
      <!--        <el-tooltip v-if="search" class="item" effect="dark" :content="showSearch ? '隐藏搜索' : '显示搜索'" placement="top">-->
      <!--          <el-button size="mini" circle icon="el-icon-search" @click="toggleSearch()" />-->
      <!--        </el-tooltip>-->
      <!--      </template>-->
      <el-tooltip v-if="search" class="item" :open-delay="500" effect="dark" :content="showSearch ? '隐藏搜索' : '显示搜索'" placement="top">
        <!-- <el-button size="mini" circle icon="el-icon-search" @click="toggleSearch()" /> -->
        <i class="el-icon-search action-item" @click="toggleSearch()" />
      </el-tooltip>
      <el-tooltip class="item" :open-delay="500" effect="dark" content="刷新" placement="top">
        <i class="el-icon-refresh action-item" @click="refresh()" />
      </el-tooltip>
      <el-tooltip v-if="columns" class="item" :open-delay="500" effect="dark" content="编辑列表" placement="top">
        <i class="el-icon-menu action-item" @click="showColumn()" />
      </el-tooltip>
    </div>
    <el-dialog :title="title" :visible.sync="open" append-to-body :fullscreen="device==='mobile'" width="75%" top="10vh">
      <el-row :gutter="20">
        <el-col :span="24">
          <h3 class="list-title">显示</h3>
          <draggable class="list-group" :list="showList" group="columns">
            <div
              v-for="(element, index) in showList"
              :key="element.name"
              class="list-group-item"
            >
              {{ element.label }}
              <el-button
                class="list-group-item-action"
                icon="el-icon-delete"
                type="text"
                size="mini"
                @click="minusColumn(index)"
              />
            </div>
          </draggable>
        </el-col>
        <el-col :span="24">
          <h3 class="list-title">隐藏</h3>
          <draggable class="list-group" :list="hideList" group="columns">
            <div
              v-for="(element, index) in hideList"
              :key="element.name"
              class="list-group-item"
            >
              {{ element.label }}
              <el-button
                class="list-group-item-action"
                icon="el-icon-plus"
                size="mini"
                type="text"
                @click="plusColumn(index)"
              />
            </div>
          </draggable>
        </el-col>
      </el-row>
      <div slot="footer" class="">
        <el-row :gutter="16">
          <el-col :sm="24" :lg="12" style="display:flex;margin-bottom: 10px">
            <el-select v-model="selectedId" placeholder="请选择" @change="handleColumnsChange">
              <el-option
                v-for="item in customList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <span style="float: left">{{ item.name }}</span>
                <div class="fr">
                  <el-button type="text" icon="el-icon-edit" @click.stop="editColumnName(item)" />
                  <el-button type="text" style=" color: #E65D6E;" icon="el-icon-delete" @click.stop="removeCustomColumns(item)" />
                </div>
              </el-option>
            </el-select>
            <el-button style="margin-left: 10px" type="primary" icon="el-icon-plus" @click="createColumns">新增</el-button>
            <el-button @click="resetColumns">默认排序
              <el-tooltip class="item" effect="dark" content="将当配置设置为默认排序，保存后生效" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </el-button>
          </el-col>
          <el-col :sm="24" :lg="12">
            <div class="float-right">
              <el-button @click="open = false">取 消</el-button>
              <!--          <el-button type="primary" @click="submitColumnChange(true)">仅保存</el-button>-->
              <el-button type="primary" @click="submitColumnChange()">保存并应用</el-button>

            </div>
          </el-col>
        </el-row></div></el-dialog>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { mapGetters } from 'vuex'
export default {
  name: 'RightToolbar',
  components: {
    draggable
  },
  props: {
    showSearch: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array,
      default: null
    },
    customList: {
      type: Array,
      default: () => []
    },
    columnsInstance: {
      type: Object,
      default: null
    },
    search: {
      type: Boolean,
      default: true
    },
    gutter: {
      type: Number,
      default: 10
    }
  },

  data() {
    return {
      // 显隐数据
      value: [],
      // 弹出层标题
      title: '编辑列表',
      // 是否显示弹出层
      open: false,
      showList: [],
      hideList: [],
      selectedId: null
    }
  },
  computed: {
    ...mapGetters([
      'device'
    ]),
    style() {
      const ret = {}
      if (this.gutter) {
        ret.marginRight = `${this.gutter / 2}px`
      }
      return ret
    }
  },
  watch: {
    columns: {
      handler(val) {
        if (val) { this.initList() }
      },
      immediate: true,
      deep: true
    },
    customList: {
      handler(val) {
        const checked = val.find(item => item.checked)
        if (checked) this.selectedId = checked.id
      }
    }
  },
  methods: {
    // 搜索
    toggleSearch() {
      this.$emit('update:showSearch', !this.showSearch)
    },
    // 刷新
    refresh() {
      this.$emit('queryTable')
    },
    initList() {
      this.showList = []
      this.hideList = []
      this.columns.forEach(item => {
        if (item.visible !== false) {
          this.showList.push(item)
        } else {
          this.hideList.push(item)
        }
      })
    },
    handleColumnsChange(id) {
      this.columnsInstance.selectColumns(id)
    },
    submitColumnChange(visible = false) {
      this.showList.forEach(item => {
        item.visible = true
      })
      this.hideList.forEach(item => {
        item.visible = false
      })
      this.columnsInstance.updateColumns([...this.showList, ...this.hideList], visible)
      this.open = visible
    },
    resetColumns() {
      this.showList = [...this.columnsInstance.getCloneColumns()]
      this.hideList = []
      this.$message({
        message: '已恢复默认排序',
        type: 'success'
      })
      // this.columnsInstance.resetColumns()
    },
    createColumns() {
      this.columnsInstance.createCustomColumns()
    },
    removeCustomColumns(item) {
      this.$confirm(`确认删除${item.name}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.columnsInstance.removeCustomColumns(item)
      })
    },
    editColumnName(item) {
      this.columnsInstance.editColumnName(item)
    },
    minusColumn(index) {
      this.hideList.push(...this.showList.splice(index, 1))
    },
    plusColumn(index) {
      this.showList.push(...this.hideList.splice(index, 1))
    },
    // 打开显隐列dialog
    showColumn() {
      this.open = true
      this.initList()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-transfer__button {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}
::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}
.list-title {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0
}
.list-group {
  height: 210px;
  border: 1px solid #cacbd2;
  border-radius: 4px;
  padding: 10px 10px 0;
  background-color: #fff;
  overflow-y: auto;
  .list-group-item {
    user-select: none;
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    margin-right: 10px;
    border: 1px solid #cacbd2;
    border-radius: 4px;
    margin-bottom: 10px;
    background-color: #fff;
    line-height: 28px;
    cursor: move;
    &:hover .list-group-item-action {
      display: inline;
    }
  }
  .list-group-item-action {
    //display: none;
    margin-left: 10px;
  }
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
}

  .action-item {
    @apply  w-8 h-8 flex items-center justify-center rounded-lg text-gray-600 cursor-pointer transition-all duration-200 hover:bg-gray-100 hover:text-blue-600 hover:-translate-y-0.5;
    border-radius: var(--ios-border-radius-small);
    color: var(--ios-text-secondary);

    &:hover {
      background: var(--ios-fill-quaternary);
      color: var(--ios-blue);
    }
  }
</style>
