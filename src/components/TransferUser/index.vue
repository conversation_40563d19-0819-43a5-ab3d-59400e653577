<template>
  <div>
    <el-form ref="queryForm" class="search-form" :model="queryParams" size="small" :inline="true">
      <el-form-item prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="用户名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="用户昵称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="userList">
      <el-table-column key="userName" label="用户名称" align="center" prop="userName" :show-overflow-tooltip="true" />
      <el-table-column key="nickName" label="用户昵称" align="center" prop="nickName" :show-overflow-tooltip="true" />
      <el-table-column key="deptName" label="部门" align="center" prop="dept.deptName" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
            size="small"
            type="primary"
            @click="select(scope.row)"
          >选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      layout="sizes, prev, pager, next"
      @pagination="getList"
    />
  </div>

</template>

<script>
import { listUser } from '@/api/system/user'

export default {
  name: 'TransferUser',
  props: {
    confirm: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      dateRange: [],
      total: 0,
      queryParams: {
        userName: '',
        nickName: '',
        phonenumber: '',
        status: '0',
        beginTime: '',
        endTime: ''
      },
      userList: []
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.rows
        this.total = response.total
        this.loading = false
      }
      )
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    select({ userId, userName }) {
      if (this.confirm) {
        this.$confirm(`将选择${userName}作为移交用户, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit('select', userId)
        }).catch(() => {})
      } else {
        this.$emit('select', userId, userName)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
