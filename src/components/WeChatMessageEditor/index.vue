<template>
  <div class="wechat-message-editor">
    <!-- 消息预览区 -->
    <div class="preview-container">
      <div class="phone-frame">
        <div class="phone-header">
          <div class="phone-status">
            <span class="time">9:41</span>
            <div class="indicators">
              <span class="signal">●●●</span>
              <span class="wifi">📶</span>
              <span class="battery">🔋</span>
            </div>
          </div>
          <div class="chat-header">
            <div class="back-button">
              <i class="el-icon-arrow-left" />
            </div>
            <div class="chat-title">消息预览</div>
            <div class="more-button">
              <i class="el-icon-more" />
            </div>
          </div>
        </div>

        <div ref="chatContent" class="chat-content">
          <div class="messages-list">
            <draggable
              v-model="internalMessages"
              :options="{ animation: 200, ghostClass: 'ghost', handle: '.message-bubble' }"
              tag="div"
              class="draggable-container"
              @end="onDragEnd"
            >
              <div
                v-for="(message, index) in internalMessages"
                :key="message.serialNumber"
                class="message-item"
                :class="{ 'editing': editingIndex === index }"
              >
                <div class="message-container">
                  <div class="sender-avatar">
                    <div class="avatar-placeholder">企</div>
                  </div>
                  <div class="message-bubble">
                    <div class="message-content">
                      <!-- 文本消息 -->
                      <div v-if="message.contentType === 10001" class="text-message">
                        <span>{{ message.msgContent }}</span>
                      </div>

                      <!-- 图片消息 -->
                      <div v-else-if="message.contentType === 10002" class="image-message">
                        <img :src="message.imgUrl" alt="图片" @error="handleImageError">
                      </div>

                      <!-- 文件消息 -->
                      <div v-else-if="message.contentType === 10006" class="file-message">
                        <div class="file-info">
                          <i class="el-icon-document" />
                          <div class="file-details">
                            <div class="file-name">{{ message.fileName }}</div>
                            <div class="file-type">文件</div>
                          </div>
                        </div>
                      </div>

                      <!-- 网页消息 -->
                      <div v-else-if="message.contentType === 10012" class="link-message">
                        <div class="link-info">
                          <div class="link-title">{{ message.title }}</div>
                          <div class="link-description">{{ message.msgContent }}</div>
                          <div class="link-url">{{ message.linkUrl }}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 消息操作按钮 -->
                  <div class="message-actions" @click.stop>
                    <el-button
                      size="mini"
                      type="text"
                      class="action-btn edit-btn"
                      @click.stop="editMessage(index)"
                    >
                      <i class="el-icon-edit" />
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      class="action-btn delete-btn"
                      @click.stop="deleteMessage(index)"
                    >
                      <i class="el-icon-delete" />
                    </el-button>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="internalMessages.length === 0" class="empty-state">
                <i class="el-icon-chat-line-square" />
                <p>暂无消息，请在下方添加</p>
              </div>
            </draggable>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息输入区 -->
    <div v-if="showEditor && !readonly" class="input-container">
      <div class="input-header">
        <span class="input-title">{{ editingIndex !== -1 ? '编辑消息' : '添加消息' }}</span>
        <span class="message-count">{{ internalMessages.length }}/{{ maxCount === 0 ? '∞' : maxCount }}</span>
      </div>

      <!-- 消息类型选择 -->
      <div class="message-type-selector">
        <el-radio-group v-model="currentMessageType" size="mini" @change="onMessageTypeChange">
          <el-radio-button :label="10001">
            <i class="el-icon-chat-line-square" />
            文本
          </el-radio-button>
          <el-radio-button :label="10002">
            <i class="el-icon-picture" />
            图片
          </el-radio-button>
          <el-radio-button :label="10006">
            <i class="el-icon-document" />
            文件
          </el-radio-button>
          <el-radio-button :label="10012">
            <i class="el-icon-link" />
            网页
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 消息内容输入 -->
      <div class="message-input">
        <!-- 文本输入 -->
        <div v-if="currentMessageType === 10001" class="text-input">
          <el-input
            v-model="currentContent.text"
            type="textarea"
            :rows="4"
            placeholder="请输入文本消息内容..."
            maxlength="1000"
            show-word-limit
          />
        </div>

        <!-- 图片选择 -->
        <div v-else-if="currentMessageType === 10002" class="image-input">
          <div class="image-selector">
            <div class="upload-area" @click="selectImage">
              <img v-if="currentContent.imgUrl" :src="currentContent.imgUrl" class="uploaded-image">
              <div v-else class="upload-placeholder">
                <i class="el-icon-picture" />
                <div>点击选择图片</div>
              </div>
            </div>
            <div v-if="currentContent.imgUrl" class="image-actions">
              <el-button size="mini" @click="selectImage">重新选择</el-button>
              <el-button size="mini" type="danger" @click="clearImage">清除</el-button>
            </div>
          </div>
        </div>

        <!-- 文件选择 -->
        <div v-else-if="currentMessageType === 10006" class="file-input">
          <div class="file-selector">
            <el-button size="small" type="primary" @click="selectFile">
              <i class="el-icon-folder" />
              选择文件
            </el-button>
            <div v-if="currentContent.fileName" class="file-preview">
              <div class="file-info">
                <i class="el-icon-document" />
                <div class="file-details">
                  <div class="file-name">{{ currentContent.fileName }}</div>
                  <div v-if="currentContent.fileSize" class="file-size">{{ formatFileSize(currentContent.fileSize) }}</div>
                </div>
              </div>
              <div class="file-actions">
                <el-button size="mini" @click="selectFile">重新选择</el-button>
                <el-button size="mini" type="danger" @click="clearFile">清除</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 网页输入 -->
        <div v-else-if="currentMessageType === 10012" class="link-input">
          <el-form :model="currentContent" label-width="80px" size="small">
            <el-form-item label="网页标题">
              <el-input v-model="currentContent.title" placeholder="请输入网页标题" />
            </el-form-item>
            <el-form-item label="网页地址">
              <el-input v-model="currentContent.linkUrl" placeholder="请输入网页地址" />
            </el-form-item>
            <el-form-item label="网页描述">
              <el-input
                v-model="currentContent.msgContent"
                type="textarea"
                :rows="2"
                placeholder="请输入网页描述（可选）"
              />
            </el-form-item>
            <el-form-item label="网页封面">
              <div class="image-selector">
                <div class="upload-area" @click="selectImage">
                  <img v-if="currentContent.imgUrl" :src="currentContent.imgUrl" class="uploaded-image">
                  <div v-else class="upload-placeholder">
                    <i class="el-icon-picture" />
                    <div>点击选择图片</div>
                  </div>
                </div>
                <div v-if="currentContent.imgUrl" class="image-actions">
                  <el-button size="mini" @click="selectImage">重新选择</el-button>
                  <el-button size="mini" type="danger" @click="clearImage">清除</el-button>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="input-actions">
        <el-button
          v-if="editingIndex !== -1"
          size="small"
          @click="cancelEdit"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="small"
          :disabled="!isContentValid"
          :loading="uploading"
          @click="saveMessage"
        >
          {{ editingIndex !== -1 ? '保存' : '添加' }}
        </el-button>
      </div>
    </div>
  </div></template>

<script>
import draggable from 'vuedraggable'
import { fileSelector } from '@/views/resource/utils/fileSelectorPlugin'
import { getFileExtension } from '@/views/resource/utils/fileUtils'

export default {
  name: 'WeChatMessageEditor',
  components: {
    draggable
  },

  model: {
    prop: 'modelValue',
    event: 'update:modelValue'
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    messages: {
      type: Array,
      default: () => []
    },
    maxCount: {
      type: Number,
      default: 10
    },
    readonly: {
      type: Boolean,
      default: false
    },
    showEditor: {
      type: Boolean,
      default: true
    }
  },
  emits: ['error'],
  data() {
    return {
      currentMessageType: 10001,
      editingIndex: -1,
      uploading: false,
      currentContent: {
        text: '',
        imgUrl: '',
        fileName: '',
        fileUrl: '',
        fileSize: 0,
        title: '',
        linkUrl: '',
        msgContent: ''
      }
    }
  },
  computed: {
    internalMessages: {
      get() {
        // 如果传入了messages属性（只读模式），使用messages，否则使用modelValue
        return this.messages.length > 0 ? this.messages : this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      },
      deep: true
    },
    isContentValid() {
      switch (this.currentMessageType) {
        case 10001:
          return this.currentContent.text.trim() !== ''
        case 10002:
          return this.currentContent.imgUrl !== ''
        case 10006:
          return this.currentContent.fileName !== '' && this.currentContent.fileUrl !== ''
        case 10012:
          return this.currentContent.title.trim() !== '' && this.currentContent.linkUrl.trim() !== ''
        default:
          return false
      }
    }
  },
  methods: {
    // 拖拽结束后重新分配序号
    onDragEnd() {
      this.internalMessages.forEach((message, index) => {
        message.serialNumber = index + 1
      })
    },

    // 编辑消息
    editMessage(index) {
      this.editingIndex = index
      const message = this.internalMessages[index]
      this.currentMessageType = message.contentType
      this.loadMessageContent(message)
    },

    // 加载消息内容到编辑器
    loadMessageContent(message) {
      this.resetCurrentContent()

      switch (message.contentType) {
        case 10001:
          this.currentContent.text = message.msgContent
          break
        case 10002:
          this.currentContent.imgUrl = message.imgUrl
          break
        case 10006: {
          this.currentContent.fileName = message.fileName
          this.currentContent.fileExtension = message.fileExtension
          this.currentContent.fileUrl = message.msgContent
          break
        }
        case 10012: {
          this.currentContent.title = message.title
          this.currentContent.linkUrl = message.linkUrl
          this.currentContent.imgUrl = message.imgUrl
          this.currentContent.msgContent = message.msgContent || ''
          break
        }
      }
    },

    // 删除消息
    deleteMessage(index) {
      this.$confirm('确认删除这条消息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.internalMessages.splice(index, 1)
        // 重新分配序号
        this.internalMessages.forEach((message, idx) => {
          message.serialNumber = idx + 1
        })

        // 如果删除的是正在编辑的消息，取消编辑
        if (this.editingIndex === index) {
          this.cancelEdit()
        } else if (this.editingIndex > index) {
          this.editingIndex--
        }
      }).catch(() => {})
    },

    // 保存消息
    saveMessage() {
      if (!this.isContentValid) {
        return
      }

      // 检查数量限制
      if (this.editingIndex === -1 && this.maxCount > 0 && this.internalMessages.length >= this.maxCount) {
        this.$emit('error', 'count_limit', `最多只能添加${this.maxCount}条消息`)
        return
      }

      const messageData = this.buildMessageData()

      if (this.editingIndex !== -1) {
        // 编辑模式：更新现有消息
        this.$set(this.internalMessages, this.editingIndex, messageData)
      } else {
        // 添加模式：新增消息
        messageData.serialNumber = this.internalMessages.length + 1
        this.internalMessages.push(messageData)
      }

      this.cancelEdit()
    },

    // 构建消息数据
    buildMessageData() {
      const data = {
        serialNumber: this.editingIndex !== -1 ? this.internalMessages[this.editingIndex].serialNumber : 0,
        contentType: this.currentMessageType,
        msgContent: ''
      }

      switch (this.currentMessageType) {
        case 10001:
          data.msgContent = this.currentContent.text
          break
        case 10002:
          data.imgUrl = this.currentContent.imgUrl
          break
        case 10006:
          data.msgContent = this.currentContent.fileUrl
          data.fileName = this.currentContent.fileName
          data.fileExtension = this.currentContent.fileExtension
          break
        case 10012:
          Object.assign(data, {
            title: this.currentContent.title,
            linkUrl: this.currentContent.linkUrl,
            msgContent: this.currentContent.msgContent,
            imgUrl: this.currentContent.imgUrl
          })
          break
      }

      return data
    },

    // 取消编辑
    cancelEdit() {
      this.editingIndex = -1
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.resetCurrentContent()
      this.currentMessageType = 10001
    },

    // 重置当前内容
    resetCurrentContent() {
      this.currentContent = {
        text: '',
        imgUrl: '',
        fileName: '',
        fileUrl: '',
        fileSize: 0,
        title: '',
        linkUrl: '',
        msgContent: ''
      }
    },

    // 消息类型改变
    onMessageTypeChange() {
      this.resetCurrentContent()
    },

    // 选择图片
    async selectImage() {
      try {
        const selectedFile = await fileSelector.selectImages({
          multiple: false
        })

        if (selectedFile && selectedFile.url) {
          this.currentContent.imgUrl = selectedFile.url
          this.$message.success('图片选择成功')
        }
      } catch (error) {
        if (error.message !== '用户取消选择') {
          console.error('选择图片失败：', error)
          this.$emit('error', 'select_failed', '选择图片失败，请重试')
        }
      }
    },

    // 清除图片
    clearImage() {
      this.currentContent.imgUrl = ''
    },

    // 选择文件
    async selectFile() {
      try {
        const selectedFile = await fileSelector.selectSingle({
          fileTypes: [] // 允许所有类型的文件
        })

        console.log(selectedFile)

        if (selectedFile && selectedFile.url) {
          this.currentContent.fileName = selectedFile.fileName
          this.currentContent.fileExtension = getFileExtension(selectedFile.fileName)
          this.currentContent.fileUrl = selectedFile.url
          this.currentContent.fileSize = selectedFile.fileSize || 0
          this.$message.success('文件选择成功')
        }
      } catch (error) {
        if (error.message !== '用户取消选择') {
          console.error('选择文件失败：', error)
          this.$emit('error', 'select_failed', '选择文件失败，请重试')
        }
      }
    },

    // 清除文件
    clearFile() {
      this.currentContent.fileName = ''
      this.currentContent.fileUrl = ''
      this.currentContent.fileSize = 0
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return ''

      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      let fileSize = size

      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024
        index++
      }

      return `${fileSize.toFixed(index === 0 ? 0 : 1)}${units[index]}`
    },

    // 图片加载错误
    handleImageError(e) {
      e.target.style.display = 'none'
    },

    // 解析文件信息
    getFileInfo(msgContent) {
      try {
        return JSON.parse(msgContent)
      } catch {
        return { fileName: '文件', fileUrl: '' }
      }
    },

    // 解析网页信息
    getLinkInfo(msgContent) {
      try {
        return JSON.parse(msgContent)
      } catch {
        return { title: '网页', url: '', description: '' }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-message-editor {
  display: flex;
  gap: 24px;
  min-height: 500px;

  .preview-container {
    flex: 1;
    max-width: 320px;

    .phone-frame {
      background: #000;
      border-radius: 24px;
      padding: 4px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

      .phone-header {
        background: #fff;
        border-radius: 20px 20px 0 0;
        padding: 8px 16px 0;

        .phone-status {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 24px;
          font-size: 12px;
          font-weight: 600;

          .time {
            color: #000;
          }

          .indicators {
            display: flex;
            gap: 6px;
            font-size: 10px;
          }
        }

        .chat-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;

          .back-button, .more-button {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007AFF;
          }

          .chat-title {
            font-weight: 600;
            color: #000;
          }
        }
      }

      .chat-content {
        background: #f7f7f7;
        height: 400px;
        overflow-y: auto;
        padding: 16px;
        border-radius: 0 0 20px 20px;

        .messages-list {
          .draggable-container {
            min-height: 100%;
          }

          .message-item {
            margin-bottom: 12px;
            transition: all 0.3s ease;

            &.editing {
              background: rgba(0, 122, 255, 0.1);
              border-radius: 12px;
              padding: 8px;
              margin: -8px;
              margin-bottom: 4px;
            }

            &.ghost {
              opacity: 0.5;
              transform: rotate(2deg);
              background: rgba(0, 122, 255, 0.1);
              border-radius: 12px;
            }

            .message-container {
              display: flex;
              gap: 8px;
              align-items: flex-start;
              position: relative;

              .sender-avatar {
                width: 32px;
                height: 32px;
                flex-shrink: 0;
                margin-top: 4px;

                .avatar-placeholder {
                  width: 100%;
                  height: 100%;
                  background: #007AFF;
                  border-radius: 6px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 12px;
                  font-weight: 500;
                }
              }

              .message-actions {
                position: absolute;
                top: 50%;
                right: 8px;
                transform: translateY(-50%);
                display: flex;
                flex-direction: column;
                gap: 4px;
                opacity: 0;
                transition: opacity 0.2s ease;
                z-index: 10;
                pointer-events: auto;

                .action-btn {
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border: none;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                  cursor: pointer;
                  padding: 0;
                  min-width: auto;

                  &.edit-btn {
                    background: var(--ios-blue);
                    color: white;
                  }

                  &.delete-btn {
                    background: #ff3b30;
                    color: white;
                    margin-left: 0px;
                  }

                  i {
                    font-size: 12px;
                  }

                  &:hover {
                    transform: scale(1.1);
                  }
                }
              }

              &:hover .message-actions {
                opacity: 1;
              }
            }

            .message-bubble {
              position: relative;
              background: #ffffff;
              border: 1px solid #e5e5ea;
              border-radius: 6px 18px 18px 18px;
              padding: 12px 16px;
              max-width: calc(100% - 50px);
              color: #000;
              word-wrap: break-word;
              cursor: move;
              flex: 1;

              .message-content {
                .text-message {
                  line-height: 1.4;
                  white-space: pre-wrap;
                }

                .image-message {
                  img {
                    max-width: 200px;
                    max-height: 200px;
                    border-radius: 8px;
                    display: block;
                  }
                }

                .file-message {
                  .file-info {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    background: #f5f5f5;
                    border-radius: 8px;
                    padding: 8px;

                    i {
                      font-size: 24px;
                      color: #007AFF;
                    }

                    .file-details {
                      .file-name {
                        font-weight: 500;
                        margin-bottom: 2px;
                        color: #333;
                      }
                      .file-type {
                        font-size: 12px;
                        color: #666;
                      }
                    }
                  }
                }

                .link-message {
                  .link-info {
                    background: #f5f5f5;
                    border-radius: 8px;
                    padding: 12px;

                    .link-title {
                      font-weight: 600;
                      margin-bottom: 4px;
                      color: #333;
                    }
                    .link-description {
                      font-size: 13px;
                      color: #666;
                      margin-bottom: 6px;
                      line-height: 1.3;
                    }
                    .link-url {
                      font-size: 12px;
                      color: #007AFF;
                      word-break: break-all;
                    }
                  }
                }
              }
            }
          }

          .empty-state {
            text-align: center;
            color: #999;
            padding: 60px 20px;

            i {
              font-size: 48px;
              margin-bottom: 16px;
              display: block;
            }

            p {
              margin: 0;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .input-container {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .input-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;

      .input-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .message-count {
        font-size: 14px;
        color: #666;
        background: #f5f5f5;
        padding: 4px 8px;
        border-radius: 8px;
      }
    }

    .message-type-selector {
      margin-bottom: 20px;

    }

    .message-input {
      margin-bottom: 20px;

      .image-input, .link-input {
        .image-selector {
          .upload-area {
            width: 200px;
            height: 120px;
            border: 2px dashed #dcdfe6;
            border-radius: 8px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: border-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              border-color: var(--ios-blue);
            }

            .uploaded-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .upload-placeholder {
              text-align: center;
              color: #999;

              i {
                font-size: 24px;
                margin-bottom: 8px;
                display: block;
              }

              div {
                font-size: 14px;
              }
            }
          }

          .image-actions {
            margin-top: 8px;
            display: flex;
            gap: 8px;
          }
        }
      }

      .file-input {
        .file-selector {
          .file-preview {
            margin-top: 12px;
            padding: 12px;
            background: #f5f5f5;
            border-radius: 8px;

            .file-info {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 8px;

              i {
                font-size: 24px;
                color: var(--ios-blue);
              }

              .file-details {
                flex: 1;

                .file-name {
                  font-weight: 500;
                  margin-bottom: 2px;
                  color: #333;
                }

                .file-size {
                  font-size: 12px;
                  color: #666;
                }
              }
            }

            .file-actions {
              display: flex;
              gap: 8px;
            }
          }
        }
      }
    }

    .input-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .wechat-message-editor {
    flex-direction: column;
    gap: 16px;

    .preview-container {
      max-width: none;
    }
  }
}
</style>
