# 微信消息编辑器文件选择器集成指南

## 概述

微信消息编辑器现已集成文件选择器功能，用户可以从资源管理系统中选择图片和文件，而不需要重新上传。

## 集成内容

### 🎯 功能变更

**原来的功能：**

- 图片：通过文件上传组件选择本地图片并上传
- 文件：通过文件上传组件选择本地文件并上传

**现在的功能：**

- 图片：通过文件选择器从资源库中选择已上传的图片
- 文件：通过文件选择器从资源库中选择已上传的文件

### 🔧 技术实现

#### 1. 导入文件选择器

```javascript
import { fileSelector } from "@/views/resource/utils/fileSelectorPlugin";
```

#### 2. 图片选择方法

```javascript
// 选择图片
async selectImage() {
  try {
    const selectedFile = await fileSelector.selectImages({
      multiple: false
    })

    if (selectedFile && selectedFile.url) {
      this.currentContent.imgUrl = selectedFile.url
      this.$message.success('图片选择成功')
    }
  } catch (error) {
    if (error.message !== '用户取消选择') {
      console.error('选择图片失败：', error)
      this.$emit('error', 'select_failed', '选择图片失败，请重试')
    }
  }
}
```

#### 3. 文件选择方法

```javascript
// 选择文件
async selectFile() {
  try {
    const selectedFile = await fileSelector.selectSingle({
      fileTypes: [] // 允许所有类型的文件
    })

    if (selectedFile && selectedFile.url) {
      this.currentContent.fileName = selectedFile.fileName
      this.currentContent.fileUrl = selectedFile.url
      this.currentContent.fileSize = selectedFile.fileSize || 0
      this.$message.success('文件选择成功')
    }
  } catch (error) {
    if (error.message !== '用户取消选择') {
      console.error('选择文件失败：', error)
      this.$emit('error', 'select_failed', '选择文件失败，请重试')
    }
  }
}
```

### 🎨 界面变更

#### 图片选择界面

- **原来**：文件上传拖拽区域
- **现在**：点击选择区域，显示"点击选择图片"
- **新增**：重新选择和清除按钮

#### 文件选择界面

- **原来**：文件上传按钮
- **现在**：文件选择按钮，显示"选择文件"
- **新增**：文件信息显示（包含文件大小）
- **新增**：重新选择和清除按钮

### 📊 数据结构变更

#### 文件消息数据结构

```javascript
// 原来的结构
{
  fileName: "example.pdf",
  fileUrl: "https://..."
}

// 现在的结构
{
  fileName: "example.pdf",
  fileUrl: "https://...",
  fileSize: 1024000  // 新增文件大小字段
}
```

#### 组件内部数据

```javascript
currentContent: {
  text: '',
  imgUrl: '',
  fileName: '',
  fileUrl: '',
  fileSize: 0,        // 新增字段
  linkTitle: '',
  linkUrl: '',
  msgContent: ''
}
```

### 🛠️ 移除的功能

1. **文件上传 API 调用**

   - 移除了 `uploadImage` 和 `uploadFile` 的导入
   - 移除了 `handleImageChange` 和 `handleFileChange` 方法

2. **Element UI 上传组件**
   - 移除了 `el-upload` 组件的使用
   - 简化了界面结构

### ✨ 新增的功能

1. **文件大小显示**

   - 在文件预览中显示文件大小
   - 使用 `formatFileSize` 方法格式化显示

2. **清除功能**

   - 图片清除：`clearImage()` 方法
   - 文件清除：`clearFile()` 方法

3. **重新选择功能**
   - 可以重新选择图片或文件
   - 保持用户体验的连贯性

### 🎮 使用方式

#### 基本使用

```vue
<template>
  <WeChatMessageEditor
    v-model="messages"
    :max-count="10"
    @error="handleError"
  />
</template>

<script>
import WeChatMessageEditor from "@/components/WeChatMessageEditor/index.vue";

export default {
  components: {
    WeChatMessageEditor,
  },
  data() {
    return {
      messages: [],
    };
  },
  methods: {
    handleError(type, message) {
      this.$message.error(message);
    },
  },
};
</script>
```

#### 测试页面

访问 `/resource/wechat-editor-test` 查看集成效果。

### 🔍 错误处理

组件会发出以下错误事件：

- `select_failed`: 文件选择失败
- 用户取消选择不会触发错误事件

### 📝 注意事项

1. **权限要求**

   - 用户需要有访问资源管理系统的权限
   - 确保文件选择器组件可用

2. **文件类型**

   - 图片选择：自动筛选图片类型文件
   - 文件选择：允许所有类型的文件

3. **向后兼容**

   - 现有的消息数据结构保持兼容
   - 新的文件大小字段为可选

4. **网络依赖**
   - 文件选择器需要网络请求
   - 注意处理网络错误情况

### 🚀 优势

1. **统一管理**：所有文件通过资源管理系统统一管理
2. **避免重复上传**：可以重复使用已上传的文件
3. **更好的组织**：文件按分类组织，便于查找
4. **权限控制**：通过资源管理系统的权限控制访问
5. **存储优化**：避免重复存储相同文件

### 🔄 迁移指南

如果您有现有的微信消息编辑器使用代码：

1. **无需修改组件使用方式**：API 保持不变
2. **错误处理可能需要更新**：新增了 `select_failed` 错误类型
3. **样式可能需要微调**：界面有轻微变化

### 📞 技术支持

如果在使用过程中遇到问题：

1. 检查文件选择器组件是否正确安装
2. 确认用户权限设置
3. 查看浏览器控制台错误信息
4. 参考文件选择器的详细文档
