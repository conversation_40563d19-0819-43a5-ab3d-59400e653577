<template>
  <div class="input-multiple-box">
    <div>
      <el-tag v-for="(tag, index) in inputTagList" :key="index" type="info" closable size="mini" @close="handleClose(index)">
        {{ tag }}
      </el-tag>
    </div>
    <el-input
      v-model="inputValue"
      style="flex: 1; min-width: 100px"
      :placeholder="placeholder"
      @keyup.enter.native="handleSearch"
      @blur="handleInputChange"
    />
    <el-button class="input-multiple-plus" type="text" @click="handleInputConfirm">
      <i class="el-icon-plus" />
    </el-button>
  </div>
</template>

<script>
import { defineComponent, nextTick, onMounted, ref, watch } from 'vue'
export default defineComponent({
  props: ['tagValue', 'separator', 'placeholder', 'valueType'],
  emits: ['update:tagValue', 'search'],
  setup(props, _) {
    const inputTagList = ref([])
    let searchList = []
    const inputValue = ref('')
    const handleClose = (index) => {
      inputTagList.value.splice(index, 1)
    }
    const handleInputConfirm = () => {
      if (inputValue.value) {
        inputTagList.value.push(inputValue.value)
        inputValue.value = ''
      }
    }

    watch(() => inputTagList.value.length, () => {
      searchList = [...inputTagList.value]
      if (inputValue.value)searchList.push(inputValue.value)
      emitValueChange()
    })
    watch(() => props.tagValue.length, (val) => {
      if (!props.tagValue?.length) {
        inputValue.value = ''
        inputTagList.value = []
      }
    })
    const handleSearch = () => {
      if (inputValue.value) {
        if (inputTagList.value.length === 0) {
          searchList = [inputValue.value]
          emitValueChange()
        } else {
          inputTagList.value.push(inputValue.value)
          inputValue.value = ''
        }
      } else {
        searchList = [...inputTagList.value]
        emitValueChange()
      }
      nextTick(() => {
        _.emit('search')
      })
    }

    const handleInputChange = () => {
      if (inputValue.value) {
        if (inputTagList.value.length === 0) {
          searchList = [inputValue.value]
        } else {
          searchList = [...inputTagList.value, inputValue.value]
        }
      } else {
        searchList = [...inputTagList.value]
      }
      emitValueChange()
    }

    const emitValueChange = () => {
      _.emit('update:tagValue', props.valueType === 'array' ? searchList : searchList.join(props.separator || ','))
    }

    onMounted(() => {
      if (props.tagValue) {
        inputTagList.value = props.valueType === 'array' ? [...props.tagValue] : props.tagValue.split(props.separator || ',')
      }
    })
    return { inputTagList, inputValue, handleClose, handleInputConfirm, handleSearch, handleInputChange }
  }
})
</script>

<style lang="scss" scoped>
.input-multiple-box{
  width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  flex-wrap: wrap;
  :deep(.el-input__inner) {
    box-sizing: border-box;
    height: 30px;
    border: none
  }

  .el-tag {
    margin-left: 4px;
  }
  .input-multiple-plus {
    margin-right: 4px;
  }
}
</style>
