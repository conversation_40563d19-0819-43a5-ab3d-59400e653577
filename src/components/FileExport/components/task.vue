<script>

export default {
  name: 'TaskComponent',
  dicts: ['media_type'],
  computed: {
    mediaTypeMap() {
      return this.dict.type.media_type.concat([{ label: '未知媒体', value: '-1' }])
    }
  }
}

</script>
<script setup>
import { onMounted, ref, getCurrentInstance, computed } from 'vue'
import { checkRole } from '@/utils/permission'
import dayjs from 'dayjs'
import {
  getTaskList,
  getTaskStatusList,
  getTaskTypeList,
  deleteTask, updataTask
} from '@/api/report/index.js'
import { parseTime } from '@/utils/ruoyi'
import { downloadFile } from '@/utils'
import ClipboardButton from '@/components/ClipboardButton/index.vue'

const self = getCurrentInstance().proxy
const paramform = ref({
  // 媒体类型
  mediaType: '',
  // 状态
  state: '',
  // 操作类型
  type: '',
  // 操作人
  createBy: '',
  // 搜索内容
  keywords: '',
  // 任务编号
  id: '',
  times: [
    dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  ]
})

const roleState = computed(() => {
  return checkRole(['admin'])
})

const total = ref(0)
const queryParam = ref({ pageNum: 1, pageSize: 10 })
const tableData = ref([])
const tableLoading = ref(false)
function getList() {
  const data = {
    ...queryParam.value,
    status: paramform.value.state,
    type: paramform.value.type,
    fileName: paramform.value.keywords,
    id: paramform.value.id,
    createBy: paramform.value.createBy,
    mediaType: paramform.value.mediaType
  }
  if (paramform.value.times && paramform.value.times.length) {
    data.startTime = paramform.value.times[0]
    data.endTime = paramform.value.times[1]
  }
  tableLoading.value = true
  getTaskList(data)
    .then((res) => {
      tableData.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      tableLoading.value = false
    })
}
// 获取状态
const stateOptions = ref([])
function getStatusList() {
  getTaskStatusList().then((res) => {
    stateOptions.value = res.data || []
  })
}
// 获取操作类型
const typeOptions = ref([])
function getTypeList() {
  getTaskTypeList().then((res) => {
    typeOptions.value = res.data || []
  })
}
// 下载
function handleDownload(row) {
  if (row.filePath) {
    downloadFile(row.filePath, row.fileName)
  }
}
// 查询
function handleQuery() {
  queryParam.value.pageNum = 1
  getList()
}
const formRef = ref()
// 刷新
function refresh() {
  queryParam.value.pageNum = 1
  paramform.value.keywords = ''
  formRef.value.resetFields()
  getList()
}
// 重试
function handleRetry(row) {
  self.$confirm('确定要重试吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    updataTask({
      id: row.id,
      status: 'PENDING',
      failMsg: ''
    }).then((res) => {
      self.$modal.msgSuccess('操作成功')
      getList()
    })
  }).catch(() => {})
}
// 删除
function handleDelete(row) {
  self.$confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteTask(row.id).then((res) => {
      self.$modal.msgSuccess('删除成功')
      getList()
    })
  }).catch(() => {})
}
onMounted(() => {
  getStatusList()
  getTypeList()
  getList()
})
</script>

<template>
  <div>
    <el-form
      ref="formRef"
      class="card form_header"
      :model="paramform"
      :inline="true"
      label-width="90px"
    >
      <el-form-item v-if="roleState" label="媒体类型" prop="mediaType">
        <el-select
          v-model="paramform.mediaType"
          style="width: 150px"
          placeholder="媒体类型"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="(dict,index) in mediaTypeMap"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="任务编号" label-width="90px" prop="id">
        <el-input v-model.trim="paramform.id" placeholder="请输入" clearable @keyup.enter.native="handleQuery()" />
      </el-form-item>
      <el-form-item label="状态" label-width="60px" prop="state">
        <el-select
          v-model="paramform.state"
          clearable
          style="width: 150px"
          placeholder="请选择"
          @change="handleQuery()"
        >
          <el-option
            v-for="item in stateOptions"
            :key="item.value"
            :label="item.desc"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作类型" prop="type">
        <el-select
          v-model="paramform.type"
          clearable
          filterable
          style="width: 160px"
          placeholder="请选择"
          @change="handleQuery()"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.desc"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作人" label-width="90px" prop="createBy">
        <el-input v-model.trim="paramform.createBy" style="width: 160px" clearable placeholder="请输入" @keyup.enter.native="handleQuery()" />
      </el-form-item>
      <el-form-item label="时间范围" prop="times">
        <el-date-picker
          v-model="paramform.times"
          style="width: 360px"
          clearable
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery()"
        />
      </el-form-item>
      <el-form-item label="">
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery()"
        >搜索</el-button>
      </el-form-item>
      <el-form-item label="">
        <el-button
          icon="el-icon-refresh"
          size="small"
          @click="refresh()"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <div class="table-content card">
      <div class="table-header">
        <div class="header-button-lf">
          <!-- <el-button
            icon="el-icon-refresh"
            size="small"
            @click="refresh()"
          >重置</el-button> -->
          <!-- <i class="el-icon-warning ml10 icon_tips" />
          <span>任务仅保留三天，请及时处理</span> -->
        </div>
        <div class="header-button-ri">
          <el-input
            v-model.trim="paramform.keywords"
            placeholder="请输入文件名称"
            @keyup.enter.native="handleQuery()"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleQuery" /></el-input>
        </div>
      </div>
      <div class="table_body export-files-table-wrap">
        <el-table v-loading="tableLoading" :data="tableData">
          <el-table-column
            prop="id"
            align="left"
            min-width="160"
            label="任务编号"
          />
          <el-table-column
            v-if="roleState"
            prop="mediaTypeDesc"
            align="left"
            min-width="100"
            label="媒体类型"
          />
          <el-table-column
            label="任务名称"
            min-width="140"
            show-overflow-tooltip
            align="left"
          >
            <template #default="scope">
              <span v-if="scope.row.type">
                <span v-if="scope.row.createTime">{{ parseTime(scope.row.createTime,'{y}-{m}-{d} {h}:{i}') }}</span>
                <span v-if="scope.row.createTime">-</span>
                <span>{{ scope.row.type.desc }}</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            align="center"
            min-width="90"
            label="操作时间"
          >
            <template #default="scope">
              {{ parseTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="updateTime"
            align="center"
            min-width="90"
            label="更新时间"
          >
            <template #default="scope">
              {{ parseTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作类型" min-width="120" align="center">
            <template #default="scope">
              <span v-if="scope.row.type">
                <span>{{ scope.row.type.desc }}</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作人" min-width="120" align="center" prop="createBy" />
          <el-table-column align="center" label="状态" min-width="120">
            <template #default="scope">
              <div class="flex-center">
                <template v-if="scope.row.status">
                  <div
                    v-if="scope.row.status.value === 'SUCCESS'"
                    class="badge_box"
                  >
                    <div class="badge success" />
                    <span>成功</span>
                  </div>
                  <div
                    v-else-if="scope.row.status.value === 'FAIL'"
                    class="badge_box"
                  >
                    <div v-if="roleState">
                      <el-tooltip class="item" effect="dark" :content="scope.row.failMsg" placement="top">
                        <div class="badge_box">
                          <div class="badge fail" />
                          <span>失败</span>
                        </div>
                      </el-tooltip>

                    </div>
                    <div v-else class="badge_box">
                      <div class="badge fail" />
                      <span>失败</span>
                    </div>
                  </div>
                  <div v-else-if="scope.row.status.value === 'RUNNING'" class="badge_box ">
                    <i class="el-icon-loading exporting" />
                    <span>执行中</span>
                  </div>
                  <div v-else class="badge_box ">
                    <div class="badge queuing" />
                    <span>排队中</span>
                  </div>
                </template>
                <el-tooltip v-if="scope.row.executeParams" effect="light" placement="top">
                  <div slot="content">
                    <div style="width: 80vw; max-height: 200px; overflow-y: auto;">
                      {{ scope.row.executeParams }}
                    </div>
                    <ClipboardButton :value="scope.row.executeParams" />
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            align="center"
            min-width="120"
            label="操作"
          >
            <template #default="scope">
              <el-button
                v-if="scope.row.status.value === 'SUCCESS'&&scope.row.filePath"
                size="mini"
                type="text"
                icon="el-icon-download"
                @click="handleDownload(scope.row)"
              >下载</el-button>
              <el-button
                v-if="scope.row.status.value === 'FAIL'"
                size="mini"
                type="text"
                icon="el-icon-refresh-right"
                @click="handleRetry(scope.row)"
              >重试</el-button>
              <el-button
                v-if="!['RUNNING'].includes(scope.row.status.value)"
                size="mini"
                type="text"
                style="color: #ff4d4f;"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

    </div>
    <pagination
      :total="total"
      :page.sync="queryParam.pageNum"
      :limit.sync="queryParam.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<style scoped lang="scss">
.card {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
}
.form_header {
  padding-bottom: 0 !important;
}
.table-content {
  margin-top: 20px;
  .table-header {
    display: flex;
    justify-content: space-between;
    .header-button-lf {
      .icon_tips {
        color: rgb(64, 158, 255);
        margin-right: 4px;
      }
    }
  }
  .table_body {
    margin-top: 10px;
    .badge_box {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .badge {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
    }
    .success {
      background: rgb(5, 180, 75);
    }
    .fail {
      background: rgb(255, 77, 77);
    }
    .queuing{
      background: rgb(200, 200, 200);
    }
    .exporting {
      // background: rgb(48, 49, 51);
      margin-right: 2px;
    }
  }
  .flex-center {
    display: flex;
    align-items: center;
  }
}
.export-files-table-wrap {
  height: calc(100vh - 400px);
}
</style>
