<script setup>
import Task from './components/task.vue'
import { ref } from 'vue'

const fileVisible = ref(false)
const showDialog = () => {
  fileVisible.value = true
}
</script>

<template>
  <div>
    <svg-icon icon-class="task" @click="showDialog" />
    <el-dialog
      title="任务管理"
      :visible.sync="fileVisible"
      width="80%"
      top="2vh"
      append-to-body
    >
      <Task v-if="fileVisible" />
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
</style>
