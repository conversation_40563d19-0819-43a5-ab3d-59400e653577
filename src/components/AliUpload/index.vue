<template>
  <div class="ali-upload">
    <el-upload
      ref="uploadRef"
      :disabled="disabled"
      :multiple="multiple"
      :show-file-list="showFileList"
      :before-upload="handleBeforeUpload"
      :on-exceed="handleExceed"
      :limit="limit"
      :http-request="customUpload"
      :auto-upload="true"
      :on-change="handleChange"
      action="#"
      class="ali-upload__uploader"
    >
      <template #trigger>
        <slot name="trigger">
          <el-button :size="size" :type="type" :disabled="disabled">
            <slot>点击上传</slot>
          </el-button>
        </slot>
      </template>

      <template #tip>
        <slot name="tip">
          <div v-if="tip" class="el-upload__tip">
            {{ tip }}
          </div>
        </slot>
      </template>
    </el-upload>
  </div>
</template>

<script setup name="AliUpload">
import { ref } from 'vue'
import { Message } from 'element-ui'
import { getAliOssUploadSignature, saveUploadFiles } from '@/api/promotion/fileres'

const props = defineProps({
  /** 分类ID */
  categoryId: {
    type: String,
    default: '0'
  },
  /** 是否禁用 */
  disabled: {
    type: Boolean,
    default: false
  },
  /** 是否多选 */
  multiple: {
    type: Boolean,
    default: false
  },
  /** 最大上传数量 */
  limit: {
    type: Number,
    default: 1
  },
  /** 显示文件列表 */
  showFileList: {
    type: Boolean,
    default: false
  },
  /** 按钮大小 */
  size: {
    type: String,
    default: 'default'
  },
  /** 按钮类型 */
  type: {
    type: String,
    default: 'primary'
  },
  /** 文件大小限制(MB) */
  fileSize: {
    type: Number,
    default: 10
  },
  /** 允许的文件类型 */
  fileTypes: {
    type: Array,
    default: () => ['jpg', 'jpeg', 'png', 'gif']
  },
  /** 提示文字 */
  tip: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['success', 'error', 'change'])
const uploadRef = ref(null)

/**
 * 处理文件选择变化
 */
const handleChange = (file) => {
  emit('change', file)
}

/**
 * 上传前校验文件
 * @param {File} file - 待上传的文件
 * @returns {boolean} 是否通过校验
 */
const handleBeforeUpload = (file) => {
  // 校验文件名是否包含特殊字符
  const specialChars = /[\\/:*?"<>|]/
  if (specialChars.test(file.name)) {
    Message.error('文件名不能包含特殊字符: \\ / : * ? " < > |')
    return false
  }

  // 校验文件类型
  const fileName = file.name.toLowerCase()
  const extension = fileName.substring(fileName.lastIndexOf('.') + 1)
  if (!props.fileTypes?.includes(extension)) {
    Message.error(`文件格式不正确，请上传 ${props?.fileTypes?.join('/')} 格式文件`)
    return false
  }

  // 校验文件大小
  const isLtSize = file.size / 1024 / 1024 < props.fileSize
  if (!isLtSize) {
    Message.error(`文件大小不能超过 ${props.fileSize}MB`)
    return false
  }

  return true
}

/**
 * 处理超出上传数量限制
 */
const handleExceed = () => {
  Message.warning(`最多只能上传 ${props.limit} 个文件`)
}

/**
 * 自定义文件上传实现
 * @param {Object} options - 上传选项
 * @param {File} options.file - 待上传的文件
 */
const customUpload = async(options) => {
  const { file } = options

  Message.info('正在上传文件，请稍候...')

  try {
    // 1. 获取OSS上传凭证
    console.log('Getting OSS token for category:', props.categoryId)
    const res = await getAliOssUploadSignature({ categoryId: props.categoryId })
    if (res.code !== 200) {
      throw new Error(res.msg || '获取上传凭证失败')
    }
    const ossConfig = res.data

    // 2. 构建表单数据
    const formData = new FormData()
    const extension = file.name.substring(file.name.lastIndexOf('.'))
    const fileName = `${file.name.split('.')[0]}-${Math.random().toString(36).slice(-6)}${extension}`
    const filePath = `${ossConfig.dir}/${fileName}`

    formData.append('name', fileName)
    formData.append('key', filePath)
    formData.append('policy', ossConfig.policy)
    formData.append('OSSAccessKeyId', ossConfig.accessKeyId)
    formData.append('success_action_status', '200')
    formData.append('signature', ossConfig.signature)
    // file必须是最后一个字段
    formData.append('file', file)

    // 3. 直接上传到OSS
    const host = ossConfig.host.startsWith('http:') ? ossConfig.host.replace('http:', 'https:') : ossConfig.host
    const uploadRes = await fetch(host, {
      method: 'POST',
      body: formData
    }).catch((error) => {
      console.error('OSS upload error:', error)
      Message.error('上传失败: ' + error.message)
      throw new Error('上传失败')
    })

    if (!uploadRes.ok) {
      throw new Error('上传失败')
    }

    // 4. 构建文件信息
    const fileInfo = {
      fileCategory: props.categoryId,
      fileName: fileName,
      fileSuffix: extension.slice(1),
      newFileName: fileName,
      originalFileName: file.name,
      bucketName: ossConfig.bucketName,
      filePath: `${ossConfig.bucketDomain}/${filePath}`,
      fileSize: file.size,
      fileType: file.type,
      url: `${ossConfig.bucketDomain}/${filePath}`
    }

    // 5. 保存文件信息
    console.log('Saving file info:', fileInfo)
    const saveRes = await saveUploadFiles({
      uploadFiles: [fileInfo]
    })

    if (saveRes.code !== 200) {
      throw new Error(saveRes.msg || '保存文件信息失败')
    }
    console.log('File info saved successfully')

    // 6. 触发成功回调
    emit('success', fileInfo)
    Message.success('上传成功')
  } catch (error) {
    console.error('Upload error:', error)
    emit('error', error)
    Message.error(error.message || '上传失败')
  }
}

// 暴露方法给父组件
defineExpose({
  upload: () => {
    uploadRef.value?.submit()
  },
  clearFiles: () => {
    uploadRef.value?.clearFiles()
  }
})
</script>

<style lang="scss" scoped>
.ali-upload {
  &__uploader {
    display: inline-block;
  }
}
</style>
