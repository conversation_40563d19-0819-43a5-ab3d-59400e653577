<template>
  <el-dialog
    title="选择用户"
    :visible.sync="visible"
    top="5vh"
    width="1400px"
    append-to-body
    @close="handleClose"
  >
    <div class="user-selector-container">
      <div class="left-panel">
        <el-form ref="queryForm" class="search-form" size="small" :model="queryParams" :inline="true">
          <el-form-item prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="用户名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item prop="userId">
            <el-input
              v-model="queryParams.userId"
              placeholder="用户ID"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item prop="licenseActivate">
            <el-select v-model="queryParams.licenseActivate" placeholder="激活状态" clearable>
              <el-option
                v-for="dict in licenseActivateOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="available">
            <el-checkbox v-model="queryParams.available">仅显示有效接粉账户</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          ref="userTable"
          v-loading="loading"
          :data="userList"
          row-key="userId"
          @selection-change="handleSelectionChange"
          @select="handleSelect"
        >
          <el-table-column type="selection" :reserve-selection="true" width="55" align="center" />
          <el-table-column label="用户ID" align="center" prop="userId" width="160" />
          <el-table-column label="用户名称" align="center" prop="userName" />
          <el-table-column label="激活状态" align="center" prop="licenseActivate" width="80">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.licenseActivate === 0" type="info">未激活</el-tag>
              <el-tag v-else type="success">已激活</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="激活时间" align="center" prop="activeTime" width="160">
            <template slot-scope="scope">
              <span>{{ scope.row.activeTime || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="过期时间" align="center" prop="expireTime" width="160">
            <template slot-scope="scope">
              <span>{{ scope.row.expireTime || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>

      <div class="right-panel">
        <div class="selected-users-header">
          <span>已选用户 ({{ selectionList.length }})</span>
          <el-button type="text" @click="clearSelection">清空</el-button>
        </div>
        <div class="selected-users-list">
          <el-tag
            v-for="user in selectionList"
            :key="user.userId"
            closable
            @close="removeSelectedUser(user)"
          >
            {{ user.userName }}
          </el-tag>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listAuthCorpUsers } from '@/api/wx/authCorp'

export default {
  name: 'CustomerLinkUserSelector',
  props: {
    corpId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 是否显示弹出层
      visible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        userId: undefined,
        licenseActivate: undefined,
        available: false
      },
      // 激活状态选项
      licenseActivateOptions: [
        { value: 0, label: '未激活' },
        { value: 1, label: '已激活' }
      ],
      // 已选中的用户列表（用于展示）
      selection: [],
      // 默认选中的用户列表
      defaultSelection: []
    }
  },
  computed: {
    selectionList() {
      const list = [...this.defaultSelection, ...this.selection]
      const userMap = new Map()
      list.forEach(user => {
        userMap.set(user.userId, user)
      })
      return Array.from(userMap.values())
    }
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true
      this.queryParams.corpId = this.corpId
      listAuthCorpUsers(this.queryParams).then(response => {
        this.userList = response.rows
        this.total = response.total
        this.loading = false
        // 数据加载完成后，回显已选中的用户
        this.$nextTick(() => {
          this.userList.forEach(row => {
            if (this.defaultSelection.some(user => user.userId === row.userId)) {
              this.$refs.userTable.toggleRowSelection(row, true)
            }
          })
        })
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 打开弹窗
    open(selectedUsers = []) {
      this.visible = true
      this.selection = []
      this.defaultSelection = [...selectedUsers]
      this.getList()
    },
    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        licenseActivate: undefined
      }
      this.selection = []
      this.$refs.userTable.clearSelection()
      this.resetForm('queryForm')
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selection = [...selection]
    },
    handleSelect(selection, row) {
      const selected = selection.length && selection.indexOf(row) !== -1
      if (!selected) {
        this.defaultSelection = this.defaultSelection.filter(item => item.userId !== row.userId)
      }
    },
    // 移除选中的用户
    removeSelectedUser(user) {
      this.$refs.userTable.toggleRowSelection(user, false)
      this.selection = this.selection.filter(item => item.userId !== user.userId)
      this.defaultSelection = this.defaultSelection.filter(item => item.userId !== user.userId)
    },
    // 清空选择
    clearSelection() {
      this.$refs.userTable.clearSelection()
      this.defaultSelection = []
    },
    // 确认选择
    handleConfirm() {
      this.$emit('confirm', this.selectionList)
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.user-selector-container {
  display: flex;
  gap: 20px;
  height: 600px;

  .left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .el-table {
      flex: 1;
      overflow: auto;
    }
  }

  .right-panel {
    width: 402px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .selected-users-header {
      padding: 10px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .selected-users-list {
      flex: 1;
      overflow: auto;
      padding: 10px;

      .el-tag {
        margin: 5px;
      }
    }
  }
}
</style>
