<template>
  <div class="base-info" :class="{inline, 'info-item': type === 'info'}">
    <div v-if="name" class="table-row-name">
      <el-tooltip effect="dark" :content="name" :disabled="!isOverflow" placement="top">
        <div ref="name" class="overflow-text info-name" :style="{lineClamp: lineClamp,'-webkit-line-clamp': lineClamp}" :class="[nameClick?'name-click':'']" :title="name" @click="nameClick">
          {{ label }} {{ name }}
        </div>
      </el-tooltip>
      <ClipboardButton v-if="!noCopy" :value="name" />
    </div>
    <div v-if="id" class="table-row-id">
      {{ subLabel }} {{ id }}
      <ClipboardButton v-if="!noCopy" :value="id" />
    </div>
  </div>
</template>

<script>
import ClipboardButton from '@/components/ClipboardButton/index.vue'

export default {
  name: 'BaseInfoCell',
  components: {
    ClipboardButton
  },
  props: {
    name: {
      type: String,
      default: ''
    },
    id: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: String,
      default: 'cell'
    },
    noCopy: {
      type: Boolean,
      default: false
    },
    img: {
      type: String,
      default: ''
    },
    inline: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    subLabel: {
      type: String,
      default: 'ID:'
    },
    lineClamp: {
      type: [Number, String],
      default: 2
    },
    overflow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isOverflow: false
    }
  },
  mounted() {
    this.isOverflow = this.overflow && this.$refs.name.offsetHeight < this.$refs.name.scrollHeight
  },
  methods: {
    nameClick() {
      this.$emit('nameClick')
    }
  }
}
</script>

<style lang="scss">
.first-cell {
  .cell {
    width: 100%;
    display: flex;
    align-items: flex-start;
  }
  .el-table__placeholder {
    flex-shrink:0
  }
}
</style>

<style lang="scss" scoped>
.base-info {
  display: inline-block;
  flex:1;
  .table-row-name {
    flex:1;
    line-height: 20px;
    display: flex;
  }
  .info-name {
    cursor: pointer;
  }
  .table-row-id {
    color: #b4b4b4;
    font-size: 12px;
    line-height: 16px;
  }
}
.name-click {
  cursor: pointer;
  &:hover {
    color: #409eff;
    text-decoration: underline;
  }
}

.inline {
  display: inline-block;
  max-width: 310px
}
.info-item {
  width:100%;margin-bottom: 10px;padding: 10px;border-bottom:1px solid silver
}
::v-deep(.el-table__expand-icon + .base-info ) {
  max-width: calc(100% - 30px);

}
</style>
