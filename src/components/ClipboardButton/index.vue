<template>
  <a v-if="value" v-clipboard:copy="value" v-clipboard:success="clipboardSuccess" class="clipboard-button">
    <i class="el-icon-document-copy" />
    <slot />
  </a>
</template>

<script>
export default {
  name: 'ClipboardButton',
  props: {
    value: {
      type: [String, Number],
      default: ''
    }
  },
  methods: {
    // 复制成功
    clipboardSuccess() {
      this.$modal.msgSuccess('复制成功')
    }
  }
}
</script>

<style scoped>
 .clipboard-button {
  cursor: pointer;color: #1890ff
 }
</style>
