import { createVersionPolling } from 'version-polling'

export default {
  install(Vue) {
    createVersionPolling({
      appETagKey: '__APP_ETAG__',
      pollingInterval: 60 * 1000, // 单位为毫秒
      silent: process.env.NODE_ENV === 'development', // 开发环境下不检测
      onUpdate: (self) => {
        // 当检测到有新版本时，执行的回调函数，可以在这里提示用户刷新页面
        Vue.prototype.$modal.confirm('页面有更新，点击确定刷新页面！').then(() => {
          self.onRefresh()
        }).catch(() => {
        })
      }
    })
  }
}
