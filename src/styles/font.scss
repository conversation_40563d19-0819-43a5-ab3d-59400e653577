// 隐藏文字
.overflow-text {
  overflow: hidden;
  text-overflow: ellipsis; //文本溢出显示省略号
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
}

.color-primary {
  color: var(--ios-blue);
}

.color-success {
  color: var(--ios-green);
}

.color-warning {
  color: var(--ios-orange);
}

.color-danger {
  color: var(--ios-red);
}

.color-info {
  color: var(--ios-gray);
}

.color-placeholder {
  color: var(--ios-gray-light);
}

.text-bold {
  font-weight: bold;
}
