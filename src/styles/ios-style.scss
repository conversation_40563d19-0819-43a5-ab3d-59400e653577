:root {
  // ==================== iOS 系统颜色 ====================

  // 主色调 - Blue
  --ios-blue: #007AFF;
  --ios-blue-light: #5AC8FA;
  --ios-blue-dark: #0051D5;

  // 次要颜色
  --ios-green: #34C759;
  --ios-green-light: #30D158;
  --ios-green-dark: #248A3D;

  --ios-indigo: #5856D6;
  --ios-indigo-light: #5E5CE6;
  --ios-indigo-dark: #3634A3;

  --ios-orange: #FF9500;
  --ios-orange-light: #FFB340;
  --ios-orange-dark: #C7700F;

  --ios-pink: #FF2D92;
  --ios-pink-light: #FF375F;
  --ios-pink-dark: #D70015;

  --ios-purple: #AF52DE;
  --ios-purple-light: #BF5AF2;
  --ios-purple-dark: #8944AB;

  --ios-red: #FF3B30;
  --ios-red-light: #FF453A;
  --ios-red-dark: #D70015;

  --ios-teal: #5AC8FA;
  --ios-teal-light: #64D2FF;
  --ios-teal-dark: #0071A4;

  --ios-yellow: #FFCC00;
  --ios-yellow-light: #FFD60A;
  --ios-yellow-dark: #B25000;

  // ==================== 中性色 / 灰度 ====================

  // 主要灰色
  --ios-gray: #8E8E93;
  --ios-gray-light: #AEAEB2;
  --ios-gray-dark: #636366;

  --ios-gray-2: #6D6D70;
  --ios-gray-3: #48484A;
  --ios-gray-4: #3A3A3C;
  --ios-gray-5: #2C2C2E;
  --ios-gray-6: #1C1C1E;

  // 浅色系统
  --ios-white: #FFFFFF;
  --ios-light-gray-05: #F8F9FA;
  --ios-light-gray: #F2F2F7;
  --ios-light-gray-2: #E5E5EA;
  --ios-light-gray-3: #D1D1D6;
  --ios-light-gray-4: #C7C7CC;
  --ios-light-gray-5: #AEAEB2;
  --ios-light-gray-6: #8E8E93;

  // 深色系统
  --ios-black: #000000;
  --ios-dark-gray: #1C1C1E;
  --ios-dark-gray-2: #2C2C2E;
  --ios-dark-gray-3: #3A3A3C;
  --ios-dark-gray-4: #48484A;
  --ios-dark-gray-5: #6D6D70;
  --ios-dark-gray-6: #8E8E93;

  // ==================== 语义化颜色 ====================

  // 背景色
  --ios-background: var(--ios-light-gray);
  --ios-background-secondary: var(--ios-white);
  --ios-background-tertiary: var(--ios-light-gray-2);

  // 文本颜色
  --ios-text-primary: var(--ios-black);
  --ios-text-secondary: var(--ios-gray-2);
  --ios-text-tertiary: var(--ios-gray);
  --ios-text-quaternary: var(--ios-gray-light);

  // 分隔线和边框
  --ios-separator-color: rgba(60, 60, 67, 0.29);
  --ios-separator-color-opaque: #C6C6C8;
  --ios-border-color: #D1D1D6;
  --ios-border-color-light: #E5E5EA;

  // 标签背景色
  --ios-label-background: rgba(120, 120, 128, 0.16);
  --ios-label-background-secondary: rgba(120, 120, 128, 0.12);
  --ios-label-background-tertiary: rgba(118, 118, 128, 0.24);

  // 填充色
  --ios-fill: rgba(120, 120, 128, 0.20);
  --ios-fill-secondary: rgba(120, 120, 128, 0.16);
  --ios-fill-tertiary: rgba(118, 118, 128, 0.12);
  --ios-fill-quaternary: rgba(116, 116, 128, 0.08);

  // 状态颜色
  --ios-success: var(--ios-green);
  --ios-warning: var(--ios-orange);
  --ios-error: var(--ios-red);
  --ios-info: var(--ios-blue);

  // ==================== 组件相关颜色 ====================

  // 链接颜色
  --ios-link: var(--ios-blue);
  --ios-link-hover: var(--ios-blue-dark);

  // 选中状态
  --ios-selection: var(--ios-blue);
  --ios-selection-background: rgba(0, 122, 255, 0.1);

  // 阴影
  --ios-shadow-light: rgba(0, 0, 0, 0.08);
  --ios-shadow-medium: rgba(0, 0, 0, 0.12);
  --ios-shadow-dark: rgba(0, 0, 0, 0.16);

  // ==================== 尺寸和圆角 ====================

  // 圆角
  --ios-border-radius: 12px;
  --ios-border-radius-small: 8px;
  --ios-border-radius-large: 16px;
  --ios-border-radius-xlarge: 20px;

  // 字体
  --ios-font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
}

body {
  font-family: var(--ios-font-family);
  background-color: var(--ios-background);
}

// 统一修改全局圆角
.el-button,
.el-input,
.el-card,
.el-dialog,
.el-message-box {
  border-radius: var(--ios-border-radius);
}

.el-input__inner {
  border-radius: 8px;
}

// 卡片化样式
.ios-card {
  background-color: #ffffff;
  border-radius: var(--ios-border-radius);
  box-shadow: 0 1px 2px 0 var(--ios-shadow-light);
  padding: 16px;
  transition: all 0.3s ease;
}

// 侧边栏菜单选中样式
.el-menu-item.is-active {
  background-color: var(--ios-blue) !important;
  color: white !important;
  border-radius: 8px;
  margin: 4px 8px;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2); // 轻微阴影
}

.el-menu-item {
  transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

// 侧边栏容器内的菜单项样式
.sidebar-container {

  .el-submenu__title:hover,
  .el-menu-item:hover {
    background-color: rgba(0, 0, 0, 0.05) !important; // 使用!important确保覆盖
    border-radius: 8px !important;
    margin: 0 8px !important;
  }

  .el-menu-item.is-active {
    background-color: var(--ios-blue) !important;
    color: white !important;
    border-radius: 8px !important;
    margin: 4px 8px !important;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2) !important;

    // 激活状态的菜单项在hover时保持蓝色背景，但稍微深一点
    &:hover {
      background-color: var(--ios-blue-dark) !important;
      color: white !important;
      box-shadow: 0 3px 12px rgba(0, 122, 255, 0.3) !important;
      transform: translateY(-1px);
    }
  }

  // 确保子菜单项也有相同样式
  .nest-menu .el-submenu>.el-submenu__title:hover,
  .nest-menu .el-menu-item:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-radius: 8px !important;
  }

  // 确保激活的子菜单项也有相同的hover效果
  .nest-menu .el-menu-item.is-active:hover {
    background-color: var(--ios-blue-dark) !important;
    color: white !important;
    box-shadow: 0 3px 12px rgba(0, 122, 255, 0.3) !important;
    transform: translateY(-1px);
  }
}

// 全局菜单项hover样式（非侧边栏）
.el-submenu__title:hover,
.el-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

// Element UI 弹出菜单的毛玻璃效果
.el-menu--popup {
  background-color: rgba(255, 255, 255, 0.95);
  // backdrop-filter: blur(20px);
  border-radius: var(--ios-border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  // will-change: backdrop-filter
}

// 侧边栏菜单项的通用样式
.el-menu-item,
.el-submenu__title {
  height: 44px; // 统一高度
  line-height: 44px; // 统一行高
  padding-left: 20px; // 调整内边距
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); // 添加分隔线
}

.el-menu-item:last-child,
.el-submenu:last-child>.el-submenu__title {
  border-bottom: none; // 最后一个菜单项没有底部边框
}

.el-menu--collapse .el-menu-item,
.el-menu--collapse .el-submenu__title {
  padding-left: 20px; // 折叠时也调整内边距
}

.el-menu--collapsed .el-menu-item,
.el-menu--collapsed .el-submenu__title {
  text-align: center; // 文本居中

  .svg-icon {
    margin-right: 0; // 移除图标右侧间距
  }

  span {
    height: 0; // 隐藏文本
    width: 0; // 隐藏文本
    overflow: hidden; // 隐藏文本
    visibility: hidden; // 隐藏文本
    display: inline-block; // 隐藏文本
  }
}

// 美化滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

// 表单项
.el-form-item {

  .el-form-item__label {
    font-weight: 600;
    color: var(--ios-dark-gray);
  }

  .el-form-item__content {
    // line-height: 1.4 ;
  }
}

// 输入框
.el-input {
  .el-input__inner {
    border-radius: var(--ios-border-radius);
    font-size: 15px;
    color: var(--ios-dark-gray);
    transition: all 0.2s ease;

    &:focus {
      background: #ffffff;
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.3);
    }

  }
}

// 文本域
.el-textarea {
  .el-textarea__inner {
    border-radius: var(--ios-border-radius);
    font-size: 15px;
    padding: 12px 16px;
    color: var(--ios-dark-gray);
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 80px;

    &:focus {
      background: #ffffff;
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.3);
    }

  }

  // 文本域 Size 适配
  &.el-textarea--medium {
    .el-textarea__inner {
      font-size: 14px;
      padding: 10px 14px;
      min-height: 72px;
      border-radius: 10px;
    }
  }

  &.el-textarea--small {
    .el-textarea__inner {
      font-size: 13px;
      padding: 8px 12px;
      min-height: 64px;
      border-radius: 8px;
    }
  }

  &.el-textarea--mini {
    .el-textarea__inner {
      font-size: 12px;
      padding: 6px 10px;
      min-height: 56px;
      border-radius: 6px;
    }
  }
}

// 单选按钮组
.el-radio-group {

  .el-radio {
    margin-right: 24px;
    margin-bottom: 8px;
    white-space: nowrap;
    font-size: 15px;

    .el-radio__label {
      color: var(--ios-dark-gray);
      font-weight: 500;
      padding-left: 8px;
    }

    .el-radio__input.is-checked+.el-radio__label {
      color: var(--ios-blue);
    }

    .el-radio__inner {
      width: 18px;
      height: 18px;
      border: 2px solid var(--ios-border-color);
      border-radius: 50%;

      &:hover {
        border-color: var(--ios-blue);
      }
    }

    .el-radio__input.is-checked .el-radio__inner {
      background: var(--ios-blue);
      border-color: var(--ios-blue);
    }
  }

  // radio-button 样式
  .el-radio-button {
    margin: 0;
    border-radius: 0;

    &:first-child .el-radio-button__inner {
      border-radius: var(--ios-border-radius) 0 0 var(--ios-border-radius);
      border-left: 1px solid #dcdfe6;
    }

    &:last-child .el-radio-button__inner {
      border-radius: 0 var(--ios-border-radius) var(--ios-border-radius) 0;
    }

    &:first-child:last-child .el-radio-button__inner {
      border-radius: var(--ios-border-radius);
    }

    .el-radio-button__inner {
      border: 1px solid #dcdfe6;
      border-left: none;
      background: #ffffff;
      color: var(--ios-dark-gray);
      font-size: 14px;
      font-weight: 500;
      padding: 8px 16px;
      transition: all 0.2s ease;
      line-height: 1.4;

      &:hover {
        background: #f8f9fa;
        border-color: var(--ios-blue);
        color: var(--ios-blue);
      }
    }

    &.is-active .el-radio-button__inner {
      background: var(--ios-blue);
      border-color: var(--ios-blue);
      color: #ffffff;
      box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3);
    }

    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
      background: var(--ios-blue);
      border-color: var(--ios-blue);
      color: #ffffff;
      box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3);
    }
  }

  // 单选按钮组大小 .el-radio-button--small .el-radio-button__inner
  .el-radio-button--small {
    .el-radio-button__inner {
      padding: 9px 15px;
      font-size: 12px;
    }
  }

  .el-radio-button--mini {
    .el-radio-button__inner {
      padding: 7px 13px;
      font-size: 12px;
    }
  }

}

// 开关
.el-switch {
  .el-switch__core {
    border-radius: 16px;
    background: #e5e5ea;
    border: none;
    width: 44px;
    height: 23px;

    &:after {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #ffffff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
  }

  &.is-checked .el-switch__core {
    background: #34c759;

    &:after {
      margin-left: -22px;
    }
  }

  // 开关 Size 适配
  &.el-switch--small {
    .el-switch__core {
      width: 36px;
      height: 20px;
      border-radius: 12px;

      &:after {
        width: 16px;
        height: 16px;
      }
    }

    &.is-checked .el-switch__core:after {
      margin-left: -18px;
    }
  }

  &.el-switch--mini {
    .el-switch__core {
      width: 30px;
      height: 16px;
      border-radius: 10px;

      &:after {
        width: 12px;
        height: 12px;
      }
    }

    &.is-checked .el-switch__core:after {
      margin-left: -16px;
    }
  }
}

// 按钮
.el-button {
  border-radius: var(--ios-border-radius);
  font-size: 15px;
  font-weight: 600;
  // padding: 12px 24px;
  border: none;
  transition: all 0.2s ease;

  &.el-button--primary {
    background: var(--ios-blue);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);

    &:hover {
      background: #0056cc;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }

  &.el-button--default {
    background: #f8f9fa;
    color: var(--ios-dark-gray);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      background: #e5e5ea;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // 按钮 Size 适配
  &.el-button--medium {
    font-size: 14px;
    // padding: 10px 20px;
    // border-radius: 10px;
  }

  &.el-button--small {
    font-size: 13px;
    // padding: 8px 16px;
    // border-radius: 8px;
  }

  &.el-button--mini {
    font-size: 12px;
    // padding: 6px 12px;
    // border-radius: 6px;
  }
}

// ==================== 表格 ====================
.el-table {

  &.el-table--border {
    overflow: hidden;
    border-radius: var(--ios-border-radius);
    box-shadow: 0 2px 12px var(--ios-shadow-light);
    border: 1px solid var(--ios-border-color-light);
  }

  .el-table__header-wrapper {
    background: var(--ios-light-gray);

    th {
      background: var(--ios-light-gray);
      color: var(--ios-text-secondary);
      font-weight: 600;
      font-size: 14px;
      border-bottom: 1px solid var(--ios-separator-color);
    }
  }

  .el-table__body-wrapper {
    tr {
      transition: background-color 0.2s ease;

      &:hover {
        background-color: var(--ios-fill-quaternary);
      }
    }

    td {
      border-bottom: 1px solid var(--ios-separator-color);
      font-size: 14px;
      color: var(--ios-text-primary);
    }
  }

  .el-table__empty-block {
    background: var(--ios-background-secondary);
  }

  // 表格 Size 适配
  &.el-table--medium {
    .el-table__header-wrapper th {
      padding: 10px 0;
      font-size: 13px;
    }

    .el-table__body-wrapper td {
      padding: 10px 0;
      font-size: 13px;
    }
  }

  &.el-table--small {
    .el-table__header-wrapper th {
      padding: 8px 0;
      font-size: 12px;
    }

    .el-table__body-wrapper td {
      padding: 8px 0;
      font-size: 12px;
    }
  }

  &.el-table--mini {
    .el-table__header-wrapper th {
      padding: 6px 0;
      font-size: 11px;
    }

    .el-table__body-wrapper td {
      padding: 6px 0;
      font-size: 11px;
    }
  }
}

// ==================== 分页器 ====================
.el-pagination {
  .el-pager li {
    border-radius: var(--ios-border-radius-small) !important;
    margin: 0 2px;
    font-weight: 500;
    color: var(--ios-text-secondary);
    background: var(--ios-background-secondary);
    border: 1px solid var(--ios-border-color-light);
    transition: all 0.2s ease;

    &:hover {
      background: var(--ios-fill-quaternary);
      border-color: var(--ios-blue);
    }

    &.active {
      background: var(--ios-blue);
      color: white;
      border-color: var(--ios-blue);
      box-shadow: 0 2px 6px rgba(0, 122, 255, 0.3);
    }
  }

  .el-select .el-input .el-input__inner {
    border-radius: var(--ios-border-radius-small);
  }

  .btn-prev,
  .btn-next {
    border-radius: var(--ios-border-radius-small) !important;
    background: var(--ios-background-secondary);
    border: 1px solid var(--ios-border-color-light);
    color: var(--ios-text-secondary);

    &:hover {
      background: var(--ios-fill-quaternary);
    }
  }

  .el-pagination__jump {
    color: var(--ios-text-secondary);
  }
}

// ==================== 标签 ====================
.el-tag {
  border-radius: var(--ios-border-radius-small);
  border: none !important;
  font-weight: 500;
  font-size: 13px;

  &.el-tag--info {
    background: var(--ios-fill-secondary);
    color: var(--ios-text-secondary);
  }

  &.el-tag--success {
    background: rgba(52, 199, 89, 0.15);
    color: var(--ios-green-dark);
  }

  &.el-tag--warning {
    background: rgba(255, 149, 0, 0.15);
    color: var(--ios-orange-dark);
  }

  &.el-tag--danger {
    background: rgba(255, 59, 48, 0.15);
    color: var(--ios-red-dark);
  }
}

// ==================== 选择器 ====================
.el-select {
  .el-input__inner {
    cursor: pointer;
  }

  .el-select__caret {
    color: var(--ios-gray);
    transition: transform 0.3s ease;
  }

  &.is-focus .el-select__caret {
    transform: rotateZ(180deg);
  }

  // 选择器 Size 适配
  &.el-select--medium {
    .el-input__inner {
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border-radius: 10px;
    }
  }

  &.el-select--small {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      border-radius: 8px;
    }
  }

  &.el-select--mini {
    .el-input__inner {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      border-radius: 6px;
    }
  }
}

.el-select-dropdown {
  border-radius: var(--ios-border-radius);
  border: 1px solid var(--ios-border-color-light);
  box-shadow: 0 8px 24px var(--ios-shadow-medium);

  .el-select-dropdown__item {
    font-size: 14px;
    color: var(--ios-text-primary);
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--ios-fill-quaternary);
    }

    &.is-disabled {
      color: var(--ios-text-quaternary);
      cursor: not-allowed;
    }

    &.selected {
      background: var(--ios-selection-background);
      color: var(--ios-blue);
      font-weight: 500;
    }
  }
}

// ==================== 复选框 ====================
.el-checkbox {
  font-size: 14px;
  color: var(--ios-text-primary);

  .el-checkbox__input {
    .el-checkbox__inner {
      border-radius: 4px;
      border: 2px solid var(--ios-border-color);
      width: 16px;
      height: 16px;

      &:hover {
        border-color: var(--ios-blue);
      }
    }

    &.is-checked .el-checkbox__inner {
      background: var(--ios-blue);
      border-color: var(--ios-blue);
    }
  }

  .el-checkbox__label {
    color: var(--ios-text-primary);
    font-weight: 500;
    padding-left: 8px;
  }

  // 复选框 Size 适配
  &.el-checkbox--medium {
    font-size: 13px;

    .el-checkbox__input .el-checkbox__inner {
      width: 15px;
      height: 15px;
    }

    .el-checkbox__label {
      font-size: 13px;
    }
  }

  &.el-checkbox--small {
    font-size: 12px;

    .el-checkbox__input .el-checkbox__inner {
      width: 14px;
      height: 14px;
    }

    .el-checkbox__label {
      font-size: 12px;
    }
  }

  &.el-checkbox--mini {
    font-size: 11px;

    .el-checkbox__input .el-checkbox__inner {
      width: 12px;
      height: 12px;
    }

    .el-checkbox__label {
      font-size: 11px;
    }
  }
}

// ==================== 复选框组 ====================
.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px 16px; // 垂直间距12px，水平间距16px

  .el-checkbox {
    margin-right: 0; // 移除默认的右边距，使用gap控制间距
    margin-bottom: 0; // 移除默认的底边距
    white-space: nowrap;

    // 复选框组内的复选框样式优化
    .el-checkbox__input {
      .el-checkbox__inner {
        transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);

        &:hover {
          border-color: var(--ios-blue);
          transform: scale(1.05);
        }
      }

      &.is-checked .el-checkbox__inner {
        background: var(--ios-blue);
        border-color: var(--ios-blue);
        box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3);

        &:hover {
          background: var(--ios-blue-dark);
          border-color: var(--ios-blue-dark);
          box-shadow: 0 2px 6px rgba(0, 122, 255, 0.4);
        }
      }

      &.is-indeterminate .el-checkbox__inner {
        background: var(--ios-blue);
        border-color: var(--ios-blue);

        &:before {
          background: white;
          border-radius: 1px;
        }
      }
    }

    .el-checkbox__label {
      transition: color 0.2s ease;

      &:hover {
        color: var(--ios-blue);
      }
    }

    &.is-checked .el-checkbox__label {
      color: var(--ios-blue);
      font-weight: 600;
    }
  }

  // 垂直布局
  &.is-vertical {
    flex-direction: column;
    gap: 8px 0;

    .el-checkbox {
      width: 100%;
    }
  }

  // 复选框组 Size 适配
  &.el-checkbox-group--medium {
    gap: 10px 14px;

    .el-checkbox {
      font-size: 13px;

      .el-checkbox__input .el-checkbox__inner {
        width: 15px;
        height: 15px;
      }

      .el-checkbox__label {
        font-size: 13px;
      }
    }
  }

  &.el-checkbox-group--small {
    gap: 8px 12px;

    .el-checkbox {
      font-size: 12px;

      .el-checkbox__input .el-checkbox__inner {
        width: 14px;
        height: 14px;
      }

      .el-checkbox__label {
        font-size: 12px;
      }
    }
  }

  &.el-checkbox-group--mini {
    gap: 6px 10px;

    .el-checkbox {
      font-size: 11px;

      .el-checkbox__input .el-checkbox__inner {
        width: 12px;
        height: 12px;
      }

      .el-checkbox__label {
        font-size: 11px;
      }
    }
  }

  // 禁用状态
  &.is-disabled {
    .el-checkbox {
      .el-checkbox__input .el-checkbox__inner {
        background: var(--ios-fill-quaternary);
        border-color: var(--ios-border-color-light);
        cursor: not-allowed;

        &:hover {
          border-color: var(--ios-border-color-light);
          transform: none;
        }
      }

      .el-checkbox__label {
        color: var(--ios-text-quaternary);
        cursor: not-allowed;

        &:hover {
          color: var(--ios-text-quaternary);
        }
      }

      &.is-checked {
        .el-checkbox__input .el-checkbox__inner {
          background: var(--ios-gray-light);
          border-color: var(--ios-gray-light);

          &:hover {
            background: var(--ios-gray-light);
            border-color: var(--ios-gray-light);
            box-shadow: none;
          }
        }

        .el-checkbox__label {
          color: var(--ios-text-quaternary);
          font-weight: 500;

          &:hover {
            color: var(--ios-text-quaternary);
          }
        }
      }
    }
  }
}

// ==================== 复选框按钮 ====================
.el-checkbox-button {
  margin: 0;
  border-radius: 0;

  &:first-child .el-checkbox-button__inner {
    border-radius: var(--ios-border-radius) 0 0 var(--ios-border-radius);
    border-left: 1px solid var(--ios-border-color);
  }

  &:last-child .el-checkbox-button__inner {
    border-radius: 0 var(--ios-border-radius) var(--ios-border-radius) 0;
  }

  &:first-child:last-child .el-checkbox-button__inner {
    border-radius: var(--ios-border-radius);
  }

  .el-checkbox-button__inner {
    border: 1px solid var(--ios-border-color);
    border-left: 1px solid var(--ios-border-color);
    background: var(--ios-background-secondary);
    color: var(--ios-text-primary);
    font-size: 14px;
    font-weight: 500;
    padding: 10px 16px;
    transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
    line-height: 1.4;
    position: relative;

    &:hover {
      background: var(--ios-fill-quaternary);
      border-color: var(--ios-blue);
      color: var(--ios-blue);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);
      z-index: 1; // 确保悬停时在上层显示
    }
  }

  &.is-checked .el-checkbox-button__inner {
    background: var(--ios-blue);
    border-color: var(--ios-blue);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    font-weight: 600;

    &:hover {
      background: var(--ios-blue-dark);
      border-color: var(--ios-blue-dark);
      box-shadow: 0 3px 12px rgba(0, 122, 255, 0.4);
      transform: translateY(-1px);
    }
  }

  .el-checkbox-button__orig-checkbox:checked+.el-checkbox-button__inner {
    background: var(--ios-blue);
    border-color: var(--ios-blue);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    font-weight: 600;
  }

  // 复选框按钮 Size 适配
  &.el-checkbox-button--medium {
    .el-checkbox-button__inner {
      padding: 8px 14px;
      font-size: 13px;
      border-radius: 10px;
    }

    &:first-child .el-checkbox-button__inner {
      border-radius: 10px 0 0 10px;
    }

    &:last-child .el-checkbox-button__inner {
      border-radius: 0 10px 10px 0;
    }

    &:first-child:last-child .el-checkbox-button__inner {
      border-radius: 10px;
    }
  }

  &.el-checkbox-button--small {
    .el-checkbox-button__inner {
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 8px;
    }

    &:first-child .el-checkbox-button__inner {
      border-radius: 8px 0 0 8px;
    }

    &:last-child .el-checkbox-button__inner {
      border-radius: 0 8px 8px 0;
    }

    &:first-child:last-child .el-checkbox-button__inner {
      border-radius: 8px;
    }
  }

  &.el-checkbox-button--mini {
    .el-checkbox-button__inner {
      padding: 4px 10px;
      font-size: 11px;
      border-radius: 6px;
    }

    &:first-child .el-checkbox-button__inner {
      border-radius: 6px 0 0 6px;
    }

    &:last-child .el-checkbox-button__inner {
      border-radius: 0 6px 6px 0;
    }

    &:first-child:last-child .el-checkbox-button__inner {
      border-radius: 6px;
    }
  }

  // 禁用状态
  &.is-disabled {
    .el-checkbox-button__inner {
      background: var(--ios-fill-quaternary);
      border-color: var(--ios-border-color-light);
      color: var(--ios-text-quaternary);
      cursor: not-allowed;

      &:hover {
        background: var(--ios-fill-quaternary);
        border-color: var(--ios-border-color-light);
        color: var(--ios-text-quaternary);
        transform: none;
        box-shadow: none;
      }
    }

    &.is-checked .el-checkbox-button__inner {
      background: var(--ios-gray-light);
      border-color: var(--ios-gray-light);
      color: white;

      &:hover {
        background: var(--ios-gray-light);
        border-color: var(--ios-gray-light);
        color: white;
        transform: none;
        box-shadow: none;
      }
    }
  }

  // 焦点状态
  &.is-focus .el-checkbox-button__inner {
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.3);
  }
}

// 复选框按钮组
.el-checkbox-group {

  // 当包含复选框按钮时的样式
  &:has(.el-checkbox-button) {
    gap: 0; // 复选框按钮组不需要间距

    .el-checkbox-button {
      &:not(:first-child) .el-checkbox-button__inner {
        margin-left: -1px; // 重叠边框避免双边框
        border-left: 1px solid var(--ios-border-color); // 确保有分割线
      }

      // 选中状态时的边框处理
      &.is-checked .el-checkbox-button__inner {
        border-color: var(--ios-blue);
        z-index: 2; // 选中状态在最上层
      }

      // 相邻选中状态的边框处理
      &.is-checked+.el-checkbox-button.is-checked .el-checkbox-button__inner {
        border-left-color: var(--ios-light-gray-2); // 两个选中按钮之间的分割线
      }
    }
  }

  // 混合模式：既有普通复选框又有复选框按钮
  &:has(.el-checkbox):has(.el-checkbox-button) {
    gap: 12px 16px; // 恢复间距用于普通复选框

    .el-checkbox-button {
      margin-right: 0;
    }
  }
}

// ==================== 标签页 ====================
.el-tabs {
  .el-tabs__header {
    border-bottom: 1px solid var(--ios-separator-color);

    .el-tabs__nav {
      .el-tabs__item {
        font-size: 15px;
        font-weight: 500;
        color: var(--ios-text-secondary);
        padding: 0 16px;
        height: 44px;
        line-height: 44px;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          color: var(--ios-blue);
        }

        &.is-active {
          color: var(--ios-blue);
          border-bottom-color: var(--ios-blue);
          font-weight: 600;
        }
      }
    }
  }

  .el-tabs__content {
    // padding: 0;
  }
}

// ==================== 步骤条 ====================
.el-steps {
  .el-step {
    .el-step__head {
      .el-step__icon {
        border-radius: 50%;
        border: 2px solid var(--ios-border-color);
        background: var(--ios-background-secondary);
        color: var(--ios-text-secondary);
        font-weight: 600;
      }

      &.is-process .el-step__icon {
        background: var(--ios-blue);
        border-color: var(--ios-blue);
        color: white;
      }

      &.is-finish .el-step__icon {
        background: var(--ios-green);
        border-color: var(--ios-green);
        color: white;
      }
    }

    .el-step__title {
      font-weight: 500;
      color: var(--ios-text-primary);

      &.is-process {
        color: var(--ios-blue);
        font-weight: 600;
      }

      &.is-finish {
        color: var(--ios-green);
      }
    }

    .el-step__description {
      color: var(--ios-text-secondary);
      font-size: 13px;
    }
  }

  // 步骤条 Size 适配
  &.el-steps--small {
    .el-step {
      .el-step__head {
        .el-step__icon {
          width: 24px;
          height: 24px;
          font-size: 12px;
        }
      }

      .el-step__title {
        font-size: 13px;
      }

      .el-step__description {
        font-size: 11px;
      }
    }
  }

  &.el-steps--mini {
    .el-step {
      .el-step__head {
        .el-step__icon {
          width: 20px;
          height: 20px;
          font-size: 10px;
        }
      }

      .el-step__title {
        font-size: 12px;
      }

      .el-step__description {
        font-size: 10px;
      }
    }
  }
}

// ==================== 进度条 ====================
.el-progress {
  .el-progress-bar__outer {
    border-radius: var(--ios-border-radius-small);
    background: var(--ios-fill-quaternary);

    .el-progress-bar__inner {
      border-radius: var(--ios-border-radius-small);
      background: linear-gradient(90deg, var(--ios-blue) 0%, var(--ios-blue-light) 100%);
      transition: width 0.3s ease;
    }
  }

  .el-progress__text {
    color: var(--ios-text-secondary);
    font-weight: 500;
    font-size: 13px;
  }

  &.el-progress--success .el-progress-bar__inner {
    background: linear-gradient(90deg, var(--ios-green) 0%, var(--ios-green-light) 100%);
  }

  &.el-progress--exception .el-progress-bar__inner {
    background: linear-gradient(90deg, var(--ios-red) 0%, var(--ios-red-light) 100%);
  }
}

// ==================== 弹出框 ====================
.el-popover {
  border-radius: var(--ios-border-radius);
  border: 1px solid var(--ios-border-color-light);
  box-shadow: 0 8px 24px var(--ios-shadow-medium);
  background: var(--ios-background-secondary);
  color: var(--ios-text-primary);

  .popper__arrow {
    border-bottom-color: var(--ios-background-secondary);
  }
}

// ==================== 消息提示 ====================
.el-message {
  border-radius: var(--ios-border-radius);
  border: none;
  box-shadow: 0 8px 24px var(--ios-shadow-medium);
  backdrop-filter: blur(20px);
  font-weight: 500;

  .el-message__content,
  .el-message__icon {
    color: white !important;
  }

  &.el-message--success {
    background: rgba(52, 199, 89, 0.9);
  }

  &.el-message--warning {
    background: rgba(255, 149, 0, 0.9);
  }

  &.el-message--error {
    background: rgba(255, 59, 48, 0.9);
  }

  &.el-message--info {
    background: rgba(0, 122, 255, 0.9);
  }
}

// ==================== 通知 ====================
.el-notification {
  border-radius: var(--ios-border-radius);
  border: 1px solid var(--ios-border-color-light);
  box-shadow: 0 12px 32px var(--ios-shadow-medium);
  background: var(--ios-background-secondary);
  backdrop-filter: blur(20px);

  .el-notification__title {
    color: var(--ios-text-primary);
    font-weight: 600;
    font-size: 16px;
  }

  .el-notification__content {
    color: var(--ios-text-secondary);
    font-size: 14px;
  }
}

// ==================== 抽屉 ====================
.el-drawer {
  .el-drawer__header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-separator-color);
    margin-bottom: 0 !important;

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: var(--ios-text-primary);
    }
  }

  .el-drawer__body {
    padding: 20px;
  }
}

// ==================== 折叠面板 ====================
.el-collapse {
  border: 1px solid var(--ios-border-color-light);
  border-radius: var(--ios-border-radius);
  overflow: hidden;

  .el-collapse-item {
    &:last-child {
      border-bottom: none;
    }

    .el-collapse-item__header {
      background: var(--ios-light-gray);
      color: var(--ios-text-primary);
      font-weight: 500;
      padding: 12px 16px;
      border-bottom: 1px solid var(--ios-separator-color);
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--ios-fill-quaternary);
      }

      .el-collapse-item__arrow {
        color: var(--ios-gray);
      }
    }

    .el-collapse-item__wrap {
      background: var(--ios-background-secondary);
      border-bottom: 1px solid var(--ios-separator-color);

      .el-collapse-item__content {
        padding: 16px;
        color: var(--ios-text-primary);
        font-size: 14px;
        line-height: 1.6;
      }
    }
  }
}

// ==================== 上传 ====================
.el-upload {
  .el-upload-dragger {
    border: 2px dashed var(--ios-border-color);
    border-radius: var(--ios-border-radius);
    background: var(--ios-background-secondary);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--ios-blue);
      background: var(--ios-fill-quaternary);
    }

    .el-icon-upload {
      color: var(--ios-gray);
      font-size: 48px;
    }

    .el-upload__text {
      color: var(--ios-text-secondary);
      font-size: 14px;
    }
  }
}

// ==================== 滑块 ====================
.el-slider {
  .el-slider__runway {
    background: var(--ios-fill-quaternary);
    border-radius: var(--ios-border-radius-small);

    .el-slider__bar {
      background: var(--ios-blue);
      border-radius: var(--ios-border-radius-small);
    }

    .el-slider__button {
      border: 3px solid var(--ios-blue);
      background: var(--ios-background-secondary);
      box-shadow: 0 2px 8px var(--ios-shadow-light);
    }
  }
}

// ==================== 评分 ====================
.el-rate {
  .el-rate__icon {
    color: var(--ios-fill-secondary);
    font-size: 18px;
    transition: color 0.2s ease;

    &.hover {
      color: var(--ios-yellow);
    }
  }

  .el-rate__item.is-selected .el-rate__icon {
    color: var(--ios-yellow);
  }
}

// ==================== 日期选择器 ====================
.el-date-editor {
  .el-input__inner {
    cursor: pointer;
  }

  // 日期选择器 Size 适配
  &.el-date-editor--medium {
    .el-input__inner {
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border-radius: 10px;
    }
  }

  &.el-date-editor--small {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      border-radius: 8px;
    }
  }

  &.el-date-editor--mini {
    .el-input__inner {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      border-radius: 6px;
    }
  }
}

.el-picker-panel {
  border-radius: var(--ios-border-radius);
  border: 1px solid var(--ios-border-color-light);
  box-shadow: 0 8px 24px var(--ios-shadow-medium);
  background: var(--ios-background-secondary);

  .el-date-picker__header {
    color: var(--ios-text-primary);
    font-weight: 600;
  }

  .el-picker-panel__content {
    .el-date-table {
      th {
        color: var(--ios-text-secondary);
        font-weight: 500;
      }

      td {
        color: var(--ios-text-primary);

        &.available:hover {
          background: var(--ios-fill-quaternary);
        }

        &.current:not(.disabled) {
          background: var(--ios-blue);
          color: white;
        }

        &.today {
          color: var(--ios-blue);
          font-weight: 600;
        }
      }
    }
  }
}

// ==================== 时间选择器 Size 适配 ====================
.el-time-select,
.el-time-picker {

  // 时间选择器 Size 适配
  &.el-input--medium {
    .el-input__inner {
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border-radius: 10px;
    }
  }

  &.el-input--small {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      border-radius: 8px;
    }
  }

  &.el-input--mini {
    .el-input__inner {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      border-radius: 6px;
    }
  }
}

// ==================== 级联选择器 Size 适配 ====================
.el-cascader {

  // 级联选择器 Size 适配
  &.el-cascader--medium {
    .el-input__inner {
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border-radius: 10px;
    }
  }

  &.el-cascader--small {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      border-radius: 8px;
    }
  }

  &.el-cascader--mini {
    .el-input__inner {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      border-radius: 6px;
    }
  }
}

// ==================== 复合型输入框 ====================
.el-input-group {
  display: inline-table;
  border-collapse: separate;
  border-spacing: 0;

  >.el-input__inner {
    vertical-align: middle;
    display: table-cell;
  }
}

// Element UI 的复合输入框实际结构
.el-input {

  // 当输入框有前置或后置内容时
  &.el-input-group {
    display: inline-table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;

    .el-input__inner {
      vertical-align: middle;
      display: table-cell;
    }
  }
}

// 复合型输入框的前置和后置元素样式
.el-input-group__prepend,
.el-input-group__append {
  background-color: var(--ios-light-gray);
  color: var(--ios-text-secondary);
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid var(--ios-border-color);
  border-radius: var(--ios-border-radius);
  padding: 0 12px;
  width: 1px;
  white-space: nowrap;
  font-weight: 500;
  font-size: 14px;
  text-align: center;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
  }

  // 前置元素样式
  &.el-input-group__prepend {
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  // 后置元素样式
  &.el-input-group__append {
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  // 按钮样式
  .el-button {
    border: none;
    background: transparent;
    color: var(--ios-blue);
    font-weight: 600;
    padding: 8px 12px;
    margin: -1px;
    border-radius: var(--ios-border-radius-small);
    transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: none;

    &:hover {
      background: transparent;
      color: var(--ios-blue);
      transform: translateY(-1px);
      box-shadow: none;
    }

    &:active {
      transform: translateY(0);
    }

    // 图标按钮
    &.el-button--icon {
      width: 32px;
      height: 32px;
      padding: 0;
      border-radius: 50%;

      &:hover {
        border-radius: 50%;
      }
    }
  }

  // 选择器样式
  .el-select {
    .el-input__inner {
      border: none;
      background: transparent;
      color: var(--ios-text-secondary);
      font-weight: 500;
      box-shadow: none;

      &:focus {
        box-shadow: none;
      }
    }

    .el-input__suffix {
      .el-select__caret {
        color: var(--ios-gray);
      }
    }
  }

  // 下拉菜单样式
  .el-dropdown {
    .el-dropdown-link {
      color: var(--ios-blue);
      font-weight: 500;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: var(--ios-blue-dark);
      }
    }
  }
}

// 有前置元素的输入框
.el-input-group--prepend {
  .el-input__inner {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

// 有后置元素的输入框
.el-input-group--append {
  .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

// 同时有前置和后置元素的输入框
.el-input-group--prepend.el-input-group--append {
  .el-input__inner {
    border-radius: 0;
  }
}

// 复合型输入框的焦点状态
.el-input.el-input-group:focus-within {

  .el-input-group__prepend,
  .el-input-group__append {
    border-color: var(--ios-blue);
  }

  .el-input__inner {
    border-color: var(--ios-blue);
  }
}

// 复合型输入框 Size 适配
.el-input-group--large {

  .el-input-group__prepend,
  .el-input-group__append {
    padding: 0 16px;
    font-size: 16px;
    border-radius: var(--ios-border-radius-large);
  }

  &.el-input-group--prepend .el-input__inner {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  &.el-input-group--append .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-input-group__prepend {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-input-group__append {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .el-button {
    font-size: 16px;
    padding: 10px 16px;
  }
}

.el-input-group--medium {

  .el-input-group__prepend,
  .el-input-group__append {
    padding: 0 14px;
    font-size: 13px;
    border-radius: 10px;
  }

  &.el-input-group--prepend .el-input__inner {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  &.el-input-group--append .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-input-group__prepend {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-input-group__append {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .el-button {
    font-size: 13px;
    padding: 7px 12px;
  }
}

.el-input-group--small {

  .el-input-group__prepend,
  .el-input-group__append {
    padding: 0 12px;
    font-size: 12px;
    border-radius: 8px;
  }

  &.el-input-group--prepend .el-input__inner {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  &.el-input-group--append .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-input-group__prepend {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-input-group__append {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .el-button {
    font-size: 12px;
    padding: 6px 10px;
  }
}

.el-input-group--mini {

  .el-input-group__prepend,
  .el-input-group__append {
    padding: 0 10px;
    font-size: 11px;
    border-radius: 6px;
  }

  &.el-input-group--prepend .el-input__inner {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  &.el-input-group--append .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-input-group__prepend {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-input-group__append {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .el-button {
    font-size: 11px;
    padding: 5px 8px;
  }
}

// 禁用状态
.el-input.is-disabled {

  .el-input-group__prepend,
  .el-input-group__append {
    background-color: var(--ios-fill-quaternary);
    color: var(--ios-text-quaternary);
    cursor: not-allowed;
  }

  .el-button {
    background: transparent;
    color: var(--ios-text-quaternary);
    cursor: not-allowed;

    &:hover {
      background: transparent;
      color: var(--ios-text-quaternary);
      transform: none;
      box-shadow: none;
    }
  }
}

// 特殊样式：用于文件路径输入框
.el-input.file-path-input {
  .el-input-group__prepend {
    background: linear-gradient(135deg, var(--ios-blue) 0%, var(--ios-blue-light) 100%);
    color: white;
    font-weight: 600;
    border-color: var(--ios-blue);
  }
}

// 特殊样式：用于搜索输入框
// .el-input.search-input {
//   .el-input-group__append {
//     .el-button {
//       background: var(--ios-blue);
//       color: white;

//       &:hover {
//         background: var(--ios-blue-dark);
//       }
//     }
//   }
// }

// 特殊样式：用于URL输入框
// .el-input.url-input {
//   .el-input-group__prepend {
//     background: linear-gradient(135deg, var(--ios-gray-light) 0%, var(--ios-light-gray-2) 100%);
//     color: var(--ios-text-secondary);
//     font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
//     font-size: 13px;
//     font-weight: 500;
//     letter-spacing: 0.5px;
//   }
// }

// ==================== 计数器 Size 适配 ====================
.el-input-number {

  // 计数器 Size 适配
  &.el-input-number--medium {
    .el-input__inner {
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border-radius: 10px;
    }

    .el-input-number__increase,
    .el-input-number__decrease {
      font-size: 13px;
    }
  }

  &.el-input-number--small {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      border-radius: 8px;
    }

    .el-input-number__increase,
    .el-input-number__decrease {
      font-size: 12px;
    }
  }

  &.el-input-number--mini {
    .el-input__inner {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      border-radius: 6px;
    }

    .el-input-number__increase,
    .el-input-number__decrease {
      font-size: 11px;
    }
  }
}

// ==================== 表单项 Size 适配 ====================
.el-form {

  // 表单项 Size 适配
  &.el-form--medium {
    .el-form-item {
      margin-bottom: 18px;

      .el-form-item__label {
        font-size: 13px;
        line-height: 36px;
      }
    }
  }

  &.el-form--small {
    .el-form-item {
      margin-bottom: 16px;

      .el-form-item__label {
        font-size: 12px;
        line-height: 32px;
      }
    }
  }

  &.el-form--mini {
    .el-form-item {
      margin-bottom: 14px;

      .el-form-item__label {
        font-size: 11px;
        line-height: 28px;
      }
    }
  }
}
