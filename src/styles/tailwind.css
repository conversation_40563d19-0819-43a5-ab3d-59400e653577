@tailwind base;
@tailwind components;
@tailwind utilities;

/* 覆盖 Tailwind Preflight 对 img 和 svg 的默认样式 */
@layer base {
  img, svg {
    display: inline;
    vertical-align: middle;
  }
}

/* 自定义组件类 */
@layer components {
  .btn {
    @apply px-4 py-1 rounded inline-block bg-primary text-white cursor-pointer hover:opacity-80 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50;
  }
  
  .icon-btn {
    @apply inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-primary;
  }
  
  /* 添加您项目中常用的类 */
  .gap-10 {
    @apply gap-2.5;
  }
  
  
  .table-wrapper {
    @apply h-0 flex-1;
  }
  
  .pointer {
    @apply cursor-pointer;
  }
  
  .pointer:hover {
    @apply underline;
  }
}
