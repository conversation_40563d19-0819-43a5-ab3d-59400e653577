.fl-r {
  float: right;
}
.fl-l {
  float: left;
}

.flex {
  display: flex;
}
.align-center {
  align-items: center;
}
.flex1 {
  flex:1
}

.flex-center {
  justify-content: center;
}
.flex-end {
  justify-content: end;
}


.dialog-footer-right {
  text-align: right;
  margin-top: 10px;
  background: #fff;
}
.dialog-no-header {
  .el-dialog__header {
    display: none;
  }
}


.width-full { width: 100%  }

.table-base-info {
  width: 100%;
  display: flex;
  flex-direction: row;
  .info-img {
    width: 50px;
    height:50px;
    margin-right: 10px;
  }
}

.z-index-99999 {
  z-index: 99999 !important;
}


.block-primary {
  border: 1px solid #409EFF;
}
.block-success {
  border: 1px solid #67C23A;
}
.block-warning {
  border: 1px solid #E6A23C;
}
.block-danger {
  border: 1px solid #F56C6C;
}
.block-info {
  border: 1px solid lightgray;
}

.c-tree-overflow {
  width: 160px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden
}
.c-tree-placeholder  {
  color: #6e859f;
  font-size: 14px;
  font-weight: bold;
}