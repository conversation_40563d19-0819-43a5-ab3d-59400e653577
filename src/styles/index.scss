@import "ios-style";
@import "font";
@import "animation";
@import "layout";
@import '@wsfe/ctree/dist/ctree.css';
@import 'tailwind.css';

.streaming-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.adaption-container {
  // overflow-y: auto;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 165px);
}

.el-tabs__content .adaption-container {
  padding-top: 0;
  height: calc(100vh - 140px);
}

@media (max-width:992px) {
  .adaption-container {
    height: auto !important;
  }

  .table-wrapper {
    flex: auto !important;
    height: 100% !important;
  }
}

.gap-10 {
  gap: 10px
}

.table-wrapper {
  height: 0;
  flex: 1;
}

@media (max-width:650px) {
  .table-wrapper {
    height: 400px
  }
}

.table-wrap {
  height: calc(100vh - 300px);
}

.table-row-name {
  line-height: 20px;
}

.table-row-id {
  color: #b4b4b4;
  font-size: 12px;
  line-height: 16px;
}

.pointer {
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.mobile-date-picker {
  width: 340px;
  left: 50% !important;
  transform: translateX(-50%) !important;

  .el-date-range-picker__time-header {

    width: 320px;
    display: flex;
    flex-direction: column;
  }

  .el-picker-panel__body {
    width: 320px;
    display: flex;
    flex-direction: column;

  }

  .el-date-range-picker__content {
    width: 340px;
  }
}


.el-table__fixed {
  height: 100% !important;
  pointer-events: none;
}


.el-table__fixed-body-wrapper {
  pointer-events: all
}

.el-table__fixed-header-wrapper {
  pointer-events: all
}

.search-form .el-input .el-input__inner {
  width: 190px;
}

.search-form .el-input__inner::placeholder {
  color: #6e859f;
  font-size: 14px;
  font-weight: bold;
}

.search-form-thin .el-input__inner::placeholder {
  color: #6e859f;
  font-size: 14px;
}

.search-form-thin .el-form-item__content {
  margin-left: 0 !important;
}

.search-form .el-textarea__inner::placeholder {
  color: #6e859f;
  font-size: 14px;
  font-weight: bold;
}

.search-form-thin .el-textarea__inner::placeholder {
  color: #6e859f;
  font-size: 14px;
}

.documentTips {
  cursor: pointer;
}

.action_btn {
  margin: 30px 0 0 30px;
}