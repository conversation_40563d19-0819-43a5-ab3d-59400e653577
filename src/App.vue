<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from '@/components/ThemePicker/index.vue'

export default {
  name: 'App',
  components: { ThemePicker },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${import.meta.env.VITE_APP_TITLE}` : import.meta.env.VITE_APP_TITLE
      }
    }
  }
}
</script>
<script setup>
import useDifferentEndpoint from '@/hooks/useDifferentEndpoint'

useDifferentEndpoint()
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
