import Vue from 'vue'
import SvgIcon from '@/components/SvgIcon/index.vue'// svg component
import { Icon } from '@iconify/vue2'

// register globally
Vue.component('svg-icon', SvgIcon)
Vue.component('Icon', Icon)

// Vite 中使用 import.meta.glob 替代 require.context
const modules = import.meta.glob('./svg/*.svg', { eager: true })
Object.keys(modules).forEach(key => {
    // SVG 文件已经通过 vite-plugin-svg-icons 处理
})
