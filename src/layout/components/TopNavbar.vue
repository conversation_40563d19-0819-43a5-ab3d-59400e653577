<template>
  <div class="top-navbar h-15 bg-white bg-opacity-95 backdrop-blur-xl border-b border-gray-100 flex items-center px-5 fixed top-0 left-0 right-0 z-50">
    <!-- 移动端菜单按钮（仅在移动端显示） -->
    <div class="mobile-menu-btn md:hidden flex-shrink-0 mr-4">
      <div class="hamburger-btn w-10 h-10 flex items-center justify-center rounded-lg cursor-pointer text-gray-600 transition-all duration-200 hover:bg-gray-100 hover:text-blue-600" @click="toggleMobileMenu">
        <i class="el-icon-s-unfold text-lg" />
      </div>
    </div>

    <!-- Logo 区域 -->
    <div class="navbar-logo flex-shrink-0" :class="{ 'mr-8': !isMobile, 'mr-4': isMobile }">
      <logo :collapse="false" />
    </div>

    <!-- 父级菜单项（移动端隐藏） -->
    <div ref="navbarMenu" class="navbar-menu hidden md:flex items-center gap-2 flex-1">
      <!-- 可见的菜单项 -->
      <div
        v-for="(route, index) in visibleMenuItems"
        :key="route.path + index"
        class="menu-item flex items-center gap-2 px-4 py-2 rounded-xl cursor-pointer transition-all duration-200 font-medium text-gray-600 hover:bg-gray-100 hover:text-blue-600 hover:-translate-y-0.5"
        :class="{ 'active text-blue-600': isActiveParent(route) }"
        @click="handleParentMenuClick(route)"
      >
        <svg-icon
          v-if="route.meta && route.meta.icon"
          :icon-class="route.meta.icon"
          class="menu-icon text-base transition-transform duration-200 hover:scale-110"
        />
        <span class="menu-title text-sm whitespace-nowrap">{{ route.meta.title }}</span>
      </div>

      <!-- 溢出菜单 -->
      <div v-if="overflowMenuItems.length > 0" class="overflow-menu">
        <el-popover
          v-model="showOverflowMenu"
          placement="bottom-start"
          width="200"
          trigger="click"
        >
          <div class="overflow-menu-container">
            <div
              v-for="(route, index) in overflowMenuItems"
              :key="route.path + index"
              class="overflow-menu-item flex items-center gap-3 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 hover:bg-gray-100 hover:text-blue-600"
              :class="{ 'active bg-blue-50 text-blue-600': isActiveParent(route) }"
              @click="handleParentMenuClick(route)"
            >
              <svg-icon
                v-if="route.meta && route.meta.icon"
                :icon-class="route.meta.icon"
                class="text-base"
              />
              <span class="text-sm">{{ route.meta.title }}</span>
            </div>
          </div>
          <div slot="reference" class="overflow-btn flex items-center gap-2 px-3 py-2 rounded-xl cursor-pointer transition-all duration-200 font-medium text-gray-600 hover:bg-gray-100 hover:text-blue-600">
            <i class="el-icon-more text-base" />
            <span class="text-sm hidden md:inline">更多</span>
          </div>
        </el-popover>
      </div>
    </div>

    <!-- 移动端占位符 -->
    <div class="flex-1 md:hidden" />

    <!-- 右侧操作区域 -->
    <div class="navbar-actions flex items-center  flex-shrink-0">
      <!-- 桌面端操作按钮（移动端隐藏） -->
      <div class="hidden md:flex items-center gap-1">
        <el-tooltip content="刷新缓存" effect="dark" placement="bottom">
          <CacheRefresh class="action-item" />
        </el-tooltip>
        <el-tooltip content="任务中心" effect="dark" placement="bottom">
          <FileExport class="action-item" />
        </el-tooltip>
        <el-tooltip content="使用文档" effect="dark" placement="bottom">
          <a href="https://ziuqxc1rltx.feishu.cn/docx/V4kTd3Z2co2qvYx2Cmhch6CrnPE" target="_blank">
            <i class="el-icon-document action-item " />
          </a>
        </el-tooltip>
        <el-tooltip content="消息通知" effect="dark" placement="bottom">
          <Notice class="action-item " />
        </el-tooltip>
      </div>

      <!-- 用户信息区域 -->
      <div class="user-section" :class="{ 'ml-3 pl-3 border-l border-gray-200': !isMobile }">
        <el-popover
          placement="bottom-end"
          width="220"
          trigger="click"
          popper-class="user-menu-popover"
        >
          <div class="user-menu-container">
            <!-- 菜单中的用户信息 -->
            <div class="user-info-section">
              <div class="user-avatar-large">
                <img :src="avatar" class="w-12 h-12 rounded-full object-cover">
              </div>
              <div class="user-details-menu">
                <div class="user-name">{{ nickname }}</div>
                <div class="user-phone">{{ phone }}</div>
              </div>
            </div>

            <div class="user-menu-item" @click="goToProfile">
              <i class="el-icon-user" /> 个人中心
            </div>
            <div class="user-menu-item" @click="handleUserCommand('settings')">
              <i class="el-icon-setting" /> 布局设置
            </div>
            <div class="user-menu-item" @click="handleUserCommand('logout')">
              <i class="el-icon-switch-button" /> 退出登录
            </div>
          </div>
          <div slot="reference" class="user-avatar-wrapper flex items-center gap-2 cursor-pointer px-2 py-1 rounded-lg transition-all duration-200 hover:bg-gray-100">
            <img :src="avatar" class="user-avatar w-8 h-8 rounded-full object-cover">
            <i class="el-icon-arrow-down text-xs text-gray-500 ml-1 hidden md:inline" />
          </div>
        </el-popover>
      </div>
    </div>

    <!-- 移动端导航抽屉 -->
    <el-drawer
      :visible.sync="showMobileMenu"
      direction="ltr"
      size="280px"
      :show-close="false"
      :modal="true"
      :lock-scroll="true"
      custom-class="mobile-nav-drawer"
      append-to-body
    >
      <div class="mobile-nav-content h-full flex flex-col">
        <!-- 移动端头部 -->
        <div class="mobile-nav-header p-4 border-b border-gray-200 flex-shrink-0">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <img :src="avatar" class="w-12 h-12 rounded-full object-cover">
              <div>
                <div class="text-base font-semibold text-gray-800">{{ nickname }}</div>
                <div class="text-sm text-gray-500">{{ phone }}</div>
              </div>
            </div>
            <div class="close-btn w-8 h-8 flex items-center justify-center rounded-lg cursor-pointer text-gray-600 transition-all duration-200 hover:bg-gray-100" @click="closeMobileMenu">
              <i class="el-icon-close text-lg" />
            </div>
          </div>
        </div>

        <!-- 移动端菜单项 -->
        <div class="mobile-nav-menu p-4 flex-1 overflow-y-auto">
          <div
            v-for="(route, index) in parentRoutes"
            :key="route.path + index"
            class="mobile-menu-item flex items-center gap-4 p-4 mb-2 rounded-xl cursor-pointer transition-all duration-200 text-gray-700 hover:bg-gray-100 hover:text-blue-600"
            :class="{ 'active text-blue-600 font-semibold': isActiveParent(route) }"
            @click="handleMobileMenuClick(route)"
          >
            <svg-icon
              v-if="route.meta && route.meta.icon"
              :icon-class="route.meta.icon"
              class="text-xl flex-shrink-0"
            />
            <span class="text-base font-medium">{{ route.meta.title }}</span>
          </div>
        </div>

        <!-- 移动端操作按钮 -->
        <div class="mobile-nav-actions p-4 border-t border-gray-200 flex-shrink-0">
          <div class="grid grid-cols-2 gap-3 mb-4">
            <div class="mobile-action-item " @click="handleUserCommand('settings')">
              <i class="el-icon-setting text-xl" />
              <span class="text-xs">设置</span>
            </div>
            <div class="mobile-action-item flex flex-col items-center gap-2 p-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-blue-600" @click="goToProfile">
              <i class="el-icon-user text-xl" />
              <span class="text-xs">个人中心</span>
            </div>
          </div>
          <div class="mobile-logout-btn flex items-center justify-center gap-2 p-3 rounded-lg cursor-pointer transition-all duration-200 text-red-600 hover:bg-red-50" @click="handleUserCommand('logout')">
            <i class="el-icon-switch-button text-lg" />
            <span class="text-sm font-medium">退出登录</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Sidebar/Logo.vue'
import Notice from '@/components/Notice/index.vue'
import FileExport from '@/components/FileExport/index.vue'
import CacheRefresh from '@/components/CacheRefresh/index.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'

export default {
  name: 'TopNavbar',
  components: {
    Logo,
    Notice,
    FileExport,
    CacheRefresh,
    SvgIcon
  },
  data() {
    return {
      visibleMenuItems: [],
      overflowMenuItems: [],
      showOverflowMenu: false,
      showMobileMenu: false
    }
  },
  watch: {
    parentRoutes() {
      this.calculateMenuItems()
    },
    // 监听路由变化，确保父级路由状态同步
    '$route.path': {
      handler() {
        // 强制更新组件，确保 isActiveParent 重新计算
        this.$forceUpdate()
      },
      immediate: false
    }
  },
  mounted() {
    this.calculateMenuItems()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  computed: {
    ...mapGetters(['sidebarRouters', 'avatar', 'nickname', 'phone', 'device']),
    // 获取当前父级路由
    currentParentRoute() {
      return this.$store.state.permission.currentParentRoute
    },
    // 获取父级路由（有子菜单的路由）
    parentRoutes() {
      const routes = this.sidebarRouters || []
      return routes.filter(route => {
        return !route.hidden && route.children && route.children.length > 0 && route.meta
      })
    },
    // 检查当前设备是否为移动端
    isMobile() {
      return this.device === 'mobile'
    }
  },
  methods: {
    // 检查父级路由是否激活
    isActiveParent(parentRoute) {
      // 基于 store 中的 currentParentRoute 来判断
      if (!this.currentParentRoute) {
        return false
      }
      return this.currentParentRoute.path === parentRoute.path
    },

    // 处理父级菜单点击
    handleParentMenuClick(parentRoute) {
      // 关闭溢出菜单（如果打开）
      this.showOverflowMenu = false

      // 更新 store 中的当前父级路由
      this.$store.commit('SET_CURRENT_PARENT_ROUTE', parentRoute)

      // 导航到第一个可用的叶子路由（没有子菜单的路由）
      const firstLeafRoute = this.findFirstLeafRoute(parentRoute)
      if (firstLeafRoute) {
        this.$router.push(firstLeafRoute.fullPath)
      }
    },

    // 递归查找第一个叶子路由（没有子菜单或没有可见子菜单的路由）
    findFirstLeafRoute(route, parentPath = '') {
      // 构建当前路由的完整路径
      const currentPath = route.path.startsWith('/')
        ? route.path
        : parentPath ? `${parentPath}/${route.path}` : route.path

      // 如果路由没有子菜单或没有可见的子菜单，它就是叶子路由
      const visibleChildren = route.children ? route.children.filter(child => !child.hidden && child.meta) : []

      if (visibleChildren.length === 0) {
        return {
          ...route,
          fullPath: currentPath
        }
      }

      // 如果路由有子菜单，递归查找子菜单中的第一个叶子路由
      for (const child of visibleChildren) {
        const leafRoute = this.findFirstLeafRoute(child, currentPath)
        if (leafRoute) {
          return leafRoute
        }
      }

      // 如果在子菜单中没有找到叶子路由，返回 null
      return null
    },

    // 用户操作
    goToProfile() {
      this.$router.push('/user/profile')
    },

    handleUserCommand(command) {
      switch (command) {
        case 'settings':
          this.$store.dispatch('settings/changeSetting', {
            key: 'showSettings',
            value: true
          })
          break
        case 'logout':
          this.$confirm('确定退出系统吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$store.dispatch('LogOut').then(() => {
              location.href = '/'
            })
          }).catch(() => {})
          break
      }
    },

    // 计算哪些菜单项应该可见，哪些应该放在溢出菜单中
    calculateMenuItems() {
      this.$nextTick(() => {
        if (!this.$refs.navbarMenu) return

        const containerWidth = this.$refs.navbarMenu.offsetWidth
        const availableWidth = containerWidth - 120 // 为溢出按钮预留空间
        let totalWidth = 0
        const itemWidth = 120 // 每个菜单项的大概宽度

        const allRoutes = this.parentRoutes
        this.visibleMenuItems = []
        this.overflowMenuItems = []

        for (let i = 0; i < allRoutes.length; i++) {
          totalWidth += itemWidth
          if (totalWidth <= availableWidth) {
            this.visibleMenuItems.push(allRoutes[i])
          } else {
            this.overflowMenuItems.push(allRoutes[i])
          }
        }
      })
    },

    // 处理窗口大小变化
    handleResize() {
      this.calculateMenuItems()
    },

    // 移动端菜单方法
    toggleMobileMenu() {
      this.showMobileMenu = !this.showMobileMenu
    },

    closeMobileMenu() {
      this.showMobileMenu = false
    },

    handleMobileMenuClick(route) {
      // 关闭移动端菜单
      this.closeMobileMenu()

      // 处理父级菜单点击
      this.handleParentMenuClick(route)
    }
  }
}
</script>

<style lang="scss" scoped>
.top-navbar {
  border-bottom: 1px solid var(--ios-light-gray-2);
  z-index: 1000;

  .menu-item {
    border-radius: var(--ios-border-radius);
    transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
    color: var(--ios-text-secondary);

    .menu-icon {
      transition: transform 0.2s ease;
    }

    &:hover {
      background: var(--ios-fill-quaternary);
      color: var(--ios-blue);

      .menu-icon {
        transform: scale(1.1);
      }
    }

    &.active {
      color: var(--ios-blue);

      &:hover {
        color: var(--ios-blue-dark);
      }
    }
  }

  .action-item {
    @apply  w-9 h-9 flex items-center justify-center rounded-lg text-gray-600 cursor-pointer transition-all duration-200 hover:bg-gray-100 hover:text-blue-600 hover:-translate-y-0.5;
    border-radius: var(--ios-border-radius-small);
    color: var(--ios-text-secondary);

    &:hover {
      background: var(--ios-fill-quaternary);
      color: var(--ios-blue);
    }
  }
}

// 移动端响应式
@media (max-width: 768px) {
  .navbar-menu {
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// 用户区域样式
.user-section {
  .user-avatar-wrapper {
    &:hover {
      .user-avatar {
        transform: scale(1.05);
      }
    }
  }
}

// 用户菜单样式
.user-menu-container {
  padding: 0;
  border-radius: var(--ios-border-radius);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.user-info-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);

  .user-avatar-large {
    flex-shrink: 0;

    img {
      border: 2px solid rgba(255, 255, 255, 0.8);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .user-details-menu {
    flex: 1;
    min-width: 0;

    .user-name {
      font-size: 14px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-phone {
      font-size: 12px;
      color: #6b7280;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin: 0 8px;
  border-radius: var(--ios-border-radius-small);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
  font-size: 14px;
  color: #374151;
  font-weight: 500;

  i {
    font-size: 16px;
    color: #6b7280;
    transition: color 0.2s ease;
  }

  &:hover {
    background: var(--ios-fill-quaternary);
    color: var(--ios-blue);
    transform: translateX(2px);

    i {
      color: var(--ios-blue);
    }
  }

  &:first-of-type {
    margin-top: 8px;
  }

  &:last-of-type {
    margin-bottom: 8px;
  }
}

// 溢出菜单样式
.overflow-menu-item {
  &:hover {
    background: var(--ios-fill-quaternary);
  }

  &.active {
    color: var(--ios-blue);
  }
}

.overflow-btn {
  &:hover {
    background: var(--ios-fill-quaternary);
    color: var(--ios-blue);
  }
}

// 移动端导航样式
.mobile-nav-drawer {
  .el-drawer__body {
    padding: 0;
  }
}

.mobile-nav-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
}

.mobile-menu-item {
  min-height: 44px; // Touch-friendly size

  &.active {
    color: var(--ios-blue);

    &:hover {
      color: var(--ios-blue-dark);
    }
  }

  &:hover {
    background: var(--ios-fill-quaternary);
    color: var(--ios-blue);
  }
}

.mobile-action-item {
  @apply flex flex-col items-center gap-2 p-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-blue-600;
  min-height: 44px; // 触摸友好的尺寸

  &:hover {
    background: var(--ios-fill-quaternary);
    color: var(--ios-blue);
  }
}

.mobile-logout-btn {
  min-height: 44px; // 触摸友好的尺寸

  &:hover {
    background: rgba(239, 68, 68, 0.1);
  }
}

.hamburger-btn {
  &:hover {
    background: var(--ios-fill-quaternary);
    color: var(--ios-blue);
  }
}
</style>

<style lang="scss">
.user-menu-popover {
    padding: 0 !important;
    border: none !important;
    border-radius: var(--ios-border-radius) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.6) !important;

}

</style>
