<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <div class="ios-card">
        <keep-alive :include="cachedViews">
          <router-view v-if="!$route.meta.link" :key="key" />
        </keep-alive>
      </div>
    </transition>
    <iframe-toggle />
  </section>
</template>

<script>
import iframeToggle from './IframeToggle/index.vue'

export default {
  name: 'AppMain',
  components: { iframeToggle },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 140px);
  width: 100%;
  position: relative;
  overflow-y: auto;
  padding: 16px; /* 调整为更合适的内边距 */
  transition: padding 0.3s ease;
}

.fixed-header + .app-main {
  min-height: calc(100vh);
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    min-height: calc(100vh);
    padding-top: 90px;
  }
}

/* 适配侧边栏展开/折叠状态 */
.app-wrapper:not(.hideSidebar) .app-main {
  padding-left: 20px;
  padding-right: 20px;
}

.app-wrapper.hideSidebar .app-main {
  padding-left: 16px;
  padding-right: 16px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    //padding-right: 17px;
  }
}
</style>
