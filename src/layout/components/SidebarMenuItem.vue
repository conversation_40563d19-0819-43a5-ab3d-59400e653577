<template>
  <div class="sidebar-menu-item">
    <!-- 如果有子菜单，显示为可展开的菜单项 -->
    <template v-if="hasChildren">
      <div
        class="menu-item-with-children"
        :class="{ 'collapsed': collapsed }"
      >
        <!-- 父级菜单项 -->
        <el-tooltip
          v-if="!collapsed"
          :content="item.meta.title"
          effect="light"
          :open-delay="600"
          placement="right"
          :disabled="!collapsed"
        >
          <div
            class="menu-item flex items-center gap-3 px-4 py-3 mb-1 rounded-xl cursor-pointer transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-blue-600 hover:translate-x-1"
            :class="{
              'active text-blue-600 font-semibold': isActiveItem,
              'has-active-child text-blue-500': hasActiveChild
            }"
            :style="{ paddingLeft: `${16 + level * 16}px` }"
            @click="toggleExpanded"
          >
            <svg-icon
              v-if="item.meta && item.meta.icon"
              :icon-class="item.meta.icon"
              class="menu-icon text-base flex-shrink-0"
            />
            <span v-if="!collapsed" class="menu-title text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis flex-1">{{ item.meta.title }}</span>
            <i
              v-if="!collapsed"
              class="expand-icon text-xs transition-transform duration-200"
              :class="isExpanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
            />
          </div>
        </el-tooltip>

        <!-- 收起模式下的悬浮菜单 -->
        <el-popover
          v-if="collapsed"
          placement="right-start"
          trigger="hover"
          :open-delay="200"
          :close-delay="100"
          popper-class="sidebar-submenu-popover"
          :offset="8"
        >
          <div class="popover-submenu">
            <div class="popover-title">{{ item.meta.title }}</div>
            <div class="popover-menu-list">
              <div
                v-for="(child, index) in visibleChildren"
                :key="child.path + index"
                class="popover-menu-item"
                :class="{ 'active': isChildActive(child) }"
                @click="handleChildClick(child)"
              >
                <svg-icon
                  v-if="child.meta && child.meta.icon"
                  :icon-class="child.meta.icon"
                  class="menu-icon"
                />
                <span class="menu-title">{{ child.meta.title }}</span>
                <i
                  v-if="child.children && child.children.length > 0"
                  class="el-icon-arrow-right submenu-arrow"
                />
              </div>
            </div>
          </div>
          <div
            slot="reference"
            class="menu-item flex items-center gap-3 px-4 py-3 mb-1 rounded-xl cursor-pointer transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-blue-600 hover:translate-x-1"
            :class="{
              'active text-blue-600 font-semibold': isActiveItem,
              'has-active-child text-blue-500': hasActiveChild
            }"
            :style="{ paddingLeft: `${16 + level * 16}px` }"
          >
            <svg-icon
              v-if="item.meta && item.meta.icon"
              :icon-class="item.meta.icon"
              class="menu-icon text-base flex-shrink-0"
            />
          </div>
        </el-popover>

        <!-- 子菜单项 -->
        <div
          v-if="!collapsed"
          class="submenu-container transition-all duration-300 overflow-hidden"
          :class="{ 'expanded': isExpanded }"
        >
          <sidebar-menu-item
            v-for="(child, index) in visibleChildren"
            :key="child.path + index"
            :item="child"
            :base-path="getChildPath(child)"
            :level="level + 1"
            :collapsed="collapsed"
          />
        </div>
      </div>
    </template>

    <!-- 如果没有子菜单，显示为普通菜单项 -->
    <template v-else>
      <el-tooltip
        :content="item.meta.title"
        effect="light"
        :open-delay="600"
        placement="right"
        :disabled="!collapsed"
      >
        <div
          class="menu-item flex items-center gap-3 px-4 py-3 mb-1 rounded-xl cursor-pointer transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-blue-600 hover:translate-x-1"
          :class="{ 'active text-blue-600 font-semibold': isActiveItem }"
          :style="{ paddingLeft: `${16 + level * 16}px` }"
          @click="handleMenuClick"
        >
          <svg-icon
            v-if="item.meta && item.meta.icon"
            :icon-class="item.meta.icon"
            class="menu-icon text-base flex-shrink-0"
          />
          <span v-if="!collapsed" class="menu-title text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis">{{ item.meta.title }}</span>
        </div>
      </el-tooltip>
    </template>
  </div>
</template>

<script>
import SvgIcon from '@/components/SvgIcon/index.vue'

export default {
  name: 'SidebarMenuItem',
  components: {
    SvgIcon
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    basePath: {
      type: String,
      default: ''
    },
    level: {
      type: Number,
      default: 0
    },
    collapsed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpanded: false
    }
  },
  computed: {
    // 检查是否有可见的子菜单
    hasChildren() {
      return this.visibleChildren.length > 0
    },

    // 获取可见的子菜单
    visibleChildren() {
      if (!this.item.children) {
        return []
      }
      return this.item.children.filter(child => !child.hidden && child.meta)
    },

    // 检查当前菜单项是否激活
    isActiveItem() {
      const currentPath = this.$route.path
      return currentPath === this.basePath
    },

    // 检查是否有激活的子菜单
    hasActiveChild() {
      if (!this.hasChildren) {
        return false
      }
      return this.checkActiveChild(this.visibleChildren)
    }
  },
  watch: {
    // 监听路由变化，自动展开包含当前路由的菜单
    '$route.path': {
      handler() {
        if (this.hasActiveChild) {
          this.isExpanded = true
        }
      },
      immediate: true
    }
  },
  methods: {
    // 切换展开状态
    toggleExpanded() {
      if (this.hasChildren) {
        this.isExpanded = !this.isExpanded
      } else {
        this.handleMenuClick()
      }
    },

    // 处理菜单点击
    handleMenuClick() {
      // 如果有子菜单，导航到第一个叶子节点
      if (this.hasChildren) {
        const firstLeafRoute = this.findFirstLeafRoute(this.item)
        if (firstLeafRoute) {
          this.$router.push(firstLeafRoute.fullPath)
        }
      } else if (this.basePath && this.basePath !== this.$route.path) {
        this.$router.push(this.basePath)
      }
    },

    // 递归查找第一个叶子节点（没有子菜单的菜单项）
    findFirstLeafRoute(route, parentPath = '') {
      // 构建当前路由的完整路径
      const currentPath = route.path.startsWith('/')
        ? route.path
        : parentPath ? `${parentPath}/${route.path}` : this.basePath || route.path

      // 如果路由没有子菜单或没有可见的子菜单，它就是叶子节点
      const visibleChildren = route.children ? route.children.filter(child => !child.hidden && child.meta) : []

      if (visibleChildren.length === 0) {
        return {
          ...route,
          fullPath: currentPath
        }
      }

      // 如果路由有子菜单，递归查找子菜单中的第一个叶子节点
      for (const child of visibleChildren) {
        const leafRoute = this.findFirstLeafRoute(child, currentPath)
        if (leafRoute) {
          return leafRoute
        }
      }

      // 如果在子菜单中没有找到叶子节点，返回 null
      return null
    },

    // 处理子菜单点击（用于悬浮菜单）
    handleChildClick(child) {
      // 如果子菜单还有子菜单，导航到第一个叶子节点
      const visibleGrandChildren = child.children ? child.children.filter(grandChild => !grandChild.hidden && grandChild.meta) : []

      if (visibleGrandChildren.length > 0) {
        const firstLeafRoute = this.findFirstLeafRoute(child, this.basePath)
        if (firstLeafRoute) {
          this.$router.push(firstLeafRoute.fullPath)
        }
      } else {
        const childPath = this.getChildPath(child)
        if (childPath && childPath !== this.$route.path) {
          this.$router.push(childPath)
        }
      }
    },

    // 检查子菜单是否激活（用于悬浮菜单）
    isChildActive(child) {
      const childPath = this.getChildPath(child)
      return this.$route.path === childPath
    },

    // 获取子菜单路径
    getChildPath(child) {
      if (child.path.startsWith('/')) {
        return child.path
      }

      // 构建完整路径
      const parentPath = this.basePath || ''
      return parentPath.endsWith('/')
        ? `${parentPath}${child.path}`
        : `${parentPath}/${child.path}`
    },

    // 递归检查是否有激活的子菜单
    checkActiveChild(children) {
      const currentPath = this.$route.path

      for (const child of children) {
        const childPath = this.getChildPath(child)

        // 检查直接匹配
        if (currentPath === childPath) {
          return true
        }

        // 递归检查子菜单
        if (child.children && child.children.length > 0) {
          const visibleGrandChildren = child.children.filter(grandChild => !grandChild.hidden && grandChild.meta)
          if (this.checkActiveChildPath(visibleGrandChildren, childPath)) {
            return true
          }
        }
      }

      return false
    },

    // 检查子路径是否激活
    checkActiveChildPath(children, parentPath) {
      const currentPath = this.$route.path

      for (const child of children) {
        const childPath = child.path.startsWith('/')
          ? child.path
          : `${parentPath}/${child.path}`

        if (currentPath === childPath) {
          return true
        }

        if (child.children && child.children.length > 0) {
          const visibleGrandChildren = child.children.filter(grandChild => !grandChild.hidden && grandChild.meta)
          if (this.checkActiveChildPath(visibleGrandChildren, childPath)) {
            return true
          }
        }
      }

      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar-menu-item {
  .menu-item {
    border-radius: var(--ios-border-radius);
    transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
    color: var(--ios-text-secondary);

    &:hover {
      background: var(--ios-fill-quaternary);
      color: var(--ios-blue);
      transform: translateX(4px);
    }

    &.active {
      background-color: var(--ios-blue);
      color: white;

      &:hover {
        background-color: var(--ios-blue-dark);
      }
    }

    &.has-active-child {
      color: var(--ios-blue);
      font-weight: 500;
    }

    .expand-icon {
      margin-left: auto;
      color: var(--ios-text-tertiary);
      transition: transform 0.2s ease;
    }
  }

  .submenu-container {
    max-height: 0;

    &.expanded {
      max-height: 1000px; // 足够大的值来容纳子菜单
    }
  }

  // 折叠状态下的样式调整
  .menu-item-with-children.collapsed {
    .menu-item {
      .menu-title,
      .expand-icon {
        display: none;
      }
    }
  }
}

// 悬浮菜单样式（全局样式，不使用 scoped）
</style>

<style lang="scss">
.sidebar-submenu-popover {
  padding: 0 !important;
  border: 1px solid var(--ios-border-color-light) !important;
  border-radius: var(--ios-border-radius) !important;
  box-shadow: 0 8px 24px var(--ios-shadow-medium) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  min-width: 200px;
  max-width: 280px;

  .popover-submenu {
    .popover-title {
      padding: 12px 16px 8px;
      font-size: 13px;
      font-weight: 600;
      color: var(--ios-text-primary);
      border-bottom: 1px solid var(--ios-separator-color);
      background: var(--ios-light-gray);
      margin: 0;
    }

    .popover-menu-list {
      display: flex;
      flex-direction: column;
      padding: 8px;
      gap: 4px;

      .popover-menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 10px 12px;
        margin-bottom: 2px;
        cursor: pointer;
        border-radius: var(--ios-border-radius);
        transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
        color: var(--ios-text-secondary);
        font-size: 14px;
        font-weight: 500;
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          background: var(--ios-fill-quaternary);
          color: var(--ios-blue);
          transform: translateX(4px);

          .menu-icon {
            color: var(--ios-blue);
          }

          .submenu-arrow {
            color: var(--ios-blue);
          }
        }

        &.active {
          background-color: var(--ios-blue);
          color: white;
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);

          .menu-icon {
            color: white;
          }

          .submenu-arrow {
            color: white;
          }

          &:hover {
            background-color: var(--ios-blue-dark);
            transform: translateX(4px);
            box-shadow: 0 3px 12px rgba(0, 122, 255, 0.4);
          }
        }

        .menu-icon {
          font-size: 16px;
          flex-shrink: 0;
          color: var(--ios-text-tertiary);
          transition: color 0.2s ease;
        }

        .menu-title {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-weight: inherit;
        }

        .submenu-arrow {
          font-size: 12px;
          color: var(--ios-text-tertiary);
          margin-left: auto;
          transition: color 0.2s ease;
        }
      }
    }
  }
}
</style>
