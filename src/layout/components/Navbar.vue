<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb v-if="!topNav" id="breadcrumb-container" class="breadcrumb-container" />
    <top-nav v-if="topNav" id="topmenu-container" class="topmenu-container" />

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <el-tooltip content="刷新缓存" effect="dark" placement="bottom">
          <CacheRefresh class="right-menu-item hover-effect" />
        </el-tooltip>
        <el-tooltip v-model="fileExportVisible" content="任务中心" effect="dark" placement="bottom">
          <FileExport class="right-menu-item hover-effect" />
        </el-tooltip>
        <!-- <el-tooltip content="全屏" effect="dark" placement="bottom">
          <screenfull id="screenfull" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <el-tooltip content="使用文档" effect="dark" placement="bottom">
          <a href="https://ziuqxc1rltx.feishu.cn/docx/V4kTd3Z2co2qvYx2Cmhch6CrnPE" target="_blank">
            <i class="el-icon-document text-xl" />
          </a>
        </el-tooltip>
        <el-tooltip content="消息通知" effect="dark" placement="bottom">
          <Notice class="right-menu-item hover-effect" />
        </el-tooltip>

        <!-- 移除原来的用户头像下拉菜单 -->
      </template>

    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Notice from '@/components/Notice/index.vue'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import FileExport from '@/components/FileExport/index.vue'
import TopNav from '@/components/TopNav/index.vue'
import Hamburger from '@/components/Hamburger/index.vue'
import Screenfull from '@/components/Screenfull/index.vue'
import SizeSelect from '@/components/SizeSelect/index.vue'
import CacheRefresh from '@/components/CacheRefresh/index.vue'

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Notice,
    FileExport,
    CacheRefresh
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'nickname',
      'phone',
      'device'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$confirm('确定退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/'
        })
      }).catch(() => {})
    }
  }
}
</script>

<script setup>
import useTooltipVisible from '@/hooks/useTooltipVisible'
import useDifferentEndpoint from '@/hooks/useDifferentEndpoint'
const { addTooltip } = useTooltipVisible()
const fileExportVisible = addTooltip('fileExport')

const { guide } = useDifferentEndpoint()
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px; // 调回 50px
  overflow: hidden;
  position: relative;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05); // 更柔和的阴影
  display: flex; // 使用 flex 布局
  align-items: center; // 垂直居中
  padding: 0 16px; // 统一内边距

  .hamburger-container {
    line-height: 48px; // 调整行高
    height: 100%;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .right-menu {
    margin-left: auto; // 推到右边
    display: flex;
    align-items: center; // 垂直居中
    gap: 16px; // 统一间距

    &:focus {
      outline: none;
    }

    .nav-link {
      font-size: 16px;
      color: var(--ios-blue);
    }

    .right-menu-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px; // 固定宽度
      height: 36px; // 固定高度
      font-size: 18px; // 调整图标大小
      color: #666; // 调整颜色
      border-radius: 8px; // 圆角

      &.hover-effect {
        cursor: pointer;
        transition: background-color .3s;

        &:hover {
          background-color: rgba(0, 0, 0, .05); // 浅灰色背景
        }

        &:active { // 添加点击反馈
          background-color: rgba(0, 0, 0, .1); // 更深的背景色
          transition: background-color 0.1s; // 更快的过渡
        }
      }
    }

    .avatar-container {
      .avatar-wrapper {
        display: flex;
        align-items: center;
        gap: 8px; // 头像和文字间距
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }
        .user-info {
          font-size: 14px;
          line-height: 1.2;
        }
        .user-info-item {
          max-width: 140px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .user-name {
          font-weight: 600;
        }
        .user-phone {
          font-size: 12px;
          color: #6c757d;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
