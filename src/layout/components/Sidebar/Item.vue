<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    if (icon) {
      vnodes.push(h('svg-icon', { props: { iconClass: icon } }))
    }

    if (title) {
      if (title.length > 5) {
        vnodes.push(h('span', { slot: 'title', attrs: { title: title } }, title))
      } else {
        vnodes.push(h('span', { slot: 'title' }, title))
      }
    }
    return vnodes
  }
}
</script>

<style lang="scss" scoped>
.el-menu-item span,
.el-submenu__title span {
  font-size: var(--menu-font-size); // 使用导出的变量
  vertical-align: middle; // 垂直居中对齐
}

.svg-icon {
  vertical-align: middle; // 垂直居中对齐
  margin-right: 8px; // 图标和文字间距
}
</style>
