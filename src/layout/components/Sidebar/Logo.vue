<template>
  <div class="sidebar-logo-container" :class="{'collapsed':collapse}" :style="{ backgroundColor: 'transparent' }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" :to="indexUrl">
        <img v-if="logo" :src="logo" class="sidebar-logo">
        <h1 v-else class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? '#bfcbd9' : '#1f2d3d' }">{{ title }} </h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" :to="indexUrl">
        <img v-if="logo" :src="logo" class="sidebar-logo">
        <h1 class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? '#bfcbd9' : '#1f2d3d' }">{{ title }} </h1>
      </router-link>
    </transition>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'Sidebar<PERSON>ogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    ...mapGetters([
      'indexUrl'
    ]),
    variables() {
      return variables
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme
    }
  },
  data() {
    return {
      // title: '花粉广告投放系统',
      // logo: logoImg
    }
  }
}
</script>

<script setup>
import useDifferentEndpoint from '@/hooks/useDifferentEndpoint'

const { title, logo } = useDifferentEndpoint()
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: center;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  // border-bottom: 1px solid rgba(0, 0, 0, 0.05);

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #1f2d3d;
      font-weight: 600;
      line-height: 50px;
      font-size: 16px;
      font-family: var(--ios-font-family);
      vertical-align: middle;
    }
  }

  &.collapsed {
    .sidebar-logo {
      margin-right: 0px;
      width: 28px;
      height: 28px;
    }
  }
}
</style>
