<template>
  <div :class="{'has-logo':showLogo}" :style="{ backdropFilter: 'blur(20px)' }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :active-text-color="settings.theme"
        :collapse-transition="false"
        mode="vertical"
        :class="{'el-menu--collapsed': isCollapse}"
        :text-color="settings.sideTheme === 'theme-dark' ? '#bfcbd9' : '#304156'"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>

      <!-- 用户信息区域 -->
      <div class="user-info-container" :class="{ 'collapsed': isCollapse }">
        <!-- <el-dropdown trigger="click" @command="handleUserCommand">
            <div class="user-avatar-wrapper" >
          <img :src="avatar" class="user-avatar"  />
          <div v-if="!isCollapse" class="user-details">
            <div class="user-name">{{ nickname }}</div>
            <div class="user-phone">{{ phone }}</div>
          </div>
        </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">
                <i class="el-icon-user"></i> 个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <i class="el-icon-setting"></i> 布局设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <i class="el-icon-switch-button"></i> 退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        <el-popover
          placement="right"
          width="200"
          trigger="click"
        >
          <div class="user-menu-container">
            <div class="user-menu-item" @click="goToProfile">
              <i class="el-icon-user" /> 个人中心
            </div>
            <div class="user-menu-item" @click="handleUserCommand('settings')">
              <i class="el-icon-setting" /> 布局设置
            </div>
            <div class="user-menu-item" @click="handleUserCommand('logout')">
              <i class="el-icon-switch-button" /> 退出登录
            </div>
          </div>
          <div slot="reference" class="user-avatar-wrapper">
            <img :src="avatar" class="user-avatar">
            <div v-if="!isCollapse" class="user-details">
              <div class="user-name">{{ nickname }}</div>
              <div class="user-phone">{{ phone }}</div>
            </div>
          </div>
        </el-popover>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import Logo from './Logo.vue'
import SidebarItem from './SidebarItem.vue'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapState(['settings']),
    ...mapGetters(['sidebarRouters', 'sidebar', 'avatar', 'nickname', 'phone']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  methods: {
    goToProfile() {
      this.$router.push('/user/profile')
    },
    handleUserCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/user/profile')
          break
        case 'settings':
          this.$store.dispatch('settings/changeSetting', {
            key: 'showSettings',
            value: true
          })
          break
        case 'logout':
          this.$confirm('确定退出系统吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$store.dispatch('LogOut').then(() => {
              location.href = '/'
            })
          }).catch(() => {})
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.user-info-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  display: flex;
  align-items: center;
    justify-content: center;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;

}

.user-avatar-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;

  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    transition: all 0.3s ease;

    &.collapsed {
      width: 32px;
      height: 32px;
    }
  }

  .user-details {
    margin-left: 12px;
    overflow: hidden;

    .user-name {
      font-weight: 600;
      font-size: 14px;
      color: #303133;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-phone {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.user-actions {
  margin-left: 12px;

  .user-menu-trigger {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}
.user-menu-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  // background: var(--ios-background-secondary);
  // border-radius: var(--ios-border-radius);
  // box-shadow: 0 8px 24px var(--ios-shadow-medium);
  // backdrop-filter: blur(20px);
  // overflow: hidden;
  .user-menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: var(--ios-text-primary);

    &:hover {
      background: var(--ios-fill-quaternary);
    }
  }
}

// 折叠时隐藏用户详情
.collapsed .user-details {
  display: none;
}

// 调整菜单高度以适应底部用户信息
.scrollbar-wrapper {
  max-height: calc(100vh - 100px);
}
</style>
