<template>
  <el-scrollbar ref="scrollContainer" :vertical="false" class="scroll-container" @wheel.native.prevent="handleScroll">
    <slot />
  </el-scrollbar>
</template>

<script>
const tagAndTagSpacing = 4 // tagAndTagSpacing

export default {
  name: 'ScrollPane',
  data() {
    return {
      left: 0
    }
  },
  computed: {
    scrollWrapper() {
      return this.$refs.scrollContainer.$refs.wrap
    }
  },
  mounted() {
    this.scrollWrapper.addEventListener('scroll', this.emitScroll, true)
  },
  beforeDestroy() {
    this.scrollWrapper.removeEventListener('scroll', this.emitScroll)
  },
  methods: {
    handleScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40
      const $scrollWrapper = this.scrollWrapper
      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4
    },
    emitScroll() {
      this.$emit('scroll')
    },
    moveToTarget(currentTag) {
      const $container = this.$refs.scrollContainer.$el
      const $containerWidth = $container.offsetWidth
      const $scrollWrapper = this.scrollWrapper

      // 修复：支持传入 DOM 元素或 Vue 组件实例
      let targetElement = currentTag
      if (currentTag && currentTag.$el) {
        targetElement = currentTag.$el
      }

      if (!targetElement) {
        return
      }

      // 获取所有标签元素
      const tagElements = this.$parent.$el.querySelectorAll('.chrome-tab')

      if (tagElements.length === 0) {
        return
      }

      const firstTag = tagElements[0]
      const lastTag = tagElements[tagElements.length - 1]

      if (firstTag === targetElement) {
        $scrollWrapper.scrollLeft = 0
      } else if (lastTag === targetElement) {
        $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth
      } else {
        // find current, prev and next tag elements
        const currentIndex = Array.from(tagElements).findIndex(item => item === targetElement)

        if (currentIndex === -1) {
          return
        }

        const prevTag = tagElements[currentIndex - 1]
        const nextTag = tagElements[currentIndex + 1]

        if (nextTag) {
          // the tag's offsetLeft after of nextTag
          const afterNextTagOffsetLeft = nextTag.offsetLeft + nextTag.offsetWidth + tagAndTagSpacing

          if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {
            $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth
          }
        }

        if (prevTag) {
          // the tag's offsetLeft before of prevTag
          const beforePrevTagOffsetLeft = prevTag.offsetLeft - tagAndTagSpacing

          if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {
            $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll-container {
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  width: 100%;
  ::v-deep {
    .el-scrollbar__bar {
      bottom: 0px;
    }
    .el-scrollbar__wrap {
      height: 40px;
    }
  }
}
</style>
