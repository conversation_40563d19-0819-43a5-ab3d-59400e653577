<template>
  <div id="tags-view" class="tags-view">
    <scroll-pane
      ref="scrollPane"
      class="tags-view-wrapper"
      @scroll="handleScroll"
    >
      <div
        v-for="(tag, index) in visitedViews"
        :key="tag.path"
        :class="[
          'chrome-tab',
          {
            'active': isActive(tag),
            'hide-divider': tabDividerStates[index],
            'hovering': hoveringIndex === index
          }
        ]"
        @click="navigateToTag(tag)"
        @click.middle="!isAffix(tag) ? closeSelectedTag(tag) : ''"
        @contextmenu.prevent="openMenu(tag, $event)"
        @mouseenter="hoveringIndex = index"
        @mouseleave="hoveringIndex = -1"
      >
        <!-- Chrome 标签页背景 -->
        <chrome-tab-bg
          :class="[
            'chrome-tab-bg',
            isActive(tag) ? 'text-white' : 'text-gray-200'
          ]"
        />

        <!-- 标签页内容 -->
        <div class="chrome-tab-content">
          <svg-icon
            v-if="tag.meta.icon"
            :icon-class="tag.meta.icon"
            class="tab-icon"
          />
          <span class="tab-title">{{ tag.title }}</span>
          <button
            v-if="!isAffix(tag)"
            class="tab-close-btn"
            @click.prevent.stop="closeSelectedTag(tag)"
          >
            <i class="el-icon-close" />
          </button>
          <div v-else class="close-placeholder" />
        </div>
      </div>
    </scroll-pane>
    <ul
      v-show="visible"
      :style="{ left: left + 'px', top: top + 'px' }"
      class="contextmenu"
    >
      <li @click="refreshSelectedTag(selectedTag)">
        <i class="el-icon-refresh-right" /> 刷新页面
      </li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">
        <i class="el-icon-close" /> 关闭当前
      </li>
      <li @click="closeOthersTags">
        <i class="el-icon-circle-close" /> 关闭其他
      </li>
      <li v-if="!isFirstView()" @click="closeLeftTags">
        <i class="el-icon-back" /> 关闭左侧
      </li>
      <li v-if="!isLastView()" @click="closeRightTags">
        <i class="el-icon-right" /> 关闭右侧
      </li>
      <li @click="closeAllTags(selectedTag)">
        <i class="el-icon-circle-close" /> 全部关闭
      </li>
    </ul>
  </div>
</template>

<script>
import ScrollPane from './ScrollPane.vue'
import ChromeTabBg from './ChromeTabBg.vue'
import path from 'path'

export default {
  name: 'TagsView',
  components: { ScrollPane, ChromeTabBg },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      hoveringIndex: -1
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    },
    theme() {
      return this.$store.state.settings.theme
    },
    // 优化：分离基础状态计算，减少重复计算
    activeTabIndex() {
      return this.visitedViews.findIndex(tag => this.isActive(tag))
    },

    baseDividerStates() {
      return this.visitedViews.map((tag, index) => {
        const nextTag = this.visitedViews[index + 1]
        // 基于激活状态的分割线隐藏逻辑
        return this.isActive(tag) || (nextTag && this.isActive(nextTag))
      })
    },

    tabDividerStates() {
      // 结合基础状态和悬停状态
      return this.baseDividerStates.map((baseState, index) => {
        return baseState ||
               this.hoveringIndex === index ||
               this.hoveringIndex === index + 1
      })
    }
  },
  watch: {
    $route() {
      this.addTags()
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.initTags()
    this.addTags()
    this.addKeyboardListeners()
  },
  beforeDestroy() {
    this.removeKeyboardListeners()
  },
  methods: {
    // 基础判断方法
    isActive(route) {
      return route.path === this.$route.path
    },

    isAffix(tag) {
      return (tag.meta && tag.meta.affix) || tag.path === '/index'
    },

    // 优化：添加实用方法提高代码可读性
    isCurrentRoute(tag) {
      return tag.fullPath === this.$route.fullPath
    },

    hasValidName(route) {
      return route && route.name
    },
    navigateToTag(tag) {
      this.$router.push({ path: tag.path, query: tag.query, fullPath: tag.fullPath }).catch(err => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Navigation to tag failed:', err.message)
        }
      })
    },
    isFirstView() {
      if (!this.selectedTag || !this.selectedTag.fullPath) {
        return false
      }

      if (this.selectedTag.fullPath === '/index') {
        return true
      }

      if (this.visitedViews.length > 1) {
        return this.selectedTag.fullPath === this.visitedViews[1].fullPath
      }

      return false
    },
    isLastView() {
      if (!this.selectedTag || !this.selectedTag.fullPath || this.visitedViews.length === 0) {
        return false
      }

      return this.selectedTag.fullPath === this.visitedViews[this.visitedViews.length - 1].fullPath
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    // 标签页管理方法
    initTags() {
      // 初始化固定标签页
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes))
      for (const tag of affixTags) {
        if (this.hasValidName(tag)) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
    },
    addTags() {
      if (this.hasValidName(this.$route)) {
        this.$store.dispatch('tagsView/addView', this.$route)
        if (this.$route.meta.link) {
          this.$store.dispatch('tagsView/addIframeView', this.$route)
        }
      }
    },
    moveToCurrentTag() {
      this.$nextTick(() => {
        // 修复：正确获取标签元素
        const tagElements = this.$el.querySelectorAll('.chrome-tab')
        const currentPath = this.$route.path

        for (let i = 0; i < tagElements.length; i++) {
          const tagElement = tagElements[i]
          const tagData = this.visitedViews[i]

          if (tagData && tagData.path === currentPath) {
            this.$refs.scrollPane.moveToTarget(tagElement)
            // when query is different then update
            if (tagData.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
      })
    },
    refreshSelectedTag(view) {
      this.$tab.refreshPage(view)
      if (this.$route.meta.link) {
        this.$store.dispatch('tagsView/delIframeView', this.$route)
      }
    },
    closeSelectedTag(view) {
      this.$tab.closePage(view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    // 优化：提取公共的标签页关闭后处理逻辑
    handleTagsAfterClose(visitedViews) {
      if (!visitedViews.find(tag => this.isCurrentRoute(tag))) {
        this.toLastView(visitedViews)
      }
    },

    closeRightTags() {
      this.$tab.closeRightPage(this.selectedTag).then(this.handleTagsAfterClose)
    },

    closeLeftTags() {
      this.$tab.closeLeftPage(this.selectedTag).then(this.handleTagsAfterClose)
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag).catch(err => {
        // 改进：记录导航错误但不阻断流程
        if (process.env.NODE_ENV === 'development') {
          console.warn('Navigation to selected tag failed:', err.message)
        }
      })
      this.$tab.closeOtherPage(this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$tab.closeAllPage().then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === this.$route.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath).catch(err => {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Navigation to latest view failed:', err.message)
          }
        })
      } else {
        // 默认重定向到首页，如果没有可用的标签页
        if (view && view.name === 'Dashboard') {
          // 重新加载首页
          this.$router.replace({ path: '/redirect' + view.fullPath }).catch(err => {
            if (process.env.NODE_ENV === 'development') {
              console.warn('Redirect to dashboard failed:', err.message)
            }
          })
        } else {
          this.$router.push('/').catch(err => {
            if (process.env.NODE_ENV === 'development') {
              console.warn('Navigation to home failed:', err.message)
            }
          })
        }
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 10 // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY - 60
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    },
    handleKeyDown(event) {
      // if (event.key === 'Escape') {
      //   if (this.$route.path !== '/index') {
      //     this.closeSelectedTag(this.$route)
      //   }
      // }
    },
    // 新增：键盘事件管理方法
    addKeyboardListeners() {
      window.addEventListener('keydown', this.handleKeyDown)
    },
    removeKeyboardListeners() {
      window.removeEventListener('keydown', this.handleKeyDown)
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view {
  // CSS 自定义属性，提高可维护性
  --tags-view-height: 40px;
  --tags-view-padding: 6px 10px 0;
  --tab-height: 33px;
  --tab-min-width: 120px;
  --tab-max-width: 240px;
  --tab-transition: all 0.15s ease;
  --tab-border-radius: 6px;
  --divider-color: #dadce0;

  height: var(--tags-view-height);
  width: 100%;
  background: #ffffff;
  padding: var(--tags-view-padding);
  overflow: hidden;

  .tags-view-wrapper {
    height: 100%;

    .chrome-tab {
      display: inline-flex;
      align-items: center;
      position: relative;
      height: var(--tab-height);
      min-width: var(--tab-min-width);
      max-width: var(--tab-max-width);
      cursor: pointer;
      z-index: 1;
      transition: var(--tab-transition);

      // 未激活标签的竖线分割
      &:not(.hide-divider)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 8px;
        bottom: 8px;
        width: 1px;
        background: var(--divider-color);
        z-index: 2;
      }

      // 激活标签样式
      &.active {
        margin-left: -1px;
        margin-right: -1px;
      }

      &:hover:not(.active) {
        z-index: 2;

        .chrome-tab-content {
          background: #f1f1f1;
          border-radius: var(--tab-border-radius);
        }

      }

      &:hover.active {
        z-index: 3;

      }

      &.active {
        z-index: 3;

        .chrome-tab-bg {
          color: rgba(0, 122, 255, 0.1);
          filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.12));
        }

        .chrome-tab-content {
          color: var(--ios-blue);
          font-weight: 500;
          border-radius: var(--tab-border-radius);
          // margin: 0 8px;
        }

      }

      &:not(.active) {
        .chrome-tab-bg {
          color: transparent;
        }

        .chrome-tab-content {
          color: #5f6368;
        }

      }

      .chrome-tab-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      .chrome-tab-content {
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;
        padding: 6px 20px;
        font-size: 13px;

        .tab-icon {
          margin-right: 8px;
          font-size: 14px;
          flex-shrink: 0;
        }

        .tab-title {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0;
        }

        .tab-close-btn {
          margin-left: 8px;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: none;
          background: transparent;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.15s ease;
          flex-shrink: 0;

          &:hover {
            background: rgba(0, 0, 0, 0.2);
            border-radius: var(--ios-border-radius);
          }

          i {
            font-size: 10px;
          }
        }

        .close-placeholder {
          width: 16px;
          height: 16px;
          margin-left: 8px;
          flex-shrink: 0;
        }
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: var(--ios-background-secondary);
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 8px 0;
    border-radius: var(--ios-border-radius);
    font-size: 14px;
    font-weight: 400;
    color: var(--ios-text-primary);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--ios-border-color-light);
    backdrop-filter: blur(20px);

    li {
      margin: 0;
      padding: 10px 20px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--ios-fill-quaternary);
      }

      i {
        margin-right: 8px;
        font-size: 14px;
      }
    }
  }
}

// 适配侧边栏展开/折叠状态
.app-wrapper:not(.hideSidebar) .tags-view-container {
  padding-left: 20px;
  padding-right: 20px;
}

.app-wrapper.hideSidebar .tags-view-container {
  padding-left: 16px;
  padding-right: 16px;
}
</style>
