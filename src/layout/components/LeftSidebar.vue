<template>
  <div v-if="!shouldHideSidebar && !isMobile" class="left-sidebar w-40 bg-white bg-opacity-95 backdrop-blur-xl border-r border-gray-100 fixed top-15 left-0 z-40 flex flex-col transition-all duration-300 shadow-sm" :class="{ 'collapsed': shouldCollapse }" style="height: calc(100vh - 60px);">
    <!-- Child Menu Items -->
    <div class="sidebar-content flex-1">
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <div class="menu-list p-2">
          <!-- 使用递归组件渲染多级菜单 -->
          <sidebar-menu-item
            v-for="(child, index) in childRoutes"
            :key="child.path + index"
            :item="child"
            :base-path="getChildPath(child)"
            :level="0"
            :collapsed="shouldCollapse"
          />
        </div>
      </el-scrollbar>
    </div>

    <!-- Collapse But<PERSON> at Bottom -->
    <div class="sidebar-footer p-3  flex items-center justify-center">
      <div class="collapse-btn w-10 h-10 rounded-md flex items-center justify-center cursor-pointer text-gray-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-100 " @click="toggleCollapse">
        <i v-if="!isCollapsed" class="el-icon-s-fold text-sm" />
        <i v-if="isCollapsed" class="el-icon-s-unfold text-sm" />
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SidebarMenuItem from './SidebarMenuItem.vue'
import layoutCache from '@/utils/layoutCache'

export default {
  name: 'LeftSidebar',
  components: {
    SidebarMenuItem
  },
  data() {
    return {
      isCollapsed: this.getCollapsedStateFromCache()
    }
  },
  computed: {
    ...mapGetters(['device']),
    // Auto-collapse on mobile
    shouldCollapse() {
      return this.device === 'mobile' || this.isCollapsed
    },
    // Get current parent route from store
    currentParentRoute() {
      return this.$store.state.permission.currentParentRoute || null
    },

    // Get child routes for current parent
    childRoutes() {
      if (!this.currentParentRoute || !this.currentParentRoute.children) {
        return []
      }
      return this.currentParentRoute.children.filter(child => !child.hidden && child.meta)
    },

    // Auto-hide sidebar for single child routes
    shouldHideSidebar() {
      // return this.childRoutes.length <= 1
      return this.$route.path === '/index'
    },

    // Check if current device is mobile
    isMobile() {
      return this.device === 'mobile'
    }
  },
  mounted() {
    // Initialize store with current collapse state from cache
    this.$store.commit('SET_SIDEBAR_COLLAPSED', this.isCollapsed)
  },
  methods: {
    // Get child path for menu item
    getChildPath(childRoute) {
      if (childRoute.path.startsWith('/')) {
        return childRoute.path
      }

      if (!this.currentParentRoute) {
        return childRoute.path
      }

      const parentPath = this.currentParentRoute.path
      return parentPath.endsWith('/')
        ? `${parentPath}${childRoute.path}`
        : `${parentPath}/${childRoute.path}`
    },

    // Toggle sidebar collapse
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
      // Update the store with collapse state for main layout to react
      this.$store.commit('SET_SIDEBAR_COLLAPSED', this.isCollapsed)
      // Save collapse state to cache
      this.saveCollapsedStateToCache(this.isCollapsed)
    },

    // Get collapsed state from cache
    getCollapsedStateFromCache() {
      // Don't use cache on mobile devices
      if (layoutCache.isMobileDevice()) {
        return false
      }

      return layoutCache.getSidebarCollapsed()
    },

    // Save collapsed state to cache
    saveCollapsedStateToCache(isCollapsed) {
      // Don't save cache on mobile devices
      if (layoutCache.isMobileDevice()) {
        return
      }

      layoutCache.setSidebarCollapsed(isCollapsed)
    }

  }
}
</script>

<style lang="scss" scoped>
.left-sidebar {
  border-right: 1px solid var(--ios-light-gray-2);
  transition: width 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

  .sidebar-header {
    border-bottom: 1px solid var(--ios-separator-color);

    .parent-icon {
      color: var(--ios-blue);
    }

    .parent-name {
      color: var(--ios-text-primary);
    }

    .collapse-btn {
      border-radius: var(--ios-border-radius-small);
      color: var(--ios-text-secondary);

      &:hover {
        background: var(--ios-fill-quaternary);
        color: var(--ios-blue);
      }
    }
  }

  .menu-item {
    border-radius: var(--ios-border-radius);
    transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
    color: var(--ios-text-secondary);

    &:hover {
      background: var(--ios-fill-quaternary);
      color: var(--ios-blue);
      transform: translateX(4px);
    }

    &.active {
      background-color: var(--ios-blue);
      color: white;

      &:hover {
        color: var(--ios-blue-dark);
      }
    }
  }

  .user-section {
    border-top: 1px solid var(--ios-separator-color);

    .user-avatar-wrapper {
      border-radius: var(--ios-border-radius);

      &:hover {
        background: var(--ios-fill-quaternary);
      }

      .user-name {
        color: var(--ios-text-primary);
      }

      .user-phone {
        color: var(--ios-text-secondary);
      }
    }
  }

  // Collapsed state adjustments
  &.collapsed {
    width: 64px;
    .menu-item {
      .menu-title {
        display: none;
      }
    }
  }
}

</style>
