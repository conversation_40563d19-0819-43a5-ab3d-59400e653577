<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item label="请求下发ID" prop="requestId">
        <el-input
          v-model="queryParams.requestId"
          placeholder="请输入请求下发ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告计划ID" prop="aid">
        <el-input
          v-model="queryParams.aid"
          placeholder="请输入广告计划ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告主ID" prop="advertiserId">
        <el-input
          v-model="queryParams.advertiserId"
          placeholder="请输入广告主ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告创意ID" prop="cid">
        <el-input
          v-model="queryParams.cid"
          placeholder="请输入广告创意ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告组ID" prop="campaignId">
        <el-input
          v-model="queryParams.campaignId"
          placeholder="请输入广告组ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化ID" prop="convertId">
        <el-input
          v-model="queryParams.convertId"
          placeholder="请输入转化ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目ID" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告ID" prop="promotionId">
        <el-input
          v-model="queryParams.promotionId"
          placeholder="请输入广告ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告名称" prop="promotionName">
        <el-input
          v-model="queryParams.promotionName"
          placeholder="请输入广告名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="点击时间(毫秒)" prop="ts">
        <el-input
          v-model="queryParams.ts"
          placeholder="请输入点击时间(毫秒)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投放位置" prop="csite">
        <el-input
          v-model="queryParams.csite"
          placeholder="请输入投放位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求语言" prop="sl">
        <el-input
          v-model="queryParams.sl"
          placeholder="请输入请求语言"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备ID_MD5" prop="imei">
        <el-input
          v-model="queryParams.imei"
          placeholder="请输入设备ID_MD5"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告标识" prop="caid">
        <el-input
          v-model="queryParams.caid"
          placeholder="请输入广告标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备ID" prop="idfa">
        <el-input
          v-model="queryParams.idfa"
          placeholder="请输入设备ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安卓ID" prop="androidId">
        <el-input
          v-model="queryParams.androidId"
          placeholder="请输入安卓ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备号" prop="oaId">
        <el-input
          v-model="queryParams.oaId"
          placeholder="请输入设备号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备号MD5" prop="oaIdMd5">
        <el-input
          v-model="queryParams.oaIdMd5"
          placeholder="请输入设备号MD5"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作系统平台" prop="os">
        <el-input
          v-model="queryParams.os"
          placeholder="请输入操作系统平台"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="mac地址" prop="mac">
        <el-input
          v-model="queryParams.mac"
          placeholder="请输入mac地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="mac地址2" prop="macOne">
        <el-input
          v-model="queryParams.macOne"
          placeholder="请输入mac地址2"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IP地址" prop="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="请输入IP地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机型号" prop="model">
        <el-input
          v-model="queryParams.model"
          placeholder="请输入手机型号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户代理" prop="ua">
        <el-input
          v-model="queryParams.ua"
          placeholder="请输入用户代理"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:monitor:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:monitor:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:monitor:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:monitor:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="monitorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键ID" align="center" prop="id" />
      <el-table-column label="请求下发ID" align="center" prop="requestId" />
      <el-table-column label="广告计划ID" align="center" prop="aid" />
      <el-table-column label="广告主ID" align="center" prop="advertiserId" />
      <el-table-column label="广告创意ID" align="center" prop="cid" />
      <el-table-column label="广告组ID" align="center" prop="campaignId" />
      <el-table-column label="转化ID" align="center" prop="convertId" />
      <el-table-column label="项目ID" align="center" prop="projectId" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="广告ID" align="center" prop="promotionId" />
      <el-table-column label="广告名称" align="center" prop="promotionName" />
      <el-table-column label="回调参数" align="center" prop="callbackParam" />
      <el-table-column label="点击时间(毫秒)" align="center" prop="ts" />
      <el-table-column label="创意样式" align="center" prop="ctype" />
      <el-table-column label="投放位置" align="center" prop="csite" />
      <el-table-column label="请求语言" align="center" prop="sl" />
      <el-table-column label="设备ID_MD5" align="center" prop="imei" />
      <el-table-column label="广告标识" align="center" prop="caid" />
      <el-table-column label="设备ID" align="center" prop="idfa" />
      <el-table-column label="安卓ID" align="center" prop="androidId" />
      <el-table-column label="设备号" align="center" prop="oaId" />
      <el-table-column label="设备号MD5" align="center" prop="oaIdMd5" />
      <el-table-column label="操作系统平台" align="center" prop="os" />
      <el-table-column label="mac地址" align="center" prop="mac" />
      <el-table-column label="mac地址2" align="center" prop="macOne" />
      <el-table-column label="IP地址" align="center" prop="ip" />
      <el-table-column label="手机型号" align="center" prop="model" />
      <el-table-column label="用户代理" align="center" prop="ua" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['promotion:monitor:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['promotion:monitor:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改商品监测对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="请求下发ID" prop="requestId">
          <el-input v-model="form.requestId" placeholder="请输入请求下发ID" />
        </el-form-item>
        <el-form-item label="广告计划ID" prop="aid">
          <el-input v-model="form.aid" placeholder="请输入广告计划ID" />
        </el-form-item>
        <el-form-item label="广告主ID" prop="advertiserId">
          <el-input v-model="form.advertiserId" placeholder="请输入广告主ID" />
        </el-form-item>
        <el-form-item label="广告创意ID" prop="cid">
          <el-input v-model="form.cid" placeholder="请输入广告创意ID" />
        </el-form-item>
        <el-form-item label="广告组ID" prop="campaignId">
          <el-input v-model="form.campaignId" placeholder="请输入广告组ID" />
        </el-form-item>
        <el-form-item label="转化ID" prop="convertId">
          <el-input v-model="form.convertId" placeholder="请输入转化ID" />
        </el-form-item>
        <el-form-item label="项目ID" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目ID" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="广告ID" prop="promotionId">
          <el-input v-model="form.promotionId" placeholder="请输入广告ID" />
        </el-form-item>
        <el-form-item label="广告名称" prop="promotionName">
          <el-input v-model="form.promotionName" placeholder="请输入广告名称" />
        </el-form-item>
        <el-form-item label="回调参数" prop="callbackParam">
          <el-input v-model="form.callbackParam" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="点击时间(毫秒)" prop="ts">
          <el-input v-model="form.ts" placeholder="请输入点击时间(毫秒)" />
        </el-form-item>
        <el-form-item label="投放位置" prop="csite">
          <el-input v-model="form.csite" placeholder="请输入投放位置" />
        </el-form-item>
        <el-form-item label="请求语言" prop="sl">
          <el-input v-model="form.sl" placeholder="请输入请求语言" />
        </el-form-item>
        <el-form-item label="设备ID_MD5" prop="imei">
          <el-input v-model="form.imei" placeholder="请输入设备ID_MD5" />
        </el-form-item>
        <el-form-item label="广告标识" prop="caid">
          <el-input v-model="form.caid" placeholder="请输入广告标识" />
        </el-form-item>
        <el-form-item label="设备ID" prop="idfa">
          <el-input v-model="form.idfa" placeholder="请输入设备ID" />
        </el-form-item>
        <el-form-item label="安卓ID" prop="androidId">
          <el-input v-model="form.androidId" placeholder="请输入安卓ID" />
        </el-form-item>
        <el-form-item label="设备号" prop="oaId">
          <el-input v-model="form.oaId" placeholder="请输入设备号" />
        </el-form-item>
        <el-form-item label="设备号MD5" prop="oaIdMd5">
          <el-input v-model="form.oaIdMd5" placeholder="请输入设备号MD5" />
        </el-form-item>
        <el-form-item label="操作系统平台" prop="os">
          <el-input v-model="form.os" placeholder="请输入操作系统平台" />
        </el-form-item>
        <el-form-item label="mac地址" prop="mac">
          <el-input v-model="form.mac" placeholder="请输入mac地址" />
        </el-form-item>
        <el-form-item label="mac地址2" prop="macOne">
          <el-input v-model="form.macOne" placeholder="请输入mac地址2" />
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="手机型号" prop="model">
          <el-input v-model="form.model" placeholder="请输入手机型号" />
        </el-form-item>
        <el-form-item label="用户代理" prop="ua">
          <el-input v-model="form.ua" placeholder="请输入用户代理" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMonitor, getMonitor, delMonitor, addMonitor, updateMonitor } from '@/api/promotion/monitor'
import SavedSearches from '@/components/SavedSearches/index.vue'

export default {
  name: 'Monitor',
  components: { SavedSearches },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品监测表格数据
      monitorList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestId: null,
        aid: null,
        advertiserId: null,
        cid: null,
        campaignId: null,
        convertId: null,
        projectId: null,
        projectName: null,
        promotionId: null,
        promotionName: null,
        callbackParam: null,
        ts: null,
        ctype: null,
        csite: null,
        sl: null,
        imei: null,
        caid: null,
        idfa: null,
        androidId: null,
        oaId: null,
        oaIdMd5: null,
        os: null,
        mac: null,
        macOne: null,
        ip: null,
        model: null,
        ua: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询商品监测列表 */
    getList() {
      this.loading = true
      listMonitor(this.queryParams).then(response => {
        this.monitorList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        requestId: null,
        aid: null,
        advertiserId: null,
        cid: null,
        campaignId: null,
        convertId: null,
        projectId: null,
        projectName: null,
        promotionId: null,
        promotionName: null,
        callbackParam: null,
        ts: null,
        ctype: null,
        csite: null,
        sl: null,
        imei: null,
        caid: null,
        idfa: null,
        androidId: null,
        oaId: null,
        oaIdMd5: null,
        os: null,
        mac: null,
        macOne: null,
        ip: null,
        model: null,
        ua: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加商品监测'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getMonitor(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改商品监测'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMonitor(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addMonitor(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除商品监测编号为"' + ids + '"的数据项？').then(function() {
        return delMonitor(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('promotion/monitor/export', {
        ...this.queryParams
      }, `monitor_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
