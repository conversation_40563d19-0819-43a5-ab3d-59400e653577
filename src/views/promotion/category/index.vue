<template>
  <div class="category-container">
    <div class="operation-header">
      <el-button
        v-hasPermi="['promotion:category:add']"
        type="primary"
        size="mini"
        icon="el-icon-plus"
        @click="handleAdd"
      >
        新增分类
      </el-button>
      <div class="flex">
        <el-input
          v-model="queryParams.categoryName"
          placeholder="请输入类别名称"
          @keyup.enter.native="handleQuery"
        >
          <template #append>
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="handleQuery"
            />
          </template>
        </el-input>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery" />
      </div>
    </div>
    <div class="category-table-wrap">
      <el-table
        ref="categoryTable"
        v-loading="loading"
        height="100%"
        :data="categoryList"
        @select="handleCurrentChange"
        @cell-click="handleCellClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="类别名称" prop="categoryName">
          <template #default="scope">
            <div class="category-col pointer">
              <div
                class="category-name overflow-text"
                :title="scope.row.categoryName"
              >
                {{ scope.row.categoryName }}
              </div>
              <div v-if="!selector" class="category-r">
                <div class="category-operation">
                  <template v-if="!scope.row.communal">
                    <el-button
                      v-hasPermi="['promotion:category:edit']"
                      type="text"
                      size="mini"
                      icon="el-icon-edit"
                      @click.stop="handleUpdate(scope.row)"
                    >编辑</el-button>
                    <el-button
                      v-hasPermi="['promotion:category:remove']"
                      type="text"
                      size="mini"
                      style="color: #ff4d4f"
                      icon="el-icon-delete"
                      @click.stop="handleDelete(scope.row)"
                    >删除</el-button>
                  </template>
                  <template v-else>
                    <span style="font-size: 12px; color: #7a7a7a">
                      共享资源不可编辑
                    </span>
                  </template>
                </div>
                <i class="el-icon-arrow-right" />
              </div>
              <i v-else class="el-icon-arrow-right" style="opacity: 1" />
            </div>
          </template>
        </el-table-column>
        <!--        <el-table-column v-if="isEdit" label="操作" width="110px">-->
        <!--          <template #default="scope">-->
        <!--            <el-button-->
        <!--              v-hasPermi="['promotion:category:edit']"-->
        <!--              type="text"-->
        <!--              size="mini"-->
        <!--              icon="el-icon-edit"-->
        <!--              @click.stop="handleUpdate(scope.row)"-->
        <!--            >编辑</el-button>-->
        <!--            <el-button-->
        <!--              v-hasPermi="['promotion:category:remove']"-->
        <!--              type="text"-->
        <!--              size="mini"-->
        <!--              style="color: #ff4d4f;"-->
        <!--              icon="el-icon-delete"-->
        <!--              @click.stop="handleDelete(scope.row)"-->
        <!--            >删除</el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      layout="total, prev, pager, next"
      @pagination="getList"
    />

    <!-- 添加或修改资源类别对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="类别名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入类别名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCategory,
  getCategory,
  delCategory,
  addCategory,
  updateCategory
} from '@/api/promotion/category'

const CategoryMap = {
  H5表单附件: '1657922264756486166',
  备案资源: '1657922264756486177',
  店铺: '1657922264756486146',
  原始商品资源: '1657921982836342785',
  导出资源: '1656556731859800000',
  落地页: '1655938287653191682'
}
export default {
  name: 'Category',
  props: {
    selector: {
      type: Boolean,
      default: false
    },
    defaultSelected: {
      type: String,
      default: ''
    }
  },
  emits: ['categoryChange'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资源类别表格数据
      categoryList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: '类别名称不能为空', trigger: 'blur' }
        ]
      },
      isEdit: false,
      currentRow: null
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.loading = true
      listCategory(this.queryParams).then((response) => {
        this.categoryList = response.rows
        if (this.defaultSelected) {
          if (this.defaultSelected === 'H5表单附件') {
            this.categoryList = this.categoryList.filter(
              (item) => item.id === CategoryMap[this.defaultSelected]
            )
          }
          const cell = this.categoryList.find(
            (item) => item.id === CategoryMap[this.defaultSelected]
          )
          if (cell) {
            this.$nextTick(() => {
              this.$refs.categoryTable.toggleRowSelection(cell)
              this.handleCurrentChange([cell])
            })
          }
        }
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询资源类别列表 */
    getList() {
      this.loading = true
      listCategory(this.queryParams).then((response) => {
        this.categoryList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        categoryName: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        categoryName: null
      }
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加资源类别'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCategory(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改资源类别'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateCategory(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addCategory(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除资源类别编号为"' + ids + '"的数据项？')
        .then(function() {
          return delCategory(ids)
        })
        .then(() => {
          this.$emit('categoryChange', undefined)
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    handleCellClick(cell) {
      this.$refs.categoryTable.clearSelection()
      if (this.currentRow !== cell) {
        this.$refs.categoryTable.toggleRowSelection(cell)
        this.handleCurrentChange([cell])
      } else {
        this.$refs.categoryTable.toggleRowSelection(cell, false)
        this.handleCurrentChange([])
      }
    },
    handleCurrentChange(selection) {
      this.currentRow = selection[selection.length - 1]
      if (selection.length > 1) {
        this.$refs.categoryTable.toggleRowSelection(selection.shift(), false)
      }
      this.$emit('categoryChange', this.currentRow)
    }
  }
}
</script>

<style lang="scss" scoped>
.category-container {
  min-width: 355px;
  padding: 10px;
  border: 1px solid #eaeaea;
}
.operation-header {
  display: flex;
  margin-bottom: 22px;
}

.category-table-wrap {
  height: calc(100vh - 250px);
}
.category-col {
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: opacity 0.3s;
  &:hover .category-name {
    text-decoration: underline;
  }
  .category-name {
    flex: 1;
    //line-height: 56.5px;
    padding: 0 10px;
  }
  .category-r {
    display: flex;
    align-items: center;
  }
  .category-operation {
    opacity: 0;
  }
  .el-icon-arrow-right {
    opacity: 1;
  }
  &:hover {
    .category-operation {
      opacity: 1;
    }
    .el-icon-arrow-right {
      opacity: 0;
    }
  }
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
::v-deep .el-form-item--small.el-form-item {
  margin: 0;
}
</style>
