<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item label="商品ID" prop="productId">
        <el-input
          v-model="queryParams.productId"
          placeholder="请输入商品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="landingPageType">
        <el-select v-model="queryParams.landingPageType" placeholder="请选择类型" clearable>
          <el-option
            v-for="dict in dict.type.landing_page_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createdTime">
        <el-date-picker
          v-model="queryParams.createdTime"
          clearable
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:landing:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:landing:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:landing:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:landing:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="landingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="商品ID" align="center" prop="productId" />
      <el-table-column label="类型" align="center" prop="landingPageType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.landing_page_type" :value="scope.row.landingPageType" />
        </template>
      </el-table-column>
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建人" align="center" prop="createdBy" />
      <el-table-column label="创建时间" align="center" prop="createdTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['promotion:landing:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['promotion:landing:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改落地页对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品ID" prop="productId">
          <el-input v-model="form.productId" placeholder="请输入商品ID" />
        </el-form-item>
        <el-form-item label="类型(A-审核页、B-投放页)" prop="landingPageType">
          <el-radio-group v-model="form.landingPageType">
            <el-radio
              v-for="dict in dict.type.landing_page_type"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="创建人" prop="createdBy">
          <el-input v-model="form.createdBy" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createdTime">
          <el-date-picker
            v-model="form.createdTime"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间"
          />
        </el-form-item>
        <el-divider content-position="center">落地页资源信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddLandingPageResource">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteLandingPageResource">删除</el-button>
          </el-col>
        </el-row>
        <el-table ref="landingPageResource" :data="landingPageResourceList" :row-class-name="rowLandingPageResourceIndex" @selection-change="handleLandingPageResourceSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50" />
          <el-table-column label="资源链接" prop="resourceUrl" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.resourceUrl" placeholder="请输入资源链接" />
            </template>
          </el-table-column>
          <el-table-column label="资源类型" prop="resourceType" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.resourceType" placeholder="请选择资源类型">
                <el-option
                  v-for="dict in dict.type.landing_page_resource_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLanding, getLanding, delLanding, addLanding, updateLanding } from '@/api/promotion/landing'
import SavedSearches from '@/components/SavedSearches/index.vue'

export default {
  name: 'Landing',
  dicts: ['landing_page_type', 'landing_page_resource_type'],
  components: { SavedSearches },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedLandingPageResource: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 落地页表格数据
      landingList: [],
      // 落地页资源表格数据
      landingPageResourceList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productId: null,
        landingPageType: null,
        title: null,
        createdBy: null,
        createdTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productId: [
          { required: true, message: '商品ID不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询落地页列表 */
    getList() {
      this.loading = true
      listLanding(this.queryParams).then(response => {
        this.landingList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        productId: null,
        landingPageType: null,
        title: null,
        remark: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      }
      this.landingPageResourceList = []
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加落地页'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getLanding(id).then(response => {
        this.form = response.data
        this.landingPageResourceList = response.data.landingPageResourceList
        this.open = true
        this.title = '修改落地页'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.landingPageResourceList = this.landingPageResourceList
          if (this.form.id != null) {
            updateLanding(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addLanding(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除落地页编号为"' + ids + '"的数据项？').then(function() {
        return delLanding(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 落地页资源序号 */
    rowLandingPageResourceIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 落地页资源添加按钮操作 */
    handleAddLandingPageResource() {
      const obj = {}
      obj.resourceUrl = ''
      obj.resourceType = ''
      this.landingPageResourceList.push(obj)
    },
    /** 落地页资源删除按钮操作 */
    handleDeleteLandingPageResource() {
      if (this.checkedLandingPageResource.length == 0) {
        this.$modal.msgError('请先选择要删除的落地页资源数据')
      } else {
        const landingPageResourceList = this.landingPageResourceList
        const checkedLandingPageResource = this.checkedLandingPageResource
        this.landingPageResourceList = landingPageResourceList.filter(function(item) {
          return checkedLandingPageResource.indexOf(item.index) == -1
        })
      }
    },
    /** 复选框选中数据 */
    handleLandingPageResourceSelectionChange(selection) {
      this.checkedLandingPageResource = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('promotion/landing/export', {
        ...this.queryParams
      }, `landing_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
