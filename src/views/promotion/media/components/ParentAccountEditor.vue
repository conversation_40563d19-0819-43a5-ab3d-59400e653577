<script setup>
import { nextTick, onMounted, reactive, ref } from 'vue'
import { addParent, getAccountChildren, listMedia, updateParent } from '@/api/promotion/media'
import { Message } from 'element-ui'

const props = defineProps({
  parent: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    default: 'add' // add, update
  }
})
const emit = defineEmits(['success', 'cancel'])

const isCustom = props.parent.advertiserId.includes('--')

const tableRef = ref(null)
const queryParams = ref({
  advertiserId: null,
  advertiserName: null
})

const handleFilterAccount = () => {
  displayList.value = [...list.value]
  let { advertiserId, advertiserName } = queryParams.value
  if (advertiserId || advertiserName) {
    advertiserId = advertiserId || null
    advertiserName = advertiserName || null
    displayList.value = displayList.value.filter(item => {
      return item.advertiserId === advertiserId || !!~item.advertiserName.indexOf(advertiserName)
    })
  }

  nextTick(() => {
    selectedAccount.value.forEach(item => {
      tableRef.value.toggleRowSelection(item, true)
    })
  })
}

const resetFilterAccount = () => {
  queryParams.value = {
    advertiserId: null,
    advertiserName: null
  }
  displayList.value = [...list.value]

  nextTick(() => {
    selectedAccount.value.forEach(item => {
      tableRef.value.toggleRowSelection(item, true)
    })
  })
}

const loading = ref(false)
const list = ref([])
const displayList = ref([])

const fetchList = () => {
  loading.value = true
  listMedia({
    parentAccountId: props.parent.advertiserId
  }).then(response => {
    if (response.code === 200) {
      list.value = response.rows
      displayList.value = [...list.value]
    }
  }).finally(() => {
    loading.value = false
  })
}
const fetchChildrenList = () => {
  loading.value = true
  getAccountChildren({
    advertiserId: props.parent.advertiserId,
    showParent: true
  }).then(response => {
    if (response.code === 200) {
      list.value = response.data
      displayList.value = [...list.value]
      displayList.value.forEach(item => {
        if (item?.params.checked) {
          selectedAccount.value.push(item)
          nextTick(() => {
            tableRef.value.toggleRowSelection(item, true)
          })
        }
      })
    }
  }).finally(() => {
    loading.value = false
  })
}
onMounted(() => {
  if (isCustom) {
    fetchChildrenList()
  } else {
    fetchList()
  }
})

const selectedAccount = ref([])
const handleSelectionChange = (selection) => {
  selectedAccount.value = selection
}

const removeAdvertiser = (row) => {
  const account = selectedAccount.value.find(item => item.advertiserId === row.advertiserId)
  if (account) {
    tableRef.value.toggleRowSelection(account, false)
    selectedAccount.value = selectedAccount.value.filter(item => item.advertiserId !== row.advertiserId)
  }
}

const accountForm = reactive({
  pid: '',
  advertiserName: '',
  children: []
})
if (props.type === 'update') accountForm.advertiserName = props.parent.advertiserName + ''

const formRef = ref(null)
const rules = {
  advertiserName: [
    { required: true, message: '请输入账户名称', trigger: 'blur' }
  ]
}

const submit = () => {
  if (props.type === 'add') {
    if (selectedAccount.value.length === 0) {
      Message.error('请选择账户')
      return
    }
    accountForm.children = selectedAccount.value.map(item => item.id)
    formRef.value.validate(valid => {
      if (!valid) return
      accountForm.pid = props.parent.id
      addParent(accountForm).then(response => {
        if (response.code === 200) {
          Message.success('添加成功')
          emit('success')
        }
      })
    })
  } else {
    const postData = {
      pid: props.parent.id,
      advertiserName: accountForm.advertiserName
    }
    if (isCustom) postData.children = selectedAccount.value.map(item => item.id)
    updateParent(postData).then(response => {
      if (response.code === 200) {
        Message.success('修改成功')
        emit('success')
      }
    })
  }
}
const cancel = () => {
  emit('cancel')
}
</script>

<template>
  <div>
    <template v-if="isCustom || type === 'add'">
      <el-form ref="authQueryForm" :model="queryParams" size="small" :inline="true" label-width="80px">
        <el-form-item label="账户ID" prop="advertiserId">
          <el-input
            v-model="queryParams.advertiserId"
            placeholder="请输入账户ID"
            clearable
            @keyup.enter.native="handleFilterAccount"
          />
        </el-form-item>
        <el-form-item label="账户名称" prop="advertiserName">
          <el-input
            v-model="queryParams.advertiserName"
            placeholder="请输入账户名称"
            clearable
            @keyup.enter.native="handleFilterAccount"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilterAccount">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetFilterAccount">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="table-wrap">
        <el-table
          ref="tableRef"
          v-loading="loading"
          height="100%"
          :data="displayList"
          border
          stripe
          @select="handleSelectionChange"
          @select-all="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="账户名称" prop="advertiserName" />
          <el-table-column label="账户ID" align="center" prop="advertiserId" width="180" />
        </el-table>
      </div></template>

    <div class="selection-wrapper">
      <el-form ref="formRef" :model="accountForm" label-width="100px" :rules="rules">
        <el-form-item label="账户名称" prop="advertiserName">
          <el-input v-model="accountForm.advertiserName" style="width: 200px" />
        </el-form-item>
        <el-form-item v-if="isCustom || type === 'add'" :label="`已选择(${selectedAccount.length})`">
          <div class="selected-list">
            <el-tag
              v-for="s in selectedAccount"
              :key="s.advertiserId"
              style="margin:10px 10px 0 0"
              closable
              :disable-transitions="false"
              @close="removeAdvertiser(s)"
            >{{ s.advertiserName }}</el-tag>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button type="primary" @click="submit">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.table-wrap {
  height: calc(100vh - 550px)
}

.selected-list {
  height: 150px;
  overflow: auto;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding:0 10px;
}
.selection-wrapper {
  margin-top: 20px;
}
.dialog-footer {
  display: flex;
  justify-content: right;
}
</style>
