<script setup>
import { computed, ref } from 'vue'
import dayjs from 'dayjs'
import { Message } from 'element-ui'
import { synchronizeReportPlan } from '@/api/promotion/media'
const props = defineProps({
  value: {
    type: Boolean,
    default: false
  },
  ids: {
    type: Array,
    default: () => []
  },
  defaultDateRange: {
    type: Array,
    default: () => [dayjs(new Date()).format('YYYY-MM-DD'), dayjs(new Date()).format('YYYY-MM-DD')]
  }
})
const loading = ref(false)
const emit = defineEmits(['Refresh', 'input'])
const visibleState = computed({
  get: () => props.value,
  set: (val) => {
    emit('input', val)
  }
})
const dataForm = ref({
  dateRange: props.defaultDateRange
})
const ruleForm = ref()
const submit = () => {
  ruleForm.value.validate((valid) => {
    if (valid) {
      const diff = dayjs(dataForm.value.dateRange[1]).diff(dayjs(dataForm.value.dateRange[0]), 'days') + 1
      if (diff > 31) {
        Message.error('时间范围不能超过31天')
        return
      }
      const data = {
        advertiserIds: props.ids,
        startDay: dataForm.value.dateRange[0],
        endDay: dataForm.value.dateRange[1]
      }
      loading.value = true
      synchronizeReportPlan(data).then(data => {
        if (data.code === 200) {
          Message.success('同步成功，请等待10分钟查询执行结果。')
          emit('Refresh')
          visibleState.value = false
        } else {
          Message.error(data.msg)
        }
      }).finally(() => {
        loading.value = false
      })
    } else {
      return false
    }
  })
}
function cancel() {
  visibleState.value = false
}
const rules = {
  dateRange: [
    { required: true, message: '请选择时间范围', trigger: 'blur' }
  ]
}

</script>

<template>
  <div>
    <el-dialog
      title="批量同步计划报表"
      :visible.sync="visibleState"
      width="500px"
      top="10vh"
      append-to-body
    >
      <el-form ref="ruleForm" label-width="80px" :model="dataForm" :rules="rules">
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="dataForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :clearable="false"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-loading="loading" type="primary" @click="submit">确认</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
