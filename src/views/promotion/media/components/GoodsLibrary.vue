<script setup>
import { dpaClueProductSave, getGoodsLibrary } from '@/api/promotion/media'
import { computed, reactive, ref, watch } from 'vue'
import { Message } from 'element-ui'
import goodsComponent from './goodsComponent.vue'
const props = defineProps({
  visible: <PERSON><PERSON>an,
  advertiserIds: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['update:visible', 'update:form'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

watch(() => props.visible, (val) => {
  if (val) {
    init()
  }
})

const loading = reactive({
  mall: false
})

// /*商品库行业*/
// private String productIndustry;
const selectRow = ref(null)
const init = () => {
  mallFilterStr.value = ''
  filterMalls.value = []
  selectMall.value = null
  selectRow.value = null
}

const mallFilterStr = ref('')
const filterMalls = ref([])
const selectMall = ref(null)

// /*商品库ID*/
// private Long platformId;
const goodsComponentRef = ref()
const submit = () => {
  if (!goodsComponentRef.value.ids || !goodsComponentRef.value.ids.length) {
    Message.warning('请选择商品')
    return
  }

  dpaClueProductSave({
    advertiserIds: props.advertiserIds,
    ids: goodsComponentRef.value.ids
  }).then(res => {
    Message.success(res.msg)
    open.value = false
  })
}
</script>

<template>
  <el-dialog :visible.sync="open" title="同步商品库" top="5vh" width="80%">
    <el-row :gutter="20">
      <el-col :span="24">
        <goods-component v-if="open" ref="goodsComponentRef" />
      </el-col>

    </el-row>
    <template #footer>
      <el-button @click="open = false">取 消</el-button>
      <el-button type="primary" @click="submit">确认</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

.sync-text {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  align-items: center;
  padding-top: 200px;
}
.font-weight {
  font-weight: bold;
}
</style>
