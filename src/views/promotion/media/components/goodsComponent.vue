<template>
  <div class="adaption-container">

    <el-form v-show="showSearch" ref="queryForm" class="search-form" :model="queryParams" size="small" :inline="true" label-width="80px">
      <el-form-item prop="platform">
        <el-select v-model="queryParams.platform" placeholder="平台类型" clearable @change="handleQuery">
          <el-option
            v-for="dict in dict.type.platform_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="goodsId">
        <el-input
          v-model.trim="queryParams.goodsId"
          placeholder="商品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item prop="goodsName">
        <el-input
          v-model.trim="queryParams.goodsName"
          placeholder="商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="mallId">
        <MallSelect ref="mallSelect" v-model="queryParams.mallId" manual @select="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">

      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="goodsList"
        v-bind="tableHeight"

        row-key="id"
        stripe
        border
        :row-class-name="handleRowClass"
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
        @sort-change="handleTableSort"
      >
        <el-table-column type="selection" width="55" align="center" reserve-selection />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50"
          fixed
        />
        <template v-for="(c,i) in columns">
          <el-table-column v-if="c.visible !==false" :key="i" :label="c.label" :align="c.align || 'center'" :prop="c.prop" :width="c.width" :fixed="c.fixed && device !== 'mobile'" :sortable="c.sortable" :show-overflow-tooltip="c.overflow">
            <template #header="scope">
              <span>{{ scope.column.label }}</span>
              <el-tooltip v-if="c.tooltip" effect="dark" :content="c.tooltip" placement="top">
                <i class="el-icon-question color-primary" />
              </el-tooltip>
            </template>
            <template #default="scope">
              <template v-if="c.prop === 'Platform'">
                <div class="icon-flex">
                  <div class="type-icon-item">
                    <svg-icon v-if="mediaTypeMap[scope.row.mediaPlatformType]" class-name="type-icon" :icon-class="mediaTypeMap[scope.row.mediaPlatformType]" />
                    <div class="type-label">{{ mediaTypeMap[scope.row.mediaPlatformType] }}</div>
                  </div>
                  <div style="border: 1px solid #dfe6ec" />
                  <div class="type-icon-item">
                    <svg-icon v-if="platformTypeMap[scope.row.platform]" class-name="type-icon" :icon-class="platformTypeMap[scope.row.platform]" />
                    <div class="type-label">{{ platformTypeMap[scope.row.platform] }}</div>
                  </div>
                </div>
              </template>
              <template v-else-if="c.prop === 'GoodsInfo'">
                <div class="table-base-info">
                  <svg-icon v-if="scope.row.platform === '2' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="taobao" />
                  <svg-icon v-else-if="scope.row.platform === '6' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="美团" />
                  <svg-icon v-else-if="scope.row.platform === '8' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="云台" />
                  <div v-else class="info-img">
                    <image-preview :src="scope.row.goodsThumbnailUrl" :width="50" :height="50" />
                  </div>
                  <BaseInfoCell :id="scope.row.goodsId" class="info-wrap" style="flex:1" :name="scope.row.goodsName" />
                  <el-tooltip content="备案即将过期" placement="top">
                    <i v-show="promotionOverdue(scope.row)" class="el-icon-question text-danger ml10" style="font-size: 18px" />
                  </el-tooltip>
                  <el-tooltip content="备案已过期" placement="top">
                    <i v-show="promotionOverdue(scope.row,true)" class="el-icon-question text-danger ml10" style="font-size: 18px" />
                  </el-tooltip>
                </div>
              </template>

              <template v-else-if="c.prop === 'status'">
                <el-switch
                  v-model="scope.row.status"
                  class="mr5"
                  active-color="#DCDFE6"
                  inactive-color="#ff4949"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleVaildChange(scope.row)"
                />
                <span v-if="!scope.row.status" class="color-danger text-bold">停用</span>
              </template>
              <template v-else-if="c.prop === 'remark'">
                <span>
                  <span v-if="scope.row.remark" style="white-space:pre;">{{ scope.row.remark }}</span>
                  <span v-else>—</span>
                </span>
              </template>
              <template v-else-if="c.prop === 'enableAmount'">

                {{ scope.row.conversionConfig&&scope.row.conversionConfig.amountProportion?(scope.row.conversionConfig.amountProportion+'%'):'100%' }}
              </template>
              <template v-else-if="c.prop === 'conversionProportion'">
                {{ scope.row.conversionConfig&&scope.row.conversionConfig.conversionProportion? (scope.row.conversionConfig.conversionProportion+'%'):'100% ' }}
              </template>
              <template v-else-if="c.prop === 'currentLimitingState'">
                <el-tag v-if="scope.row.currentLimitingState === 1" type="danger">
                  限制推广
                </el-tag>
                <span v-else>-</span>
              </template>
              <template v-else-if="c.prop === 'putOnRecordState'">
                <template v-if="scope.row.putOnRecordState!==undefined">
                  <dict-tag v-if="scope.row.putOnRecordState!==5" :options="dict.type.record_state" :value="scope.row.putOnRecordState" />
                  <el-tooltip v-else class="item" content="更新状态" placement="top">
                    <el-tag type="primary">
                      备案中
                      <i class="el-icon-refresh pointer" @click="getList" />
                    </el-tag>
                  </el-tooltip>
                </template>
              </template>
              <template v-else-if="c.prop === 'putOnRecordFailComment'">
                <el-tooltip v-if="scope.row.putOnRecordFailComment" class="item" :content="scope.row.putOnRecordFailComment" placement="top">
                  <i class="el-icon-warning" style="color:#F56C6C;font-size: 16px" />
                </el-tooltip>
                <span v-else>-</span>
              </template>
              <template v-else-if="c.prop === 'disabledGoodsConversion'">
                <el-button type="text" :style="{color: scope.row.disabledGoodsConversion?'#ff4d4f':'#a1a1a1'}">
                  {{ scope.row.disabledGoodsConversion ? '是': '否' }}
                </el-button>
              </template>
              <template v-else-if="c.prop === 'profit'">
                {{ scope.row.profit || '-.--' }}
              </template>
              <template v-else-if="c.prop === 'profitRoi'">
                {{ scope.row.profitRoi || '-.--' }}
              </template>
              <template v-else-if="c.prop === 'minGroupPrice'">
                <div style="text-align: left; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                  <span style="display: inline-block; width: 92px;">最小拼团价: </span>
                  <span>{{ scope.row.minGroupPrice ? formatPrice(scope.row.minGroupPrice) : '' }}</span>
                </div>
                <div style="text-align: left;">
                  <span style="display: inline-block; width: 92px;">最小单买价格: </span>
                  <span>{{ scope.row.minNormalPrice ? formatPrice(scope.row.minNormalPrice) : '' }}</span>
                </div>
              </template>
              <template v-else-if="c.prop === 'linkType'">
                <dict-tag :options="linkTypeMap[scope.row.platform]" :value="scope.row.linkType" />
              </template>
              <template v-else-if="c.prop === 'UserInfo'">
                <el-tooltip :disabled="!scope.row.nickName" effect="dark" :content="scope.row.nickName" placement="top">
                  <div style="text-align: left; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    <span style="display: inline-block; width: 64px;">用户昵称: </span>
                    <span>{{ scope.row.nickName ?? '' }}</span>
                  </div>
                </el-tooltip>
                <div style="text-align: left;">
                  <span style="display: inline-block; width: 64px;">负责人: </span>
                  <span>{{ scope.row.createBy ?? '' }}</span>
                </div>
              </template>
              <template v-else>
                <RenderComponent v-if="c.render" :render="c.render(scope.row)" />
                <BaseInfoCell v-else-if="c.info" :id="scope.row[c.info.id]" :name="scope.row[c.info.name]" :label="c.info.label" :sub-label="c.info.subLabel" />
                <TableColumnSet v-else-if="c.set" :set="c.set" :row="scope.row" :label-width="c.labelWidth" />
                <template v-else>{{ scope.row[c.prop] }}</template>
                <ClipboardButton v-if="c.copy" :value="c.render ? c.render(scope.row) : scope.row[c.prop]" />
              </template>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {
  listGoods,
  refreshPddGoods
} from '@/api/promotion/goods'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import isToday from 'dayjs/plugin/isToday'
import { formatPrice, getExistingObj } from '@/utils'
import { loadPageSize } from '@/utils/beforeList'
dayjs.extend(isToday)
export default {
  name: 'GoodsComponent',
  components: {

  },
  dicts: ['goods_link_type', 'merchant_type', 'goods_plan_type', 'platform_type', 'media_type', 'record_state', 'goods_link_type_tb', 'goods_link_type_jd', 'goods_link_type_pdd', 'goods_link_type_djk', 'goods_link_type_pangda', 'duo_duo_jin_bao'],
  data() {
    return {
      // 遮罩层
      loading: false,
      urlLoading: false,
      // 表单遮罩
      formLoading: false,
      // 提交Loading
      submitLoading: false,
      recordSubmitLoading: false,
      // 选中数组
      ids: [],
      selections: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品信息表格数据
      goodsList: [],
      // 弹出层标题
      title: '',

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platform: null,
        goodsId: null,
        goodsSign: null,
        goodsName: null,
        goodsLink: null,
        goodsThumbnailUrl: null,
        goodsType: null,
        goodsDesc: null,
        minGroupPrice: null,
        minNormalPrice: null,
        optId: null,
        optName: null,
        mallId: null,
        mallName: null,
        merchantType: null,
        salesTip: null,
        servTxt: null,
        planType: null,
        linkType: null,
        searchId: null,
        tjmUrl: null,
        couponUrl: null,
        companyId: null,
        deptIds: [],
        createBy: null,
        putOnRecordState: null,
        whetherTheNameIsTheSame: null,
        business: null,
        currentLimitingState: null,
        remark: null,
        mediaPlatformType: 1
      },
      mallList: [],
      // 表单参数
      form: {
        platform: '1',
        linkType: '1',
        numIid: ''
      },

      laterPay: false,
      editVisible: false,
      // 推广链接
      urlVisible: false,
      // 选中的商品
      selectedGoods: {},
      // 详情展示
      infoDrawerVisible: false,
      // 详情样式
      desStyle: { fontSize: '16px' },
      // 备案
      recordVisible: false,
      recordType: 'single',
      nameOptions: [
        {
          label: '正常',
          value: '1'
        }, {
          label: '异常',
          value: '0'
        }
      ],
      // 时间
      promotionRange: [],

      // 落地页列表
      landingListVisible: false,

      // 替换商品链接
      replaceLinkVisible: false,

      transferVisible: false,
      synchronousOrNot: false,

      pageList: [],

      activeUrlType: '1',
      txAppletUrlOldVO: {},
      txAppletUrlVO: {},
      txOriginalUrlVO: {},

      recordNameVisible: false,
      currentRecordGoods: {},

      remarkVisible: false,

      // 计划
      switchVisible: false,
      isInPool: false,
      switchForm: {
        id: '',
        switch: 0,
        goodsCount: 0,
        type: 1
      },
      replaceSwitchData: null,
      switchMap: {
        0: '-',
        1: '暂停计划',
        2: '替换链接'
      },
      advertiserIds: [],

      openCopy: false,

      conversionVisible: false,
      goodsConversionDisabled: 0,

      plantVisible: false,
      plantUrl: '',

      createRange: [],

      landingVisible: false,

      productRow: null

    }
  },
  computed: {
    ...mapGetters([
      'device',
      'tableHeight'
    ]),
    linkTypeMap() {
      return {
        '1': this.dict.type.goods_link_type_pdd,
        '2': this.dict.type.goods_link_type_tb,
        '3': this.dict.type.goods_link_type_jd,
        '4': this.dict.type.goods_link_type_pangda,
        '5': this.dict.type.goods_link_type_tb,
        '8': this.dict.type.goods_link_type_djk
      }
    },
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    },
    platformTypeMap() {
      return this.dict.type.platform_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    }
  },
  created() {
    loadPageSize(this.queryParams)
  },
  methods: {
    /** 查询商品信息列表 */
    getList() {
      this.loading = true
      listGoods(this.getQuery()).then(response => {
        this.goodsList = response.rows
        this.goodsList.forEach(item => {
          if (item.conversionJson && item.conversionJson !== '0') {
            item.conversionData = JSON.parse(item.conversionJson)
          }
        })
        this.total = response.total
        // 对 Table 进行重新布局
        this.$refs.tableRef.doLayout()
        this.loading = false
      })
    },
    getQuery() {
      const query = Object.assign({}, this.queryParams)
      if (this.createRange && this.createRange.length > 0) {
        query.params = {
          createStart: this.createRange[0],
          createEnd: this.createRange[1]
        }
      }
      return query
    },
    handleTableSort(val) {
      const { prop, order } = val
      switch (prop) {
        case 'orderCount':
          if (order === 'ascending') {
            this.queryParams.orderCount = 2
          } else if (order === 'descending') {
            this.queryParams.orderCount = 1
          } else {
            delete this.queryParams.orderCount
          }
          break
      }
      this.getList()
    },

    handleScrollerClick(item) {
      this.queryParams.goodsId = item.goodsId
      this.getList()
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        platform: '1',
        goodsId: null,
        numIid: null,
        goodsSign: null,
        goodsName: null,
        goodsLink: null,
        goodsThumbnailUrl: null,
        goodsType: null,
        goodsDesc: null,
        minGroupPrice: null,
        minNormalPrice: null,
        optId: null,
        optName: null,
        mallId: null,
        mallName: null,
        merchantType: null,
        salesTip: null,
        servTxt: null,
        planType: null,
        linkType: '1',
        searchId: null,
        tjmUrl: null,
        originalTjmUrl: null,
        couponUrl: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null,
        deleted: null,
        companyId: null,
        mediaPlatformTypeArr: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.createRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selections = selection
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 处理备案名
    handleRowClass({ row }) {
      let classNames = ''
      if (row.recordGoodsName && row.goodsName !== row.recordGoodsName) {
        classNames += 'record-warning '
      }
      if (row.promotionEnd) {
        const diff = dayjs(row.promotionEnd).diff(dayjs(new Date()), 'days') + 1
        if (diff <= 3 && diff > 0) {
          classNames += 'promotion-warning '
        }
        if (diff <= 0) {
          classNames += 'promotion-danger '
        }
      }

      return classNames
    },
    promotionOverdue(row, out = false) {
      if (!row.promotionEnd) return false
      const diff = dayjs(row.promotionEnd).diff(dayjs(new Date()), 'days') + 1
      if (out) {
        return diff <= 0
      }
      return diff <= 3 && diff > 0
    },

    // 刷新商品
    refreshGoods(row) {
      this.loading = true
      const id = row.id
      refreshPddGoods(id).then(() => {
        this.$modal.msgSuccess('刷新成功')
        this.getList()
      }).finally(() => {
        this.loading = false
      })
    }

  }
}
</script>

<script setup>
import { getCurrentInstance, ref } from 'vue'
import useStoreTableScroller from '@/hooks/useStoreTableScroller'
import ClipboardButton from '@/components/ClipboardButton/index.vue'
import useDeptOptions, { setDefaultIds, getDefaultIds } from '@/components/DeptTreeSelect/useDeptOptions'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import useConversion from '@/components/ConversionSetups/hooks/useConversion'
import { Message, MessageBox } from 'element-ui'
import { updateStatus } from '@/api/promotion/goods'
import useColumns from '@/hooks/useColumns'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import RenderComponent from '@/components/RenderComponent/index.vue'
import MallSelect from '@/components/CtreeSelect/MallSelect.vue'
const tableRef = ref(null)
const defaultColumns = [
  { prop: 'Platform', label: '类型', width: 100, fixed: true },
  { prop: 'GoodsInfo', label: '商品信息', width: 240, align: 'left', fixed: true },
  {
    label: `店铺信息`, prop: 'MallInfo', width: '200', align: 'left',
    info: { id: 'mallId', name: 'mallName' }
  },
  // { prop: 'action', label: '操作', width: 200 },
  // { prop: 'status', label: '停用', width: 110, tooltip: '停用后，该商品的所有操作将失效' },
  { prop: 'remark', label: '备注', width: 150 },
  // { prop: 'enableAmount', label: '回传金额比例', width: 130, tooltip: '回传金额比例开关关闭时，100%回传' },
  { prop: 'conversionProportion', label: '回传配置', width: 130, tooltip: '回传配置开关关闭时，100%回传' },
  { prop: 'currentLimitingState', label: '推广限制', width: 100 },
  { prop: 'putOnRecordState', label: '备案状态', width: 110 },
  { prop: 'putOnRecordFailComment', label: '失败原因', width: 100 },
  {
    prop: 'disabledGoodsConversion',
    label: '禁止回传',
    width: 100,
    permissions: ['promotion:goods:disabledGoodsConversion']
  },
  {
    prop: 'pauseSwitch',
    label: '限推触发事件',
    width: 120,
    permissions: ['promotion:goods:updateSwitch'],
    tooltip: '商品限制推广后系统自动执行的操作',
    render: (row) => {
      return row.pauseSwitch === undefined ? '-' : {
        0: '-',
        1: '暂停计划',
        2: '替换链接'
      }[row.pauseSwitch]
    }
  },
  { prop: 'profit', label: '盈亏成本', width: 120, tooltip: '盈亏成本 = 拿货成本 + 运费 + 其它非广告消耗成本' },
  { prop: 'profitRoi', label: '盈亏ROI', width: 120, tooltip: '设置预估的保本ROI' },
  { prop: 'orderCount', label: '总成交订单数', width: 130, sortable: 'custom' },
  { prop: 'minGroupPrice', label: '最小价格', width: 180 },
  { prop: 'linkType', label: '链接类型', width: 120 },
  { prop: 'UserInfo', label: '用户信息', width: 180 },
  {
    prop: 'business', label: '商务负责人', width: 150, permissions: ['promotion:config:duoduo'], render: (row) => {
      return row.business ? `${row.business} (${row.personInCharge})` : '-'
    }
  },
  {
    prop: 'Dept', label: '公司信息', width: 160, labelWidth: 40,
    set: [
      { label: `公司`, prop: 'firstDeptName' },
      { label: `部门`, prop: 'deptName' }
    ]
  },
  {
    prop: 'promotionStart', label: '推广日期', width: 160, labelWidth: 60,
    set: [
      { label: `开始时间`, prop: 'promotionStart' },
      { label: `结束时间`, prop: 'promotionEnd' }
    ]
  },
  { prop: 'createTime', label: '创建时间', width: 180 }
]

const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({ defaultColumns, tableRef })
useStoreTableScroller(tableRef)

const self = getCurrentInstance().proxy
const deptOptions = useDeptOptions(() => {
  setDefaultIds(self)
  self.getList()
  self.$refs.mallSelect.getList({ deptIds: getDefaultIds() })
})

// 回传
const { conversionType,
  conversionForm,
  conversionDialogVisible,
  conversionFormType,
  showConversion,
  batchUpdateConversion,
  handleConversionSave
} = useConversion({
  type: 'goods',
  onSave: () => self.getList()
})

// 启用禁用
const handleVaildChange = (row) => {
  MessageBox.confirm(`确认${row.status ? '启用' : '禁用'}商品?`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    updateStatus({
      id: row.id,
      status: row.status
    }).then(res => {
      Message.success('修改成功')
    })
  }).catch(() => {
    row.status = +!row.status
  })
}
</script>

<style lang="scss" scoped>
.adaption-container{
  height: calc(100vh - 285px) !important;
}
.info-drawer-body {
  display: flex;
  flex-direction: column;
  padding: 0 20px;
  height: 100%;
  .main-info {
    overflow: auto;
    flex: 1
  }
  .info-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
  }
  .info-drawer-action {
    width: 100%;
    align-self: end;
    margin-bottom: 20px;
  }
}
.url-wrap {
  overflow: hidden;
  /*将对象作为弹性伸缩盒子模型显示*/
  display: -webkit-box;
  /*设置子元素排列方式*/
  -webkit-box-orient: vertical;
  /*设置显示的行数，多出的部分会显示为...*/
  -webkit-line-clamp: 2;
}

.info-wrap {
  width: calc(100% - 60px);
  flex: 1;
}

.link-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
}
.in-pool {
  color: #f56c6c ;
  font-weight: bold;
  border-bottom: 1px solid #DCDFE6;
  padding-bottom: 10px;
  margin:0 10px 10px;
}

::v-deep .record-warning {
  color: #f56c6c;
}
::v-deep .promotion-warning {
  .el-table__cell {
    background: #fdf6ec !important;
  }
}
::v-deep .promotion-danger {
  .el-table__cell {
    background: #fef0f0 !important;
  }
}

::v-deep .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 10px;
}

.dialog-pt-0 ::v-deep .el-dialog__body {
 padding-top: 0;
}
.icon-flex {
  display: flex;
  justify-content: space-between;
  text-align: center;
  .type-icon {
    width:25px;
    height: 25px;
    font-size: 25px;
  }
  .type-label {
    font-size: 12px;
    color: #999;
  }
}

.submit-btn {
  display: inline-block;
  margin-right: 10px;
}
</style>
