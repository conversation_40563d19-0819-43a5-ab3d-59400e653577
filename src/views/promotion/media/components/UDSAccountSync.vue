<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useStore } from '@/store'
import {
  getSyncaccount, UDSAccountSync
} from '@/api/promotion/media'
import { Message } from 'element-ui'
const store = useStore()
const userName = computed(() => store.getters.userInfo.userName)

const props = defineProps({
  visible: Boolean,
  form: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['update:visible'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

const loading = ref(false)
const list = ref([])
const selectedList = ref([])
const fetchList = () => {
  list.value = []
  loading.value = true
  const data = {
    username: userName.value
  }
  getSyncaccount(data).then((res) => {
    list.value = res.data || []
  }).finally(() => {
    loading.value = false
  })
}
const submitLoading = ref(false)
function submit() {
  const arr = selectedList.value.map(item => ({
    advId: item.advertiserId,
    advName: item.advertiserName
  }))
  submitLoading.value = true
  UDSAccountSync(arr).then(res => {
    Message.success(res.msg)
    open.value = false
  }).finally(() => {
    submitLoading.value = false
  })
}
onMounted(() => {
  if (props.form) {
    selectedList.value = props.form
  }
  fetchList()
})
</script>

<template>
  <el-dialog
    title="UDSmart账户同步"
    :visible.sync="open"
    width="70%"
    top="10vh"
    append-to-body
  >
    <div class="flex">
      <div class="account_left">
        <div class="account_title">已选中</div>
        <el-table
          :data="selectedList"
          style="width: 100%"
          height="100%"
        >
          <el-table-column label="账户id" align="center" prop="advertiserId" min-width="200" />
          <el-table-column label="账户名称" align="center" prop="advertiserName" min-width="200" />
        </el-table>
      </div>
      <div class="account_right">
        <div class="account_title">已同步</div>
        <el-table
          v-loading="loading"
          :data="list"
          style="width: 100%"
          height="100%"
        >
          <el-table-column label="账户id" align="center" prop="advId" min-width="200" />
          <el-table-column label="账户名称" align="center" prop="advName" min-width="200" />
        </el-table>
      </div>
    </div>
    <div class="footer_btn">
      <el-button @click="open = false">取消</el-button>
      <el-button v-loading="submitLoading" type="primary" @click="submit">同步</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.account_title{
  font-size: 16px;
  padding: 12px;
  font-weight: bold;
}
.account_left{
  flex: 1;
  border: 1px solid #f1f1f1;
  border-radius: 8px;
  overflow: hidden;
  height: 600px;
}
.account_right{
  margin-left: 20px;
  flex: 1;
  border: 1px solid #f1f1f1;
  border-radius: 8px;
  overflow: hidden;
  height: 600px;
}
.footer_btn{
  display: flex;
  justify-content: right;
  padding: 20px 0 12px 0;
}
</style>
