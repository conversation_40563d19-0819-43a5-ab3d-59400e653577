<script setup>
import { reactive, ref } from 'vue'
import { isNil } from '@/utils'
import {
  getRefundAlert,
  setRefundAlert
} from '@/api/promotion/media'
import { Message } from 'element-ui'

const validateRoi = (rule, value, callback) => {
  if (preWarningForm.roiSwitch) {
    if (isNil(value)) {
      callback(new Error('请输入ROI预警值'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
const validateKilobar = (rule, value, callback) => {
  if (preWarningForm.kilobarThresholdSwitch) {
    if (isNil(value)) {
      callback(new Error('请输入千展阈值'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
const validateFund = (rule, value, callback) => {
  if (preWarningForm.kilobarThresholdSwitch) {
    if (isNil(value)) {
      callback(new Error('请输入余额阈值'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
const validateAlertCost = (rule, value, callback) => {
  if (preWarningForm.costSwitch) {
    if (isNil(value)) {
      callback(new Error('请输入消耗预警值'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
const rules = {
  roiThreshold: [
    { validator: validateRoi, trigger: 'blur' }
  ],
  kilobarThreshold: [
    { validator: validateKilobar, trigger: 'blur' }
  ],
  balanceThreshold: [
    { validator: validateFund, trigger: 'blur' }
  ],
  alertCost: [
    { validator: validateAlertCost, trigger: 'blur' }
  ]
}

const props = defineProps({
  form: {
    type: [Object, Array],
    default: () => ({})
  },
  businessType: {
    type: [Number, String],
    default: 1
  }
})
const emit = defineEmits(['save', 'cancel'])

const preWarningRef = ref(null)
const preWarningForm = reactive({
  id: null,
  businessId: null,
  businessType: null,
  orderSwitch: 0,
  orderCount: 10,
  orderRefundRate: 0,
  balanceThreshold: 0,
  balanceThresholdSwitch: 0,
  kilobarThreshold: 0,
  kilobarThresholdSwitch: 0,
  cost: 0,
  roiThreshold: 0,
  roiSwitch: 0,
  costSwitch: 0,
  alertCost: 0
})

const loading = ref(false)
const initPreWarning = () => {
  loading.value = true
  getRefundAlert({ businessType: props.businessType, businessId: getBusinessId(props.form) }).then(response => {
    if (response.code === 200) {
      if (response.data) {
        Object.assign(preWarningForm, response.data)
        preWarningForm.balanceThreshold = preWarningForm.balanceThreshold / 100
      }
    }
  }).finally(() => {
    loading.value = false
  })
}

const getBusinessId = (item) => {
  switch (+props.businessType) {
    case 1:
      return item.advertiserId
    case 3:
      return item.planId
    default:
      return ''
  }
}

if (!Array.isArray(props.form)) {
  initPreWarning()
}
const savePreWarning = () => {
  if (loading.value) {
    Message.info('请稍等，预警正在保存中')
    return
  }
  const formList = Array.isArray(props.form) ? props.form : [props.form]
  const postData = []
  formList.forEach(item => {
    const data = { ...preWarningForm,
      businessType: props.businessType,
      businessId: getBusinessId(item),
      balanceThreshold: preWarningForm.balanceThreshold * 100
    }
    if (item.mediaType) {
      data.mediaType = item.mediaType
    }
    if (!data.id) {
      delete data.id
    }
    postData.push(data)
  })
  loading.value = true
  setRefundAlert(postData).then(() => {
    Message.success('预警保存成功')
    emit('save')
  }).finally(() => {
    loading.value = false
    if (+props.businessType === 2 && !preWarningForm.id) {
      initPreWarning()
    }
  })
}
const cancel = () => {
  emit('cancel')
}

defineExpose({
  savePreWarning,
  initPreWarning
})
</script>

<template>
  <el-form ref="preWarningRef" v-loading="loading" :model="preWarningForm" :label-width="+businessType===2?'120px':'80px'" :rules="rules">
    <el-form-item
      label="ROI预警"
      prop="roiThreshold"
    >
      <el-switch
        v-model="preWarningForm.roiSwitch"
        :active-value="1"
        :inactive-value="0"
        active-color="#13ce66"
      />
      <template v-if="preWarningForm.roiSwitch">
        当消耗大于
        <el-input-number v-model="preWarningForm.cost" :min="0" :controls="false" style="width: 100px" />
        元，并且ROI低于
        <el-input-number v-model="preWarningForm.roiThreshold" :min="0.01" :max="2" :precision="2" label="请输入预警值" :controls="false" style="margin-right: 10px;width: 100px" />
      </template>
      <el-tooltip class="item" effect="dark" content="ROI不小于0，不大于2，低于该值则发送短信通知并暂停计划" placement="top">
        <i class="el-icon-question" style="margin-left: 5px" />
      </el-tooltip>
    </el-form-item>
    <el-form-item
      label="千展阈值"
      prop="kilobarThreshold"
    >
      <el-switch
        v-model="preWarningForm.kilobarThresholdSwitch"
        :active-value="1"
        :inactive-value="0"
        active-color="#13ce66"
      />
      <template v-if="preWarningForm.kilobarThresholdSwitch">
        <el-input-number v-model="preWarningForm.kilobarThreshold" :min="0" :max="1000" :precision="2" label="请输入千展阈值" :controls="false" style="margin: 0 10px;width: 150px" />
      </template>
      <el-tooltip class="item" effect="dark" content="低于该值则自动暂停广告计划" placement="top">
        <i class="el-icon-question" style="margin-left: 5px" />
      </el-tooltip>
    </el-form-item>
    <el-form-item
      label="余额阈值"
      prop="balanceThreshold"
    >
      <el-switch
        v-model="preWarningForm.balanceThresholdSwitch"
        :active-value="1"
        :inactive-value="0"
        active-color="#13ce66"
      />
      <template v-if="preWarningForm.balanceThresholdSwitch">
        <el-input-number v-model="preWarningForm.balanceThreshold" :min="0" :max="10000000" :precision="2" label="请输入余额阈值" :controls="false" style="margin: 0 10px;width: 150px" />元
      </template>
      <el-tooltip class="item" effect="dark" content="低于该值则自动发送通知" placement="top">
        <i class="el-icon-question" style="margin-left: 5px" />
      </el-tooltip>
    </el-form-item>
    <el-form-item
      label="退单率"
      prop="refund"
    >
      <el-switch
        v-model="preWarningForm.orderSwitch"
        :active-value="1"
        :inactive-value="0"
        active-color="#13ce66"
      />
      <template v-if="preWarningForm.orderSwitch">
        订单数 <el-input-number v-model="preWarningForm.orderCount" :min="10" label="请输入订单数" :controls="false" style="margin: 0 10px;width: 150px" />
        退款率 <el-input-number v-model="preWarningForm.orderRefundRate" style="width: 100px;margin-right: 10px" :min="0" :max="100" :controls="false" />%
      </template>
      <el-tooltip class="item" effect="dark" :content="`开启时，订单数达到${preWarningForm.orderCount}时，退款率大于${preWarningForm.orderRefundRate}%则暂停计划`" placement="top">
        <i class="el-icon-question" style="margin-left: 5px" />
      </el-tooltip>
    </el-form-item>
    <el-form-item
      v-if="+businessType===1"
      label="消耗预警"
      prop="costSwitch"
    >
      <el-radio-group v-model="preWarningForm.costSwitch" size="small">
        <el-radio-button :label="0">关闭</el-radio-button>
        <el-radio-button :label="1">仅通知</el-radio-button>
        <el-radio-button :label="2">通知并暂停</el-radio-button>
      </el-radio-group>
      <template v-if="preWarningForm.costSwitch">
        消耗大于 <el-input-number v-model="preWarningForm.alertCost" size="small" style="width: 100px;margin-right: 10px" :min="0" :controls="false" />元时，执行
      </template>
    </el-form-item>
    <el-form-item v-if="+businessType!==2">
      <el-button type="primary" size="mini" @click="savePreWarning">保存</el-button>
      <el-button type="danger" size="mini" @click="cancel">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">

</style>
