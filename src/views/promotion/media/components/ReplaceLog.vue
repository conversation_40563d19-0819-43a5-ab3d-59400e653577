<template>
  <div>
    <el-form ref="operateForm" class="search-form" :model="operateParams" size="mini" :inline="true" label-width="80px">
      <el-form-item prop="businessId">
        <el-input
          v-model.trim="operateParams.businessId"
          placeholder="业务ID"
          clearable
          @keyup.enter.native="handleOperateQuery"
        />
      </el-form-item>
      <el-form-item prop="businessName">
        <el-input
          v-model.trim="operateParams.businessName"
          placeholder="业务名称"
          clearable
          @keyup.enter.native="handleOperateQuery"
        />
      </el-form-item>
      <el-form-item prop="operateState">
        <el-select v-model="operateParams.operateState" placeholder="操作状态" clearable @change="handleOperateQuery">
          <el-option
            v-for="dict in operateStateOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="operateType">
        <el-select v-model="operateParams.operateType" placeholder="操作类型" clearable @change="handleOperateQuery">
          <el-option label="暂停计划" value="1" />
          <el-option label="替换链接" value="2" />
          <el-option label="更新监控链接" value="3" />
          <el-option label="更新出价" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="operateParams"
          storage-key="replace_log"
          @search="handleOperateQuery"
        />
        <el-button icon="el-icon-refresh" @click="resetOperateQuery">重置</el-button>
        <el-button class="fl-r" icon="el-icon-video-pause" :disabled="multiple" @click="pausePlan()">批量暂停</el-button>
        <el-button v-if="mediaAuth.includes(mediaType)" class="fl-r" icon="el-icon-video-pause" :disabled="multiple" @click="handleReplaceLink()">替换链接</el-button>
      </el-form-item>
    </el-form>
    <div class="operate-table-wrap">
      <el-table
        height="100%"
        :data="recordList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" :selectable="row => row.operateState === 0" />
        <el-table-column label="业务ID" align="center" prop="businessId" show-overflow-tooltip>
          <template #header>
            <span>业务ID</span>
            <el-tooltip effect="dark" content="巨量: 广告ID；腾讯: 创意ID" placement="top">
              <i class="el-icon-question color-primary" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="业务名称" align="center" prop="businessName" show-overflow-tooltip />
        <el-table-column label="广告主ID" align="center" prop="advertiserId" show-overflow-tooltip />
        <el-table-column label="操作类型" align="center" prop="operateType" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ operateTypeMap[scope.row.operateType] }}
          </template>
        </el-table-column>
        <el-table-column label="操作状态" align="center" prop="operateState" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.operateState === 1" type="success">成功</el-tag>
            <el-tag v-else-if="scope.row.operateState === 0" type="danger">失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="来源" align="center" prop="source" show-overflow-tooltip />
        <el-table-column label="消息" align="center" prop="operateErrorMessage">
          <template slot-scope="scope">
            <el-tooltip placement="top">
              <div slot="content" style="width: 500px">{{ scope.row.operateErrorMessage }} </div>
              <div class="overflow-text">{{ scope.row.operateErrorMessage }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作时间" align="center" width="165" prop="operateTime">
          <template slot-scope="scope">
            <span>{{ scope.row.operateTime.replace('T', ' ') }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="operateRow" label="操作" align="center" width="165" prop="action">
          <template slot-scope="{row}">
            <el-button icon="el-icon-video-pause" :disabled="row.operateState === 1" size="small" @click="pausePlan(row)">暂停计划</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="operateParams.pageNum"
      :limit.sync="operateParams.pageSize"
      @pagination="fetchRecordList"
    />
    <!-- 替换直达链接 -->
    <ReplaceUrl
      :visible.sync="replaceVisible"
      :selections="selections"
      :media-type="mediaType"
      :is-form-log="true"
      @close="handleClose"
    />
  </div>
</template>

<script>
import { getRecordList } from '@/api/promotion/goods'
import { planPause } from '@/api/statistics/plan'
import ReplaceUrl from '@/components/ReplaceUrl/index.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
const operateTypeMap = {
  1: '暂停计划',
  2: '替换链接',
  3: '更新监控链接',
  4: '更新出价',
  5: '修改回传',
  6: '修改锚点'
}
export default {
  name: 'ReplaceLog',
  components: {
    SavedSearches,

    ReplaceUrl
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    mediaType: {
      type: String,
      default: '1'
    },
    operateRow: {
      type: Boolean,
      default: true
    },
    operateType: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      mediaAuth: ['1', '4'],
      replaceVisible: false,
      multiple: true,
      selections: [],
      recordList: [],
      total: 0,
      operateParams: {
        pageNum: 1,
        pageSize: 10
      },
      operateStateOptions: [
        {
          label: '成功',
          value: 1
        },
        {
          label: '失败',
          value: 0
        }
      ],
      operateTypeMap
    }
  },
  mounted() {
    this.fetchRecordList()
  },
  methods: {
    handleClose(state) {
      if (state) {
        this.fetchRecordList()
      }
    },
    handleReplaceLink(row) {
      this.replaceVisible = true
    },

    fetchRecordList() {
      const postData = {
        advertiserId: this.id,
        mediaPlatformType: this.mediaType,
        ...this.operateParams
      }
      if (this.operateType) {
        postData.operateType = this.operateType
      }
      getRecordList(postData).then(response => {
        this.recordList = response.rows
        this.total = response.total
      })
    },

    resetOperateQuery() {
      this.resetForm('operateForm')
      this.fetchRecordList()
    },
    handleOperateQuery() {
      this.fetchRecordList()
    },

    handleSelectionChange(selection) {
      this.selections = selection
      this.multiple = !selection.length
    },
    pausePlan(row) {
      let planIds = null
      if (row) {
        planIds = [row.businessId]
      } else {
        if (this.selections.some(item => item.operateState === 1)) {
          this.$message.error('只能选择操作状态未失败的计划')
          return
        }
        planIds = this.selections.map(item => item.businessId)
      }

      planPause({
        advertiserId: this.id,
        traceId: row ? row.traceId : this.selections[0].traceId,
        planIds
      }).then(() => {
        this.$message.success('操作成功')
        this.fetchRecordList()
      })
    }

  }
}
</script>

<style scoped>
.operate-table-wrap {
  height: calc(100vh - 400px);
}
</style>
