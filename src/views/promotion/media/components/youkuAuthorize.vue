<script setup>
import { ykAuthorize } from '@/api/promotion/media'
import { computed, ref } from 'vue'
const props = defineProps({
  visible: Boolean

})
const emit = defineEmits(['update:visible', 'Refresh'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

const dataForm = ref({
  accountName: '',
  accountId: '',
  accessToken: '',
  appKey: ''
})
const rules = {
  accountName: [
    { required: true, message: '账户名称不能为空', trigger: 'blur' }
  ],
  accessToken: [
    { required: true, message: '账户token不能为空', trigger: 'blur' }
  ],
  accountId: [
    {
      required: true,
      message: '账户ID不能为空',
      trigger: 'blur'
    }
  ],
  appKey: [
    { required: true, message: 'appKey不能为空', trigger: 'blur' }
  ]
}
const dataFormRef = ref()
const loading = ref(false)
function submit() {
  dataFormRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      ykAuthorize(dataForm.value).then(res => {
        emit('Refresh')
        open.value = false
      }).finally(() => {
        loading.value = false
      })
    }
  })
}

</script>

<template>
  <el-dialog
    :visible.sync="open"
    title="优酷授权账户"
    top="25vh"
    width="700px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="rules"
      size="small"
      label-width="130px"
    >
      <el-form-item label="账户名称:" prop="accountName">
        <el-input
          v-model="dataForm.accountName"
          placeholder="请输入账户名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="账户ID:" prop="accountId">
        <el-input
          v-model="dataForm.accountId"
          placeholder="请输入账户ID"
          clearable
        />
      </el-form-item>
      <el-form-item label="账户token:" prop="accessToken">
        <el-input
          v-model="dataForm.accessToken"
          placeholder="请输入账户token"
          clearable
        />
      </el-form-item>
      <el-form-item label="appKey:" prop="appKey">
        <el-input
          v-model="dataForm.appKey"
          placeholder="请输入appKey"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="open = false">取 消</el-button>
      <el-button v-loading="loading" type="primary" @click="submit">确认</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

.sync-text {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  align-items: center;
  padding-top: 200px;
}
.font-weight {
  font-weight: bold;
}
</style>
