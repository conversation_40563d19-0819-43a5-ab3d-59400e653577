<template>
  <el-tabs v-model="activeName" style="margin-top: 0px" @tab-click="handleTabClick">
    <el-tab-pane name="project" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="form" />项目列表</div>
      </template>
      <ProjectStatistics default-media-type="1" @routeJump="routeJump" />
    </el-tab-pane>
    <el-tab-pane name="plan" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="cascader" />广告列表</div>
      </template>
      <PlanStatistics ref="planStatisticsRef" default-media-type="1" :route-row="routeRow" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import ProjectStatistics from './jlList/projectStatistics/index.vue'
import PlanStatistics from './jlList/planStatistics/index.vue'

export default {
  name: 'MediaList',
  components: {
    ProjectStatistics,
    PlanStatistics
  },
  data() {
    return {
      activeName: 'project',
      loading: false,
      routeRow: null
    }
  },
  created() {
    this.setTabs()
  },
  activated() {
    this.setTabs()
  },
  methods: {
    setTabs() {
      const query = { ...this.$route.query }
      if (query.state) {
        this.activeName = query.state
      } else if (query.activeTab) {
        this.activeName = query.activeTab
        delete query.activeTab
        this.$router.replace({ path: this.$route.path, query })
      }
    },
    handleTabClick() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 500)
    },
    routeJump(list) {
      this.routeRow = list
      this.activeName = 'plan'
      if (this.$refs.planStatisticsRef) {
        this.$refs.planStatisticsRef.queryParams.advertiserId = list.advertiserId
        this.$refs.planStatisticsRef.queryParams.projectId = list.projectId
        this.$refs.planStatisticsRef.getList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__item{
  height: 42px;
  line-height: 42px;
  font-size: 16px;
  font-weight: bold;
}
</style>
