<template>
  <el-tabs v-model="activeName" style="margin-top: 0px" @tab-click="handleTabClick">
    <el-tab-pane name="advertisement" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="cascader" />广告</div>
      </template>
      <Advertisement default-media-type="2" @routeJump="routeJump" />
    </el-tab-pane>
    <el-tab-pane name="adGroup" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="cascader" />广告组</div>
      </template>
      <AdGroup ref="adGroupRef" default-media-type="2" :route-row="adGroupRow" @routeJump="routeJump" />
    </el-tab-pane>
    <el-tab-pane name="adCreativity" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="cascader" />广告创意</div>
      </template>
      <AdCreativity ref="adCreativityRef" default-media-type="2" :route-row="routeRow" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import Advertisement from './ksList/advertisement/index.vue'
import AdGroup from './ksList/adGroup/index.vue'
import AdCreativity from './ksList/adCreativity/index.vue'
export default {
  name: 'KsList',
  components: {
    Advertisement,
    AdGroup,
    AdCreativity
  },
  data() {
    return {
      activeName: 'advertisement',
      loading: false,
      routeRow: null,
      adGroupRow: null
    }
  },
  created() {
    this.setTabs()
  },
  activated() {
    this.setTabs()
  },
  methods: {
    setTabs() {
      const query = { ...this.$route.query }
      if (query.state) {
        this.activeName = query.state
      } else if (query.activeTab) {
        this.activeName = query.activeTab
        delete query.activeTab
        this.$router.replace({ path: this.$route.path, query })
      }
    },
    handleTabClick() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 500)
    },
    routeJump(list) {
      this.activeName = list.activeName
      if (list.activeName === 'adGroup') {
        this.adGroupRow = list
        if (this.$refs.adGroupRef) {
          this.$refs.adGroupRef.queryParams.advertiserId = list.advertiserId
          this.$refs.adGroupRef.queryParams.campaignId = list.campaignId
          this.$refs.adGroupRef.getList()
        }
      } else if (list.activeName === 'adCreativity') {
        this.routeRow = list
        if (this.$refs.adCreativityRef) {
          this.$refs.adCreativityRef.queryParams.advertiserId = list.advertiserId
          this.$refs.adCreativityRef.queryParams.campaignId = list.campaignId
          this.$refs.adCreativityRef.queryParams.planId = this.routeRow.planId
          this.$refs.adCreativityRef.getList()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__item{
  height: 42px;
  line-height: 42px;
  font-size: 16px;
  font-weight: bold;
}
</style>
