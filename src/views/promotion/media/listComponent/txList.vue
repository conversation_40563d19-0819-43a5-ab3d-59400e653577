<template>
  <el-tabs v-model="activeName" style="margin-top: 0px" @tab-click="handleTabClick">
    <el-tab-pane name="adGroup" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="cascader" />广告</div>
      </template>
      <AdGroup ref="adGroupRef" default-media-type="4" :route-row="adGroupRow" @routeJump="routeJump" />
    </el-tab-pane>
    <el-tab-pane name="adCreativity" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="cascader" />创意</div>
      </template>
      <AdCreativity ref="adCreativityRef" default-media-type="4" :route-row="routeRow" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import AdGroup from './txList/adGroup/index.vue'
import AdCreativity from './txList/adCreativity/index.vue'
export default {
  name: 'TxList',
  components: {
    AdGroup,
    AdCreativity
  },
  data() {
    return {
      activeName: 'adGroup',
      loading: false,
      routeRow: null,
      adGroupRow: null
    }
  },
  created() {
    this.setTabs()
  },
  activated() {
    this.setTabs()
  },
  methods: {
    setTabs() {
      const query = { ...this.$route.query }
      if (query.state) {
        this.activeName = query.state
      } else if (query.activeTab) {
        this.activeName = query.activeTab
        delete query.activeTab
        this.$router.replace({ path: this.$route.path, query })
      }
    },
    handleTabClick() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 500)
    },
    routeJump(list) {
      this.activeName = list.activeName
      if (list.activeName === 'adCreativity') {
        this.routeRow = list
        if (this.$refs.adCreativityRef) {
          this.$refs.adCreativityRef.queryParams.advertiserId = list.advertiserId
          this.$refs.adCreativityRef.queryParams.adgroupId = list.adgroupId
          this.$refs.adCreativityRef.getList()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__item{
  height: 42px;
  line-height: 42px;
  font-size: 16px;
  font-weight: bold;
}
</style>
