<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="70px"
    >
      <el-form-item prop="advertiserId">
        <el-input
          v-model.trim="queryParams.advertiserId"
          placeholder="广告主ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="adgroupId">
        <el-input
          v-model.trim="queryParams.adgroupId"
          placeholder="广告ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="dynamicCreativeId">
        <el-input
          v-model.trim="queryParams.dynamicCreativeId"
          placeholder="创意ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-button v-hasPermi="['promotion:media:updateStatus']" size="mini" type="primary" :disabled="multiple" @click="handleOpen">批量开启</el-button>
      <el-button v-hasPermi="['promotion:media:updateStatus']" size="mini" type="warning" :disabled="multiple" @click="handleSuspend">批量暂停</el-button>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="statisticsList"
        lazy
        stripe
        border
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          :fixed="device !== 'mobile'"
        />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :fixed="device !== 'mobile'"
          width="50"
        />
        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <el-table-column
              v-if="c.prop === 'mediaType'"
              :key="i"
              label="媒体类型"
              align="center"
              prop="mediaType"
              :width="c.width"
              :fixed="device !== 'mobile'"
            >
              <template slot-scope="scope">
                <svg-icon
                  v-if="mediaTypeMap[scope.row.mediaType]"
                  :icon-class="mediaTypeMap[scope.row.mediaType]"
                />
                <dict-tag
                  :options="dict.type.media_type"
                  :value="scope.row.mediaType"
                  style="display: inline-block; margin-left: 5px"
                />
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="c.prop === 'adgroupId'"
              :key="'adgroupId'+i"
              label="广告ID"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell
                  :name="scope.row.adgroupId"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'dynamicCreativeId'"
              :key="i"
              label="创意ID"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell
                  :name="scope.row.dynamicCreativeId"
                />
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="c.prop === 'deliveryMode'"
              :key="'deliveryMode'+i"
              label="创意类型"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                {{ scope.row.deliveryMode|deliveryModeFilter }}
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="c.prop === 'pageUrl'"
              :key="'pageUrl'+i"
              :label="c.label"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell
                  :name="scope.row.pageUrl"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'androidDeepLinkUrl'"
              :key="'androidDeepLinkUrl'+i"
              :label="c.label"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell
                  :name="scope.row.androidDeepLinkUrl"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'iosDeepLinkUrl'"
              :key="'iosDeepLinkUrl'+i"
              :label="c.label"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell
                  :name="scope.row.iosDeepLinkUrl"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'universalLinkUrl'"
              :key="'universalLinkUrl'+i"
              :label="c.label"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell
                  :name="scope.row.universalLinkUrl"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'miniProgramPath'"
              :key="i"
              :label="c.label"
              align="center"
              :prop="c.prop"
              :width="c.width"
            >
              <template #default="scope">
                <BaseInfoCell
                  v-if="scope.row.miniProgramPath"
                  :name="scope.row.miniProgramPath"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'deepLinkUrl'"
              :key="'deepLinkUrl'+i"
              :label="c.label"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell
                  :name="scope.row.deepLinkUrl"
                />
              </template>
            </el-table-column>
            <CustomTableColumn
              v-else
              :key="i"
              :data="c"
              :render-map="renderMap"
            />
          </template>
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { listDynamicCreative } from '@/api/statistics/list'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { updateStatus } from '@/api/statistics/common'
import { mapGetters } from 'vuex'
import { loadPageSize } from '@/utils/beforeList'
export default {
  name: 'AdCreativity',
  components: {
    BaseInfoCell
  },
  dicts: ['media_type'],
  filters: {
    deliveryModeFilter(val) {
      const list = {
        'DELIVERY_MODE_COMPONENT': '组件化创意',
        'DELIVERY_MODE_CUSTOMIZE': '自定义创意'
      }
      return list[val]
    }
  },
  props: {
    defaultMediaType: {
      type: [String, Number],
      default: null
    },
    routeRow: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      statisticsList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mediaType: null,
        advertiserId: null,
        adgroupId: null,
        dynamicCreativeId: null
      },
      // 日期范围
      dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      selections: [],
      multiple: true
    }
  },
  computed: {
    ...mapGetters(['device', 'permissions', 'roles', 'tableHeight']),
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    }
  },
  created() {
    this.getRouteParams()
    // loadPageSize(this.queryParams)
  },
  activated() {
    this.getRouteParams()
  },

  mounted() {},
  methods: {
    // 获取路由携带的参数&props传递的参数
    getRouteParams() {
      if (this.defaultMediaType) { this.queryParams.mediaType = this.defaultMediaType }
      this.queryParams.advertiserId = this.$route.query.advertiserId
      if (this.routeRow) {
        this.queryParams.advertiserId = this.routeRow.advertiserId
        this.queryParams.adgroupId = this.routeRow.adgroupId
      }
      this.getList()
    },
    /** 查询表列表 */
    getList() {
      if (!this.queryParams.advertiserId) {
        this.$message.error('广告主ID不能为空')
        return
      }
      if (this.loading) return
      this.loading = true
      const query = this.queryParams
      Promise.all([listDynamicCreative(query)])
        .then(([listResponse]) => {
          this.statisticsList = listResponse.data || []
          if (listResponse.data && listResponse.data.length) {
            this.total = listResponse.data[0].total
          } else {
            this.total = 0
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },

    handleJump(row) {
      // this.$emit('routeJump', {
      //   advertiserId: row.advertiserId || this.queryParams.advertiserId,
      //   campaignId: row.campaignId
      // })
    },
    handleOpen() {
      this.$confirm('确定要开启选中的项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          advertiserId: this.queryParams.advertiserId,
          mediaType: this.defaultMediaType,
          statusType: 0,
          type: 3,
          ids: this.ids
        }
        updateStatus([data]).then(response => {
          this.$modal.msgSuccess('开启成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleSuspend() {
      this.$confirm('确定要暂停选中的项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          advertiserId: this.queryParams.advertiserId,
          mediaType: this.defaultMediaType,
          statusType: 1,
          type: 3,
          ids: this.ids
        }
        updateStatus([data]).then(response => {
          this.$modal.msgSuccess('暂停成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleSelectionChange(selection) {
      this.selections = selection
      this.ids = selection.map(item => item.dynamicCreativeId)
      this.multiple = !selection.length
    }
  }
}
</script>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import useColumns from '@/hooks/useColumns'
import { MaxDangerRate, PercentValue } from '@/utils/render'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import { useRoute } from 'vue-router/composables'
const renderMap = {
  chargeBackRate: (row) => {
    return MaxDangerRate(row.chargeBackRate)
  }
}

const defaultColumns = [
  // { label: `广告id`, prop: 'campaignId', width: '200' },
  { label: `广告组ID`, prop: 'adgroupId', width: '200' },
  { label: `创意ID`, prop: 'dynamicCreativeId', width: '200' },
  { label: `广告创意名称`, prop: 'dynamicCreativeName', width: '150' },
  { label: `投放模式`, prop: 'deliveryMode', overflow: true, width: '200' },
  { label: `客户设置的状态`, prop: 'configuredStatus', width: '150' },
  { label: `曝光监控地址`, prop: 'impressionTrackingUrl', width: '230', overflow: true },
  { label: `监控链接`, prop: 'clickTrackingUrl', width: '150' },
  { label: `小程序路径`, prop: 'miniProgramPath', width: '200', overflow: true },
  { label: `小程序落地页 path 列表`, prop: 'miniProgramPaths', width: '200', overflow: true },
  { label: `落地页 url`, prop: 'pageUrl', width: '200', overflow: true },
  { label: `android应用直达 schema`, prop: 'androidDeepLinkUrl', width: '200', overflow: true },
  { label: `ios应用直达 schema`, prop: 'iosDeepLinkUrl', width: '200', overflow: true },
  { label: `通用链接页 URL`, prop: 'universalLinkUrl', width: '200', overflow: true },
  { label: `通用应用直达 schema`, prop: 'deepLinkUrl', width: '200', overflow: true }
]

const self = getCurrentInstance().proxy
const tableRef = ref(null)
const {
  columnsInstance,
  columns,
  operatedColumns,
  customList,
  handleHeaderDragend
} = useColumns({ defaultColumns, tableRef, name: self.defaultMediaType && useRoute().name + '_adCreativity' })
</script>

