<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="70px"
    >
      <el-form-item prop="advertiserId">
        <el-input
          v-model.trim="queryParams.advertiserId"
          placeholder="广告主ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="adgroupId">
        <el-input
          v-model.trim="queryParams.adgroupId"
          placeholder="广告ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-button v-hasPermi="['promotion:media:updateStatus']" size="mini" type="primary" :disabled="multiple" @click="handleOpen">批量开启</el-button>
      <el-button v-hasPermi="['promotion:media:updateStatus']" size="mini" type="warning" :disabled="multiple" @click="handleSuspend">批量暂停</el-button>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="statisticsList"
        lazy
        stripe
        border
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          :fixed="device !== 'mobile'"
        />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :fixed="device !== 'mobile'"
          width="50"
        />
        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <el-table-column
              v-if="c.prop === 'mediaType'"
              :key="i"
              label="媒体类型"
              align="center"
              prop="mediaType"
              :width="c.width"
              :fixed="device !== 'mobile'"
            >
              <template slot-scope="scope">
                <svg-icon
                  v-if="mediaTypeMap[scope.row.mediaType]"
                  :icon-class="mediaTypeMap[scope.row.mediaType]"
                />
                <dict-tag
                  :options="dict.type.media_type"
                  :value="scope.row.mediaType"
                  style="display: inline-block; margin-left: 5px"
                />
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="c.prop === 'adgroupId'"
              :key="'unit_id'+i"
              :label="c.label"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell
                  :name="scope.row.adgroupId"
                  @nameClick="handleJump(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'configuredStatus'"
              :key="'configuredStatus'+i"
              :label="c.label"
              align="center"
              :width="c.width"
            >
              <template slot-scope="scope">
                {{ scope.row.configuredStatus | configuredStatusFilter }}
              </template>
            </el-table-column>

            <CustomTableColumn
              v-else
              :key="i"
              :data="c"
              :render-map="renderMap"
            />
          </template>
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { listAdgroupList } from '@/api/statistics/list'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { updateStatus } from '@/api/statistics/common'
import { mapGetters } from 'vuex'
import { loadPageSize } from '@/utils/beforeList'
export default {
  name: 'AdGroup',
  components: {
    BaseInfoCell
  },
  dicts: ['media_type'],
  filters: {
    configuredStatusFilter(val) {
      const list = {
        'AD_STATUS_NORMAL': '有效',
        'AD_STATUS_SUSPEND': '暂停'
      }
      return list[val]
    }
  },
  props: {
    defaultMediaType: {
      type: [String, Number],
      default: null
    },
    routeRow: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      statisticsList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mediaType: null,
        advertiserId: null,
        adgroupId: null
      },
      // 日期范围
      dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      selections: [],
      multiple: true
    }
  },
  computed: {
    ...mapGetters(['device', 'permissions', 'roles', 'tableHeight']),
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    }
  },
  created() {
    this.getRouteParams()
    // loadPageSize(this.queryParams)
  },

  mounted() {},
  methods: {
    // 获取路由携带的参数&props传递的参数
    getRouteParams() {
      if (this.defaultMediaType) { this.queryParams.mediaType = this.defaultMediaType }
      this.queryParams.advertiserId = this.$route.query.advertiserId
      if (this.routeRow) {
        this.queryParams.advertiserId = this.routeRow.advertiserId
        this.queryParams.campaignId = this.routeRow.campaignId
        this.queryParams.dynamicCreativeId = null
      }
      this.getList()
    },
    /** 查询列表 */
    getList() {
      if (!this.queryParams.advertiserId) {
        this.$message.error('广告主ID不能为空')
        return
      }
      if (this.loading) return
      this.loading = true
      const query = this.queryParams
      Promise.all([listAdgroupList(query)])
        .then(([listResponse]) => {
          this.statisticsList = listResponse.data || []
          if (listResponse.data && listResponse.data.length) {
            this.total = listResponse.data[0].total
          } else {
            this.total = 0
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },

    handleJump(row) {
      this.$emit('routeJump', {
        activeName: 'adCreativity',
        advertiserId: row.advertiserId || this.queryParams.advertiserId,
        adgroupId: row.adgroupId
      })
    },
    handleOpen() {
      this.$confirm('确定要开启选中的项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          advertiserId: this.queryParams.advertiserId,
          mediaType: this.defaultMediaType,
          statusType: 0,
          type: 2,
          ids: this.ids
        }
        updateStatus([data]).then(response => {
          this.$modal.msgSuccess('开启成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleSuspend() {
      this.$confirm('确定要暂停选中的项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          advertiserId: this.queryParams.advertiserId,
          mediaType: this.defaultMediaType,
          statusType: 1,
          type: 2,
          ids: this.ids
        }
        updateStatus([data]).then(response => {
          this.$modal.msgSuccess('暂停成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleSelectionChange(selection) {
      this.selections = selection
      this.ids = selection.map(item => item.adgroupId)
      this.multiple = !selection.length
    }
  }
}
</script>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import useColumns from '@/hooks/useColumns'
import { useRoute } from 'vue-router/composables'
import { MaxDangerRate, PercentValue } from '@/utils/render'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
const renderMap = {
  chargeBackRate: (row) => {
    return MaxDangerRate(row.chargeBackRate)
  }
}

const defaultColumns = [
  { label: `广告ID`, prop: 'adgroupId', width: '200' },
  { label: `广告名称`, prop: 'adgroupName', width: '200' },
  { label: `客户设置的状态`, prop: 'configuredStatus', width: '150' },
  { label: `广告在系统中的状态`, prop: 'systemStatus', width: '180' },
  { label: `营销目的类型`, prop: 'marketingGoal', width: '150' },
  { label: `推广产品类型`, prop: 'marketingTargetType', overflow: true, width: '150' },
  { label: `开始投放日期`, prop: 'beginDate', width: '150' },
  { label: `结束投放日期`, prop: 'endDate', width: '150' },
  { label: `广告出价`, prop: 'bidAmount', width: '230' },
  { label: `广告优化目标类型`, prop: 'optimizationGoal', width: '200' },
  { label: `一键起量预算`, prop: 'autoAcquisitionBudget', width: '200' }
]

const self = getCurrentInstance().proxy
const tableRef = ref(null)
const {
  columnsInstance,
  columns,
  operatedColumns,
  customList,
  handleHeaderDragend
} = useColumns({ defaultColumns, tableRef, name: self.defaultMediaType && useRoute().name + '_adGroup' })

</script>

