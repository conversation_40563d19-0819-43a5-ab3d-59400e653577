<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="70px"
    >
      <el-form-item prop="advertiserId">
        <el-input
          v-model.trim="queryParams.advertiserId"
          placeholder="广告主ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="projectId">
        <el-input
          v-model.trim="queryParams.projectId"
          placeholder="项目ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="planId">
        <el-input
          v-model.trim="queryParams.planId"
          placeholder="计划id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-button v-hasPermi="['promotion:media:updateStatus']" size="mini" type="primary" :disabled="multiple" @click="handleOpen">批量开启</el-button>
      <el-button v-hasPermi="['promotion:media:updateStatus']" size="mini" type="warning" :disabled="multiple" @click="handleSuspend">批量暂停</el-button>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="statisticsList"
        lazy
        stripe
        border
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          :fixed="device !== 'mobile'"
        />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :fixed="device !== 'mobile'"
          width="50"
        />
        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <el-table-column
              v-if="c.prop === 'mediaType'"
              :key="i"
              label="媒体类型"
              align="center"
              prop="mediaType"
              :width="c.width"
              :fixed="device !== 'mobile'"
            >
              <template slot-scope="scope">
                <svg-icon
                  v-if="mediaTypeMap[scope.row.mediaType]"
                  :icon-class="mediaTypeMap[scope.row.mediaType]"
                />
                <dict-tag
                  :options="dict.type.media_type"
                  :value="scope.row.mediaType"
                  style="display: inline-block; margin-left: 5px"
                />
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="c.prop === 'projectName'"
              :key="i"
              :label="c.label"
              :fixed="device !== 'mobile'"
              align="left"
              :prop="c.prop"
              :width="c.width"
            >
              <template #default="scope">
                <BaseInfoCell
                  v-if="!scope.row.timePeriod"
                  :id="scope.row.projectId"
                  :name="scope.row.projectName"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'externalUrlMaterialList'"
              :key="i"
              :label="c.label"
              align="center"
              :prop="c.prop"
              :width="c.width"
            >
              <template #default="scope">
                <BaseInfoCell
                  v-if="scope.row.externalUrlMaterialList"
                  :name="scope.row.externalUrlMaterialList"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'openUrl'"
              :key="i"
              :label="c.label"
              align="center"
              :prop="c.prop"
              :width="c.width"
            >
              <template #default="scope">
                <BaseInfoCell
                  v-if="scope.row.openUrl"
                  :name="scope.row.openUrl"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'promotionName'"
              :key="i"
              :label="c.label"
              :fixed="device !== 'mobile'"
              align="left"
              :prop="c.prop"
              :width="c.width"
            >
              <template #default="scope">
                <BaseInfoCell
                  :id="scope.row.promotionId"
                  :name="scope.row.promotionName"
                />
              </template>
            </el-table-column>
            <CustomTableColumn
              v-else
              :key="i"
              :data="c"
              :render-map="renderMap"
            />
          </template>
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { getListPlan } from '@/api/statistics/plan'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { updateStatus } from '@/api/statistics/common'
import { mapGetters } from 'vuex'
import { loadPageSize } from '@/utils/beforeList'
export default {
  name: 'PlanStatisticsList',
  components: {
    BaseInfoCell
  },
  dicts: ['media_type'],
  props: {
    defaultMediaType: {
      type: [String, Number],
      default: null
    },
    routeRow: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 媒体账户报表表格数据
      statisticsList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mediaType: null,
        goodsPlatform: null,
        advertiserId: null,
        advertiserName: null,
        projectId: null,
        planId: null,
        planName: null,
        deptIds: [],
        createBy: null
      },
      // 日期范围
      dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      selections: [],
      multiple: true
    }
  },
  computed: {
    ...mapGetters(['device', 'permissions', 'roles', 'tableHeight']),
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    }
  },
  created() {
    if (this.defaultMediaType) { this.queryParams.mediaType = this.defaultMediaType }
    this.queryParams.advertiserId = this.$route.query.advertiserId
    if (this.routeRow) {
      this.queryParams.advertiserId = this.routeRow.advertiserId
      this.queryParams.projectId = this.routeRow.projectId
    }
    // loadPageSize(this.queryParams)
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      if (!this.queryParams.advertiserId) {
        this.$message.error('广告主ID不能为空')
        return
      }
      if (this.loading) return
      this.loading = true
      const query = this.queryParams
      Promise.all([getListPlan(query)])
        .then(([listResponse]) => {
          this.statisticsList = listResponse.data || []
          if (listResponse.data && listResponse.data.length) {
            this.total = listResponse.data[0].total
          } else {
            this.total = 0
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },

    handleJump(row) {
      this.$emit('routeJump', {
        path: '/report/JLStatistics',
        advertiserId: row.advertiserId
      })
    },
    handleOpen() {
      this.$confirm('确定要开启选中的项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          advertiserId: this.queryParams.advertiserId,
          mediaType: this.defaultMediaType,
          statusType: 0,
          type: 3,
          ids: this.ids
        }
        updateStatus([data]).then(response => {
          this.$modal.msgSuccess('开启成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleSuspend() {
      this.$confirm('确定要暂停选中的项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          advertiserId: this.queryParams.advertiserId,
          mediaType: this.defaultMediaType,
          statusType: 1,
          type: 3,
          ids: this.ids
        }
        updateStatus([data]).then(response => {
          this.$modal.msgSuccess('暂停成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleSelectionChange(selection) {
      this.selections = selection
      this.ids = selection.map(item => item.promotionId)
      this.multiple = !selection.length
    }
  }
}
</script>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import useColumns from '@/hooks/useColumns'
import { MaxDangerRate, PercentValue } from '@/utils/render'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import { useRoute } from 'vue-router/composables'
const renderMap = {
  chargeBackRate: (row) => {
    return MaxDangerRate(row.chargeBackRate)
  }
}

const defaultColumns = [
  { label: `项目名称`, prop: 'projectName', width: '200' },
  { label: `广告名称`, prop: 'promotionName', width: '200' },
  { label: `广告创建时间`, prop: 'promotionCreateTime', width: '200' },
  { label: `广告更新时间`, prop: 'promotionModifyTime', width: '200' },
  { label: `广告状态`, prop: 'status', width: '120' },
  { label: `操作状态`, prop: 'optStatus', width: '120' },
  { label: `落地页素材`, prop: 'externalUrlMaterialList', width: '200' },
  { label: `直达链接，用于打开电商app，调起店铺`, prop: 'openUrl', width: '280', overflow: true },
  { label: `电商优选直达链接组`, prop: 'openUrls', width: '200' },
  { label: `点击出价/展示出价`, prop: 'bid', width: '200' },
  { label: `目标转化出价/预期成本`, prop: 'cpaBid', width: '200' },
  { label: `深度优化出价`, prop: 'deepCpabid', width: '200' },
  { label: `深度转化ROI系数`, prop: 'roiGoal', width: '200' },
  { label: `产品名称`, prop: 'titles', width: '200' },
  // { label: `产品主图`, prop: 'imageIds', width: '130' },
  { label: `产品卖点`, prop: 'sellingPoints', width: '200' }

]

const self = getCurrentInstance().proxy
const tableRef = ref(null)
const {
  columnsInstance,
  columns,
  operatedColumns,
  customList,
  handleHeaderDragend
} = useColumns({ defaultColumns, tableRef, name: self.defaultMediaType && useRoute().name + '_planStatistics' })

</script>
<style lang="scss" scoped>
.code-title {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #bdbdbd;
}
::v-deep .el-dialog__body {
  //padding-top: 0;
}
.conversion-footer {
  display: flex;
  justify-content: space-between;
}
.dialog-conversion-rate {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 40px;
}
</style>
