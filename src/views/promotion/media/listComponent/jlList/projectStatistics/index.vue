<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="70px"
    >
      <!-- <el-form-item prop="goodsPlatform">
        <el-select
          v-model="queryParams.goodsPlatform"
          placeholder="商品平台"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dicts.platform_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item prop="advertiserId">
        <el-input
          v-model.trim="queryParams.advertiserId"
          placeholder="广告主ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item prop="advertiserName">
        <el-input
          v-model.trim="queryParams.advertiserName"
          placeholder="账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item prop="projectId">
        <el-input
          v-model.trim="queryParams.projectId"
          placeholder="项目ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="planId">
        <el-input
          v-model.trim="queryParams.planId"
          placeholder="计划id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-button v-hasPermi="['promotion:media:updateStatus']" size="mini" type="primary" :disabled="multiple" @click="handleOpen">批量开启</el-button>
      <el-button v-hasPermi="['promotion:media:updateStatus']" size="mini" type="warning" :disabled="multiple" @click="handleSuspend">批量暂停</el-button>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="statisticsList"
        lazy
        stripe
        border
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          :fixed="device !== 'mobile'"
        />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :fixed="device !== 'mobile'"
          width="50"
        />
        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <el-table-column
              v-if="c.prop === 'mediaType'"
              :key="i"
              label="媒体类型"
              align="center"
              prop="mediaType"
              :width="c.width"
              :fixed="device !== 'mobile'"
            >
              <template slot-scope="scope">
                <svg-icon
                  v-if="mediaTypeMap[scope.row.mediaType]"
                  :icon-class="mediaTypeMap[scope.row.mediaType]"
                />
                <dict-tag
                  :options="dict.type.media_type"
                  :value="scope.row.mediaType"
                  style="display: inline-block; margin-left: 5px"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="c.prop === 'projectName'"
              :key="i"
              :label="c.label"
              :fixed="device !== 'mobile'"
              align="left"
              :prop="c.prop"
              :width="c.width"
            >
              <template #default="scope">
                <BaseInfoCell
                  v-if="!scope.row.timePeriod"
                  :id="scope.row.projectId"
                  :name="scope.row.projectName"
                  @nameClick="handleJump(scope.row)"
                />
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="c.prop === 'actionTrackUrl'"
              :key="i"
              :label="c.label"
              align="left"
              :prop="c.prop"
              :width="c.width"
            >
              <template #default="scope">
                <BaseInfoCell
                  v-if="scope.row.actionTrackUrl"
                  :name="scope.row.actionTrackUrl"
                />
              </template>
            </el-table-column>
            <CustomTableColumn
              v-else
              :key="i"
              :data="c"
              :render-map="renderMap"
            />
          </template>
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { getListProject } from '@/api/statistics/plan'
import { updateStatus } from '@/api/statistics/common'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { mapGetters } from 'vuex'
import { loadPageSize } from '@/utils/beforeList'
export default {
  name: 'PlanStatisticsList',
  components: {
    BaseInfoCell
  },
  dicts: ['media_type'],
  props: {
    defaultMediaType: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 媒体账户报表表格数据
      statisticsList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mediaType: null,
        goodsPlatform: null,
        advertiserId: null,
        advertiserName: null,
        projectId: null,
        planId: null,
        planName: null,
        deptIds: [],
        createBy: null
      },
      // 日期范围
      dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      selections: [],
      multiple: true

    }
  },
  computed: {
    ...mapGetters(['device', 'permissions', 'roles', 'tableHeight']),
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    }
  },
  created() {
    if (this.defaultMediaType) { this.queryParams.mediaType = this.defaultMediaType }
    this.queryParams.advertiserId = this.$route.query.advertiserId
    // loadPageSize(this.queryParams)
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      if (!this.queryParams.advertiserId) {
        this.$message.error('广告主ID不能为空')
        return
      }
      if (this.loading) return
      this.loading = true
      const query = this.queryParams
      Promise.all([getListProject(query)])
        .then(([listResponse]) => {
          this.statisticsList = listResponse.data || []
          if (listResponse.data && listResponse.data.length) {
            this.total = listResponse.data[0].total
          } else {
            this.total = 0
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },

    handleJump(row) {
      this.$emit('routeJump', {
        advertiserId: row.advertiserId || this.queryParams.advertiserId,
        projectId: row.projectId
      })
    },
    handleOpen() {
      this.$confirm('确定要开启选中的项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          advertiserId: this.queryParams.advertiserId,
          mediaType: this.defaultMediaType,
          statusType: 0,
          type: 2,
          ids: this.ids
        }
        updateStatus([data]).then(response => {
          this.$modal.msgSuccess('开启成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleSuspend() {
      this.$confirm('确定要暂停选中的项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          advertiserId: this.queryParams.advertiserId,
          mediaType: this.defaultMediaType,
          statusType: 1,
          type: 2,
          ids: this.ids
        }
        updateStatus([data]).then(response => {
          this.$modal.msgSuccess('暂停成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleSelectionChange(selection) {
      this.selections = selection
      this.ids = selection.map(item => item.projectId)
      this.multiple = !selection.length
    }
  }
}
</script>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import useColumns from '@/hooks/useColumns'
import { MaxDangerRate, PercentValue } from '@/utils/render'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import { useRoute } from 'vue-router/composables'
const renderMap = {
  chargeBackRate: (row) => {
    return MaxDangerRate(row.chargeBackRate)
  }
}

const defaultColumns = [
  { label: `项目名称`, prop: 'projectName', width: '200' },
  { label: `投放时间类型`, prop: 'scheduleType', width: '180' },
  { label: `投放起始时间`, prop: 'startTime', width: '200' },
  { label: `投放结束时间`, prop: 'endTime', width: '200' },
  { label: `客户操作状态`, prop: 'optStatus', width: '150' },
  { label: `项目状态`, prop: 'status', width: '120' },
  { label: `投放模式`, prop: 'deliveryMode', width: '120' },
  { label: `投放类型`, prop: 'deliveryType', width: '130' },
  { label: `推广目的`, prop: 'landingType', width: '150' },
  { label: `营销场景`, prop: 'marketingGoal', width: '120' },
  { label: `广告类型`, prop: 'adType', width: '120' },
  { label: `出价方式`, prop: 'pricing', width: '200' },
  { label: `出价系数`, prop: 'searchBidRatio', width: '120' },
  { label: `下载链接`, prop: 'downloadUrl', width: '130', overflow: true },
  { label: `下载方式`, prop: 'downloadType', width: '130' },
  { label: `调起方式`, prop: 'launchType', width: '130' },
  { label: `投放内容`, prop: 'promotionType', width: '130' },
  { label: `Deeplink直达链接`, prop: 'openUrl', width: '200', overflow: true },
  { label: `ulink直达链接`, prop: 'ulinkUrl', width: '200', overflow: true },
  { label: `预约下载链接`, prop: 'subscribeUrl', width: '200', overflow: true },
  { label: `资产类型`, prop: 'assetType', width: '145' },
  { label: `小程序类型`, prop: 'microPromotionType', width: '150' },
  { label: `广告位大类`, prop: 'inventoryCatalog', width: '170' },
  {
    label: `广告投放位置（首选媒体）`,
    prop: 'inventoryType',
    width: '240',
    sortable: false
  },
  {
    label: `投放形式（穿山甲视频创意类型）`,
    prop: 'unionVideoType',
    width: '240',
    sortable: false
  },
  { label: `点击出价/展示出价`, prop: 'bid', width: '200' },
  { label: `目标转化出价/预期成本`, prop: 'cpaBid', width: '200' },
  { label: `深度优化出价`, prop: 'deepCpabid', width: '200' },
  { label: `深度转化ROI系数`, prop: 'roiGoal', width: '200' },
  // { label: `竞价策略`, prop: 'bidType', width: '200' },
  { label: `项目预算类型`, prop: 'budgetMode', width: '200' },
  { label: `项目预算`, prop: 'budget', width: '200' },
  { label: ` 展示（监测链接`, prop: 'trackUrl', width: '200', overflow: true },
  { label: `点击（监测链接）`, prop: 'actionTrackUrl', width: '200', overflow: true },
  { label: `激活（监测链接）`, prop: 'activeTrackUrl', width: '200', overflow: true },
  {
    label: `视频有效播放（监测链接）`,
    prop: 'videoPlayEffectiveTrackUrl',
    width: '240', overflow: true
  }
]

const self = getCurrentInstance().proxy
const tableRef = ref(null)
const {
  columnsInstance,
  columns,
  operatedColumns,
  customList,
  handleHeaderDragend
} = useColumns({ defaultColumns, tableRef, name: self.defaultMediaType && useRoute().name + '_projectStatistics' })

</script>
<style lang="scss" scoped>
.code-title {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #bdbdbd;
}
::v-deep .el-dialog__body {
  //padding-top: 0;
}
.conversion-footer {
  display: flex;
  justify-content: space-between;
}
.dialog-conversion-rate {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 40px;
}
</style>
