<template>
  <el-tabs v-model="activeName" style="margin-top: 0px">
    <el-tab-pane
      v-for="dict in dict.type.media_type"
      :key="dict.value"
      :name="dict.value"
      lazy
    >
      <span slot="label">
        <div
          style="padding: 0 60px"
        >
          <svg-icon :icon-class="dict.label" />
          {{ dict.label }}
          <el-tooltip class="item" effect="dark" content="跳转授权说明文档地址" placement="top">
            <i v-if="authList.includes(+dict.value)" class="el-icon-question ml10 documentTips" @click.stop="handelDocument(dict.value)" />
          </el-tooltip></div>
      </span>
      <MediaPage :media-type="dict.value" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import MediaPage from './media.vue'

export default {
  name: 'Media',
  components: {
    MediaPage
  },
  dicts: ['media_type'],
  data() {
    return {
      activeName: '1',
      loading: false,
      authList: [5, 6, 7, 10, 13]
    }
  },
  created() {
    this.setTabs()
  },
  activated() {
    this.setTabs()
  },
  methods: {
    setTabs() {
      const query = { ...this.$route.query }
      if (query.state) {
        if (query.state === '15') {
          this.activeName = '2'
        } else {
          this.activeName = query.state
        }
      } else if (query.activeTab) {
        this.activeName = query.activeTab
        delete query.activeTab
        this.$router.replace({ path: this.$route.path, query })
      }
    },
    handelDocument(type) {
      switch (+type) {
        case 5:
          window.open('https://ziuqxc1rltx.feishu.cn/docx/PIeqdcNY4oeafwx8cdqcC3cnnTf#share-ASaZdD8MNo9WEox4uQQcVSJenld')
          break
        case 13:
          window.open('https://ziuqxc1rltx.feishu.cn/docx/PIeqdcNY4oeafwx8cdqcC3cnnTf#share-MaTXdQKn7o727Ax12Z4cdaBNnYj')
          break
        case 7:
          window.open('https://ziuqxc1rltx.feishu.cn/docx/Q8X1dGPenoRsFKxqtPrcqmrknKf')
          break
        case 6:
          window.open('https://ziuqxc1rltx.feishu.cn/docx/PIeqdcNY4oeafwx8cdqcC3cnnTf#share-ZNL2dt7TRob24HxYiAocz8vgnCd')
          break
        case 10:
          window.open('https://ziuqxc1rltx.feishu.cn/docx/PIeqdcNY4oeafwx8cdqcC3cnnTf#share-RGwfdXurAoKbFuxetFjcZoq7nCd')
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__item{
  height: 42px;
  line-height: 42px;
  font-size: 16px;
  font-weight: bold;
}

::v-deep .el-tabs__nav-prev {
  width: 24px;
  text-align: center;
}
::v-deep .el-tabs__nav-next  {
  width: 24px;
  text-align: center;
}
</style>
