<template>
  <div>
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="90px"
    >
      <el-form-item v-if="isChild" prop="parentAccountId">
        <CTreeDrop
          v-model="queryParams.parentAccountId"
          :data="parentList"
          title-field="label"
          style="width: 190px"
          :dropdown-min-width="200"
          selectable
          clearable
          default-expand-all
          @select="handleQuery"
        >
          <template #display="scope">
            <div
              :class="[scope.selectedNode ? 'c-tree-overflow': 'c-tree-placeholder']"
            >
              {{ scope.selectedNode? scope.selectedNode.label : '父账户' }}
            </div>
          </template>
        </CTreeDrop>
      </el-form-item>
      <el-form-item prop="advertiserIds">
        <el-input
          v-model.trim="queryParams.advertiserIds"
          placeholder="账户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />  </el-form-item>
      <el-form-item prop="advertiserName">
        <el-input
          v-model.trim="queryParams.advertiserName"
          placeholder="账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <UserSearchInput
        :create-by.sync="queryParams.createBy"
        @query="handleQuery"
      />
      <el-form-item prop="remark">
        <el-input
          v-model.trim="queryParams.remark"
          placeholder="备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="isEnableConversion">
        <el-select
          v-model="queryParams.isEnableConversion"
          placeholder="是否扣回传"
          clearable
          @change="handleQuery"
        >
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <BusinessSelector :business.sync="queryParams.business" @query="handleQuery" />
      <el-form-item prop="isValid">
        <el-select v-model="queryParams.isValid" placeholder="是否停用" clearable @change="handleQuery">
          <el-option label="启用" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item prop="deptIds">
        <DeptTreeSelect v-model="queryParams.deptIds" :options="deptOptions" />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :storage-key="`media_${mediaType}`"
          @search="handleQuery"
        />
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <!-- 核心操作按钮 -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:media:add']"
          type="primary"
          plain
          icon="el-icon-unlock"
          size="mini"
          @click="handleAuth"
        >账户授权</el-button>
      </el-col>

      <el-col v-if="isChild" key="toListParent" :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="toListParent"
        >查看父账户</el-button>
      </el-col>

      <el-col v-if="!isChild" :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-close"
          size="mini"
          @click="handleClose"
        >关闭</el-button>
      </el-col>
      <el-col v-if="isChild" key="handleJoinComment" :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          :disabled="multiple"
          icon="el-icon-s-flag"
          @click="handleJoinComment(selections.map((item) => item.advertiserId))"
        >
          评论控制
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-tooltip
          content="请勿选择主账户"
          :disabled="!hasParent"
          placement="top"
        >
          <el-button
            v-hasPermi="['promotion:media:upmonitor']"
            plain
            size="mini"
            icon="el-icon-sort"
            :disabled="multiple || hasParent"
            @click="handleReplaceLink()"
          >替换链接</el-button>
        </el-tooltip>
      </el-col>

      <!-- 数据同步下拉菜单 -->
      <el-col :span="1.5">
        <el-dropdown :disabled="multiple" @command="handleSyncDropdownCommand">
          <el-button
            plain
            size="mini"
            icon="el-icon-refresh"
            :disabled="multiple"
          >
            数据同步<i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-hasPermi="['promotion:media:advsync']" command="syncAccount">同步账户报表</el-dropdown-item>
            <el-dropdown-item v-if="isChild" command="updateFund">同步账户余额</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>

      <!-- 批量操作下拉菜单 -->
      <el-col :span="1.5">
        <el-dropdown :disabled="multiple" @command="handleBatchDropdownCommand">
          <el-button
            plain
            size="mini"
            icon="el-icon-s-operation"
            :disabled="multiple"
          >
            批量操作<i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-hasPermi="['promotion:media:transfer']" command="transfer">一键移交</el-dropdown-item>
            <el-dropdown-item v-if="isChild" command="updateConversion">修改回传比例</el-dropdown-item>
            <el-dropdown-item command="updateRemark">批量备注</el-dropdown-item>
            <template v-if="mediaType === '1'">
              <el-dropdown-item v-has-permi="['promotion:media:edit']" command="batchBid">批量修改出价</el-dropdown-item>
            </template>
            <el-dropdown-item command="createEventInAsset">创建app内下单事件</el-dropdown-item>
            <el-dropdown-item v-hasPermi="['promotion:media:remove']" command="batchDelete" class="color-danger" divided>批量删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>

      <!-- 特殊功能按钮 -->
      <el-col v-if="mediaType === '2'" :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="showJointCommissioning"
        >联调</el-button>
      </el-col>

      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="mediaList"
      stripe
      border
      :cell-class-name="firstCellClassName"
      @selection-change="handleSelectionChange"
      @expand-change="defaultExpandedKeys"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        type="index"
        label="序号"
        align="center"
        width="50"
      />
      <!--      <el-table-column label="序号" type="index" width="50" align="center" />-->
      <el-table-column label="账户名称" prop="advertiserName" min-width="240">
        <template #default="scope">
          <div style="display: flex; gap: 5px">
            <BaseInfoCell
              :id="scope.row.advertiserId"
              style="flex: 1"
              :name="
                (scope.row.parentAccountId ? '' : '> ') +
                  scope.row.advertiserName
              "
              @nameClick="toChildren(scope.row)"
            />
            <div>
              <div>
                <el-tooltip
                  v-if="scope.row.tokenRefreshErrorMsg"
                  class="item"
                  :content="'刷新Token：' + scope.row.tokenRefreshErrorMsg"
                  placement="top"
                >
                  <i
                    class="el-icon-warning"
                    style="color: #f56c6c; font-size: 16px"
                  />
                </el-tooltip>
              </div>
              <div>
                <el-tooltip
                  v-if="scope.row.reportStatisticsErrorMsg"
                  class="item"
                  :content="'报表同步：' + scope.row.reportStatisticsErrorMsg"
                  placement="top"
                >
                  <i
                    class="el-icon-warning"
                    style="color: #f56c6c; font-size: 16px"
                  />
                </el-tooltip>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="270"
      >
        <template #default="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-order"
            :disabled="scope.row.isValid==='0'"
            @click="showUpdateRemark(scope.row)"
          >添加备注</el-button>
          <template v-if="!scope.row.hasChildren">
            <template v-if="mediaType === '1' || mediaType === '4'">
              <el-button
                v-hasPermi="['promotion:media:upmonitor']"
                size="mini"
                type="text"
                icon="el-icon-link"
                :disabled="scope.row.isValid==='0'"
                @click="handleReplaceLink(scope.row)"
              >替换链接</el-button>
            </template>
            <el-button
              v-if="mediaType !== '3'"
              size="mini"
              type="text"
              icon="el-icon-s-flag"
              :disabled="scope.row.isValid==='0'"
              @click="handleJoinComment(scope.row)"
            >评论控制</el-button>
          </template>
          <el-dropdown
            placement="bottom-start"
            trigger="click"
            :disabled="scope.row.isValid==='0'"
            @command="handleCommand($event, scope.row)"
          >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-more"
              style="margin: 0 7px"
            >更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <template v-if="mediaType === '1' || mediaType === '4'">
                <el-dropdown-item
                  v-if="scope.row.parentAccountId"
                  v-hasPermi="['promotion:media:batchReplaceDirectLinks']"
                  icon="el-icon-s-order"
                  command="handleCheckReplaceLog"
                >查看替换日志</el-dropdown-item>

              </template>
              <el-dropdown-item
                v-if="!scope.row.parentAccountId && mediaType !== '2'"
                v-hasPermi="['promotion:media:sync']"
                icon="el-icon-refresh"
                command="handleSync"
              >增量同步账户</el-dropdown-item>
              <el-dropdown-item
                v-hasPermi="['promotion:media:sync']"
                icon="el-icon-refresh"
                command="handleSyncMediaAccount"
              >同步账户名称</el-dropdown-item>
              <el-dropdown-item
                v-if="mediaType === '8'"
                icon="el-icon-share"
                command="showBdJoin"
              >百度联调</el-dropdown-item>
              <el-dropdown-item
                v-hasPermi="['promotion:advertising:list']"
                icon="el-icon-film"
                command="showAdvertising"
              >查看广告</el-dropdown-item>
              <template v-if="!scope.row.hasChildren">
                <el-dropdown-item
                  icon="el-icon-discount"
                  command="updateConversion"
                >修改回传比例</el-dropdown-item>
                <el-dropdown-item
                  icon="el-icon-refresh"
                  command="updateFund"
                >
                  同步账户余额
                </el-dropdown-item>
              </template>
              <!-- <template v-if="scope.row.hasChildren">
                  <el-dropdown-item
                    v-if="!scope.row.advertiserId.includes('--')"
                    v-has-permi="['promotion:media:custom']"
                    icon="el-icon-plus"
                    command="addParentAccount"
                  >新增账户组</el-dropdown-item>
                  <el-dropdown-item
                    v-has-permi="['promotion:media:custom']"
                    icon="el-icon-edit"
                    command="updateParentAccount"
                  >更新账户组</el-dropdown-item>
                </template> -->
              <!-- <el-dropdown-item
                v-if="isJl(mediaType)"
                icon="el-icon-link"
                command="updateTrackUrl"
              >更新监控链接</el-dropdown-item>
              <el-dropdown-item
                v-if="isJl(scope.row.mediaType)"
                icon="el-icon-circle-close"
                style="color: #f56c6c"
                command="clearDirectLinks"
              >清除直达链接</el-dropdown-item> -->
              <el-dropdown-item
                v-if="!mediaAuthList.includes(scope.row.mediaType)"
                v-hasPermi="['promotion:media:edit']"
                icon="el-icon-circle-close"
                command="handleDeleteJLAuth"
                style="color: #f56c6c"
              >删除巨量鉴权</el-dropdown-item>
              <el-dropdown-item
                v-hasPermi="['promotion:media:remove']"
                icon="el-icon-circle-close"
                command="handleDelete"
                style="color: #f56c6c"
              >删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <!--        <el-table-column label="账户ID" align="center" prop="advertiserId" width="180">-->
      <!--          <template #default="scope">-->
      <!--            <span>{{ scope.row.advertiserId }}</span>-->
      <!--            <el-button v-clipboard:copy="scope.row.advertiserId" v-clipboard:success="clipboardSuccess" type="text">-->
      <!--              <i class="el-icon-document-copy" />-->
      <!--            </el-button>-->
      <!--          </template>-->
      <!--        </el-table-column>-->
      <el-table-column
        label="状态"
        align="center"
        prop="isValid"
        width="110"
      >
        <template #header>
          <span>状态</span>
          <el-tooltip
            effect="dark"
            content="停用后，该账户的所有操作将失效"
            placement="top"
          >
            <i class="el-icon-question color-danger" />
          </el-tooltip>
        </template>
        <template #default="{row}">
          <el-switch
            v-model="row.isValid"
            class="mr5"
            active-color="#DCDFE6"
            inactive-color="#ff4949"
            active-value="1"
            inactive-value="0"
            @change="handleVaildChange(row)"
          />
          <span v-if="row.isValid==='0'" class="color-danger text-bold">停用</span>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        width="150"
        show-overflow-tooltip
      >
        <template #default="scope">
          <!--            <el-tooltip v-if="scope.row.remark" class="item" :content="scope.row.remark" placement="top">-->
          <!--              <i class="el-icon-info color-primary" style="font-size: 16px" />-->
          <!--            </el-tooltip>-->
          <span class="pointer" @click="showUpdateRemark(scope.row)">
            <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
            <span v-else>—</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="mediaType !== '3'"
        label="评控状态"
        align="center"
        prop="controlStatus"
        width="140"
      >
        <template #default="scope">
          <span>{{ ControlStatusMap[scope.row.controlStatus] }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
          label="回传金额比例"
          align="center"
          prop="enableAmount"
          width="130"
        >
          <template #header>
            <span>回传金额比例</span>
            <el-tooltip
              effect="dark"
              content="回传金额比例开关关闭时，100%回传"
              placement="top"
            >
              <i class="el-icon-question color-primary" />
            </el-tooltip>
          </template>
          <template #default="scope">
            <ConversionItem
              v-if="!scope.row.hasChildren"
              :form.sync="scope.row"
              item-type="amount"
              :type="conversionType"
              :disabled="scope.row.isValid==='0'"
              @setConversion="showConversion"
              @switchConversion="getList"
            />
          </template>
        </el-table-column> -->
      <el-table-column
        label="回传配置"
        align="center"
        prop="conversionProportion"
        width="130"
      >
        <template #header>
          <span>回传配置</span>
          <el-tooltip
            effect="dark"
            content="回传配置开关关闭时，100%回传"
            placement="top"
          >
            <i class="el-icon-question color-primary" />
          </el-tooltip>
        </template>
        <template #default="scope">
          <ConversionItem
            v-if="!scope.row.hasChildren"
            :form.sync="scope.row"
            item-type="order"
            :type="conversionType"
            :disabled="scope.row.isValid==='0'"
            @setConversion="showConversion"
            @switchConversion="getList"
          />
        </template>
      </el-table-column>
      <!-- <el-table-column
          label="千展阈值"
          align="center"
          prop="kilobarThreshold"
          width="130"
        >
          <template #default="scope">
            <el-button
              type="text"
              :disabled="scope.row.isValid==='0'"
              @click="showPreWarning(scope.row)"
            >{{
              scope.row.kilobarThreshold || "-.--"
            }}</el-button>
          </template>
        </el-table-column> -->
      <!--        <el-table-column label="刷新Token" align="center" prop="tokenRefreshErrorMsg" width="100">-->
      <!--          <template #default="scope">-->
      <!--            <el-tooltip v-if="scope.row.tokenRefreshErrorMsg" class="item" :content="scope.row.tokenRefreshErrorMsg" placement="top">-->
      <!--              <i class="el-icon-warning" style="color:#F56C6C;font-size: 16px" />-->
      <!--            </el-tooltip>-->
      <!--            <span v-else>-</span>-->
      <!--          </template>-->
      <!--        </el-table-column>-->
      <!--        <el-table-column label="报表同步" align="center" prop="reportStatisticsErrorMsg" width="100">-->
      <!--          <template #default="scope">-->
      <!--            <el-tooltip v-if="scope.row.reportStatisticsErrorMsg" class="item" :content="scope.row.reportStatisticsErrorMsg" placement="top">-->
      <!--              <i class="el-icon-warning" style="color:#F56C6C;font-size: 16px" />-->
      <!--            </el-tooltip>-->
      <!--            <span v-else>-</span>-->
      <!--          </template>-->
      <!--        </el-table-column>-->
      <el-table-column
        label="账户余额"
        align="center"
        prop="fund"
        width="100"
      />
      <!-- <el-table-column
          label="盈亏成本"
          align="center"
          prop="profit"
          width="120"
        >
          <template #header>
            <span>盈亏成本</span>
            <el-tooltip
              effect="dark"
              content="盈亏成本 = 拿货成本 + 运费 + 其它非广告消耗成本"
              placement="top"
            >
              <i class="el-icon-question color-primary" />
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-button type="text" @click="setRoi(scope.row)">{{
              scope.row.profit || "-.--"
            }}</el-button>
          </template>
        </el-table-column> -->
      <!-- <el-table-column
          label="盈亏ROI"
          align="center"
          prop="profitRoi"
          width="120"
        >
          <template #header>
            <span>盈亏ROI</span>
            <el-tooltip
              effect="dark"
              content="设置预估的保本ROI"
              placement="top"
            >
              <i class="el-icon-question color-primary" />
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-button type="text" @click="setRoi(scope.row)">{{
              scope.row.profitRoi || "-.--"
            }}</el-button>
          </template>
        </el-table-column> -->
      <!-- 添加充值返点列 -->
      <!-- <el-table-column
          label="充值返点"
          align="center"
          prop="profitBackRate"
          width="120"
        >
          <template #default="scope">
            <el-button type="text" @click="setRoi(scope.row)">
              {{ scope.row.profitBackRate ? scope.row.profitBackRate / 10 + '%' : '--' }}
            </el-button>
          </template>
        </el-table-column> -->
      <el-table-column
        v-if="!(isBili(mediaType) && isZh(mediaType)) && checkPermi(['promotion:media:token'])"
        label="过期时间"
        align="center"
        prop="createTime"
        width="280"
      >
        <template #header>
          <span>过期时间</span>
          <el-tooltip
            effect="dark"
            content="点击refresh过期时间跳转账户授权"
            placement="top"
          >
            <i class="el-icon-question color-primary" />
          </el-tooltip>
        </template>
        <template #default="scope">
          <div style="text-align: left">
            <span style="display: inline-block; width: 112px">token过期时间:
            </span>
            <span :style="{ color: handleExpiresIn(scope.row) }">
              {{ scope.row.expiresIn ?? "" }}</span>
          </div>
          <div style="text-align: left">
            <span style="display: inline-block; width: 112px">refresh过期时间:
            </span>
            <span
              :class="[handleRefreshExpiresIn(scope.row)]"
              @click="toHandleAuth(scope.row)"
            >
              {{ scope.row.refreshTokenExpiresIn ?? "" }}
            </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="父ID"
        align="center"
        prop="parentAccountId"
        width="180"
      >
        <template #default="scope">
          <template v-if="scope.row.parentAccountId">
            <el-button
              type="text"
              class="parent-id"
              @click="queryChildren(scope.row.parentAccountId)"
            >{{ scope.row.parentAccountId }}</el-button>
            <el-button
              v-clipboard:copy="scope.row.parentAccountId"
              v-clipboard:success="clipboardSuccess"
              type="text"
            >
              <i class="el-icon-document-copy" />
            </el-button>
          </template>
          <template v-else>-</template>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="isValid" width="80">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.media_account_valid"
            :value="scope.row.isValid"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="用户信息"
        align="center"
        prop="nickName"
        width="180"
      >
        <template #default="scope">
          <el-tooltip
            :disabled="!scope.row.nickName"
            effect="dark"
            :content="scope.row.nickName"
            placement="top"
          >
            <div
              style="
                  text-align: left;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                "
            >
              <span style="display: inline-block; width: 64px">用户昵称:
              </span>
              <span>{{ scope.row.nickName ?? "" }}</span>
            </div>
          </el-tooltip>
          <div style="text-align: left">
            <span style="display: inline-block; width: 64px">负责人: </span>
            <span>{{ scope.row.createBy ?? "" }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        v-if="checkPermi(['promotion:config:duoduo'])"
        label="商务负责人"
        align="center"
        prop="createBy"
        width="150"
      >
        <template #default="scope">{{
          scope.row.business
            ? `${scope.row.business} (${scope.row.personInCharge})`
            : "-"
        }}
        </template>
      </el-table-column>
      <el-table-column
        label="公司信息"
        align="center"
        prop="deptName"
        width="200"
      >
        <template #default="scope">
          <div style="text-align: left">
            {{ "部门名称: " + (scope.row.deptName ?? "") }}
          </div>
          <div
            style="text-align: left"
            class="overflow-text"
            :title="scope.row.categoryName"
          >
            {{ "所属公司: " + (scope.row.firstDeptName ?? "") }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作时间"
        align="center"
        prop="createTime"
        width="220"
      >
        <template #default="scope">
          <div style="text-align: left">
            {{ "创建时间: " + (scope.row.createTime ?? "") }}
          </div>
          <div style="text-align: left">
            {{ "更新时间: " + (scope.row.updateTime ?? "") }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[10, 50, 100, 500, 1000]"
      @pagination="getList"
    />
    <!-- 添加或修改媒体账户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账户ID" prop="advertiserId">
          <el-input v-model="form.advertiserId" placeholder="请输入账户ID" />
        </el-form-item>
        <el-form-item label="账户名称" prop="advertiserName">
          <el-input
            v-model="form.advertiserName"
            placeholder="请输入账户名称"
          />
        </el-form-item>
        <el-form-item label="父账户ID" prop="parentAccountId">
          <el-input
            v-model="form.parentAccountId"
            placeholder="请输入父账户ID"
          />
        </el-form-item>
        <el-form-item label="状态" prop="isValid">
          <el-input v-model="form.isValid" placeholder="请输入状态" />
        </el-form-item>
        <el-form-item label="Token令牌" prop="accessToken">
          <el-input v-model="form.accessToken" placeholder="请输入Token令牌" />
        </el-form-item>
        <el-form-item label="令牌过期时间" prop="expiresIn">
          <el-date-picker
            v-model="form.expiresIn"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择令牌过期时间"
          />
        </el-form-item>
        <el-form-item label="刷新令牌" prop="refreshToken">
          <el-input v-model="form.refreshToken" placeholder="请输入刷新令牌" />
        </el-form-item>
        <el-form-item label="刷新过期时间" prop="refreshTokenExpiresIn">
          <el-date-picker
            v-model="form.refreshTokenExpiresIn"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择刷新过期时间"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="commander">
          <el-input v-model="form.commander" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="删除状态" prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入删除状态" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--  选择授权账户   -->
    <el-dialog
      title="选择授权账户"
      width="900px"
      :visible.sync="authDialogVisible"
      top="5vh"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form size="small" :inline="true" label-width="80px">
        <!--              <el-form-item  v-if="mediaType === '1'" label="组织方式">-->
        <!--                <el-radio-group v-model="userParent">-->
        <!--                  &lt;!&ndash;            <el-radio label="0">组织</el-radio>&ndash;&gt;-->
        <!--                  <el-radio label="1">巨量用户</el-radio>-->
        <!--                </el-radio-group>-->
        <!--              </el-form-item>-->
        <el-row>
          <el-col :span="8">
            <el-form-item label="自动移交">
              <div class="vc-layout">
                <el-switch
                  v-model="changeCreateBy"
                  :active-value="1"
                  :inactive-value="0"
                  active-color="#67C23A"
                  inactive-color="#9f9f9f"
                />
                <span :style="{ color: changeCreateBy ? '#67C23A' : '#9f9f9f' }">{{
                  changeCreateBy ? "是" : "否"
                }}</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="默认不修改原始授权的负责人；开启后将会强制将他人名下的账户，转移到当前的授权人。"
                  placement="top"
                >
                  <i class="el-icon-question" />
                </el-tooltip>
              </div>
            </el-form-item>
          </el-col>
          <el-col v-if="isJl(mediaType)" :span="8">
            <el-form-item label="创建资产">
              <div class="vc-layout">
                <el-switch
                  v-model="createAsset"
                  :active-value="1"
                  :inactive-value="0"
                  active-color="#67C23A"
                  inactive-color="#9f9f9f"
                />
                <span :style="{ color: createAsset ? '#67C23A' : '#9f9f9f' }">{{
                  createAsset ? "是" : "否"
                }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form
        ref="authQueryForm"
        :model="authQueryParams"
        size="small"
        :inline="true"
        label-width="80px"
        class="search-form"
      >
        <el-form-item prop="advertiserId">
          <el-input
            v-model="authQueryParams.advertiserId"
            placeholder="账户ID"
            clearable
            @keyup.enter.native="handleFilterAuthAccount"
          />
        </el-form-item>
        <el-form-item prop="advertiserName">
          <el-input
            v-model.trim="authQueryParams.advertiserName"
            placeholder="账户名称"
            clearable
            @keyup.enter.native="handleFilterAuthAccount"
          />
        </el-form-item>
        <el-form-item prop="authorized">
          <el-select
            v-model="authQueryParams.authorized"
            placeholder="是否授权"
            clearable
            @change="handleFilterAuthAccount"
          >
            <el-option label="未授权" :value="false" />
            <el-option label="已授权" :value=" true" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleFilterAuthAccount"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetFilterAuthAccount"
          >重置</el-button>
        </el-form-item>
      </el-form>
      <div
        v-if="authDialogVisible"
        class="auth-table-wrap"
      >
        <div class="mb10">
          <el-button size="mini" icon="el-icon-plus" @click="$refs.authTable.setAllTreeExpansion()">展开所有</el-button>
          <el-button size="mini" icon="el-icon-minus" @click="$refs.authTable.clearTreeExpand()">关闭所有</el-button>
        </div>
        <u-table
          ref="authTable"
          v-loading="authLoading"
          fixed-columns-roll
          beautify-table
          :height="450"
          :tree-config="{
            children: 'filterChildren',
            expandAll: false}"
          use-virtual
          row-id="advertiserId"
          border
        >
          <u-table-column
            prop="checked"
            label=""
            width="60"
          >
            <template slot-scope="scope">
              <div class="flex align-center">
                <div class="check-box" :class="{'check-box-active': authMap[scope.row.advertiserId].checked}" @click="handleAuthSelect(scope.row)">
                  <i class="el-icon-check" />
                </div>
                <template v-if="isAuthFilter && !scope.row.parentAccountId">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="筛选后，勾选父账户仅勾选已筛选子账户"
                    placement="top"
                  >
                    <i class="el-icon-question color-primary" />
                  </el-tooltip>
                </template>
              </div>
            </template>
          </u-table-column>
          <u-table-column
            :tree-node="true"
            prop="advertiserId"
            label="账户ID"
          />
          <u-table-column
            prop="advertiserName"
            label="账户名称"
          />
          <u-table-column
            prop="authorized"
            label="是否授权"
            width="100"
          >
            <template #default="scope">
              <el-tag :type="scope.row.authorized ? 'success' : 'info'">
                {{ scope.row.authorized ? '已授权' : '未授权' }}
              </el-tag>
            </template>
          </u-table-column>

        </u-table>
      </div>
      <div class="auth-select-all">
        <el-button
          size="mini"
          :type="isSelectAll ? 'default' : 'primary'"
          @click="authSelectAll(!isSelectAll)"
        >{{ isSelectAll ? "全不选" : "全选" }}</el-button>已选择账户（{{ `${authSelection.length} / ${authSize}` }}）
        <!--      <div class="mt10">已有账户（{{ `${authSize}` }}）-->
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="submitAuth"
        >确 定</el-button>
        <el-button @click="handleAuthCancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="uc授权账户"
      width="700px"
      :visible.sync="ucAuthDialogVisible"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="cancelUcAuth"
    >
      <template slot="title">
        uc授权账户
        <el-tooltip class="item" effect="dark" content="跳转授权说明文档地址" placement="top">
          <i class="el-icon-question ml10 documentTips" @click="handelDocument('https://ziuqxc1rltx.feishu.cn/docx/Q8X1dGPenoRsFKxqtPrcqmrknKf')" />
        </el-tooltip>
      </template>
      <el-form
        ref="ucAuthorizeForm"
        :model="ucAuthorizeForm"
        :rules="ucRules"
        size="small"
        label-width="90px"
      >
        <el-form-item label="账户ID" prop="advertiserId">
          <el-input
            v-model="ucAuthorizeForm.advertiserId"
            placeholder="请输入账户ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="账户名称" prop="advertiserName">
          <el-input
            v-model="ucAuthorizeForm.advertiserName"
            placeholder="请输入账户名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="UC账户名" prop="ucUsername">
          <el-input
            v-model="ucAuthorizeForm.ucUsername"
            placeholder="请输入UC账户名"
            clearable
          />
        </el-form-item>
        <el-form-item label="UC密码" prop="ucPassword">
          <el-input
            v-model="ucAuthorizeForm.ucPassword"
            placeholder="请输入UC密码"
            clearable
            type="password"
          />
        </el-form-item>
        <el-form-item label="Token" prop="ucToken">
          <el-input
            v-model="ucAuthorizeForm.ucToken"
            placeholder="请输入token"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="submitUCAuth"
        >确 定</el-button>
        <el-button @click="cancelUcAuth">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="支付宝授权账户"
      width="700px"
      :visible.sync="dhAuthDialogVisible"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="cancelDHAuth"
    >
      <template slot="title">
        支付宝授权账户
        <el-tooltip class="item" effect="dark" content="跳转授权说明文档地址" placement="top">
          <i class="el-icon-question ml10 documentTips" @click="handelDocument('https://ziuqxc1rltx.feishu.cn/docx/PIeqdcNY4oeafwx8cdqcC3cnnTf#share-ZNL2dt7TRob24HxYiAocz8vgnCd')" />
        </el-tooltip>
      </template>
      <div class="color-danger" style="padding: 0 20px 20px">
        提示: 添加支付宝授权账户时请先联系技术人员。
      </div>
      <el-form
        ref="dhAuthorizeForm"
        :model="dhAuthorizeForm"
        :rules="dhRules"
        size="small"
        label-width="90px"
      >
        <el-form-item label="用户ID" prop="userId">
          <el-input
            v-model="dhAuthorizeForm.userId"
            placeholder="请输入用户ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="商家ID" prop="principalId">
          <el-input
            v-model="dhAuthorizeForm.principalId"
            placeholder="请输入商家ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="商家标识" prop="principalTag">
          <el-input
            v-model="dhAuthorizeForm.principalTag"
            placeholder="请输入商家标识"
            clearable
          />
        </el-form-item>
        <el-form-item label="商家名称" prop="advertiserName">
          <el-input
            v-model="dhAuthorizeForm.advertiserName"
            placeholder="请输入商家名称"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="商家应用授权令牌"
          prop="appAuthToken"
          label-width="146px"
        >
          <el-input
            v-model="dhAuthorizeForm.appAuthToken"
            placeholder="请输入商家应用授权令牌"
            clearable
          />
        </el-form-item>
        <el-form-item label="商家权限令牌" prop="bizToken" label-width="118px">
          <el-input
            v-model="dhAuthorizeForm.bizToken"
            placeholder="请输入商家权限令牌"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="submitDHAuth"
        >确 定</el-button>
        <el-button @click="cancelDHAuth">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="Bilibili授权账户"
      width="700px"
      :visible.sync="blAuthDialogVisible"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="cancelBLAuth"
    >
      <template slot="title">
        Bilibili授权账户
        <el-tooltip class="item" effect="dark" content="跳转授权说明文档地址" placement="top">
          <i class="el-icon-question ml10 documentTips" @click="handelDocument('https://ziuqxc1rltx.feishu.cn/docx/PIeqdcNY4oeafwx8cdqcC3cnnTf#share-ASaZdD8MNo9WEox4uQQcVSJenld')" />
        </el-tooltip>
      </template>
      <el-form
        ref="blAuthorizeForm"
        :model="blAuthorizeForm"
        :rules="blRules"
        size="small"
        label-width="130px"
      >
        <el-form-item label="账户名称" prop="name">
          <el-input
            v-model="blAuthorizeForm.name"
            placeholder="请输入账户名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="三连推广账户ID" prop="account_id">
          <el-input
            v-model="blAuthorizeForm.account_id"
            placeholder="请输入三连推广账户ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="动态授权码" prop="access_code">
          <el-input
            v-model="blAuthorizeForm.access_code"
            placeholder="请输入动态授权码"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="submitBLAuth"
        >确 定</el-button>
        <el-button @click="cancelBLAuth">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="知乎授权账户"
      width="700px"
      :visible.sync="zhAuthDialogVisible"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="cancelBLAuth"
    >

      <template slot="title">
        知乎授权账户
        <el-tooltip class="item" effect="dark" content="跳转授权说明文档地址" placement="top">
          <i class="el-icon-question ml10 documentTips" @click="handelDocument('https://ziuqxc1rltx.feishu.cn/docx/PIeqdcNY4oeafwx8cdqcC3cnnTf#share-MaTXdQKn7o727Ax12Z4cdaBNnYj')" />
        </el-tooltip>
      </template>
      <el-form
        ref="zhAuthorizeForm"
        :model="zhAuthorizeForm"
        :rules="zhRules"
        size="small"
        label-width="130px"
      >
        <el-form-item label="账户ID" prop="accountId">
          <el-input
            v-model="zhAuthorizeForm.accountId"
            placeholder="请输入账户ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="账户名称" prop="name">
          <el-input
            v-model="zhAuthorizeForm.name"
            placeholder="请输入账户名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="Token" prop="accessCode">
          <el-input
            v-model="zhAuthorizeForm.accessCode"
            placeholder="请输入Token"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="submitZHAuth"
        >确 定</el-button>
        <el-button @click="cancelZHAuth">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="快手授权账户"
      width="300px"
      :visible.sync="ksAuthDialogVisible"
      append-to-body
      @closed="ksAuthDialogVisible = false"
    >
      <div class="ks-auth-dialog">
        <el-button type="primary" plain @click="toKsAuth(2)">快手授权</el-button>
        <el-button type="primary" plain @click="toKsAuth(15)">磁力金牛授权</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="customType === 'add' ? '新增账户组' : '编辑账户组'"
      width="900px"
      :visible.sync="parentVisible"
      top="5vh"
      append-to-body
      :close-on-click-modal="false"
    >
      <ParentAccountEditor
        v-if="parentVisible"
        :parent="form"
        :type="customType"
        @success="handleCustomSuccess"
        @cancel="parentVisible = false"
      />
    </el-dialog>
    <el-dialog
      title="评论模板"
      :visible.sync="joinVisible"
      width="500px"
      top="10vh"
      append-to-body
    >
      <el-form ref="joinForm" :model="joinForm" label-width="80px">
        <el-form-item label="评控状态" prop="controlStatus">
          <el-radio-group v-model="joinForm.controlStatus">
            <el-radio-button label="0">{{
              mediaType === "4" ? "开启评论" : "未启用"
            }}</el-radio-button>
            <el-radio-button label="1">{{ mediaType === "4" ? "关闭评论" : "隐藏评论" }}
              <el-tooltip
                effect="dark"
                :content="`${
                  mediaType === '4'
                    ? '仅显示精选评论'
                    : '开启之后的评论会被隐藏'
                }`"
                placement="top"
              >
                <i class="el-icon-question color-primary" /> </el-tooltip></el-radio-button>
            <el-radio-button
              v-if="mediaType !== '4'"
              label="2"
            >自动回复评论
              <el-tooltip
                effect="dark"
                content="开启之后根据模板自动回复评论"
                placement="top"
              >
                <i class="el-icon-question color-primary" />
              </el-tooltip>
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-show="joinForm.controlStatus === '2'"
          label="优化模板"
          prop="templateIds"
        >
          <el-select
            v-model="joinForm.templateIds"
            multiple
            collapse-tags
            placeholder="请选择优化模板"
            style="width: 100%"
          >
            <el-option
              v-for="item in templateList"
              :key="item.id"
              :label="item.templateName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="mediaType !== '4'"
          v-show="joinForm.controlStatus !== '0'"
          label="隐藏评论"
          prop="templateIds"
        >
          <el-checkbox-group v-model="commentList">
            <el-checkbox
              v-for="dict in dict.type.emotion_type"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="submitJoin"
        >确 定</el-button>
        <el-button @click="cancelJoin">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 替换直达链接 -->
    <ReplaceUrl
      :visible.sync="replaceVisible"
      :batch="isBatchReplacement"
      :form="form"
      :selections="selections"
      :media-type="mediaType"
    />
    <!--  一键移交  -->
    <el-dialog
      title="选择一键移交目标用户"
      :visible.sync="transferVisible"
      width="50%"
      top="5vh"
      append-to-body
    >
      <el-form size="small" label-width="auto">
        <el-form-item label="移交报表和订单数据">
          <div style="display: flex; align-items: center">
            <el-switch
              v-model="synchronousOrNot"
              active-color="#13ce66"
              style="margin-right: 10px"
            />
            {{ synchronousOrNot ? "是" : "否" }}
          </div>
        </el-form-item>
      </el-form>
      <TransferUser @select="handleTransferSelect" />
    </el-dialog>

    <el-dialog
      title="广告列表"
      :visible.sync="advertisingVisible"
      width="70%"
      top="5vh"
      append-to-body
    >
      <Advertising v-if="advertisingVisible" :advertiser-id="advertisingId" />
    </el-dialog>
    <el-dialog
      title="替换日志"
      :visible.sync="replaceLogVisible"
      width="70%"
      top="5vh"
      append-to-body
    >
      <ReplaceLog
        v-if="replaceLogVisible"
        :id="form.advertiserId"
        :media-type="mediaType"
      />
    </el-dialog>
    <!-- 回传比例 -->
    <el-dialog title="修改回传配置" :visible.sync="conversionDialogVisible" width="820px" top="5vh" append-to-body>
      <ConversionSetups v-if="conversionDialogVisible" :form="conversionForm" :form-type="conversionFormType" :type="conversionType" @save="handleConversionSave" @cancel="conversionDialogVisible = false" />
    </el-dialog>

    <!--  延迟回传  -->
    <el-dialog
      title="修改扣退款"
      :visible.sync="delayVisible"
      width="500px"
      top="10vh"
      append-to-body
      @close="closeSwitchDelay"
    >
      <el-form ref="form" :model="delayForm" label-width="100px">
        <el-form-item label="自动识别时长" prop="delay">
          <el-switch v-model="delaySwitch" active-color="#13ce66" />
          <template v-if="delaySwitch">
            <el-input-number
              v-model="delayForm.delay"
              :min="60"
              :max="3600"
              :precision="0"
              label="请输入（秒）"
              :controls="false"
              style="margin: 0 10px"
            />
            <span>秒</span>
            <div>（不小于60秒，不大于3600秒）</div>
          </template>
        </el-form-item>
        <el-form-item>
          <el-button
            :loading="submitLoading"
            type="primary"
            size="mini"
            @click="saveSwitchDelay"
          >保存</el-button>
          <el-button
            type="danger"
            size="mini"
            @click="closeSwitchDelay"
          >关闭</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!--  账户预警 -->
    <el-dialog
      title="账户预警"
      :visible.sync="preWarningVisible"
      width="600px"
      top="10vh"
      append-to-body
      @close="preWarningVisible = false"
    >
      <BaseInfoCell
        v-if="!Array.isArray(preWarningForm)"
        :id="preWarningForm.advertiserId"
        :name="preWarningForm.advertiserName"
        label="名称:"
        type="info"
        style="margin-bottom: 20px"
        no-copy
      />
      <PreWarning
        v-if="preWarningVisible"
        :form="preWarningForm"
        business-type="1"
        @save="handlePreWarningSave"
        @cancel="preWarningVisible = false"
      />
    </el-dialog>

    <!--  备注  -->
    <el-dialog
      title="添加备注"
      :visible.sync="remarkVisible"
      width="700px"
      top="10vh"
      append-to-body
    >
      <el-form label-width="80px">
        <el-form-item v-if="isBatch" label="账户名称" prop="advertiserName">
          {{ form.advertiserName }}
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="mediaRemark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入内容"
            clearable
            resize="none"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="handleUpdateRemark"
        >确认</el-button>
        <el-button @click="remarkVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!--  批量同步报表  -->
    <el-dialog
      title="批量同步报表"
      :visible.sync="syncAccountsVisible"
      width="500px"
      top="10vh"
      append-to-body
    >
      <el-form label-width="80px">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :clearable="false"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="synchronizeAccounts">确认</el-button>
        <el-button @click="syncAccountsVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="联调"
      :visible.sync="jointVisible"
      width="600px"
      top="10vh"
      append-to-body
    >
      <el-form label-width="50px">
        <div style="text-align: right; margin-bottom: 10px">
          <a
            href="https://ziuqxc1rltx.feishu.cn/docx/Go1ddRtstoZrv9xlsIZcb22VnY7"
            class="right-menu-item hover-effect"
            target="_blank"
          >
            <span style="font-size: 14px; color: #1890ff"> 联调文档</span>
          </a>
        </div>
        <el-form-item label="地址">
          <el-input
            v-model="jointUrl"
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 5 }"
            placeholder="请输入点击[去联调]后获得的地址"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitJoint">确认</el-button>
        <el-button @click="jointVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <SetProfit
      :visible.sync="profitVisible"
      :items="profitItems"
      type="media"
      @success="getList"
    />

    <MediaProfitConfigDialog :visible.sync="profitConfigVisible" :media-list="formList" @success="getList" />

    <el-dialog
      title="百度联调"
      :visible.sync="bdJoinVisible"
      width="600px"
      top="10vh"
      append-to-body
    >
      <el-form
        ref="bdJoinForm"
        :model="bdJoinForm"
        label-width="90px"
        :rules="bdJoinRules"
      >
        <el-form-item label="联调URL" prop="submitUrl">
          <el-input v-model="bdJoinForm.submitUrl" />
        </el-form-item>
        <el-form-item label="联调Token" prop="token">
          <el-input v-model="bdJoinForm.token" />
        </el-form-item>
        <el-form-item label="回传事件" prop="events">
          <el-checkbox-group v-model="bdJoinForm.events">
            <el-checkbox
              v-for="dict in dict.type.report_event_baidu"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBdJoin">确认</el-button>
        <el-button @click="bdJoinVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <GoodsLibrary :visible.sync="goodsLibraryVisible" :advertiser-ids="selections.map((item) => item.advertiserId)" />
    <!-- UDS账号同步 -->
    <UDSAccountSync v-if="UDSAccountSyncVisible" :visible.sync="UDSAccountSyncVisible" :form="selections" />
    <!-- 优酷授权账户 -->
    <youkuAuthorize v-if="youkuVisible" :visible.sync="youkuVisible" @Refresh="getList" />
    <el-dialog
      title="更新监控链接"
      :visible.sync="trackUrlDialogVisible"
      width="700px"
      top="10vh"
      append-to-body
    >
      <el-form
        ref="trackUrlForm"
        :model="trackUrlForm"
        :rules="trackUrlRules"
        label-width="120px"
      >
        <el-form-item label="点击监测链接" prop="trackUrl">
          <el-input v-model="trackUrlForm.trackUrl" />
        </el-form-item>
        <el-form-item label="展示监测链接" prop="exposureRenewalUrl">
          <el-input v-model="trackUrlForm.exposureRenewalUrl" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="submitTrackUrl"
        >确 定</el-button>
        <el-button @click="trackUrlDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <GoodsSelector :visible.sync="goodsVisible" type="single" :auto-search="false" @select="handleMainProductSelect" />

    <el-dialog
      title="批量修改出价"
      :visible.sync="batchBidVisible"
      width="400px"
      append-to-body
    >
      <el-form :model="batchBidForm" label-width="80px">
        <el-form-item label="新出价" prop="newBid">
          <el-input-number v-model="batchBidForm.newBid" :min="0" :precision="2" label="请输入新出价" :controls="false" style="margin: 0 10px" />
          元
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="handleBatchBid">确认</el-button>
        <el-button @click="batchBidVisible = false">取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  listMedia,
  delMedia,
  delBatchMedia,
  addMedia,
  updateMedia,
  authorizeMediaAccount,
  changeControlStatus,
  updateRemark,
  delPublicKey,
  authBiliApp,
  ksJointCommissioning,
  listByAuth,
  getParentList,
  bdJointCommissioning,
  authorizeUCMediaAccount,
  authorizeDHMediaAccount,
  syncMediaAccount,
  syncAccountPlanName,
  createEventInAsset,
  updateFundRequest, authZhApp, updateProjectTrackUrl
} from '@/api/promotion/media'
import { getConfigKey, getDelay, updateDelay } from '@/api/system/config'
import {
  getJoinDetails,
  getTemplateList,
  saveCommentJoin
} from '@/api/system/template'
import { authCallbackMap, authUrlKeyMap } from '@/views/promotion/media/config'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { transfer } from '@/api/promotion/media'
import TransferUser from '@/components/TransferUser/index.vue'
import Advertising from '@/components/Advertising/index.vue'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import { syncPlan } from '@/api/promotion/fileres'
import ReplaceLog from '@/views/promotion/media/components/ReplaceLog.vue'

import ParentAccountEditor from '@/views/promotion/media/components/ParentAccountEditor.vue'
import ConversionSetups from '@/components/ConversionSetups/index.vue'
import PreWarning from '@/views/promotion/media/components/PreWarning.vue'
import SetProfit from '@/components/SetProfit/index.vue'
import ReplaceUrl from '@/components/ReplaceUrl/index.vue'
import { loadPageSize } from '@/utils/beforeList'
import { isKs, isKsJn, isBili, isUc, isZfb, isZh, isYouku } from '@/utils/judge/media'
import clearDirectLinks from '@/hooks/useClearDirectLinks'
import { isNil, idsToArr } from '@/utils'
import { checkRole } from '@/utils/permission'
import planReportSync from './components/planReportSync.vue'
import UDSAccountSync from './components/UDSAccountSync.vue'
import youkuAuthorize from './components/youkuAuthorize.vue'
import GoodsSelector from '@/components/GoodsSelector/index.vue'
import useTooltipVisible from '@/hooks/useTooltipVisible'
import { batchUpdateBid } from '@/api/promotion/media'

const ControlStatusMap = {
  0: '未启用',
  1: '隐藏评论',
  2: '自动回复评论'
}
export default {
  name: 'MediaPage',
  components: {
    ReplaceLog,
    BaseInfoCell,
    TransferUser,
    Advertising,
    ParentAccountEditor,
    ConversionSetups,
    PreWarning,
    SetProfit,
    ReplaceUrl,
    planReportSync,
    UDSAccountSync,
    youkuAuthorize,
    GoodsSelector
  },
  props: {
    mediaType: {
      type: String,
      default: '1'
    }
  },
  emits: ['update:mediaType'],

  dicts: ['media_account_valid', 'control_status', 'emotion_type', 'report_event_baidu'],
  data() {
    return {
      isChild: false,
      parentList: [],
      // 遮罩层
      loading: false,
      submitLoading: false,
      // 选中数组
      ids: [],
      selections: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      hasParent: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 媒体账户表格数据
      mediaList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        advertiserIds: null,
        advertiserName: null,
        deptIds: [],
        createBy: null,
        business: null,
        parentAccountId: null,
        params: {
          parentAccountId: null
        }
      },
      // 表单参数
      form: {},
      formList: [],
      // 表单校验
      rules: {
        advertiserId: [
          { required: true, message: '账户ID不能为空', trigger: 'blur' }
        ],
        advertiserName: [
          { required: true, message: '账户名称不能为空', trigger: 'blur' }
        ]
      },
      // 处理内层删除问题
      defaultExpandedKeysList: [],
      tableTreeRefreshTool: {},
      // 账户授权
      authUrl: '',
      authDialogVisible: false,
      isAuthFilter: false,
      authData: [],
      authMap: {},
      authLoading: false,
      authSize: 0,
      userParent: '1',
      changeCreateBy: 0,
      createAsset: 0,
      authSelection: [],
      disabledSelection: [],
      isChangeToken: false,
      // 账户授权查询
      authQueryParams: {
        advertiserId: null,
        advertiserName: null,
        authorized: null
      },
      // uc账户授权
      ucAuthDialogVisible: false,

      ucAuthorizeForm: {
        advertiserId: null,
        advertiserName: null,
        ucUsername: null,
        ucPassword: null,
        ucToken: null
      },
      // 支付宝账户授权
      dhAuthDialogVisible: false,

      dhAuthorizeForm: {
        userId: null,
        principalId: null,
        principalTag: null,
        advertiserName: null,
        appAuthToken: null,
        bizToken: null
      },
      // bilibili
      blAuthDialogVisible: false,

      blAuthorizeForm: {
        name: null,
        access_code: null,
        account_id: null
      },
      // 知乎
      zhAuthDialogVisible: false,
      // 优酷
      youkuVisible: false,
      // 快手, 快手金牛
      ksAuthDialogVisible: false,
      ksAuthorizeForm: {
        ksMediaType: null
      },
      zhAuthorizeForm: {
        accountId: null,
        name: null,
        accessCode: null
      },
      ucRules: {
        advertiserId: [
          { required: true, message: '账户ID不能为空', trigger: 'blur' }
        ],
        advertiserName: [
          { required: true, message: '账户名称不能为空', trigger: 'blur' }
        ],
        ucUsername: [
          { required: true, message: 'UC账户名不能为空', trigger: 'blur' }
        ],
        ucPassword: [
          { required: true, message: 'UC密码不能为空', trigger: 'blur' }
        ],
        ucToken: [
          { required: true, message: 'Token不能为空', trigger: 'blur' }
        ]
      },
      dhRules: {
        userId: [
          { required: true, message: '用户ID不能为空', trigger: 'blur' }
        ],
        principalId: [
          { required: true, message: '商家ID不能为空', trigger: 'blur' }
        ],
        principalTag: [
          { required: true, message: '商家标识不能为空', trigger: 'blur' }
        ],
        advertiserName: [
          { required: true, message: '商家名称不能为空', trigger: 'blur' }
        ],
        appAuthToken: [
          {
            required: true,
            message: '商家应用授权令牌不能为空',
            trigger: 'blur'
          }
        ],
        bizToken: [
          { required: true, message: '商家权限令牌不能为空', trigger: 'blur' }
        ]
      },
      blRules: {
        name: [
          { required: true, message: '账户名称不能为空', trigger: 'blur' }
        ],
        access_code: [
          { required: true, message: '动态授权码不能为空', trigger: 'blur' }
        ],
        account_id: [
          {
            required: true,
            message: '三连推广账户ID不能为空',
            trigger: 'blur'
          }
        ]
      },
      zhRules: {
        name: [
          { required: true, message: '账户名称不能为空', trigger: 'blur' }
        ],
        accountId: [
          { required: true, message: '账户ID不能为空', trigger: 'blur' }
        ],
        accessCode: [
          {
            required: true,
            message: 'Token不能为空',
            trigger: 'blur'
          }
        ]
      },

      conversionFormType: 'default',

      isBatchReplacement: false,
      replaceVisible: false,

      replaceLogVisible: false,

      transferId: [],
      transferVisible: false,
      synchronousOrNot: true,
      advertisingVisible: false,
      advertisingId: '',

      joinVisible: false,
      isbatchJoin: false,
      joinForm: {
        advertiserId: null,
        templateIds: [],
        controlStatus: 0
      },
      commentList: [],
      templateList: [],
      ControlStatusMap,

      delayVisible: false,
      delaySwitch: false,
      delayForm: {
        delay: 180
      },

      remarkVisible: false,
      mediaRemark: '',
      parentVisible: false,
      customType: 'add',

      preWarningVisible: false,
      preWarningForm: {},
      profitVisible: false,
      profitConfigVisible: false,
      profitItems: [],

      bdJoinVisible: false,
      bdJoinForm: {
        submitUrl: '',
        token: '',
        events: []
      },
      bdJoinRules: {
        submitUrl: [
          { required: true, message: '联调URL不能为空', trigger: 'blur' }
        ],
        token: [
          { required: true, message: '联调Token不能为空', trigger: 'blur' }
        ],
        events: [
          { required: true, message: '回传事件不能为空', trigger: 'blur' }
        ]
      },

      goodsLibraryVisible: false,
      firstRequest: false,
      UDSAccountSyncVisible: false,
      mediaAuthList: [14],
      trackUrlDialogVisible: false,
      trackUrlForm: {
        trackUrl: '',
        exposureRenewalUrl: '',
        advertiserId: '',
        mediaPlatformType: ''
      },
      trackUrlRules: {
        trackUrl: [
          { required: true, message: '请输入点击监测链接', trigger: 'blur' },
          { pattern: /^(https):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.([a-zA-Z]+))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/, message: '请输入正确的https监控链接', trigger: 'blur' }
        ],
        exposureRenewalUrl: [
          { pattern: /^(https):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.([a-zA-Z]+))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/, message: '请输入正确的https监控链接', trigger: 'blur' }
        ]
      },
      isBatch: false, // 是否为批量操作
      goodsVisible: false,

      batchBidVisible: false,
      batchBidForm: {
        newBid: 0
      }
    }
  },
  computed: {
    ...mapGetters(['permissions', 'roles', 'tableHeight']),
    advertisingPermission() {
      return (
        this.roles[0] === 'admin' ||
        this.permissions.includes('promotion:media:add')
      )
    },
    isSelectAll() {
      return this.authSelection.length === this.authSize
    }
  },
  created() {
    loadPageSize(this.queryParams)
  },
  mounted() {
    this.initPage()
    this.initSearch()
    this.handleAuthCallback()
    this.getAuthUrl()

    // this.authDialogVisible = true
    // setTimeout(() => {
    //   this.handleSelectAuthAccount(fakeData)
    // }, 1000)
    // this.getList()
  },
  activated() {
    this.initSearch()
  },
  methods: {
    initPage() {
      const { parentAccountId } = this.$route.params
      if (parentAccountId !== '0') {
        this.queryParams.params = {
          parentAccountId: '1'
        }
        this.isChild = true
        getParentList({ mediaType: this.mediaType }).then((res) => {
          this.parentList = [
            ...res.data.map((item) => ({
              label: `${item.advertiserName || '-'} (${item.advertiserId})`,
              id: item.advertiserId
            }))
          ]
        })
      }
    },
    initSearch() {
      const query = { ...this.$route.query }
      if (query && query.mediaType === this.mediaType) {
        this.queryParams.parentAccountId = query.parentAccountId
        delete query.parentAccountId
        delete query.mediaType
        this.$router.replace({ path: this.$route.path, query: query })
        this.getList()
      }
    },
    /** 查询媒体账户列表 */
    async getList() {
      // if (checkRole(['admin']) && !this.firstRequest) {
      //   this.firstRequest = true
      //   return
      // }
      this.loading = true
      if (this.mediaType) this.queryParams.mediaType = this.mediaType
      const query = { ...this.queryParams }
      query.advertiserIds = idsToArr(query.advertiserIds)

      const response = await listMedia(query)
      this.mediaList = response.rows.map((item) => {
        item.hasChildren = !item.parentAccountId
        item.rowKey = item.id
        item.businessInfo = item.business
          ? `${item.business} (${item.personInCharge})`
          : '-'
        return item
      })
      this.$nextTick(function() {
        this.expandableListView()
      })
      this.total = response.total
      this.loading = false
    },
    getTemplateList() {
      getTemplateList().then((response) => {
        this.templateList = response.rows
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        advertiserId: null,
        advertiserName: null,
        parentAccountId: null,
        isValid: null,
        controlStatus: null,
        accessToken: null,
        expiresIn: null,
        refreshToken: null,
        refreshTokenExpiresIn: null,
        commander: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleted: null
      }
      this.resetForm('form')
    },
    handleSelectionChange(selection) {
      this.selections = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
      this.hasParent = selection.some((item) => !item.parentAccountId)
    },

    handleCheckboxChange({ records }) {
      this.selections = records
      this.single = records.length !== 1
      this.multiple = !records.length
      this.hasParent = records.some((item) => !item.parentAccountId)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.$nextTick(() => {
        this.getList()
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.deptIds = null
      this.$refs.xTable.clearCheckboxRow()
      this.handleQuery()
    },
    queryChildren(id) {
      this.queryParams.parentAccountId = id
      this.getList()
    },
    handleTransfer() {
      this.transferVisible = true
      this.synchronousOrNot = true
    },
    handleTransferSelect(userId) {
      this.transferVisible = false
      transfer({
        ids: this.selections.map((item) => item.id),
        userId,
        synchronousOrNot: this.synchronousOrNot
      }).then((response) => {
        this.$message({
          message: '移交成功',
          type: 'success'
        })
        this.getList()
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.form.mediaType = this.mediaType
          if (this.form.id != null) {
            updateMedia(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addMedia(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.advertiserId || this.ids
      this.$modal
        .confirm('删除后将影响该账户报表统计，是否确认删除所选媒体账户？')
        .then(function() {
          return delMedia(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 批量删除按钮操作 */
    handleBatchDelete() {
      const arr = this.selections.map((item) => item.advertiserId)
      this.$modal
        .confirm('删除后将影响该账户报表统计，是否确认删除所选媒体账户？')
        .then(function() {
          return delBatchMedia({ ids: arr })
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 删除按钮操作 */
    handleDeleteJLAuth(row) {
      this.$modal
        .confirm(`是否确认删除媒体账户:${row.advertiserName}的巨量鉴权？`)
        .then(function() {
          return delPublicKey({
            id: row.id
          })
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除鉴权成功')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'promotion/media/export',
        {
          ...this.queryParams
        },
        `media_${new Date().getTime()}.xlsx`
      )
    },
    getAuthUrl() {
      const key = authUrlKeyMap[this.mediaType]
      getConfigKey(key).then((result) => {
        if (result.code === 200) {
          this.authUrl = result.msg
        } else {
          this.$modal.msgError('获取授权地址失败')
        }
      })
    },
    // 账户授权
    handleAuth() {
      if (isUc(this.mediaType)) {
        this.ucAuthDialogVisible = true
        return
      }
      if (isZfb(this.mediaType)) {
        this.dhAuthDialogVisible = true
        return
      }
      if (isBili(this.mediaType)) {
        this.blAuthDialogVisible = true
        return
      }
      if (isYouku(this.mediaType)) {
        this.youkuVisible = true
        return
      }
      if (isZh(this.mediaType)) {
        this.zhAuthDialogVisible = true
        return
      }

      if (isKsJn(this.mediaType) || isKs(this.mediaType)) {
        this.ksAuthDialogVisible = true
        return
      }

      window.open(this.authUrl)
      this.handleSuccessAuth()
    },
    handelDocument(url) {
      window.open(url)
    },
    toKsAuth(type) {
      getConfigKey(authUrlKeyMap[type]).then((result) => {
        if (result.code === 200) {
          window.open(result.msg)
          this.ksAuthDialogVisible = false
          this.handleSuccessAuth()
        } else {
          this.$modal.msgError('获取授权地址失败')
        }
      })
    },
    handleSuccessAuth() {
      this.$confirm('已跳转授权页面，若成功授权请点击刷新列表。', '提示', {
        confirmButtonText: '刷新',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$router.push(this.$route.path)
          this.getList()
        })
        .catch(() => {})
    },
    // 处理授权成功回调
    handleAuthCallback() {
      const { auth_code, authorization_code, state, code, authCode } =
        this.$route.query
      if (state && (auth_code || code || authorization_code || authCode)) {
        const callback = authCallbackMap[state]
        const params = {
          state
        }
        if (auth_code) params.auth_code = auth_code
        if (authorization_code) params.authorization_code = authorization_code
        if (code) params.code = code
        if (state === '3' || state === '4') { params.redirectUri = location.origin + location.pathname }
        if (state === '8') {
          Object.assign(params, this.$route.query)
        }
        this.authDialogVisible = true
        this.authLoading = true
        callback(params)
          .then((data) => {
            if (data.code === 200) {
              this.handleSelectAuthAccount(data.data)
            } else {
              this.$modal.msgError('认证失败: ', data.msg)
            }
          })
          .finally(() => {
            this.authLoading = false
          })
      }
    },
    // 选择授权账户
    handleSelectAuthAccount(data) {
      this.authData = data
      this.authMap = {}
      const authList = this.getAuthListByData()
      this.calcAuthSize(authList)
      authList.forEach((parent) => {
        parent.authorized = !!parent.authorized
        parent.checked = parent.authorized
        this.authMap[parent.advertiserId] = parent
        if (parent.children) {
          parent.children.forEach((child) => {
            child.authorized = !!child.authorized
            child.checked = parent.authorized && child.authorized
            this.authMap[child.advertiserId] = child
          })
        }
      })

      this.authSelection = Object.values(this.authMap).filter(item => item.checked)
      this.$refs.authTable.reloadData(authList)
      this.$nextTick(() => {
        this.originTableData = [...this.$refs.authTable.getUTreeData()]
        this.$refs.authTable.setAllTreeExpansion()
      })
    },
    // 筛选授权账户
    handleFilterAuthAccount() {
      let list = [...this.originTableData]
      let { advertiserId, advertiserName } = this.authQueryParams
      if (advertiserId || advertiserName) {
        advertiserId = advertiserId?.replace(/\s/g, '').split(/[，,]/) || []
        advertiserName = advertiserName || null
        list = list.filter((item) => {
          const isQuery =
            advertiserId.includes(item.advertiserId) ||
            !!~item.advertiserName?.indexOf(advertiserName)
          if (item.children?.length) {
            item.filterChildren = item.children.filter((child) => {
              const isFilter = (
                advertiserId.includes(child.advertiserId) ||
                !!~child.advertiserName?.indexOf(advertiserName)
              )
              child.isFilter = isFilter
              return isFilter
            })
          }
          return isQuery || item.filterChildren?.length
        })
      } else {
        list.forEach(item => {
          item.filterChildren = [...item.children]
        })
      }

      const { authorized } = this.authQueryParams
      if (!isNil(authorized)) {
        list = list.filter((item) => {
          if (item.children?.length) {
            item.filterChildren = item.children.filter((child) => {
              const isQuery = child.authorized === authorized
              child.isFilter = isQuery
              return isQuery
            })
          }
          return item.authorized === authorized || item.filterChildren?.length
        })
      }

      this.isAuthFilter = advertiserId || advertiserName || !isNil(authorized)

      this.$refs.authTable.reloadData(list)
      this.$refs.authTable.setAllTreeExpansion()
    },
    resetFilterAuthAccount() {
      this.resetForm('authQueryForm')
      this.handleFilterAuthAccount()
    },
    calcAuthSize(list) {
      this.authSize = list.reduce((total, item) => {
        return total + (item.children ? item.children.length : 0) + 1
      }, 0)
    },
    getAuthListByData() {
      return this.authData.map((item) => {
        const parent = item.parentAccount
        parent.children = parent.filterChildren = item.childAccountList
        return parent
      })
    },
    handleAuthSelect(item) {
      const checked = !this.authMap[item.advertiserId].checked
      this.authMap[item.advertiserId].checked = checked
      if (!item.parentAccountId && item.children) {
        item.children.forEach(child => {
          if (this.isAuthFilter) {
            this.authMap[child.advertiserId].checked = checked ? child.isFilter : checked
          } else {
            this.authMap[child.advertiserId].checked = checked
          }
        })
      }
      if (item.parentAccountId) {
        if (checked) {
          this.authMap[item.parentAccountId].checked = checked
        } else {
          this.authMap[item.parentAccountId].checked = !!this.authMap[item.parentAccountId].children?.some(child => child.checked)
        }
      }

      this.authSelection = Object.values(this.authMap).filter(item => item.checked)
    },

    authSelectAll(selected) {
      this.$refs.authTable.getUTreeData().forEach((parent) => {
        this.authMap[parent.advertiserId].checked = selected
        if (parent.filterChildren) {
          parent.filterChildren.forEach((child, i) => {
            this.authMap[child.advertiserId].checked = selected
          })
        }
      })

      this.authSelection = Object.values(this.authMap).filter(item => item.checked)
    },
    submitAuth() {
      if (this.authSelection.length === 0) {
        this.$modal.msgWarning('请选择授权账户')
        return
      }
      const temp = {}
      this.authSelection.forEach((item) => {
        if (item.parentAccountId) {
          if (temp[item.parentAccountId]) {
            temp[item.parentAccountId].childAccountList.push(item)
          } else {
            temp[item.parentAccountId] = {
              parentAccount: null,
              childAccountList: [item]
            }
          }
        } else {
          if (temp[item.advertiserId]) {
            temp[item.advertiserId].parentAccount = item
          } else {
            temp[item.advertiserId] = {
              parentAccount: item,
              childAccountList: []
            }
          }
          delete item.children
          delete item.filterChildren
        }
      })
      const postData = {
        userParent: '0',
        mediaAccounts: Object.values(temp),
        changeCreateBy: this.changeCreateBy,
        createAsset: this.createAsset
      }
      if (this.mediaType === '1') {
        postData.userParent = this.userParent
      }
      if (this.isChangeToken) {
        postData.changeToken = 0
        this.isChangeToken = false
      }
      this.submitLoading = true
      authorizeMediaAccount(postData)
        .then((response) => {
          if (response.code === 200) {
            this.$router.push(this.$route.path).then(() => {
              this.$modal.msgSuccess('认证成功')
              this.getList()
            })
            this.authDialogVisible = false
          } else {
            this.$modal.msgError('认证失败: ', response.msg)
            this.handleSelectAuthAccount(this.authData)
          }
        })
        .finally(() => {
          this.submitLoading = false
        })
    },
    handleAuthCancel() {
      this.$confirm('是否关闭选择授权账户窗口?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$router.push(this.$route.path).then(() => {
            this.getList()
          })
          this.resetForm('authQueryForm')
          this.authDialogVisible = false
          this.isChangeToken = false
        })
        .catch(() => {})
    },
    submitUCAuth() {
      this.$refs['ucAuthorizeForm'].validate((valid) => {
        if (valid) {
          this.submitLoading = true
          authorizeUCMediaAccount(this.ucAuthorizeForm)
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess('认证成功')
                this.cancelUcAuth()
                this.getList()
              } else {
                this.$modal.msgError('认证失败: ', response.msg)
              }
            })
            .finally(() => {
              this.submitLoading = false
            })
        }
      })
    },
    cancelUcAuth() {
      this.ucAuthDialogVisible = false
      this.resetForm('ucAuthorizeForm')
    },
    submitDHAuth() {
      this.$refs['dhAuthorizeForm'].validate((valid) => {
        if (valid) {
          this.submitLoading = true
          authorizeDHMediaAccount(this.dhAuthorizeForm)
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess('认证成功')
                this.cancelDHAuth()
                this.getList()
              } else {
                this.$modal.msgError('认证失败: ', response.msg)
              }
            })
            .finally(() => {
              this.submitLoading = false
            })
        }
      })
    },
    cancelDHAuth() {
      this.dhAuthDialogVisible = false
      this.resetForm('dhAuthorizeForm')
    },
    submitBLAuth() {
      this.$refs['blAuthorizeForm'].validate((valid) => {
        if (valid) {
          this.submitLoading = true
          authBiliApp(this.blAuthorizeForm)
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess('认证成功')
                this.cancelBLAuth()
                this.getList()
              } else {
                this.$modal.msgError('认证失败: ', response.msg)
              }
            })
            .finally(() => {
              this.submitLoading = false
            })
        }
      })
    },
    cancelBLAuth() {
      this.blAuthDialogVisible = false
      this.resetForm('blAuthorizeForm')
    },
    submitZHAuth() {
      this.$refs['zhAuthorizeForm'].validate((valid) => {
        if (valid) {
          this.submitLoading = true
          authZhApp(this.zhAuthorizeForm)
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess('认证成功')
                this.cancelZHAuth()
                this.getList()
              } else {
                this.$modal.msgError('认证失败: ', response.msg)
              }
            })
            .finally(() => {
              this.submitLoading = false
            })
        }
      })
    },
    cancelZHAuth() {
      this.zhAuthDialogVisible = false
      this.resetForm('zhAuthorizeForm')
    },
    // 同步账户
    handleSync(item) {
      if (item.id) {
        this.authDialogVisible = true
        this.authLoading = true

        this.changeCreateBy = this.createAsset = 0
        this.authSelection = []
        listByAuth(item.id)
          .then((data) => {
            this.isChangeToken = true
            this.handleSelectAuthAccount(data.data)
          })
          .finally(() => {
            this.authLoading = false
          })
      }
    },
    handleSyncMediaAccount({ id }) {
      if (id) {
        this.loading = true
        syncMediaAccount(id)
          .then((data) => {
            if (data.code === 200) {
              this.$modal.msgSuccess(data.msg || '同步成功')
            } else {
              this.$modal.msgError('同步失败：' + data.msg)
            }
          })
          .finally(() => {
            this.loading = false
            this.getList()
          })
      }
    },
    handleSyncAccountPlanName({ id }) {
      if (id) {
        this.loading = true
        syncAccountPlanName(id)
          .then((data) => {
            if (data.code === 200) {
              this.$modal.msgSuccess(data.msg || '同步成功')
            } else {
              this.$modal.msgError('同步失败：' + data.msg)
            }
          })
          .finally(() => {
            this.loading = false
            this.getList()
          })
      }
    },
    checkSelectable(row) {
      if (this.isChangeToken) {
        return !this.disabledSelection.includes(row)
      }
      return true
    },
    handleReplaceLink(row) {
      this.isBatchReplacement = !row
      if (row) this.form = row
      this.replaceVisible = true
    },
    handleCheckReplaceLog(row) {
      this.form = row
      this.replaceLogVisible = true
    },
    handleJoinComment(row) {
      this.isbatchJoin = Array.isArray(row)
      if (this.isbatchJoin) {
        this.getTemplateList()
        this.joinForm.advertiserId = row
        this.joinForm.controlStatus = '0'
        this.joinForm.templateIds = []
        this.commentList = []
        this.joinVisible = true

        if (this.mediaType !== '4') {
          this.commentList = this.dict.type.emotion_type.map(
            (item) => item.value
          )
        }
      } else {
        if (row.mediaType === '1' || row.mediaType === '2') {
          // 巨量、快手跳转评论内容列表
          this.$router.push({ path: '/promotion/comment', query: { advertiserId: row.advertiserId, mediaType: row.mediaType }})
          return
        }
        this.getTemplateList()
        getJoinDetails({ advertiserId: row.advertiserId, mediaType: row.mediaType }).then((data) => {
          this.joinForm = data.data
          if (this.mediaType !== '4') {
            if (!this.joinForm.commentList) {
              this.commentList = this.dict.type.emotion_type.map(
                (item) => item.value
              )
            } else this.commentList = this.joinForm.commentList
          }
          this.joinForm.advertiserId = row.advertiserId
          this.joinVisible = true
        })
      }
    },
    submitJoin() {
      if (
        this.joinForm.controlStatus === '2' &&
        !this.joinForm.templateIds.length
      ) {
        this.$modal.msgError('请选择模板')
        return
      }

      const postData = {
        advertiserIds: Array.isArray(this.joinForm.advertiserId)
          ? this.joinForm.advertiserId
          : [this.joinForm.advertiserId],
        templateIds: this.joinForm.templateIds,
        controlStatus: this.joinForm.controlStatus,
        mediaType: +this.mediaType
      }
      if (this.mediaType !== '4') {
        if (this.joinForm.controlStatus !== '0') {
          if (this.commentList.length === 0) {
            this.$modal.msgError('请选择评论类型')
            return
          }
          postData.commentList = this.commentList
        }
      }
      this.submitLoading = true
      saveCommentJoin(postData)
        .then((data) => {
          this.$modal.msgSuccess('提交成功')
          this.getList()
          this.joinVisible = false
        })
        .finally(() => {
          this.submitLoading = false
        })
    },
    cancelJoin() {
      this.joinVisible = false
      this.joinForm = {
        advertiserId: '',
        templateIds: [],
        controlStatus: 0
      }
    },
    // 评控开关
    handleControlStatus(row) {
      this.loading = true
      changeControlStatus({
        id: row.id,
        controlStatus: row.controlStatus
      })
        .then((response) => {
          if (response.code === 200) {
            this.$modal.msgSuccess('操作成功')
            this.getList()
          } else {
            this.$modal.msgError('操作失败：' + response.msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 广告
    showAdvertising({ advertiserId }) {
      this.advertisingVisible = true
      this.advertisingId = advertiserId
    },
    handleSynchronizationPlan({ id }) {
      syncPlan({ id }).then((response) => {
        if (response.code === 200) {
          this.$modal.msgSuccess(response.msg || '同步成功')
        } else {
          this.$modal.msgError('同步失败：' + response.msg)
        }
      })
    },

    addParentAccount(row) {
      this.customType = 'add'
      this.form = row
      this.parentVisible = true
    },
    updateParentAccount(row) {
      this.customType = 'update'
      this.form = row
      this.parentVisible = true
    },
    handleCustomSuccess() {
      this.parentVisible = false
      this.getList()
    },

    handleSwitchDelay(row) {
      this.form = row
      this.delayVisible = true
      getDelay({ advertiserId: row.advertiserId }).then((response) => {
        if (response.data) {
          this.delaySwitch = true
          this.delayForm.delay = response.data
        }
      })
    },
    saveSwitchDelay() {
      if (this.delaySwitch === false) this.delayForm.delay = 0
      this.delayForm.advertiserId = this.form.advertiserId
      this.submitLoading = true
      updateDelay(this.delayForm)
        .then(() => {
          this.$modal.msgSuccess('修改成功')
          this.closeSwitchDelay()
        })
        .finally(() => {
          this.submitLoading = false
        })
    },
    closeSwitchDelay() {
      this.delayVisible = false
      this.delaySwitch = false
      this.delayForm.delay = 300
    },

    showUpdateRemark(row) {
      this.isBatch = !!row
      this.mediaRemark = row ? row.remark : ''
      this.form = row || {}
      this.remarkVisible = true
    },
    handleUpdateRemark() {
      this.submitLoading = true
      const postData = this.isBatch ? [{
        id: this.form.id,
        remark: this.mediaRemark
      }] : this.selections.map(item => ({
        id: item.id,
        remark: this.mediaRemark
      }))
      updateRemark(postData)
        .then((response) => {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.remarkVisible = false
          this.getList()
        })
        .finally(() => {
          this.submitLoading = false
        })
    },

    showPreWarning(data) {
      this.preWarningForm = data
      this.preWarningVisible = true
    },
    handlePreWarningSave() {
      this.getList()
      this.preWarningVisible = false
    },

    setRoi(row) {
      this.profitVisible = true
      this.profitItems = Array.isArray(row) ? row : [row]
    },

    setMediaProfitConfig(data) {
      this.profitConfigVisible = true
      this.formList = Array.isArray(data) ? data : [data]
    },

    showBdJoin(row) {
      this.bdJoinVisible = true
      this.bdJoinForm = {
        submitUrl: '',
        token: '',
        events: []
      }
      this.form = row
    },
    submitBdJoin() {
      this.$refs['bdJoinForm'].validate((valid) => {
        if (valid) {
          bdJointCommissioning({
            advertiserId: this.form.advertiserId,
            ...this.bdJoinForm
          }).then((response) => {
            if (response.code === 200) {
              this.$modal.msgSuccess('联调成功')
              this.bdJoinVisible = false
            }
          })
        }
      })
    },
    handleCommand(command, row) {
      switch (command) {
        case 'handleSync':
          this.handleSync(row)
          break
        case 'handleSyncMediaAccount':
          this.handleSyncMediaAccount(row)
          break
        case 'handleSyncAccountPlanName':
          this.handleSyncAccountPlanName(row)
          break
        case 'replaceLink':
          this.handleReplaceLink(row)
          break
        case 'handleCheckReplaceLog':
          this.handleCheckReplaceLog(row)
          break
        case 'joinComment':
          this.handleJoinComment(row)
          break
        case 'updateConversion':
          this._setupProxy.showConversion(row)
          break
        case 'controlStatus':
          this.handleControlStatus(row)
          break
        case 'switchDelay':
          this.handleSwitchDelay(row)
          break
        case 'showAdvertising':
          this.showAdvertising(row)
          break
        case 'handleSynchronizationPlan':
          this.handleSynchronizationPlan(row)
          break
        case 'showUpdateRemark':
          this.showUpdateRemark(row)
          break
        case 'addParentAccount':
          this.addParentAccount(row)
          break
        case 'updateParentAccount':
          this.updateParentAccount(row)
          break
        case 'showPreWarning':
          this.showPreWarning(row)
          break
        case 'setRoi':
          this.setRoi(row)
          break
        case 'setMediaProfitConfig':
          this.setMediaProfitConfig(row)
          break
        case 'showBdJoin':
          this.showBdJoin(row)
          break
        case 'handleDeleteJLAuth':
          this.handleDeleteJLAuth(row)
          break
        case 'handleDelete':
          this.handleDelete(row)
          break
        case 'createEventInAsset':
          this.handleCreateEventInAsset(row)
          break

        case 'updateFund':
          this.updateFundHandle(row)
          break
        case 'updateTrackUrl':
          this.updateTrackUrl(row)
          break
        case 'clearDirectLinks':
          clearDirectLinks({
            advertiserId: row.advertiserId,
            type: 1
          })
          break
      }
    },
    // 处理内层删除问题
    tableLoad(tree, treeNode, resolve) {
      this.tableTreeRefreshTool[tree.id] = {
        resolve
      }
      // 请求api接口获取数据
      listMedia({
        parentAccountId: tree.advertiserId
      }).then((response) => {
        if (response.code === 200) {
          response.rows.forEach((item) => {
            item.rowKey = item.id + tree.advertiserId
          })
          tree.children = response.rows
          resolve(response.rows)
        } else {
          resolve([])
        }
      })
    },
    defaultExpandedKeys(expandedRows, expanded) {
      const number = this.defaultExpandedKeysList.indexOf(expandedRows)
      expandedRows.expanded = expanded
      if (expanded) {
        this.defaultExpandedKeysList.push(expandedRows)
      } else {
        this.defaultExpandedKeysList.splice(number, 1)
      }
      this.defaultExpandedKeysList = Array.from(
        new Set(this.defaultExpandedKeysList)
      )
    },
    expandableListView() {
      if (this.defaultExpandedKeysList.length > 0) {
        this.defaultExpandedKeysList.forEach((element) => {
          // 注意： 这里必须使用表格绑定的Array中的列去展开，否则会没效果
          this.mediaList.forEach((deptElement) => {
            if (element.id === deptElement.id) {
              this.$refs.tableRef.toggleRowExpansion(deptElement, true)
              listMedia({
                parentAccountId: deptElement.advertiserId
              }).then((response) => {
                if (response.code === 200) {
                  response.rows.forEach((item) => {
                    item.rowKey = item.id + deptElement.advertiserId
                  })
                  this.$nextTick(() => {
                    this.tableTreeRefreshTool[deptElement.id].resolve(
                      response.rows
                    )
                  })
                } else {
                  this.tableTreeRefreshTool[deptElement.id].resolve([])
                }
              })
            }
          })
        })
      }
    },
    // 处理过期显示
    handleExpiresIn({ expiresIn }) {
      return dayjs(expiresIn).isBefore(dayjs()) ? '#f56c6c' : ''
    },
    handleRefreshExpiresIn({ refreshTokenExpiresIn }) {
      return dayjs(refreshTokenExpiresIn).subtract(7, 'day').isBefore(dayjs())
        ? 'refresh-expires'
        : ''
    },
    toHandleAuth(row) {
      if (this.handleRefreshExpiresIn(row)) {
        window.open(this.authUrl, '_blank')
        this.handleAuth()
      }
    },
    // 第一列样式
    firstCellClassName({ columnIndex }) {
      if (columnIndex === 0) {
        return 'first-cell'
      }
    },
    // 复制成功
    clipboardSuccess() {
      this.$modal.msgSuccess('复制成功')
    },
    handleClose() {
      const obj = { path: '/promotion/media' }
      this.$tab.closeOpenPage(obj)
    },
    handleCreateEventInAsset() {
      const data = {
        advertiserIds: this.selections.map(item => item.advertiserId).join(',')
      }
      this.$modal
        .confirm('是否确认创建app内下单事件' + '？')
        .then(function() {
          return createEventInAsset(data)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('创建app内下单事件成功')
        })
        .catch(() => {})
    },
    updateFundHandle(row) {
      this.$modal
        .confirm('是否进行同步账户余额' + '？')
        .then(() => {
          const data = {
            advertiserId: row ? row.advertiserId : this.selections.map((item) => item.advertiserId).join(','),
            mediaPlatformType: this.mediaType
          }
          return updateFundRequest(data)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('同步账户余额成功')
        })
        .catch(() => {})
    },
    updateTrackUrl(row) {
      this.trackUrlForm = {
        trackUrl: '',
        exposureRenewalUrl: '',
        advertiserId: row.advertiserId,
        mediaPlatformType: +this.mediaType
      }
      this.trackUrlDialogVisible = true
    },
    submitTrackUrl() {
      this.$refs['trackUrlForm'].validate((valid) => {
        if (valid) {
          updateProjectTrackUrl(this.trackUrlForm).then((res) => {
            this.$message.success('更新成功')
            this.trackUrlDialogVisible = false
          })
        }
      })
    },
    // UDS账号同步
    UDSAccountSyncHandle() {
      // const ids = this.selections.map(item => item.goodsId)
      this.UDSAccountSyncVisible = true
    },
    calculateCashCost(row) {
      if (!row.cost) return '0'
      const backRate = (row.profitBackRate || 0) / 1000 // 转换千分数为小数
      const cashCost = row.cost / (1 + backRate)
      return cashCost.toFixed(2)
    },
    handleMainProductSelect(goods) {
      this.mainProduct = goods
    },
    // 显示批量修改出价对话框
    showBatchBidDialog() {
      this.batchBidVisible = true
    },
    // 批量修改出价
    async handleBatchBid() {
      if (!this.batchBidForm.newBid) {
        this.$message.error('请输入新出价')
        return
      }

      const selectItems = this.selections.filter(item => item.mediaType === 1)
      if (selectItems.length === 0) {
        this.$message.error('请选择媒体类型为抖音的账户')
        return
      }

      // 构造参数
      const advertiserIds = {}
      this.selections.forEach(item => {
        if (!advertiserIds[item.advertiserId]) {
          advertiserIds[item.advertiserId] = []
        }
        // if (item.planId) {
        //   advertiserIds[item.advertiserId].push(item.planId)
        // }
      })
      const params = {
        advertiserIds,
        mediaType: 1,
        newBid: this.batchBidForm.newBid
      }
      this.submitLoading = true
      try {
        const res = await batchUpdateBid(params)
        this.$message.success(res.msg || '批量修改出价成功')
        this.batchBidVisible = false
        this.getList()
      } catch (e) {
        // 错误处理
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router/composables'
import { CTreeDrop } from '@wsfe/ctree'
import { useSynchronizeAccounts } from '@/hooks/mediaAction/useSynchronizeAccounts'
import DeptTreeSelect from '@/components/DeptTreeSelect/DeptTreeSelect.vue'

import { Message, MessageBox } from 'element-ui'
import useDeptOptions, {
  setDefaultIds
} from '@/components/DeptTreeSelect/useDeptOptions'
import GoodsLibrary from '@/views/promotion/media/components/GoodsLibrary.vue'
import useConversion from '@/components/ConversionSetups/hooks/useConversion'
import ConversionItem from '@/components/ConversionSetups/ConversionItem.vue'
import { updateStatus } from '@/api/promotion/media'
import { isJl } from '@/utils/judge/media'
import BusinessSelector from '@/components/BusinessSelector/index.vue'
import { checkPermi } from '@/utils/permission'
import SavedSearches from '@/components/SavedSearches/index.vue'
import MediaProfitConfigDialog from '@/components/SetProfitConfig/MediaProfitConfigDialog.vue'

const router = useRouter()
const self = getCurrentInstance().proxy
const {
  visible: syncAccountsVisible,
  dateRange,
  showSelectDate,
  synchronizeAccounts
} = useSynchronizeAccounts({
  getList: self.getList,
  mediaType: self.mediaType
})

const toListParent = () => {
  router.push(`/promotion/media-parent/index/0?activeTab=${self.mediaType}`)
}
const toChildren = (row) => {
  if (!row.parentAccountId) {
    router.push({
      path: '/promotion/media',
      query: {
        parentAccountId: row.advertiserId,
        mediaType: self.mediaType,
        activeTab: self.mediaType
      }
    })
  } else {
    if (self.roles[0] === 'admin' || self.permissions.includes('promotion:media:dynamicList')) {
      // 跳转列表页
      const routerList = {
        1: {
          path: '/promotion/media-list/index'
        },
        2: {
          path: '/promotion/media-list/ksIndex'
        },
        4: {
          path: '/promotion/media-list/txIndex'
        }
      }
      if (routerList[self.mediaType]) {
        router.push({
          path: routerList[self.mediaType].path,
          query: {
            advertiserId: row.advertiserId,
            mediaType: self.mediaType
            // activeTab: self.mediaType
          }
        })
      }
    }
  }
}

// 联调
const jointVisible = ref(false)
const jointUrl = ref('')
const showJointCommissioning = () => {
  jointVisible.value = true
  jointUrl.value = ''
}
const submitJoint = () => {
  if (!jointUrl.value) {
    Message.warning('请输入联调地址')
    return
  }
  ksJointCommissioning(jointUrl.value).then((res) => {
    if (res.code === 200) {
      Message.success('联调成功')
      jointVisible.value = false
    }
  })
}

const deptOptions = useDeptOptions(() => {
  setDefaultIds(self)
  self.getList()
})

// 回传
const { conversionType,
  conversionForm,
  conversionDialogVisible,
  conversionFormType,
  showConversion,
  batchUpdateConversion,
  handleConversionSave
} = useConversion({
  type: 'media',
  onSave: () => self.getList()
})

// 启用禁用
const handleVaildChange = (row) => {
  MessageBox.confirm(`确认${row.isValid === '1' ? '启用' : '禁用'}账户?`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    updateStatus({
      id: row.id,
      isValid: row.isValid
    }).then(res => {
      Message.success('修改成功')
    })
  }).catch(() => {
    row.isValid = row.isValid === '1' ? '0' : '1'
  })
}

// 数据同步下拉菜单处理
const handleSyncDropdownCommand = (command) => {
  switch (command) {
    case 'syncAccount':
      showSelectDate(self.selections.map(item => item.advertiserId))
      break
    case 'updateFund':
      self.updateFundHandle()
      break
  }
}

// 批量操作下拉菜单处理
const handleBatchDropdownCommand = (command) => {
  switch (command) {
    case 'transfer':
      self.handleTransfer()
      break
    case 'updateConversion':
      batchUpdateConversion(self.selections)
      break
    case 'updateRemark':
      self.showUpdateRemark()
      break
    case 'preWarning':
      self.showPreWarning(self.selections)
      break
    case 'batchSetRoi':
      self.setRoi(self.selections)
      break
    case 'batchBid':
      self.showBatchBidDialog()
      break
    case 'profitConfig':
      self.setMediaProfitConfig(self.selections)
      break
    case 'batchDelete':
      self.handleBatchDelete()
      break
    case 'createEventInAsset':
      self.handleCreateEventInAsset()
      break
  }
}

</script>

<style lang="scss" scoped>
.form-date-picker {
  width: 185px;
}

.auth-table-wrap {
  //height: calc(100vh - 450px);
}

.selected-list {
  height: 100px;
  overflow: auto;
}

.conversion-rate {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.dialog-conversion-rate {
  display: grid;
  grid-template-columns: 1fr 100px 20px;
  grid-gap: 10px;
  text-align: center;
  line-height: 38px;
  font-size: 18px;
}

.parent-id {
  font-size: 14px;
  line-height: 16px;
}

.refresh-expires {
  color: #f56c6c;
  cursor: pointer;
}

.vc-layout {
  display: flex;
  gap: 10px;
  align-items: center;
}

.select-items {
  padding: 10px 0;
}

.auth-table-wrap :deep(.el-table__header .el-checkbox) {
  display: none;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}

.check-box {
  border-radius: 2px;
  color: transparent;
  text-align: center;
  border: #d2d2d2 2px solid;
  font-size: 14px;
  line-height: 18px;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  cursor: pointer;
}
.check-box-active {
  border: #409EFF 2px solid;
  background: #409EFF;
  color: white;
}
.auth-select-all {
  display: flex;
  align-items: center;
  margin-top: 10px;
  gap: 10px
}
.ks-auth-dialog {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}

.order-transfer-description {
  margin-bottom: 20px;
}

.order-transfer-description .el-alert {
  padding: 15px;
}

.order-transfer-description ul li {
  margin-bottom: 5px;
  line-height: 1.5;
}
</style>
