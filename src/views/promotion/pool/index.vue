<template>
  <div class="adaption-container">
    <el-form v-show="showSearch" ref="queryForm" class="search-form" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item prop="mediaPlatformType">
        <el-select v-model="queryParams.mediaPlatformType" placeholder="媒体类型" clearable @change="handleQuery">
          <el-option
            v-for="dict in dict.type.media_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="goodsPoolName">
        <el-input
          v-model.trim="queryParams.goodsPoolName"
          placeholder="商品池名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="goodsId">
        <el-input
          v-model.trim="queryParams.goodsId"
          placeholder="商品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="goodsName">
        <el-input
          v-model.trim="queryParams.goodsName"
          placeholder="商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="advertiserId">
        <el-input
          v-model.trim="queryParams.advertiserId"
          placeholder="媒体ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="advertiserName">
        <el-input
          v-model.trim="queryParams.advertiserName"
          placeholder="媒体名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['goodsPool:pool:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['goodsPool:pool:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <div class="table-wrapper">
      <el-table
        v-loading="loading"
        v-bind="tableHeight"
        stripe
        border
        :data="poolList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="类型" align="center" prop="mediaPlatformType" width="60">
          <template slot-scope="scope">
            <div class="icon-flex">
              <div class="type-icon-item">
                <svg-icon v-if="mediaTypeMap[scope.row.mediaPlatformType]" class-name="type-icon" :icon-class="mediaTypeMap[scope.row.mediaPlatformType]" />
                <div class="type-label">{{ mediaTypeMap[scope.row.mediaPlatformType] }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品池名称" align="center" prop="goodsPoolName" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="所属部门" align="center" prop="firstDeptName" />
        <el-table-column label="负责人" align="center" prop="createBy" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="160" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['goodsPool:pool:edit']"
              size="mini"
              type="text"
              icon="el-icon-info"
              @click="handleUpdate(scope.row)"
            >详情</el-button>
            <el-button
              v-hasPermi="['goodsPool:pool:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              class="text-danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改商品池对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px" size="mini">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="名称" prop="goodsPoolName">
              <el-input v-if="type==='edit'" v-model="form.goodsPoolName" placeholder="请输入商品池名称" />
              <span v-else-if="type==='detail'">{{ form.goodsPoolName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注" prop="remark">
              <el-input v-if="type==='edit'" v-model="form.remark" placeholder="请输入备注" />
              <span v-else-if="type==='detail'">{{ form.remark }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="媒体类型" prop="mediaPlatformType">
              <el-select v-if="type==='edit'" v-model="form.mediaPlatformType" placeholder="媒体类型" @change="handleAddMediaTypeChange">
                <el-option
                  v-for="dict in dict.type.media_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="+dict.value"
                >
                  <svg-icon :icon-class="dict.label" />
                  <span> {{ dict.label }}</span>
                </el-option>
              </el-select>
              <span v-else-if="type==='detail'">{{ dictMap(dict.type.media_type)[form.mediaPlatformType] }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商品订单数" prop="orderNum">
              <template v-if="type==='edit'">
                <el-input-number v-model="form.orderNum" :min="0" :max="10000000" :precision="0" label="请输入配置商品订单数" :controls="false" style="width: 150px" />
              </template>
              <span v-else-if="type==='detail'">{{ form.orderNum }}</span>
              <el-tooltip effect="dark" content="当商品订单数超过该值时，触发替换操作；为0时不触发操作" placement="top">
                <i class="el-icon-question color-primary ml5" />
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <template v-if="form.mediaPlatformType">
          <el-divider />
          <el-row :gutter="20">
            <el-col :span="16">
              <div class="operate-header">
                <div class="operate-title">商品</div>
                <div v-if="type==='edit'">
                  <el-button type="primary" size="small" icon="el-icon-plus" style="margin-right: 10px" @click="showGoodsSelector">
                    添加商品
                  </el-button>
                  <el-popconfirm
                    title="确定清空选择项？"
                    @confirm="removeGoodsSelect"
                  >
                    <el-button slot="reference" type="danger" size="small" plain>清空</el-button>
                  </el-popconfirm>
                </div>
              </div>
              <el-table
                ref="tableRef"
                :data="goodsList"
                stripe
                border
                size="small"
                height="420px"
              >
                <el-table-column label="商品信息" align="left" prop="goodsName">
                  <template #default="scope">
                    <div class="table-base-info">
                      <svg-icon v-if="scope.row.platform === '2' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="taobao" />
                      <div v-else class="mr10" style="height: 40px">
                        <image-preview :src="scope.row.goodsThumbnailUrl" :width="40" :height="40" />
                      </div>
                      <BaseInfoCell :id="scope.row.goodsId" style="flex:1" :name="scope.row.goodsName" />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="店铺信息" align="left" prop="mallName" width="200">
                  <template #default="scope">
                    <BaseInfoCell :id="scope.row.mallId" :name="scope.row.mallName" />
                  </template>
                </el-table-column>
                <el-table-column label="限制推广" align="center" prop="currentLimitingState" width="100">
                  <template #default="scope">
                    <el-tag v-if="scope.row.currentLimitingState" type="danger">
                      限制推广
                    </el-tag>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
                  <template #default="scope">
                    <el-button v-if="type==='detail'" type="text" class="mr5" icon="el-icon-document" @click="showLog(scope.row)">替换日志</el-button>
                    <el-button v-if="type==='edit'" class="text-danger" type="text" icon="el-icon-delete" @click="removeGoodsSelect(scope)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="8">
              <MediaAccountSelector v-if="refresh && type==='edit'" show-id full-back :default-selections="mediaList" :media-platform-type="form.mediaPlatformType" @change="handleAccountSelect" />
              <template v-if="type === 'detail'">
                <div class="operate-header">
                  <div class="operate-title">媒体账户</div>
                </div>
                <el-table
                  :data="mediaList"
                  height="420"
                  stripe
                  border
                  size="small"
                >
                  <el-table-column
                    prop="name"
                    label="名称"
                  >
                    <template #default="scope">
                      <BaseInfoCell :id="scope.row.value" :name="scope.row.label" no-copy />
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-col>
          </el-row>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <template v-if="type === 'detail'">
          <el-button type="primary" icon="el-icon-edit" @click="switchTo('edit')">编 辑</el-button>
          <el-button @click="cancel">关 闭</el-button>
        </template>
        <template v-if="type === 'edit'">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </template>
      </div>
    </el-dialog>
    <GoodsSelector
      type="pool"
      :visible.sync="goodsVisible"
      :default-query-params="{mediaPlatformType: form.mediaPlatformType}"
      :default-selections="goodsList"
      :table-list="['goodsName', 'remark','mallName']"
      @select="handleGoodsSelect"
    />

    <ReplaceLog :visible.sync="logVisible" :query-params="logQuery" :goods="selectGoods" />
  </div>
</template>

<script>
import { listPool, getPool, delPool, addPool, updatePool } from '@/api/promotion/pool'
import { goodsFindAIdToGoodsPool } from '@/api/promotion/goods'
import { mapGetters } from 'vuex'

export default {
  name: 'Pool',
  dicts: ['media_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品池表格数据
      poolList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 弹窗类型
      type: 'detail',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        goodsPoolName: null,
        goodsId: null,
        goodsName: null,
        advertiserId: null,
        advertiserName: null
      },
      // 表单参数
      form: {},
      lastMediaType: null,
      // 表单校验
      rules: {
        goodsPoolName: [
          { required: true, message: '请输入商品池名称', trigger: 'blur' }
        ],
        mediaPlatformType: [
          { required: true, message: '请选择媒体类型', trigger: 'blur' }
        ]
      },
      refresh: true,
      goodsVisible: false,
      goodsList: [],
      mediaList: [],

      logVisible: false,
      selectGoods: null,
      logQuery: {
        goodsId: '',
        operateType: 2,
        mediaPlatformType: null }
    }
  },
  computed: {
    ...mapGetters([
      'tableHeight'
    ]),
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((acc, cur) => {
        acc[cur.value] = cur.label
        return acc
      }, {})
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询商品池列表 */
    getList() {
      this.loading = true
      const query = {
        // params: {
        //   pageNum: this.queryParams.pageNum,
        //   pageSize: this.queryParams.pageSize
        // },
        ...this.queryParams
      }
      listPool(query).then(response => {
        this.poolList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        goodsPoolName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        orderNum: null
      }
      this.goodsList = []
      this.mediaList = []
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.switchTo('edit')
      this.title = '添加商品池'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      this.loading = true
      getPool(id).then(response => {
        this.form = response.data
        this.form.joinList.forEach(item => {
          if (item.businessType === 1) {
            this.goodsList.push({
              id: item.businessId,
              goodsId: item.otherId,
              goodsName: item.businessName,
              ...item
            })
          } else if (item.businessType === 2) {
            this.mediaList.push({
              data: {
                id: item.businessId
              },
              label: item.businessName,
              value: item.otherId
            })
          }
        })
        this.open = true
        this.switchTo('detail')
      }).finally(() => {
        this.loading = false
      })
    },
    switchTo(type) {
      this.type = type
      switch (type) {
        case 'edit': {
          this.title = '修改商品池'
          break
        }
        case 'detail': {
          this.title = '商品池详情'
          break
        }
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.goodsList.length < 2) {
            this.$modal.msgError('请至少选择2件商品')
            return
          }
          // if (this.mediaList.length === 0) {
          //   this.$modal.msgError('请选择媒体账号')
          //   return
          // }
          const mediaMap = {}
          this.mediaList.forEach(item => {
            mediaMap[item.data.id] = {
              businessId: item.data.id,
              businessType: 2
            }
          })
          const postData = {
            ...this.form,
            joinList: [
              ...this.goodsList.map(item => ({
                businessId: item.id,
                businessType: 1
              })),
              ...Object.values(mediaMap)
            ]
          }
          if (this.form.id != null) {
            updatePool(postData).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addPool(postData).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除选中商品池数据？').then(function() {
        return delPool(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    handleAddMediaTypeChange(val) {
      if (this.goodsList.length || this.mediaList.length) {
        this.$modal.confirm('切换媒体类型将清空已选择的商品和媒体账号，是否继续？').then(() => {
          this.goodsList = []
          this.mediaList = []
          this.lastMediaType = val
          this.refreshMedia()
        }).catch(() => {
          this.form.mediaPlatformType = this.lastMediaType
        })
      } else {
        this.refreshMedia()
      }
    },
    handleAccountSelect(list) {
      this.mediaList = list
    },
    removeGoodsSelect(scope) {
      if (scope) { this.goodsList.splice(scope.$index, 1) } else { this.goodsList = [] }
    },
    showGoodsSelector() {
      this.goodsVisible = true
    },
    handleGoodsSelect(list) {
      this.goodsList = list
      if (!list.length) return
      goodsFindAIdToGoodsPool({
        goodsIdArray: list.map(item => item.goodsId),
        mediaType: this.form.mediaPlatformType
      }).then(response => {
        if (response.code === 200) {
          const goodsMediaList = Object.values(response.data).map(item => ({
            label: item.advertiser_name,
            value: item.a_id
          }))
          if (goodsMediaList.length > 0) {
            if (this.mediaList.length > 0) {
            // 去重
              this.mediaList = [...this.mediaList, ...goodsMediaList]
              this.mediaList = this.mediaList.filter((item, index, self) =>
                index === self.findIndex((t) => t.value === item.value)
              )
            } else {
              this.mediaList = goodsMediaList
            }
            this.refreshMedia()
          }
        }
      })
    },
    refreshMedia() {
      this.refresh = false
      this.$nextTick(() => {
        this.refresh = true
      })
    },
    showLog(row) {
      this.selectGoods = row
      this.logQuery.goodsId = row.goodsId
      this.logQuery.mediaPlatformType = this.form.mediaPlatformType
      this.logVisible = true
    }
  }
}
</script>
<script setup>
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import GoodsSelector from '@/components/GoodsSelector/index.vue'
import MediaAccountSelector from '@/views/promotion/goods/components/MediaAccountSelector.vue'
import ReplaceLog from '@/components/ReplaceLog/index.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 10px 20px;
}
.operate-header {
  margin-bottom: 10px;
  .operate-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }
}
::v-deep {
  .el-divider--horizontal {
    margin: 18px 0;
  }
}
</style>
