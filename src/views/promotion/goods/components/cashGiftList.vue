<template>
  <div class="streaming-container" style="padding-top: 0">
    <div>
      <el-form label-width="90px">
        <el-form-item label="商品信息:">
          <div class="cashGift_info-img">
            <div>
              <image-preview :src="goods.goodsThumbnailUrl" :width="50" :height="50" />
            </div>
            <BaseInfoCell :id="goods.goodsId" class="info-wrap ml10" style="flex:1" :name="goods.goodsName" />
          </div>
        </el-form-item>
      </el-form>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="90px">

      <el-form-item label="礼金ID:" prop="cashgiftId">
        <el-input
          v-model="queryParams.cashgiftId"
          placeholder="搜索礼金ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="使用状态:" prop="inUse">
        <el-select v-model="queryParams.inUse" clearable placeholder="请选择" @change="handleQuery">
          <el-option
            v-for="item in inUseOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="礼金状态:" prop="status">
        <el-select v-model="queryParams.status" clearable placeholder="请选择" @change="handleQuery">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:pddCashgift:add']"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
        <el-button
          v-hasPermi="['promotion:pddCashgift:add']"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleBatchAdd"
        >批量新增</el-button>
        <el-button
          type="warning"
          icon="el-icon-close"
          size="mini"
          @click="close"
        >关闭</el-button>

      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <div style="flex: 1">
      <el-table v-loading="loading" :data="landingList" border>

        <el-table-column label="礼金信息" align="center" prop="couponAmount" min-width="130">
          <template slot-scope="scope">
            <div class="couponAmount_info">
              <span>{{ scope.row.couponAmount/100 }}</span>
              <span>元礼金</span>
            </div>
            <div class="couponThresholdAmount_info">满{{ scope.row.couponThresholdAmount/100 }}可用 </div>
            <div class="cashgiftId_info">ID:{{ scope.row.cashgiftId }}</div>
          </template>
        </el-table-column>
        <el-table-column label="礼金数量" align="left" prop="quantity" min-width="200">
          <template slot-scope="scope">
            <div>
              <div>
                <span>总数量：</span>
                <span class="remark">{{ scope.row.quantity||0 }}</span>
                <span>张</span>
              </div>
              <div>
                <span>已发放数量：</span>
                <span class="remark">{{ scope.row.fetchQuantity||0 }}</span>
                <span>张</span>
              </div>
              <div class="red_text">
                <span>已使用数量：</span>
                <span>{{ scope.row.orderQuantity||0 }}张</span>
              </div>
              <div class="red_text">
                <span>已退款数量：</span>
                <span>{{ scope.row.refundQuantity ||0 }}张</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="续券信息" align="left" min-width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.isOpenRenew" class="renew-info">
              <div>
                <span>触发续券剩余数量：</span>
                <span>{{ scope.row.remainCountForRenew||0 }} 张</span>
              </div>
              <div>
                <span>每次续券上限：</span>
                <span>{{ scope.row.renewNumberPerTime||0 }} 张</span>
              </div>
              <div>
                <span>单日续券上限：</span>
                <span>{{ scope.row.renewMaxNumberPerDay||0 }} 张</span>
              </div>
              <div>
                <span>今日续券数量：</span>
                <span class="remark">{{ scope.row.renewCountToday||0 }}</span>
                <span>张</span>
              </div>
              <div>
                <span>已续券总量：</span>
                <span class="remark">{{ scope.row.renewCountTotal||0 }}</span>
                <span>张</span>
              </div>
            </div>
            <div v-else>
              <span class="disabled-text">未开启续券</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="140">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['promotion:pddCashgift:add']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >复制</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-paperclip"
              @click="showGoodsUrl(scope.row)"
            >获取链接</el-button>
            <el-button
              v-hasPermi="['promotion:pddCashgift:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              style="color: #f56c6c"
              @click="handleDelete(scope.row)"
            >删除</el-button>

          </template>
        </el-table-column>
        <!-- <el-table-column label="使用状态" align="center" prop="inUse" min-width="130">
          <template slot-scope="scope">
            <div v-if="isCashgiftEdit">
              <div v-if="scope.row.inUse">
                <el-tooltip class="item" effect="dark" content="使用状态不允许停用" placement="top">
                  <el-switch
                    v-model="scope.row.inUse"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    :disabled="scope.row.inUse"
                    @change="handleInUseChange(scope.row)"
                  />
                </el-tooltip>
              </div>
              <el-switch
                v-else
                v-model="scope.row.inUse"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="handleInUseChange(scope.row)"
              />
            </div>
            <div v-else>
              <span>{{ scope.row.inUse?'使用中':'停用' }}</span>
            </div>
          </template>
        </el-table-column> -->
        <el-table-column label="礼金状态" align="left" prop="duration" min-width="240">
          <template slot-scope="scope">
            <div class="flex_status">
              <div v-hasPermi="['promotion:pddCashgift:edit']" class="status_switch">
                <div v-if="scope.row.status!==2">
                  <el-tooltip class="item" effect="dark" content="礼金状态不允许手动启用" placement="top">
                    <el-switch
                      v-model="defaultStatus"
                      active-color="#13ce66"
                      disabled
                      inactive-color="#ff4949"
                      :active-value="2"
                      :inactive-value="4"
                    />
                  </el-tooltip>
                </div>
                <el-switch
                  v-else
                  v-model="scope.row.status"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :active-value="2"
                  :inactive-value="4"
                  @change="handleVaildChange(scope.row)"
                />
              </div>
              <div class="status_text">
                <span>当前状态:</span>
                <span>{{ scope.row.status|formatStatus }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="礼金券信息" align="left" prop="fetchAmount" min-width="260">
          <template slot-scope="scope">
            <div>
              <span>已领取礼金券总金额：</span>
              <span v-if="scope.row.fetchAmount">{{ (scope.row.fetchAmount /100).toFixed(2) }}</span>
              <span v-else>0</span>
              <span>元</span>
            </div>
            <div>
              <span>已使用的券总金额：</span>
              <span v-if="scope.row.orderCouponAmount ">{{ (scope.row.orderCouponAmount /100).toFixed(2) }}</span>
              <span v-else>0</span>
              <span>元</span>
            </div>
            <div>
              <span>退回礼金券总金额：</span>
              <span v-if="scope.row.refundAmount">{{ (scope.row.refundAmount /100).toFixed(2) }}</span>
              <span v-else>0</span>
              <span>元</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="领取有效期" align="left" prop="createTime" min-width="200">
          <template slot-scope="scope">
            <div>开始时间:
              <span>{{ getTime(scope.row.acquireStartTime) }} </span>
            </div>

            <div>结束时间:
              <span>{{ getTime(scope.row.acquireEndTime) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="使用有效期" align="center" min-width="120">
          <template slot-scope="scope">
            <span>
              <span>{{ scope.row.duration }}</span>
              <span>{{ scope.row.relativeTimeType ===1?'天':scope.row.relativeTimeType ===2?'小时':scope.row.relativeTimeType ===3?'分钟':'' }}</span>
            </span>
          </template>
        </el-table-column>

        <el-table-column label="单用户可领券数量(张)" align="center" prop="userLimit" min-width="150" />
        <el-table-column label="创建信息" align="center" prop="userLimit" min-width="220">
          <template slot-scope="scope">
            <div style="text-align: left;">创建人:
              <span>{{ scope.row.createBy }} </span>
            </div>

            <div style="text-align: left;">创建时间:
              <span>{{ scope.row.createTime }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="备注" align="center" prop="remark" min-width="150">
          <template slot-scope="scope">
            <div class="remark-overflow">
              <div v-if="scope.row.remark" class="remark">{{ scope.row.remark }}</div>
              <i class="el-icon-edit remarkEdit" @click="editRemark(scope.row)" />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="GMV" align="center" prop="orderGmv" min-width="130">
          <template slot-scope="scope">
            <span v-if="scope.row.orderGmv ">{{ scope.row.orderGmv /100 }}</span>
            <span v-else>0</span>
          </template>
        </el-table-column>

      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-drawer
      title="新增多多礼金"
      :visible.sync="landingVisible"
      direction="rtl"
      size="50%"
      append-to-body
      destroy-on-close
      :wrapper-closable="false"
      @close="handleLandingClose"
    >
      <cashGiftEdit v-if="landingVisible" :pdd-cashgift-amount-rate="pdd_cashgift_amount_rate" :edit-row="editRow" :goods="innerGoods" :platform-type="platformType" @success="handleLandingSuccess" @close="landingVisible = false" />
    </el-drawer>
    <el-dialog
      title="修改备注"
      :visible.sync="remarkVisible"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form ref="ruleForm" :model="dataForm" :rules="ruleForm" label-width="100px">
        <el-form-item label="备注：" prop="remark">
          <el-input v-model="dataForm.remark" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="remarkVisible = false">取 消</el-button>
        <el-button v-loading="dataForm.remarkLoading" type="primary" @click="handleRemark">确 定</el-button>
      </span>
    </el-dialog>
    <!--  链接展示  -->
    <el-dialog title="推广链接" :visible.sync="urlVisible" width="900px" append-to-body top="10vh" class="dialog-pt-0">
      <!-- @openLanding="(goods) => {urlVisible = false;showLandingList(goods)}" -->
      <URLGetter v-if="urlVisible" :goods="selectedGoods" show-personal-btn :is-pdd-cash-gift="true" />
    </el-dialog>
    <el-drawer
      title="批量新增多多礼金"
      :visible.sync="batchVisible"
      direction="rtl"
      size="50%"
      append-to-body
      destroy-on-close
      :wrapper-closable="false"
      @close="handleBatchClose"
    >
      <cashGiftBatchEdit v-if="batchVisible" :pdd-cashgift-amount-rate="pdd_cashgift_amount_rate" :goods="innerGoods" :platform-type="platformType" @success="handleBatchSuccess" @close="batchVisible = false" />
    </el-drawer>
    <el-dialog title="直达链接" :visible.sync="urlCopyVisible" width="900px" append-to-body top="10vh" class="dialog-pt-0">
      <div style="text-align: right;padding-bottom: 20px;">
        <el-button v-clipboard:copy="urlStr" v-clipboard:success="clipboardSuccess" size="mini" type="primary">
          复制全部链接
        </el-button>
      </div>
      <div>
        <div v-for="(item, index) in urlList" :key="index" style="padding-bottom: 20px;">
          <url-clipboard :url="item" />
        </div>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import auth from '@/plugins/auth'
import { listCashgift, delCashgift, updateCashgift, updateCashgiftStatus, updateCashgiftInfo } from '@/api/promotion/cashGift'
import cashGiftEdit from './cashGiftEdit.vue'
import useConfigs from '@/hooks/useConfigs'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import URLGetter from '@/components/URLGetter/index.vue'
import cashGiftBatchEdit from './cashGiftBatchEdit.vue'
import UrlClipboard from '@/views/promotion/goods/components/UrlClipboard.vue'
import { Message } from 'element-ui'
export default {
  name: 'CashGiftList',
  components: {
    cashGiftEdit, BaseInfoCell, URLGetter, cashGiftBatchEdit, UrlClipboard
  },
  filters: {
    formatStatus(status) {
      const list = {
        1: '未生效',
        2: '生效中',
        3: '已过期',
        4: '活动中止（用户主动停止）',
        5: '活动中止（佣金降低）',
        6: '活动中止（推广活动异常）'
      }
      return list[status]
    }
  },
  props: {
    goods: {
      type: Object,
      default: () => {}
    },
    platformType: {
      type: Number,
      default: null
    }
  },
  emits: ['close', 'refresh'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 内部goods
      innerGoods: null,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedLandingPageResource: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 多多礼金表格数据
      landingList: [],
      // 弹出层标题
      title: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // inUse: null,
        cashgiftId: null,
        // name: null,
        status: null
      },
      // 表单参数
      form: {},

      // 多多礼金编辑
      landingVisible: false,
      editRow: null,
      // 修改备注
      remarkVisible: false,
      dataForm: {
        remark: '',
        id: null,
        remarkLoading: false
      },
      ruleForm: {
        // remark: [
        //   { required: true, message: '请输入备注', trigger: 'blur' }
        // ]
      },
      // inUseOptions: [
      //   {
      //     label: '使用中',
      //     value: true
      //   },
      //   {
      //     label: '停用',
      //     value: false
      //   }
      // ],
      statusOptions: [
        {
          label: '未生效',
          value: 1
        },
        {
          label: '生效中',
          value: 2
        },
        {
          label: '已过期',
          value: 3
        },
        {
          label: '活动中止（用户主动停止）',
          value: 4
        },
        {
          label: '活动中止（佣金降低）',
          value: 5
        },
        {
          label: '活动中止（推广活动异常）',
          value: 6
        }
      ],
      // 礼金配置-比例
      pdd_cashgift_amount_rate: null,
      urlVisible: false,
      // 选中的商品
      selectedGoods: {},
      defaultStatus: 4,
      batchVisible: false,
      urlCopyVisible: false,
      urlStr: '',
      urlList: []
    }
  },
  computed: {
    addDisabled() {
      if (this.loading) return true
      return this.landingList.length > 0
    },
    isCashgiftEdit() {
      return auth.hasPermi('promotion:pddCashgift:edit')
    }
  },
  created() {
    useConfigs('pdd_cashgift_amount_rate', (configStore) => {
      this.pdd_cashgift_amount_rate = configStore.pdd_cashgift_amount_rate
    })

    this.getList()
  },

  methods: {
    /** 查询多多礼金列表 */
    async getList() {
      this.loading = true
      this.innerGoods = this.goods
      this.queryParams.goodsInfoId = this.goods.id
      // this.queryParams.mediaType = this.platformType

      const res = await listCashgift(this.queryParams)
      this.landingList = res.rows
      this.total = res.total
      this.loading = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.landingVisible = true
    },
    // 批量新增
    handleBatchAdd() {
      this.batchVisible = true
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.landingVisible = true
      this.editRow = row
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = [row.id]

      this.$modal.confirm('是否确认删除多多礼金？').then(function() {
        return delCashgift(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },

    handleLandingSuccess() {
      this.landingVisible = false
      this.getList()
      this.$emit('refresh')
    },
    handleLandingClose() {

    },
    handleBatchClose() {},
    handleBatchSuccess(response) {
      this.batchVisible = false
      this.getList()
      this.$emit('refresh')

      if (response.data && response.data.length > 0) {
        this.urlList = response.data
        this.urlList.forEach((string, index) => {
          this.urlStr += `${string}\n`
        })
        this.urlCopyVisible = true
      }
    },
    close() {
      this.$emit('close')
    },
    // 使用状态
    // handleInUseChange(row) {
    //   const text = row.inUse ? '启用' : '停用'
    //   this.$confirm(`确定${text}该礼金吗？`, '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     updateCashgift({ id: row.id, cashgiftId: row.cashgiftId, goodsInfoId: row.goodsInfoId }).then(response => {
    //       this.getList()
    //       this.$modal.msgSuccess(`${text}成功`)
    //     }).catch(() => {
    //       row.inUse = !row.inUse
    //     })
    //   }).catch(() => {
    //     row.inUse = !row.inUse
    //   })
    // },
    // 礼金状态
    handleVaildChange(row) {
      const text = row.status === 2 ? '启用' : '停用'
      this.$confirm(`确定${text}该礼金吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        await updateCashgiftStatus({
          id: row.id,
          goodsInfoId: row.goodsInfoId,
          cashgiftId: row.cashgiftId,
          status: row.status
        }).then(response => {
          this.getList()
          this.$modal.msgSuccess(`礼金${text}成功`)
        }).catch(() => {
          row.status = row.status === 2 ? 4 : 2
        })
      }).catch(() => {
        row.status = row.status === 2 ? 4 : 2
      })
    },
    // 根据时间戳计算年月日时分
    getTime(time) {
      return dayjs(time * 1000).format('YYYY-MM-DD HH:mm')
    },
    editRemark(row) {
      this.dataForm.remark = row.remark || ''
      this.dataForm.id = row.id
      this.dataForm.remarkLoading = false
      this.remarkVisible = true
    },
    // 修改备注
    handleRemark() {
      this.dataForm.remarkLoading = true
      updateCashgiftInfo({ id: this.dataForm.id, remark: this.dataForm.remark }).then(response => {
        this.remarkVisible = false
        this.getList()
      }).finally(() => {
        this.dataForm.remarkLoading = false
      })
    },
    // 显示链接窗口
    showGoodsUrl(goods) {
      this.urlVisible = true
      this.selectedGoods = goods
      this.selectedGoods.mediaPlatformType = this.platformType
      this.selectedGoods.goodsName = this.goods.goodsName
    },
    clipboardSuccess() {
      Message.success('复制成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.streaming-container{
  display: flex;
  flex-direction: column;
  height: 100%;
}
.ab {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

::v-deep .el-drawer__header {
  margin-bottom: 20px;
}
.remarkEdit{
  cursor: pointer;
}
.remark-overflow{
  display: flex;
  align-items: center;
  justify-content: center;
  .remark{
    // width: calc(100% - 40px);
    overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: right;
  }
}
.flex_status{
  display: flex;
  align-items: center;
  .status_switch{
    width: 60px;
  }
  .status_text{
    margin-left: 10px;
    width: calc(100% - 60px);
  }
}
.cashGift_info-img{
  display: flex;
  width:400px;
}
.couponAmount_info{
  color: #fff;
  background: rgb(227,84,76);
}
.couponThresholdAmount_info{
  color: rgb(227,84,76);
  background: rgb(254,246,246);
}
.red_text{
  color: rgb(227,84,76);
}
.cashgiftId_info{
  color: #999;
}
.divider-line {
  margin: 8px 0;
  border-top: 1px dashed #eee;
}

.renew-info {
  color: #606266;

  > div {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .remark {
    color: #409EFF;
    margin: 0 4px;
  }
}

.disabled-text {
  color: #909399;
  font-size: 14px;
}
</style>
