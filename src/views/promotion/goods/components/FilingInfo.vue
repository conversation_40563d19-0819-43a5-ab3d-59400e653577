<script setup>
import dayjs from 'dayjs'
import { computed, onMounted, ref, watch } from 'vue'
import {
  getFilingInformation
} from '@/api/promotion/goods'
const props = defineProps({
  visible: Boolean,
  form: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['update:visible'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

const loading = ref(false)
const list = ref([])
const fetchList = () => {
  list.value = []
  loading.value = true
  const data = {
    goodsId: props.form.goodsId,
    duoId: props.form.duoId
  }
  getFilingInformation(data).then((res) => {
    const message = res.data && res.data?.response?.applicationList || []
    list.value = message
  }).finally(() => {
    loading.value = false
  })
}
onMounted(() => {
  fetchList()
})
const statusFilter = (status) => {
  const list = {
    0: '已创建',
    1: '已提交',
    2: '已通过',
    3: '已驳回'
  }
  return list[status]
}
const timeFilter = (timestamp) => {
  const correctTimestamp = timestamp / 1000
  const date = dayjs.unix(correctTimestamp)
  const formattedDate = date.format('YYYY-MM-DD HH:mm')
  return formattedDate
}

</script>

<template>
  <el-dialog
    title="备案信息"
    :visible.sync="open"
    width="70%"
    top="10vh"
    append-to-body
  >

    <el-table
      v-loading="loading"
      :data="list"
      style="width: 100%"
    >
      <el-table-column label="商品id" align="center" prop="goodsId" min-width="200" />
      <el-table-column label="店铺id" align="center" prop="mallId" min-width="200" />
      <el-table-column label="推广开始时间" align="center" prop="promotionStartTime" min-width="200">
        <template #default="scope">
          {{ timeFilter(scope.row.promotionStartTime) }}
        </template>
      </el-table-column>
      <el-table-column label="推广结束时间" align="center" prop="promotionEndTime" min-width="200">
        <template #default="scope">
          {{ timeFilter(scope.row.promotionEndTime) }}
        </template>
      </el-table-column>
      <el-table-column label="报备提交时间" align="center" prop="commitTime" min-width="200">
        <template #default="scope">
          {{ timeFilter(scope.row.commitTime) }}
        </template>
      </el-table-column>
      <el-table-column label="报备状态" align="center" prop="status" min-width="200">
        <template #default="scope">
          <el-tag :type="scope.row.status===0?'info':scope.row.status===1?'warning':scope.row.status===2?'success':scope.row.status===3?'danger':''">{{ statusFilter(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核信息" align="center" prop="comment" min-width="200" />
      <el-table-column label="最后更新时间" align="center" prop="updatedAt" min-width="200">
        <template #default="scope">
          {{ timeFilter(scope.row.updatedAt) }}
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
