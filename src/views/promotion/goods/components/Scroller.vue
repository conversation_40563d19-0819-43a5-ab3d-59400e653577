<template>
  <div class="scroller" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <transition-group name="list-complete" tag="span">
      <span
        v-for="(item, i) in items"
        :key="i + item.title"
        class="list-complete-item"
        @click="handleClick(item)"
      >
        限制推广商品：
        <span style="margin-right: 10px">ID: {{ item.goodsId }}</span>
        <span style="margin-right: 10px">名称: {{ item.goodsName }}</span>
        <span>店铺: {{ item.mallName }}</span>
      </span>
    </transition-group>

    <el-dialog
      :title="currentNotice.title"
      :visible.sync="noticeVisible"
      width="50%"
    >
      <div v-html="currentNotice.noticeContent" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="noticeVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'ListScroller',
  props: {
    type: {
      type: String,
      default: '' // notice
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  emits: ['itemClick'],
  data: function() {
    return {
      items: [],
      currentIndex: 0,
      timer: null,
      noticeVisible: false,
      currentNotice: {}
    }
  },

  watch: {
    list: {
      handler(val) {
        if (val.length > 0) {
          this.items = [val[0]]
          this.currentIndex = 0
        }
        if (val.length > 1) this.loop()
      },
      deep: true,
      immediate: true
    }
  },
  // mounted() {
  //   this.loop()
  // },
  destroyed() {
    this.clearTimer()
  },
  methods: {
    add() {
      this.currentIndex++
      if (this.currentIndex >= this.list.length) {
        this.currentIndex = 0
      }
      this.items.splice(1, 0, this.list[this.currentIndex])
      this.items.splice(0, 1)
    },
    loop() {
      this.clearTimer()
      this.timer = setTimeout(() => {
        this.add()
        this.loop()
      }, 3000)
    },

    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    },

    handleMouseEnter() {
      this.clearTimer()
    },
    handleMouseLeave() {
      this.loop()
    },
    handleClick(item) {
      if (this.type === 'notice') {
        this.currentNotice = item
        this.noticeVisible = true
      } else {
        this.$emit('itemClick', item)
      }
    }
  }

}
</script>
<script setup>

</script>
<style lang="scss" scoped>
.scroller {
  height: 35px;
}
.list-complete-item {
  width: 100%;
  line-height: 30px;
  font-size: 14px;
  transition: all .5s;
  display: inline-block;
  cursor: pointer;
  overflow:hidden; //超出的文本隐藏
  text-overflow:ellipsis; //溢出用省略号显示
  white-space:nowrap; //溢出不换行
  color: #ff4949;
  &:hover {
    text-decoration: underline;
  }
}
.list-complete-enter
  /* .list-complete-leave-active for below version 2.1.8 */ {
  opacity: 0;
  transform: translateY(15px);
}
.list-complete-leave-to
  /* .list-complete-leave-active for below version 2.1.8 */ {
  opacity: 0;
  transform: translateY(-15px);
}
.list-complete-leave-active {
  position: absolute;
}
</style>
