<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { Message } from 'element-ui'
import { handleLandingPage } from '@/api/promotion/goods'
import MediaAccountSelector from '@/views/promotion/goods/components/MediaAccountSelector.vue'
const props = defineProps({
  visible: Boolean,
  info: {
    type: Object,
    default: () => {}
  }

})
const emit = defineEmits(['success', 'close'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

const profitLoading = ref(false)

const profitForm = reactive({

})
const formRef = ref()
const submit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      const data = {
        goodsName: props.info.goodsName,
        url: props.info.url,
        advertiserId: advertiserIds.value
      }
      profitLoading.value = true
      handleLandingPage(data).then(response => {
        Message.success('添加成功')
        emit('success')
        close()
      }).finally(() => {
        profitLoading.value = false
      })
    } else {
      return false
    }
  })
}

const advertiserIds = ref([])
const handleAccountSelect = (ids) => {
  advertiserIds.value = ids
}

const close = () => {
  open.value = false
}
const validatePass = (rule, value, callback) => {
  if (!advertiserIds.value || !advertiserIds.value.length) {
    callback(new Error('请至少选择一个媒体账户'))
  } else {
    callback()
  }
}
const rules = {
  advertiserIds: [
    { validator: validatePass, required: true, trigger: 'change' }
  ]
}
</script>

<template>
  <el-dialog
    title="巨量第三方落地页"
    :visible.sync="open"
    width="700px"
    top="10vh"
    append-to-body
  >
    <el-form ref="formRef" :rules="rules" :model="profitForm" label-width="68px">
      <el-form-item label="名称：" prop="name">
        {{ info.goodsName }}
      </el-form-item>
      <el-form-item label="" prop="advertiserIds" label-width="0">

        <MediaAccountSelector v-if="open" :goods="info" :show-tips="false" @change="handleAccountSelect" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button v-loading="profitLoading" type="primary" @click="submit">确认</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
