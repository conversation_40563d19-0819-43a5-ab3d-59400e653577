<template>
  <div class="edit-landing-wrap">
    <div class="edit-form">
      <div class="landing-rule">
        <div class="landing-rule-title">自建地页规则</div>
        <div class="landing-rule-item text-bold">落地页所有区域点击后均可跳转到目标商品。</div>
        <div class="landing-rule-item">基础图片：jpg，png，jpeg等图片格式，请选择宽度一致的图片，限3张。</div>
        <div class="landing-rule-item">按钮图片：jpg，png，jpeg等图片格式，请选择宽度一致的图片，限1张。</div>
        <div class="landing-rule-item color-danger text-bold">图片来源：请优先前往千牛素材库、拼多多素材库、京东素材库获取图片地址，以获得更加稳定高效的落地页体验。</div>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <!--        <el-form-item label="类型" prop="landingPageType">-->
        <!--          <el-radio-group v-model="form.landingPageType">-->
        <!--            <el-radio-->
        <!--              v-for="dict in dict.type.landing_page_type"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.value"-->
        <!--            >{{ dict.label }}</el-radio>-->
        <!--          </el-radio-group>-->
        <!--        </el-form-item>-->
        <el-form-item v-if="form.slides.length" label="轮播图" prop="slides">
          <ImagesInputList
            v-model="form.slides"
            placeholder="请拷贝图片地址到此处"
            @upload="showFileRes('slides')"
          />
        </el-form-item>
        <el-form-item label="基础图片" prop="normalImages">
          <ImagesInputList
            v-model="form.normalImages"
            placeholder="请拷贝图片地址到此处"
            :file-size="1"
            :max="3"
            @upload="showFileRes('normalImages')"
          />
        </el-form-item>
        <el-form-item prop="buttonImage">
          <template #label>
            按钮图片
          </template>
          <div class="image-btn">
            <el-input
              v-model="form.buttonImage"
              placeholder="请拷贝图片地址到此处"
              clearable
            />
            <el-button style="margin-left: 10px" @click="showFileRes('buttonImage')">
              <i class="el-icon-upload" />
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="按钮图片布局" prop="buttonType">
          <el-radio-group v-model="form.buttonType">
            <el-radio-button label="3">浮动
              <el-tooltip class="item" effect="dark" content="图片固定在手机屏幕对应的百分比位置，受手机高度影响;按钮图片可上下拖动，如无特殊需求，不建议移动位置" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </el-radio-button>
            <el-radio-button label="4">末尾
              <el-tooltip class="item" effect="dark" content="按钮图片将跟在基础图片末尾" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="点击区域">
          <template #label>
            点击区域
            <el-tooltip class="item" effect="dark" content="添加后将取消全局点击跳转目标商品，仅点击区域和按钮图片可跳转。" placement="top">
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
          <div v-for="(area,i) in form.clickAreas" :key="area.color" class="clear-area-icon" :style="{ backgroundColor: area.color }" @click="removeArea(i)">
            <i class="el-icon-delete icon" />
          </div>
          <el-button @click="addClickArea">
            <i class="el-icon-plus" />
          </el-button>
        </el-form-item>
        <el-form-item label="点击按钮">
          <el-popover
            v-for="(btn,i) in form.clickBtns"
            :key="i"
            placement="top-start"
            width="320"
            trigger="click"
          >
            <el-form :model="btn" label-width="80px" size="small" style="width: 280px">
              <el-form-item label="内容">
                <el-input v-model="btn.text" />
              </el-form-item>
              <el-form-item label="尺寸">
                <el-slider v-model="btn.size" :min="12" :max="32" :step="1" />
              </el-form-item>
              <el-form-item label="文本颜色">
                <el-color-picker v-model="btn.color" />
              </el-form-item>
              <el-form-item label="背景色">
                <el-color-picker v-model="btn.background" />
              </el-form-item>
              <el-form-item label="跳动">
                <el-switch v-model="btn.flash" />
              </el-form-item>
              <el-form-item>
                <el-button @click="removeClickBtn(i)">删除</el-button>
              </el-form-item>
            </el-form>
            <div slot="reference" :style="{ backgroundColor: btn.background }" class="clear-area-icon">
              <i class="el-icon-edit icon" />
            </div>
          </el-popover>

          <el-button @click="addClickBtns">
            <i class="el-icon-plus" />
          </el-button>
        </el-form-item>
        <el-form-item label="跳转方式" prop="redirectType">
          <el-radio-group v-model="form.redirectType">
            <el-radio
              v-for="o in redirectOptions"
              :key="o.value"
              :label="o.value"
            >{{ o.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="!pageBAdvertiserId && isWeibo" label="媒体账户" prop="advertiserId">
          <el-select
            v-model="form.advertiserId"
            filterable
            remote
            clearable
            placeholder="请选择"
            :remote-method="requestRemote"
            style="width: 100%"
            :loading="selectLoading"
            @change="requestRemote('')"
          >
            <el-option
              v-for="item in mediaAccountOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主体名称" prop="name">
          <el-input v-model="form.companyName" filterable placeholder="请输入名称（可选）" style="width: 100%" clearable />
        </el-form-item>
        <el-form-item v-if="domainOptions.length" label="自定义域名" prop="domainName">
          <el-select
            v-model="form.domainName"
            placeholder="未选择则使用默认域名"
            clearable
            style="width: 100%"
            @change="handleDomainNameChange"
          >
            <el-option
              v-for="item in domainOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-loading="btnLoading" @click="close">取消</el-button>
          <el-button v-loading="btnLoading" type="primary" @click="createLanding">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="phone-wrap">
      <div class="phone-tip">
        <div class="phone-tip-item">尺寸:
          <el-select
            v-model="phoneSize"
            placeholder="请选择"
            size="mini"
            value-key="name"
            style="width:240px "
          >
            <el-option
              v-for="item in phoneSizes"
              :key="item.name"
              :label="`${item.name} (${item.width} × ${item.height})`"
              :value="item"
            />
          </el-select>
        </div>
        <div class="phone-tip-item">机型比例仅供参考，实际展示效果与用户手机型号相关。</div>
      </div>
      <div class="phone" :style="{width: phoneSize.width + 'px', height: phoneSize.height + 'px'}">
        <!--      <div class="phone">-->
        <div ref="phoneHeaderRef" class="phone-header">
          <el-image :src="phoneHeader" />
          <div class="phone-title overflow-text">{{ form.title }}</div>
        </div>
        <div class="scroll-phone-container">
          <el-scrollbar class="scroll-phone-main">
            <div ref="phoneContent" class="phone-content">
              <Slide :images="form.slides.filter(i => i)" />
              <img
                v-for="(image, index) in form.normalImages.filter(i => i)"
                :key="index"
                class="normal-image"
                :src="image"
                @load="refreshClickAreas"
              >
              <img
                v-if="form.buttonImage && form.buttonType === LandingImageType.BOTTOM"
                class="normal-image"
                :src="form.buttonImage"
                @load="refreshClickAreas"
              >
              <template v-if="refreshAreas">
                <VueDragResize v-for="(area,i) in form.clickAreas" :key="area.color" :parent-limitation="true" :x="area.left" :y="area.top" :w="area.width" :h="area.height" @resizestop="onResizstop($event, i)" @dragstop="onDragstop($event, i)">
                  <div class="click-area" :style="{ backgroundColor: area.color }" />
                </VueDragResize>
              </template>
              <template v-if="refreshBtns">
                <VueDragResize v-for="(btn,i) in form.clickBtns" :key="btn.key" :parent-limitation="true" :x="btn.left" :y="btn.top" :w="btn.width" :h="btn.height" @resizestop="onBtnResizstop($event, i)" @dragstop="onBtnDragstop($event, i)">
                  <div class="click-btn-wrap">
                    <div
                      class="click-btn"
                      :class="{'click-btn-flash': btn.flash}"
                      :style="{
                        backgroundColor: btn.background,
                        color: btn.color,
                        fontSize: btn.size + 'px',
                      }"
                    >{{ btn.text }}</div>
                  </div>
                </VueDragResize>
              </template>
              <div class="company-name">{{ form.companyName }}</div>
            </div>
          </el-scrollbar>
          <VueDragResize v-if="refreshBottom && form.buttonImage && form.buttonType === LandingImageType.FIXED" is-active :is-resizable="false" axis="y" :y="btnImgTop" :w="windowWidth" :h="btnImgHeight" @dragstop="handleDragStop">
            <img
              ref="buttonImage"
              class="normal-image"
              :src="form.buttonImage"
              @load="handleButtonImageLoad"
            >
          </VueDragResize>
        </div>
      </div>
    </div>

    <el-dialog
      title="选择文件"
      :visible.sync="fileVisible"
      width="90%"
      top="2vh"
      append-to-body
    >
      <FileRes default-selected="落地页" selector @select="handleFileSelect" />
    </el-dialog>
  </div>
</template>

<script>
import FileRes from '@/views/promotion/fileres/index.vue'
import Slide from '@/components/Slide/index.vue'
import ImagesInputList from './ImagesInputList.vue'
import VueDragResize from 'vue-drag-resize'

import phoneHeader from '@/assets/images/phone-title.png'
import { data2Html } from '@/template/compileHtml'
import { addLanding, getGoodsFirstDeptDomainName, updateLanding } from '@/api/promotion/landing'
import { getConfigKey } from '@/api/system/config'
import { phoneSizes } from '@/config/phone'
import { LandingImageType } from '@/config'

function scaleSize(num, scale) {
  return Math.round(num * scale)
}

const MonitorConfigKeyMap = {
  1: 'landing_page_monitor_url',
  2: 'landing_page_monitor_url',
  3: 'landing_page_monitor_url',
  4: 'landing_page_monitor_url',
  5: 'tb_landing_page_monitor_url'
}

export default {
  name: 'Landing',
  components: {
    Slide,
    ImagesInputList,
    VueDragResize,
    FileRes
  },
  dicts: ['landing_page_type', 'landing_page_resource_type'],
  props: {
    goods: {
      type: Object,
      default: () => ({})
    },
    landing: {
      type: Object
    },
    platformType: {
      type: Number
    },
    pageBid: {
      type: String
    },
    pageBAdvertiserId: {
      type: String
    }
  },
  emits: ['close', 'success'],
  data: () => ({
    form: {
      id: '',
      title: '',
      landingPageType: '1',
      slides: [],
      normalImages: [],
      buttonImage: '',
      buttonType: LandingImageType.FIXED,
      clickAreas: [],
      clickBtns: [],
      redirectType: '1',
      advertiserId: '',
      buttonMargin: 0,
      enableAbPage: false,
      companyName: '',
      domainName: ''
    },
    rules: {
      title: [
        { required: true, message: '请输入标题', trigger: 'blur' }
      ],
      landingPageType: [
        { required: true, message: '请选择类型', trigger: 'change' }
      ],
      advertiserId: [
        { required: true, message: '请选择媒体账户', trigger: 'blur' }
      ]
    },
    redirectOptions: [
      { label: '点击跳转', value: '1' },
      { label: '自动跳转', value: '2' }
    ],
    phoneHeader,
    btnLoading: false,

    isFirst: true,
    btnImgHeight: 1000,
    btnImgTop: 0,

    // 文件系统
    fileVisible: false,
    fileFormKey: 'button',

    refreshAreas: true,
    refreshBtns: true,
    refreshBottom: true,
    colors: [
      '#FF000080',
      '#FF7F0080',
      '#FFFF0080',
      '#00FF0080',
      '#0000FF80'
    ],
    domainOptions: [],
    phoneSize: phoneSizes[1],
    phoneSizes,
    isButtonRedraw: false,
    LandingImageType
  }),
  computed: {
    isWeibo() {
      return this.platformType === 3
    },
    windowWidth() {
      return this.phoneSize.width
    },
    windowHeight() {
      return this.phoneSize.height - this.$refs['phoneHeaderRef'].offsetHeight
    }
  },
  watch: {
    phoneSize(newSize, oldSize) {
      this.redraw(newSize, oldSize)
    }
  },
  mounted() {
    if (this.landing) {
      this.analysisLanding()
    }

    if (this.goods && !this.landing) {
      this.form.title = this.goods.goodsName
      if (this.goods.goodsImageUrl) this.form.normalImages = [this.goods.goodsImageUrl, this.goods.goodsImageUrl]
    }

    this.fetchDomainName()
  },
  methods: {
    createLanding() {
      if (!this.hasImages(this.form) && this.form.redirectType === '1') {
        this.$modal.msgWarning('请至少上传一张图片，或选择自动跳转模式。')
        return
      }
      if (this.form.normalImages.length > 3) {
        this.$modal.msgWarning('基础图片最多上传3张')
        return
      }
      if (this.checkHttps(this.form)) {
        this.$modal.msgWarning('只能上传https协议图片')
        return
      }
      this.$refs['form'].validate(valid => {
        if (!valid) return
        const resourceList = this.getResourceList()
        const formData = {
          goodsId: this.goods.goodsId,
          mediaType: this.platformType,
          landingPageType: this.form.landingPageType,
          redirectType: this.form.redirectType,
          title: this.form.title,
          advertiserId: this.form.advertiserId,
          landingPageResourceList: resourceList,
          buttonMargin: this.form.buttonMargin,
          enableAbPage: this.form.enableAbPage,
          companyName: this.form.companyName,
          clickAreas: JSON.stringify([...this.form.clickAreas, ...this.form.clickBtns]),
          content: '',
          domainName: this.form.domainName
        }
        if (this.form.id) formData.id = this.form.id
        if (this.pageBid) {
          formData.pageBid = this.pageBid
          formData.advertiserId = this.pageBAdvertiserId
          formData.landingPageType = '2'
        }
        this.btnLoading = true
        getConfigKey(MonitorConfigKeyMap[this.goods.platform]).then(data => {
          if (data.code === 200) {
            formData.content = data2Html(
              { form: this.form,
                goods: this.goods,
                monitorUrl: data.msg,
                mediaType: this.platformType,
                advertiserId: this.form.advertiserId,
                btnImgPosition: {
                  bottom: this.getBtnImgPosition(this.form.buttonMargin)
                },
                areas: this.generateAreas(this.form.clickAreas),
                btns: this.generateBtns(this.form.clickBtns)
              }
            )
            console.log(formData)
            // this.btnLoading = false
            // return
            if (this.form.id) {
              updateLanding(formData).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.$emit('success')
              }).finally(() => {
                this.btnLoading = false
              })
            } else {
              addLanding(formData).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.$emit('success')
              }).finally(() => {
                this.btnLoading = false
              })
            }
          } else {
            this.$modal.msgError('获取监测链接失败')
            this.btnLoading = false
          }
        })
      })
    },
    // 获取自定义域名
    fetchDomainName() {
      if (!this.goods.id) return
      getGoodsFirstDeptDomainName(this.goods.id).then(response => {
        if (response.data) {
          this.domainOptions = response.data.split(',')
          let url = ''
          if (this.landing) {
            const reg = /^(?:(http|https|ftp):\/\/)?((?:[\w-]+\.)+[a-z0-9]+)((?:\/[^/?#]*)+)?(\?[^#]+)?(#.+)?$/i
            url = reg.exec(this.landing.filePath)[2] + '/'
          } else {
            url = this._setupProxy.getStorage('domainName')
          }
          this.form.domainName = this.domainOptions.includes(url) ? url : ''
        } else {
          this.domainOptions = []
        }
      })
    },
    getResourceList() {
      const resourceList = []
      this.form.slides.forEach(item => {
        if (item) {
          resourceList.push({
            resourceType: LandingImageType.SLIDE,
            resourceUrl: item
          })
        }
      })
      this.form.normalImages.forEach(item => {
        if (item) {
          resourceList.push({
            resourceType: LandingImageType.NORMAL,
            resourceUrl: item
          })
        }
      })
      this.form.buttonImage && resourceList.push({
        resourceType: `${this.form.buttonType}`,
        resourceUrl: this.form.buttonImage
      })
      return resourceList
    },
    analysisLanding() {
      const {
        id,
        title,
        landingPageType,
        landingPageResourceList,
        redirectType,
        advertiserId,
        buttonMargin,
        enableAbPage,
        companyName,
        clickAreas
      } = this.landing
      const btnType = landingPageResourceList.find(item => item.resourceType === LandingImageType.FIXED)
        ? LandingImageType.FIXED : LandingImageType.BOTTOM
      this.form = {
        id: id,
        title: title,
        landingPageType: landingPageType,
        slides: this.getImagesByType(landingPageResourceList, LandingImageType.SLIDE),
        normalImages: this.getImagesByType(landingPageResourceList, LandingImageType.NORMAL),
        buttonImage: this.getImagesByType(landingPageResourceList, btnType)[0],
        redirectType: redirectType,
        advertiserId: advertiserId,
        enableAbPage: enableAbPage,
        buttonMargin: buttonMargin ?? 0,
        buttonType: btnType,
        companyName: companyName,
        clickAreas: [],
        clickBtns: [],
        domainName: ''
      }
      const clickList = clickAreas ? JSON.parse(clickAreas) : []
      clickList.forEach(item => {
        switch (item.type) {
          case 'area':
            this.form.clickAreas.push(item)
            this.colors = this.colors.filter(color => color !== item.color)
            break
          case 'button':
            this.form.clickBtns.push(item)
            break
          default:
            this.form.clickAreas.push(item)
            break
        }
      })
      this.refreshBtns = false
      setTimeout(() => {
        this.refreshBtns = true
      }, 100)
    },
    getImagesByType(list, type) {
      return list.filter(item => item.resourceType === type).map(item => item.resourceUrl)
    },

    hasImages() {
      let has = false
      this.form.slides.forEach(item => {
        if (item) {
          has = true
        }
      })
      this.form.normalImages.forEach(item => {
        if (item) {
          has = true
        }
      })
      this.form.buttonImage && (has = true)
      return has
    },
    checkHttps() {
      let hasHttp = 0
      this.form.slides.forEach(item => {
        if (item && item.indexOf('http://') > -1) { hasHttp++ }
      })
      this.form.normalImages.forEach(item => {
        if (item && item.indexOf('http://') > -1) { hasHttp++ }
      })
      if (this.form.buttonImage && this.form.buttonImage.indexOf('http://') > -1) hasHttp++

      return hasHttp !== 0
    },

    handleDragStop(newRect) {
      this.form.buttonMargin = this.windowHeight - newRect.top - newRect.height
    },
    getBtnImgPosition(bottom) {
      // if (bottom >= 0) {
      return Math.round((bottom / this.windowHeight) * 100) + '%'
      // } else {
      //   return bottom + 'px'
      // }
    },
    handleButtonImageLoad() {
      if (this.isButtonRedraw) {
        this.isButtonRedraw = false
        return
      }
      if (this.btnImgTop) return
      this.btnImgHeight = this.$refs['buttonImage'].height
      this.$nextTick(() => {
        let top = this.windowHeight - this.btnImgHeight
        if (this.isFirst) {
          top = top - this.form.buttonMargin
          this.isFirst = false
        }
        this.btnImgTop = top
      })
    },
    generateAreas(items) {
      const phoneContent = this.$refs['phoneContent']
      const areas = []
      items.forEach((item) => {
        areas.push({
          left: Math.round((item.left / this.windowWidth) * 100) + '%',
          top: Math.round((item.top / phoneContent.offsetHeight) * 100) + '%',
          width: Math.round((item.width / this.windowWidth) * 100) + '%',
          height: Math.round((item.height / phoneContent.offsetHeight) * 100) + '%'
        })
      })
      return areas
    },
    generateBtns(items) {
      const phoneContent = this.$refs['phoneContent']
      const btns = []
      items.forEach((item) => {
        btns.push({
          ...item,
          left: Math.round((item.left / this.windowWidth) * 100) + '%',
          top: Math.round((item.top / phoneContent.offsetHeight) * 100) + '%',
          width: Math.round((item.width / this.windowWidth) * 100) + '%',
          height: Math.round((item.height / phoneContent.offsetHeight) * 100) + '%'
        })
      })
      return btns
    },
    onResizstop(newRect, i) {
      this.form.clickAreas[i] = Object.assign(this.form.clickAreas[i], newRect)
    },
    onDragstop(newRect, i) {
      this.form.clickAreas[i] = Object.assign(this.form.clickAreas[i], newRect)
    },
    onBtnResizstop(newRect, i) {
      this.form.clickBtns[i] = Object.assign(this.form.clickBtns[i], newRect)
    },
    onBtnDragstop(newRect, i) {
      this.form.clickBtns[i] = Object.assign(this.form.clickBtns[i], newRect)
    },
    showFileRes(key) {
      this.fileFormKey = key
      this.fileVisible = true
    },
    handleFileSelect(url) {
      const key = this.fileFormKey

      if (Array.isArray(this.form[key])) {
        const index = this.form[key].findIndex(item => !item)
        if (index !== -1) {
          this.$set(this.form[key], index, url)
        } else {
          this.$set(this.form[key], this.form[key].length, url)
        }
      } else {
        this.form[key] = url
      }
      this.fileVisible = false
    },
    addClickArea() {
      const len = this.form.clickAreas.length
      if (len === 3) {
        this.$message.warning('最多添加3个点击区域')
        return
      }

      this.form.clickAreas.push({
        left: 0,
        top: len * 100,
        width: 375,
        height: 150,
        color: this.colors.pop(),
        type: 'area'
      })
    },
    addClickBtns() {
      this.form.clickBtns.push({
        key: new Date().getTime(),
        left: 0,
        top: 0,
        bottom: 0,
        width: 375,
        height: 100,
        text: '立即购买',
        color: '#ffffff',
        background: '#e02e24',
        size: 16,
        flash: true,
        type: 'button'
      })
    },
    removeArea(index) {
      this.colors.push(this.form.clickAreas.splice(index, 1)[0]?.color)
    },
    removeClickBtn(index) {
      this.form.clickBtns.splice(index, 1)
    },
    redraw(newSize, oldSize) {
      this.form.clickAreas.forEach(item => {
        if (newSize.width !== oldSize.width) {
          const scale = newSize.width / oldSize.width
          item.left = scaleSize(item.left, scale)
          item.top = scaleSize(item.top, scale)
          item.width = scaleSize(item.width, scale)
          item.height = scaleSize(item.height, scale)
        }
      })
      this.form.clickBtns.forEach(item => {
        if (newSize.width !== oldSize.width) {
          const scale = newSize.width / oldSize.width
          item.left = scaleSize(item.left, scale)
          item.top = scaleSize(item.top, scale)
          item.width = scaleSize(item.width, scale)
          item.height = scaleSize(item.height, scale)
        }
      })
      if (this.form.buttonImage && this.form.buttonType === LandingImageType.FIXED) {
        this.isButtonRedraw = true
        this.btnImgHeight = scaleSize(this.btnImgHeight, newSize.width / oldSize.width)
        this.form.buttonMargin = scaleSize(this.form.buttonMargin, newSize.height / oldSize.height)
        this.btnImgTop = this.windowHeight - this.form.buttonMargin - this.btnImgHeight
        this.refreshBottomImg()
      }
      this.refreshClickAreas()
      this.refreshClickBtns()
    },
    refreshClickAreas() {
      this.refreshAreas = false
      this.$nextTick(() => {
        this.refreshAreas = true
      })
    },
    refreshClickBtns() {
      this.refreshBtns = false
      this.$nextTick(() => {
        this.refreshBtns = true
      })
    },
    refreshBottomImg() {
      this.refreshBottom = false
      this.$nextTick(() => {
        this.refreshBottom = true
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>

<script setup>
import { getCurrentInstance } from 'vue'
import useAccountSelect from '@/hooks/mediaAction/useAccountSelect'
import useLocalStorage from '@/hooks/useLocalStorage'

const self = getCurrentInstance().proxy
const { options: mediaAccountOptions, requestRemote, selectLoading } = useAccountSelect(self.platformType)

const { setStorage, getStorage } = useLocalStorage('domainName', { observe: false })
const handleDomainNameChange = (val) => setStorage('domainName', val)

</script>

<style lang="scss" scoped>
.edit-landing-wrap {
  padding:0 20px 20px ;
  display: flex;
}
.edit-form {
  box-sizing: border-box;
  width: 530px;
  min-width: 500px;
  margin-right: 100px;
  //height: calc(100vh - 100px);
  //overflow-y: auto;
  .landing-rule {
    margin: 0 0 20px 20px;
    border: 1px solid #DCDFE6;
    font-size: 14px;
    border-radius: 10px;
    padding: 15px 15px 5px;
    .landing-rule-title {
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 10px;
      border-bottom: 1px solid #DCDFE6;
      margin-bottom: 10px;
    }
    .landing-rule-item {
      margin-bottom: 10px;
    }
    .red {
      color: #f56c6c;
      font-weight: bold;
    }
  }
  .clear-area-icon {
    width: 56px;
    display: inline-block;
    border: 1px solid #DCDFE6;
    border-radius: 5px;
    text-align: center;
    color: white;
    margin-right: 10px;
    cursor: pointer;
    .icon {
      opacity: 0;
    }
    &:hover {
      .icon {
        opacity: 1;
      }
    }
  }
  .images-main-item {
    width: 300px;
    margin-right: 12px;
  }
  .images-item {
    margin-bottom: 10px;
  }
}
.phone-wrap {
  flex:1;
  position: relative;
}
.phone {
  box-sizing: content-box;
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 405px;
  height: 840px;
  border: 10px solid #d1eaf8;
  border-radius: 50px ;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transform: scale(0.75,0.75);
  transform-origin: 0 0;
  .phone-header {
    position: relative;
    height: 82px;
    .phone-title {
      position: absolute;
      top:44px;
      left: 20px;
      width: 250px;
    }
  }

  .scroll-phone-container {
    box-sizing: border-box;
    position: relative;
    flex:1;
    width: 100%;
    overflow: hidden;
  }
  .scroll-phone-main {
    box-sizing: border-box;
    position: relative;
    width: 100%;
    height: 100%;
    //height: 735px; // 728
  }
  .phone-content {
    position: relative;
    overflow-y: hidden;
    overflow-x: hidden;
  }
  .normal-image {
    width: 100%;
    display: block;
    border: 0;
    object-fit: cover;
  }
  .button-image-fixed {
    position: absolute;
    bottom:0;
    width: 100%;
  }
}
.phone-tip {
  margin-bottom: 10px;
}
.phone-tip-item {
  color: #999;
  font-size: 14px;
  margin-bottom: 5px;
}
.image-btn {
  display: flex;
}
::v-deep .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
.dialog-file-res {
  height: calc(100vh - 250px);
  display: flex;
  flex-direction: column;
}
.click-area {
  width: 100%;
  height: 100%;
}
.click-btn-wrap{
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
  flex-direction: row ;
  align-items: center;
}
.click-btn {
  flex: 1;
  background: #00afff;
  padding: 10px;
  text-align: center;
  border-radius: 10px;
  color: white;
  font-weight: 500;
}

.click-btn-flash {
  animation-name: flash;
  animation-duration: 2s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
}

@keyframes flash{
  0%,50%,to {
    opacity: 1;
    transform: scale(1)
  }

  25%,75% {
    opacity: 1;
    transform: scale(.9)
  }
}
.company-name {
  text-align: center;
  font-size: 14px;
  color: #949494;
  line-height: 18px;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>
