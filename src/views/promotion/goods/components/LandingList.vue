<template>
  <div class="streaming-container" style="padding-top: 0">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:landing:add']"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
        <el-button
          type="warning"
          icon="el-icon-close"
          size="mini"
          @click="close"
        >关闭</el-button>

      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <div style="flex: 1">
      <el-table v-loading="loading" :data="landingList">
        <el-table-column label="商品ID" align="center" prop="goodsId" width="160" />
        <el-table-column label="标题" align="center" prop="title" show-overflow-tooltip />
        <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip width="200" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="platformType!==1" label="审核页(A页)" align="center" prop="createTime" width="120">
          <template #header>
            <span>审核页(A页)</span>
            <el-tooltip effect="dark" content="" placement="top">
              <i class="el-icon-question  color-primary" />
              <div slot="content">
                通过开关启用A页，点击编辑按钮再次编辑A页<br>
                开关开启：表示启用A页，接受审核<br>
                开关关闭：表示启用B页，真实投放商品
              </div>
            </el-tooltip>
          </template>
          <template #default="scope">
            <div
              v-if="scope.row.pageAid"
              class="ab"
            >
              <el-switch
                v-model="scope.row.enableAbPage"
                v-has-permi="['promotion:landing:enableAbPage']"
                size="mini"
                active-color="#13ce66"
                @change="handleEnableAbPage(scope.row)"
              />
              <el-button v-has-permi="['promotion:landing:enableAbPage']" type="text" icon="el-icon-edit" @click="editABPage(scope.row)">编辑</el-button>
            </div>
            <div v-else>
              <el-button
                v-hasPermi="['promotion:landing:add']"
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="createABPage(scope.row)"
              >新增</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['promotion:landing:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              v-hasPermi="['promotion:landing:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              style="color: #f56c6c"
              @click="handleDelete(scope.row)"
            >删除</el-button>
            <el-tooltip class="item" effect="dark" content="仅供预览，投放链接请从[获取链接]按钮复制" placement="top">
              <el-button
                size="mini"
                type="text"
                @click="openPath(scope.row.filePath)"
              >预览
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-drawer
      title="自建落地页"
      :visible.sync="landingVisible"
      direction="rtl"
      size="90%"
      append-to-body
      destroy-on-close
      :wrapper-closable="false"
      @close="handleLandingClose"
    >
      <Landing :goods="aGoods || innerGoods" :landing="landing" :page-bid="pageBid" :page-b-advertiser-id="pageBAdvertiserId" :platform-type="platformType" @success="handleLandingSuccess" @close="landingVisible = false" />
    </el-drawer>
    <el-dialog
      title="选择商品"
      :visible.sync="goodsVisible"
      top="5vh"
      width="60%"
      append-to-body
    >
      <GoodsList :goods="goods" :platform-type="platformType" @select="handleGoodsSelect" />
    </el-dialog>
  </div>
</template>

<script>
import { listLanding, getLanding, delLanding, enableAbPage } from '@/api/promotion/landing'
import Landing from './LandingEditor.vue'
import { getGoods } from '@/api/promotion/goods'
import GoodsList from './GoodsList.vue'
import { isJd } from '@/utils/judge/platform'

export default {
  name: 'LandingList',
  dicts: ['landing_page_type', 'landing_page_resource_type'],
  components: {
    GoodsList,
    Landing
  },
  props: {
    goods: {
      type: Object
    },
    platformType: {
      type: Number
    }
  },
  emits: ['close', 'refresh'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 内部goods
      innerGoods: null,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedLandingPageResource: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 落地页表格数据
      landingList: [],
      // 落地页资源表格数据
      landingPageResourceList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        goodsId: null,
        landingPageType: null,
        title: null,
        createdBy: null,
        createTime: null
      },
      // 表单参数
      form: {},

      // 落地页编辑
      landing: null,
      landingVisible: false,

      pageBid: null,
      pageBAdvertiserId: null,
      aGoods: null,
      goodsLoading: false,
      goodsVisible: false
    }
  },
  computed: {
    addDisabled() {
      if (this.loading) return true
      return this.landingList.length > 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询落地页列表 */
    async getList() {
      this.loading = true
      this.innerGoods = this.goods
      this.queryParams.goodsId = isJd(this.goods.platform) ? this.goods.goodsSign : this.goods.goodsId
      this.queryParams.mediaType = this.platformType
      this.queryParams.platform = this.goods.platform

      const res = await listLanding(this.queryParams)
      this.landingList = res.rows
      this.total = res.total
      this.loading = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.landing = null
      this.landingVisible = true
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.id || this.ids
      getLanding(id).then(response => {
        this.landingVisible = true
        this.landing = response.data
      })
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = [row.id]
      if (row.pageAid) ids.push(row.pageAid)
      this.$modal.confirm('是否确认删除落地页？').then(function() {
        return delLanding(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 打开链接 */
    openPath(path) {
      window.open(path + '?t=' + new Date().getTime(), '_blank')
    },
    handleLandingSuccess() {
      this.landingVisible = false
      this.getList()
      this.$emit('refresh')
    },
    handleLandingClose() {
      this.pageBid = null
      this.pageBAdvertiserId = null
      this.aGoods = null
    },
    handleEnableAbPage(row) {
      this.loading = true
      enableAbPage({
        id: row.id,
        pageAid: row.pageAid,
        enableAbPage: row.enableAbPage
      }).then(() => {
        this.$modal.msgSuccess('切换成功，请稍后刷新页面查看')
        this.getList()
      }).finally(() => {
        this.loading = false
      })
    },
    editABPage(row) {
      this.pageBid = row.id
      this.pageBAdvertiserId = row.advertiserId
      getLanding(row.pageAid).then(response => {
        this.landingVisible = true
        this.landing = response.data
      })
    },
    createABPage(row) {
      this.pageBid = row.id
      this.pageBAdvertiserId = row.advertiserId
      this.goodsVisible = true
    },
    handleGoodsSelect(goods) {
      this.goodsVisible = false
      this.aGoods = goods
      this.landing = null
      this.landingVisible = true
      // getLanding(goods.id).then(response => {
      //   this.landingVisible = true
      //   this.landing = response.data
      // })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.streaming-container{
  display: flex;
  flex-direction: column;
  height: 100%;
}
.ab {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

::v-deep .el-drawer__header {
  margin-bottom: 20px;
}
</style>
