<template>
  <div class="edit-landing-wrap">
    <div class="edit-form">
      <el-form ref="form" :model="dataForm" :rules="rules" label-width="170px">
        <el-form-item label="领取有效期：" prop="times">
          <!-- 券批次领取时间。note：此时间为时间戳，指格林威治时间 1970 年01 月 01 日 00 时 00 分 00 秒(北京时间 1970 年 01 月 01 日 08 时 00 分 00 秒)起至现在的总秒数 -->
          <el-date-picker
            v-model="dataForm.times"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :picker-options="pickerOption"
            format="yyyy-MM-dd HH:mm"
            :clearable="true"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>

        <el-form-item label="有效期类型：" prop="relativeTimeType">
          <template #label>
            <span>有效期类型</span>
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content">
                类型为天级时，最大值为30，即领取后30天内有效；<br>
                类型为小时级时，最大值为24，即领取后24小时内有效；<br>
                类型为分钟级时，则最大值为60，即领取后60分钟内有效。
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
            <span>：</span>
          </template>
          <el-select v-model="dataForm.relativeTimeType" style="width: 200px;" placeholder="请选择" @change="handleRelativeTimeTypeChange">
            <el-option

              v-for="item in relativeTimeTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动持续时间：" prop="duration">
          <div>
            <el-input-number v-model="dataForm.duration" style="width: 200px;" placeholder="请输入活动持续时间" :step="1" step-strictly controls-position="right" :min="durationOptions[dataForm.relativeTimeType].min" :max="durationOptions[dataForm.relativeTimeType].max" />
            <span class="ml10">{{ dataForm.relativeTimeType===1?'天':dataForm.relativeTimeType===2?'小时':dataForm.relativeTimeType===3?'分钟':'' }}</span>
          </div>
          <div class="tips">
            <span v-if="dataForm.relativeTimeType===1">有效期类型为天级时，最大值为30，即领取后30天内有效</span>
            <span v-else-if="dataForm.relativeTimeType===2"> 有效期类型为小时级时，最大值为24，即领取后24小时内有效</span>
            <span v-else>有效期类型为分钟级时，则最大值为60，即领取后60分钟内有效</span>
          </div>
        </el-form-item>
        <el-form-item prop="userLimit">
          <template #label>
            <span>单用户可领券数量</span>
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content">
                可设置范围为1~10张
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
            <span>：</span>
          </template>
          <el-input-number v-model="dataForm.userLimit" :step="1" step-strictly controls-position="right" :min="1" :max="10" placeholder="请输入单用户可领券数量" />
        </el-form-item>
        <el-form-item label="是否开启续券：" prop="isOpenRenew">
          <el-switch v-model="dataForm.isOpenRenew" />
        </el-form-item>
        <el-form-item v-if="dataForm.isOpenRenew" label="触发续券剩余券数量：" prop="remainCountForRenew">
          <el-input-number
            v-model="dataForm.remainCountForRenew"
            :step="1"
            step-strictly
            controls-position="right"
            :min="1"
            :max="99999"
            placeholder="请输入触发续券剩余券数量"
          />
        </el-form-item>
        <el-form-item v-if="dataForm.isOpenRenew" label="每次续券上限：" prop="renewNumberPerTime">
          <el-input-number
            v-model="dataForm.renewNumberPerTime"
            :step="1"
            step-strictly
            controls-position="right"
            :min="1"
            :max="99999"
            placeholder="请输入每次续券上限"
            @change="handleRenewNumberPerTimeChange"
          />
        </el-form-item>
        <el-form-item v-if="dataForm.isOpenRenew" label="单日续券上限：" prop="renewMaxNumberPerDay">
          <el-input-number
            v-model="dataForm.renewMaxNumberPerDay"
            :step="1"
            step-strictly
            controls-position="right"
            :min="1"
            :max="99999"
            placeholder="请输入单日续券上限"
            @change="handleRenewMaxNumberPerDayChange"
          />
        </el-form-item>
        <div class="edit-form-box">
          <div v-for="(item, index) in dataForm.list" :key="index" class="list-box">
            <el-card class="box-card2">
              <el-form-item
                :prop="'list.' + index + '.couponAmount'"
                :rules="rules.couponAmount"
              >
                <template #label>
                  <span>礼金券面额</span>
                  <el-tooltip class="item" effect="dark" content="券面额 = 商品券后价 - 期望礼金券后价" placement="top">
                    <i class="el-icon-question" />
                  </el-tooltip>
                  <span>：</span>
                </template>
                <div>
                  <el-input v-model="item.couponAmount" style="width: 370px;" :maxlength="10" placeholder="请输入礼金券面额" @input="onInputNum('couponAmount', $event,index)" />
                  <span class="ml10">元</span>
                </div>
                <div class="tips">
                  <span>可设置范围为1元~</span>
                  <span>{{ maxMoney }}</span>
                  <span>元</span>
                </div>
              </el-form-item>
              <el-form-item label="礼金券数量：" :prop="'list.' + index + '.quantity'">
                <!-- 礼金券数量，创建固定面额礼金券必填（创建灵活面额礼金券时，礼金券数量不固定，礼金总预算用完为止） -->
                <el-input-number v-model="item.quantity" :step="1" step-strictly controls-position="right" :min="20" :max="99999" placeholder="请输入礼金券数量" />
                <div class="tips">
                  礼金券数量最少为20张
                </div>
              </el-form-item>
              <el-form-item label="备注：" prop="remark">
                <el-input v-model="item.remark" placeholder="请输入备注" />
              </el-form-item>
            </el-card>
            <div class="ml20">
              <el-button :disabled="dataForm.list.length>=3" icon="el-icon-plus" class="addBtn" @click="addHandle(index)">新增</el-button>
              <el-button v-if="index>0" type="danger" icon="el-icon-delete" class="addBtn" @click="deleteHandle(index)">删除</el-button>
            </div>
          </div>

        </div>
        <el-form-item>
          <el-button v-loading="btnLoading" @click="close">取消</el-button>
          <el-button v-loading="btnLoading" type="primary" @click="createLanding">确定</el-button>
        </el-form-item>
      </el-form>
    </div>

  </div>
</template>

<script>
import { batchAddPddCashgift } from '@/api/promotion/cashGift'
import dayjs from 'dayjs'
export default {
  name: 'CashGiftBatchEdit',
  components: {

  },
  props: {
    goods: {
      type: Object,
      default: () => ({})
    },

    platformType: {
      type: Number,
      default: null
    },
    pddCashgiftAmountRate: {
      type: [Number, String],
      default: 15
    }
  },
  emits: ['close', 'success'],
  data() {
    var validatePass2 = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入活动持续时间'))
      } else {
        callback()
      }
    }
    var validatePass3 = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入礼金券数量'))
      } else if (value < 20) {
        callback(new Error('礼金券数量不能少于20张'))
      } else {
        callback()
      }
    }
    var validatePass4 = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入礼金券面额'))
      } else if (value) {
        if (value < 1) {
          callback(new Error('礼金券面额不能低于1元'))
        } else if (value * 100 > this.maxMoney * 100) {
          callback(new Error(`礼金券面额不能大于${this.maxMoney}元`))
        } else {
          callback()
        }
      }
    }

    var validatePass5 = (rule, value, callback) => {
      if (!value || !value.length) {
        callback(new Error('请选择领取有效期'))
      } else if (value) {
        const overTime = dayjs(value[1]).diff(dayjs(value[0]), 'days') + 1 > 30
        if (overTime) {
          callback('用户可领券的时间范围不能超过30天, 请重新选择')
        } else {
          callback()
        }
      }
    }

    return {
      pickerOption: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 8.64e7 表示一天的毫秒数，禁止选择今天之前的日期
        }
      },
      dataForm: {
        id: '',
        times: [dayjs(new Date()).valueOf(), dayjs(new Date()).add(15, 'day').valueOf()],
        // quantity: '',
        relativeTimeType: 2,
        duration: 2,
        userLimit: '',
        isOpenRenew: true,
        remainCountForRenew: 20,
        renewNumberPerTime: 50,
        renewMaxNumberPerDay: 2000,
        list: [
          {
            couponAmount: '',
            couponThresholdAmount: '',
            remark: '一档',
            quantity: 50
          },
          {
            couponAmount: '',
            couponThresholdAmount: '',
            remark: '二档',
            quantity: 50
          },
          {
            couponAmount: '',
            couponThresholdAmount: '',
            remark: '三档',
            quantity: 50
          }
        ]
      },
      rules: {
        couponAmount: [
          { required: true, validator: validatePass4, trigger: 'blur' }
        ],
        times: [
          { required: true, validator: validatePass5, trigger: 'blur' }
        ],
        quantity: [
          { required: true, validator: validatePass3, trigger: 'blur' }
        ],
        relativeTimeType: [
          { required: true, message: '请选择有效期类型', trigger: 'blur' }
        ],
        duration: [
          { required: true, validator: validatePass2, trigger: 'blur' }
        ],
        userLimit: [
          { required: true, message: '请输入单用户可领券数量', trigger: 'blur' }
        ],
        isOpenRenew: [
          { required: true, message: '请选择是否开启续券', trigger: 'blur' }
        ],
        remainCountForRenew: [
          { required: true, message: '请输入触发续券的剩余券数量', trigger: 'blur' }
        ],
        renewMaxNumberPerDay: [
          { required: true, message: '请输入单日续券上限', trigger: 'blur' }
        ],
        renewNumberPerTime: [
          { required: true, message: '请输入每次续券上限', trigger: 'blur' }
        ]
      },
      durationOptions: {
        1: {
          max: 30,
          min: 1
        },
        2: {
          max: 24,
          min: 1
        },
        3: {
          max: 60,
          min: 1
        }
      },
      btnLoading: false,
      relativeTimeTypeOptions: [
        {
          value: 1,
          label: '天'
        },
        {
          value: 2,
          label: '小时'
        },
        {
          value: 3,
          label: '分钟'
        }
      ],
      minGroupPrice: null,
      maxMoney: null

    }
  },
  created() {
    this.minGroupPrice = +this.goods.minGroupPrice
    this.maxMoney = Math.ceil((this.minGroupPrice / 100 * this.pddCashgiftAmountRate / 100)).toFixed(2)

    this.dataForm.list[0].couponAmount = Math.ceil((this.minGroupPrice / 100 * 0.1))
    this.dataForm.list[0].couponThresholdAmount = Math.ceil((this.minGroupPrice / 100 * 0.1)) * 2
    this.dataForm.list[1].couponAmount = Math.ceil((this.minGroupPrice / 100 * 0.15))
    this.dataForm.list[1].couponThresholdAmount = Math.ceil((this.minGroupPrice / 100 * 0.15)) * 2
    this.dataForm.list[2].couponAmount = Math.ceil((this.minGroupPrice / 100 * 0.2))
    this.dataForm.list[2].couponThresholdAmount = Math.ceil((this.minGroupPrice / 100 * 0.2)) * 2
  },
  mounted() {

  },
  methods: {
    createLanding() {
      this.$refs['form'].validate(valid => {
        if (!valid) return
        const formData = {
          goodsInfoId: this.goods.id,
          acquireStartTime: Math.ceil(this.dataForm.times[0] / 1000),
          acquireEndTime: Math.ceil(this.dataForm.times[1] / 1000),
          quantity: +this.dataForm.quantity,
          relativeTimeType: +this.dataForm.relativeTimeType,
          duration: +this.dataForm.duration,
          userLimit: +this.dataForm.userLimit,
          isOpenRenew: this.dataForm.isOpenRenew,
          remainCountForRenew: this.dataForm.isOpenRenew ? +this.dataForm.remainCountForRenew : null,
          renewMaxNumberPerDay: this.dataForm.isOpenRenew ? +this.dataForm.renewMaxNumberPerDay : null,
          renewNumberPerTime: this.dataForm.isOpenRenew ? +this.dataForm.renewNumberPerTime : null
        }
        const list = JSON.parse(JSON.stringify(this.dataForm.list))
        list.forEach(item => {
          item.couponAmount = item.couponAmount * 100
          item.couponThresholdAmount = item.couponThresholdAmount * 100
        })
        formData['pddCashgiftInfoVoList'] = list
        this.btnLoading = true
        batchAddPddCashgift(formData).then(response => {
          this.$modal.msgSuccess('新增成功')
          this.$emit('success', response)
        }).finally(() => {
          this.btnLoading = false
        })
      })
    },

    close() {
      this.$emit('close')
    },
    onInputNum(key, event, index) {
      // 限制输入为数值，且支持小数点后2位
      if (event.match(/^\d+/)) {
        this.dataForm.list[index][key] = event.match(/^\d*(\.?\d{0,2})/g)[0]
        this.dataForm.list[index].couponThresholdAmount = this.dataForm.list[index][key] * 2
      } else {
        this.dataForm.list[index][key] = ''
        return
      }
    },
    handleRelativeTimeTypeChange() {
      this.dataForm.duration = null
    },
    addHandle(index) {
      const numMap = {
        1: '一档',
        2: '二档',
        3: '三档'
      }
      const list = {
        couponAmount: '',
        couponThresholdAmount: '',
        remark: numMap[index + 2],
        quantity: 50
      }
      this.dataForm.list.splice(index + 1, 0, list)
    },
    deleteHandle(index) {
      this.dataForm.list.splice(index, 1)
    },
    handleRenewNumberPerTimeChange(value) {
      if (this.dataForm.renewMaxNumberPerDay && value) {
        const oldValue = this.dataForm.renewMaxNumberPerDay
        const newValue = Math.round(this.dataForm.renewMaxNumberPerDay / value) * value
        this.$nextTick(() => {
          this.dataForm.renewMaxNumberPerDay = newValue
        })

        if (oldValue !== newValue) {
          this.$message(`单日续券上限已自动调整为${newValue}，以符合每次续券上限的整数倍`)
        }
      }
    },
    handleRenewMaxNumberPerDayChange(value) {
      if (this.dataForm.renewNumberPerTime && value) {
        const oldValue = value
        const newValue = Math.round(value / this.dataForm.renewNumberPerTime) * this.dataForm.renewNumberPerTime
        this.$nextTick(() => {
          this.dataForm.renewMaxNumberPerDay = newValue
        })

        if (oldValue !== newValue) {
          this.$message(`单日续券上限已自动调整为${newValue}，以符合每次续券上限的整数倍`)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-landing-wrap {
  padding:0 20px 20px ;
  display: flex;
}

.tips {
  color: #999;
  font-size: 14px;
}

::v-deep .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
.edit-form-box{

  .list-box{
    display: flex;
    margin-bottom: 20px;
  }
}
</style>
