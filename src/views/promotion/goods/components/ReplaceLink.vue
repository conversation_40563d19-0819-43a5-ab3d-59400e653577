<!--注意修改replaceTable组件-->
<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="withMediaAccount? 16:24">
        <div>
          <div v-if="!goods.replaceGoodsId" class="el-descriptions__title">已替换商品信息: 无</div>
          <template v-else-if="replaceGoods">
            <div class="el-descriptions__title">已替换商品信息</div>
            <div style="display: flex;margin-top: 10px">
              <el-descriptions style="flex:1" size="small" border>
                <el-descriptions-item label="商品ID">{{ replaceGoods.goodsId }}</el-descriptions-item>
                <el-descriptions-item label="商品名称">{{ replaceGoods.goodsName }}</el-descriptions-item>
              </el-descriptions>
              <el-button type="primary" @click="restoreReplace">还原</el-button>
            </div>
          </template>
          <div v-else class="el-descriptions__title">待替换商品信息: 无</div>
        </div>
        <el-divider />
        <el-form ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="80px">
          <el-form-item label="商品ID" prop="goodsId">
            <el-input
              v-model="queryParams.goodsId"
              placeholder="请输入商品ID"
              clearable
              style="width: 150px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="商品名称" prop="goodsName">
            <el-input
              v-model="queryParams.goodsName"
              placeholder="请输入商品名称"
              clearable
              style="width: 150px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
          <el-form-item style="float: right">
            <el-button icon="el-icon-date" size="mini" @click="showOperateLog">替换记录</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="authTable"
          height="345px"
          :data="goodsList"
          row-key="goodsId"
          border
          @select="handleGoodsSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="商品名称" prop="goodsName" show-overflow-tooltip />
          <el-table-column label="商品ID" align="center" prop="goodsId" width="180" />
          <!--        <el-table-column label="操作" align="center" width="180">-->
          <!--          <template slot-scope="scope">-->
          <!--            <el-button v-loading="replaceLoading" type="primary" size="mini" @click="handleReplace(scope.row)">选择</el-button>-->
          <!--          </template>-->
          <!--        </el-table-column>-->
        </el-table>
        <el-checkbox v-model="replaceExternalUrl" class="replace-url">只替换巨量一跳落地页
          <el-tooltip content="勾选只会替换巨量一跳落地页链接，不勾选则会替换直达和巨量一跳落地页链接 " effect="dark" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </el-checkbox>
      </el-col>
      <el-col v-if="withMediaAccount" :span="8">
        <MediaAccountSelector :goods="goods" replace-link show-id @change="handleAccountSelect" />
      </el-col>
    </el-row>
    <div class="footer">
      <el-button type="primary" @click="handleReplace">确定</el-button>
      <el-button @click="close">取消</el-button>
    </div>

    <el-dialog
      title="替换记录"
      :visible.sync="operateVisible"
      top="5vh"
      width="80%"
      append-to-body
    >
      <el-form ref="operateForm" :model="operateParams" size="small" :inline="true" label-width="80px">
        <el-form-item label="业务ID" prop="businessId">
          <el-input
            v-model.trim="operateParams.businessId"
            placeholder="请输入业务ID"
            clearable
            @keyup.enter.native="handleOperateQuery"
          />
        </el-form-item>
        <el-form-item label="业务名称" prop="businessName">
          <el-input
            v-model.trim="operateParams.businessName"
            placeholder="请输入业务名称"
            clearable
            @keyup.enter.native="handleOperateQuery"
          />
        </el-form-item>
        <el-form-item label="广告主ID" prop="advertiserId">
          <el-input
            v-model.trim="operateParams.advertiserId"
            placeholder="请输入广告主ID"
            clearable
            @keyup.enter.native="handleOperateQuery"
          />
        </el-form-item>
        <el-form-item label="操作状态" prop="operateState">
          <el-select v-model="operateParams.operateState" placeholder="请选择操作状态" clearable>
            <el-option
              v-for="dict in operateStateOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleOperateQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetOperateQuery">重置</el-button>
          <el-button v-if="withMediaAccount" size="mini" class="fl-r" icon="el-icon-video-pause" :disabled="multiple" @click="pausePlan()">批量暂停</el-button>
        </el-form-item>
      </el-form>
      <div class="operate-table-wrap">
        <el-table
          height="100%"
          :data="recordList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="业务ID" align="center" prop="businessId" show-overflow-tooltip>
            <template #header>
              <span>业务ID</span>
              <el-tooltip effect="dark" content="巨量: 广告ID；腾讯: 创意ID" placement="top">
                <i class="el-icon-question color-primary" />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="业务名称" align="center" prop="businessName" show-overflow-tooltip />
          <el-table-column label="广告主ID" align="center" prop="advertiserId" show-overflow-tooltip />
          <el-table-column label="操作类型" align="center" prop="operateType" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.operateType === 1">暂停计划</span>
              <span v-else-if="scope.row.operateType === 2">替换链接</span>
            </template>
          </el-table-column>
          <el-table-column label="操作状态" align="center" prop="operateState" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag v-if="scope.row.operateState === 1" type="success">成功</el-tag>
              <el-tag v-else-if="scope.row.operateState === 0" type="danger">失败</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="来源" align="center" prop="source" show-overflow-tooltip />
          <el-table-column label="消息" align="center" prop="operateErrorMessage" show-overflow-tooltip />
          <el-table-column label="操作时间" align="center" width="165" prop="operateTime">
            <template slot-scope="scope">
              <span>{{ scope.row.operateTime.replace('T', ' ') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="165" prop="action">
            <template slot-scope="{row}">
              <el-button v-if="withMediaAccount" icon="el-icon-video-pause" :disabled="row.operateState === 1" size="small" @click="pausePlan(row)">暂停计划</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="operateParams.pageNum"
        :limit.sync="operateParams.pageSize"
        @pagination="fetchRecordList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="operateVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getGoods, getRecordList, listGoods, replaceGoodsLink } from '@/api/promotion/goods'
import MediaAccountSelector from '@/views/promotion/goods/components/MediaAccountSelector.vue'
import { planPause } from '@/api/statistics/plan'

export default {
  name: 'ReplaceLink',
  components: { MediaAccountSelector },
  props: {
    goods: {
      type: Object,
      default: () => ({})
    },
    platformType: {
      type: Number,
      default: 1
    }
  },
  emits: ['replace', 'submit', 'close'],
  data() {
    return {
      multiple: true,
      selections: [],
      selectGoods: null,
      replaceExternalUrl: false,
      queryParams: {
        goodsId: '',
        goodsName: '',
        mediaPlatformType: ''
      },
      replaceGoods: {},
      replaceLoading: false,
      goodsList: [],

      recordList: [],
      operateVisible: false,

      total: 0,
      operateParams: {
        pageNum: 1,
        pageSize: 10
      },
      operateStateOptions: [
        {
          label: '成功',
          value: 1
        },
        {
          label: '失败',
          value: 0
        }
      ],
      advertiserIds: []
    }
  },
  computed: {
    withMediaAccount() {
      return this.platformType === 1 || this.platformType === 4
    }
  },
  mounted() {
    this.getReplaceGoods()
    this.fetchRecordList()
  },
  methods: {
    getList() {
      this.loading = true
      this.queryParams.mediaPlatformType = this.platformType
      this.queryParams.replaceGoodsId = -100
      listGoods(this.queryParams).then(response => {
        this.goodsList = response.rows
        this.loading = false
      })
    },
    getReplaceGoods() {
      if (this.goods.replaceGoodsId) {
        getGoods(this.goods.replaceGoodsId).then(response => {
          this.replaceGoods = response.data
        })
      }
    },
    showOperateLog() {
      this.operateVisible = true
    },

    fetchRecordList() {
      getRecordList({
        goodsId: this.goods.goodsId,
        operateType: 2,
        mediaPlatformType: this.platformType,
        ...this.operateParams
      }).then(response => {
        this.recordList = response.rows
        this.total = response.total
      })
    },

    resetOperateQuery() {
      this.resetForm('operateForm')
      this.fetchRecordList()
    },
    handleQuery() {
      if (this.queryParams.goodsId === this.goods.goodsId) {
        this.$message.warning('目标商品不能与原商品相同')
        return
      }

      if (this.queryParams.goodsId === '' && this.queryParams.goodsName === '') {
        this.$message.warning('请输入商品ID或商品名称')
        return
      }
      this.getList()
    },
    handleOperateQuery() {
      this.fetchRecordList()
    },
    handleReplace() {
      const row = this.selectGoods
      if (!row) {
        this.$message.warning('请选择目标商品')
        return
      }
      // if (this.goods.landingPageUrl && !row.landingPageUrl) {
      //   this.$message.warning('目标商品未设置落地页')
      //   return
      // }
      if (this.goods.goodsId === row.goodsId) {
        this.$message.warning('目标商品不能与原商品相同')
        return
      }
      this.replaceLoading = true
      const postData = {
        sourceId: row.id,
        targetId: this.goods.id,
        mediaPlatformType: this.platformType,
        replaceExternalUrl: this.replaceExternalUrl,
        reduction: false
      }
      if (this.withMediaAccount) {
        postData.advertiserIds = this.advertiserIds
      }
      replaceGoodsLink(postData).then(response => {
        if (response.code !== 200) {
          this.$message.error(response.message)
          return
        }
        this.$message.success('替换成功，请稍后查看')
        this.$emit('replace')
      }).finally(() => {
        this.replaceLoading = false
      })
    },

    handleGoodsSelectionChange(selection) {
      this.selectGoods = selection[selection.length - 1]
      if (selection.length > 1) {
        this.$refs.authTable.toggleRowSelection(selection.shift(), false)
      }
    },

    // 还原链接
    restoreReplace() {
      this.$confirm('将还原替换的商品链接到原商品（请注意选择媒体账户），是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const postData = {
          sourceId: this.goods.id,
          targetId: this.replaceGoods.id,
          mediaPlatformType: this.platformType,
          reduction: true
        }
        if (this.withMediaAccount) {
          postData.advertiserIds = this.advertiserIds
        }
        replaceGoodsLink(postData).then(response => {
          if (response.code !== 200) {
            this.$message.error(response.message)
            return
          }
          this.$message.success('还原成功，请稍后查看')
          this.$emit('replace')
        }).finally(() => {
          this.replaceLoading = false
        })
      }).catch(() => {})
    },
    handleAccountSelect(ids) {
      this.advertiserIds = ids
    },
    close() {
      this.$emit('close')
    },

    handleSelectionChange(selection) {
      this.selections = selection
      this.multiple = !selection.length
    },
    pausePlan(row) {
      let planIds = null
      if (row) {
        planIds = [row.businessId]
      } else {
        if (this.selections.some(item => item.operateState === 1)) {
          this.$message.error('只能选择操作状态为失败的计划')
          return
        }
        planIds = this.selections.map(item => item.businessId)
        row = this.selections[0]
      }

      planPause({
        advertiserId: row.advertiserId,
        traceId: row.traceId,
        planIds
      }).then(() => {
        this.$message.success('操作成功')
        this.fetchRecordList()
      })
    }
  }
}
</script>

<style scoped>
.media-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
.footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0 0;
}
.operate-table-wrap {
  height: calc(100vh - 450px);
}
.replace-url {
  padding: 10px 10px 0 10px;
  .tip {
    color: #c4c4c4;
  }
}
</style>
