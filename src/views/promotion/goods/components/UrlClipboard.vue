<template>
  <div style="display: flex" :class="[disabled? 'disabled': '']" :style="{color: fontColor}">
    <a v-if="!notRedirect && url" class="url-wrap hover-text" target="_blank" :href="url">{{ url || '-' }}</a>
    <a v-else class="url-wrap">{{ url || '-' }}</a>
    <el-button v-if="url" v-clipboard:copy="url" v-clipboard:success="clipboardSuccess" :style="{color: fontColor}" type="text" :disabled="disabled" :class="[disabled? 'disabled': '']">
      <i class="el-icon-document-copy" />复制
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'UrlClipboard',
  props: {
    url: {
      type: String,
      default: ''
    },
    notRedirect: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    fontColor: {
      type: String,
      default: ''
    }
  },
  methods: {
    clipboardSuccess() {
      this.$message.success('复制成功')
    }
  }
}
</script>

<style scoped>
.url-wrap {
  flex:1;
  margin-right: 10px;
  overflow: hidden;
  /*将对象作为弹性伸缩盒子模型显示*/
  display: -webkit-box;
  /*设置子元素排列方式*/
  -webkit-box-orient: vertical;
  /*设置显示的行数，多出的部分会显示为...*/
  -webkit-line-clamp: 2;
  user-select: none;
}
.hover-text {
  &:hover {
    text-decoration: underline;
    color: #409eff
  }
}
.disabled {
  color: #c9c9c9;
}
</style>
