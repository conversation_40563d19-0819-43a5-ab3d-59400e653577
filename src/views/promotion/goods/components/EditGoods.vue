<script setup>
import { computed, onMounted, reactive, ref } from 'vue'
import useDicts from '@/hooks/useDicts'
import { editGoods } from '@/api/promotion/goods'
import { Message } from 'element-ui'
import { isPdd, isTbPro, isJd, isJdPro, isUDSmart } from '@/utils/judge/platform'
import TextHint from '@/components/TextHint/index.vue'
import GoodsSelector from '@/components/GoodsSelector/index.vue'

const props = defineProps({
  goods: {
    type: Object,
    default: null
  }
})
const { dicts, dictMap } = useDicts(['platform_type', 'media_type', 'goods_link_type_pdd', 'goods_link_type_jd', 'goods_link_type_pangda', 'goods_link_type_tb', 'goods_link_type_mt', 'goods_link_type_djk', 'goods_link_type_uds', 'goods_link_type_tbpro', 'goods_link_type_jdpro'], () => {
  loading.value = false
})
const rules = {
  goodsId: [
    { required: true, message: '商品ID不能为空', trigger: 'change' }
  ],
  goodsName: [
    { required: true, message: '商品名称不能为空', trigger: 'change' }
  ],
  mallId: [
    { required: true, message: '店铺ID不能为空', trigger: 'change' }
  ],
  mallName: [
    { required: true, message: '店铺名称不能为空', trigger: 'change' }
  ],
  minNormalPrice: [
    { required: true, message: '价格不能为空', trigger: 'change' }
  ]
}

const emit = defineEmits(['success', 'cancel'])

const goodsFormRef = ref(null)
const goodsForm = reactive({
  goodsName: '',
  mallName: '',
  goodsId: '',
  mallId: '',
  numIid: '',
  minNormalPrice: '',
  mediaPlatformType: '',
  linkType: '',
  platform: '',
  originalTjmUrl: '',
  shopAttribute: false,
  autoRedirect: false,
  jdTraceEventType: 10,
  jdTracePoint: 1,
  jdTracePeriod: 1,
  jdTraceRepeat: 0,
  tbProSyn: 3,
  enableWB: 0,
  WBList: '',
  itemSugarType: 0
})
const showJdTraceRepeat = computed(() => {
  return [2, 3].includes(goodsForm.jdTracePoint)
})
const handleJdTracePointChange = () => {
  if (goodsForm.jdTracePoint === 1) {
    goodsForm.jdTraceRepeat = 0
  }
}

const linkType = computed(() => {
  return {
    '1': dicts.value.goods_link_type_pdd,
    '3': dicts.value.goods_link_type_jd,
    '4': dicts.value.goods_link_type_pangda,
    '6': dicts.value.goods_link_type_mt,
    '8': dicts.value.goods_link_type_djk,
    '9': dicts.value.goods_link_type_uds,
    '10': dicts.value.goods_link_type_tbpro,
    '11': dicts.value.goods_link_type_jdpro
  }[goodsForm.platform] || dicts.value.goods_link_type_tb
})

const originLinkType = ref('')
const isLinkTypeChange = computed(() => {
  return originLinkType.value !== goodsForm.linkType
})

onMounted(() => {
  originLinkType.value = props.goods.linkType
  Object.assign(goodsForm, props.goods)
  if (goodsForm.platform === '6') {
    goodsForm.minNormalPrice = goodsForm.minNormalPrice / 100
    goodsForm.minGroupPrice = goodsForm.minGroupPrice / 100
  }
  if (goodsForm.goodsWhiteList) {
    goodsForm.enableWB = 1
    goodsForm.WBList = goodsForm.goodsWhiteList
  } else if (goodsForm.goodsBlackList) {
    goodsForm.enableWB = 2
    goodsForm.WBList = goodsForm.goodsBlackList
  }
  goodsForm.itemSugarType = goodsForm.itemSugarType || 0
})

const goodsVisible = ref(false)
const wbExpandVisible = ref(false)
const handleGoodsSelect = (selections) => {
  goodsVisible.value = false
  const selectedIds = selections.map(item => item.goodsId).join(',')
  goodsForm.WBList = goodsForm.WBList ? `${goodsForm.WBList},${selectedIds}` : selectedIds
}

// 淘积木链接
const isTjm = computed(() => {
  return (goodsForm.platform === '2' || goodsForm.platform === '5' || isUDSmart(goodsForm.platform) || isTbPro(goodsForm.platform)) && (goodsForm.linkType === '2')
})
const loading = ref(true)
// 提交
const submitForm = () => {
  if (isTjm.value) {
    if (!goodsForm.originalTjmUrl && goodsForm.itemSugarType === 0) {
      Message.error('淘积木链接不能为空')
      return
    }
  }

  goodsFormRef.value.validate((valid) => {
    if (valid) {
      // 美团
      if (goodsForm.platform === '6') {
        goodsForm.minGroupPrice = goodsForm.minNormalPrice = goodsForm.minNormalPrice * 100
        goodsForm.goodsImageUrl = goodsForm.goodsThumbnailUrl
      }

      goodsForm.goodsWhiteList = ''
      goodsForm.goodsBlackList = ''
      if ((isPdd(goodsForm.platform) || isTbPro(goodsForm.platform) || isJd(goodsForm.platform) || isJdPro(goodsForm.platform)) && goodsForm.enableWB) {
        if (!goodsForm.WBList) {
          Message.warning(`${goodsForm.enableWB === 1 ? '白名单' : '黑名单'}商品id不能为空`)
          return
        }
        const list = goodsForm.WBList.replace(/，/g, ',')
        if (list.split(',').filter(item => item).length > 50) {
          Message.warning(`${goodsForm.enableWB === 1 ? '白名单' : '黑名单'}商品不能超过 50 个`)
          return
        }

        if (goodsForm.enableWB === 1) {
          goodsForm.goodsWhiteList = list
        } else {
          goodsForm.goodsBlackList = list
        }
      }

      if ((isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform)) && goodsForm.linkType === '2') {
        goodsForm.itemSugarType = goodsForm.itemSugarType || 0
      }

      loading.value = true
      editGoods(goodsForm).then(response => {
        if (response.code === 200) {
          Message.success(response.msg)
          emit('success')
        } else {
          Message.error(response.msg)
        }
      }).finally(() => {
        loading.value = false
      })
    }
  })
}
const cancel = () => {
  emit('cancel')
}

</script>

<template>
  <div v-loading="loading" class="dialog-body" style="padding: 0 20px">
    <div class="flex gap-10">
      <div class="flex1">
        <el-form ref="goodsFormRef" size="small" :model="goodsForm" :rules="rules" label-width="90px">
          <el-form-item label="商品名称" prop="goodsName">
            <el-input v-if="goodsForm.platform === '6'" v-model="goodsForm.goodsName" placeholder="商品名称" clearable />
            <span v-else>{{ goodsForm.goodsName }}</span>
          </el-form-item>
          <el-form-item label="商品ID" prop="goodsId">
            <el-input v-if="goodsForm.platform === '6'" v-model.trim="goodsForm.goodsId" placeholder="商品ID" clearable />
            <span v-else>{{ goodsForm.goodsId }}</span>
          </el-form-item>
          <el-form-item label="平台类型" prop="platform">
            <svg-icon v-if="dictMap.platform_type[goodsForm.platform]" :icon-class="dictMap.platform_type[goodsForm.platform]" />
            <span> {{ dictMap.platform_type[goodsForm.platform] }}</span>
          </el-form-item>
          <el-form-item label="媒体类型" prop="mediaPlatformType">
            <svg-icon v-if="dictMap.media_type[goodsForm.mediaPlatformType]" :icon-class="dictMap.media_type[goodsForm.mediaPlatformType]" />
            <span> {{ dictMap.media_type[goodsForm.mediaPlatformType] }}</span>

            <!--            <el-select v-model="goodsForm.mediaPlatformType">-->
            <!--              <el-option-->
            <!--                v-for="dict in dicts.media_type"-->
            <!--                :key="dict.value"-->
            <!--                :label="dict.label"-->
            <!--                :value="+dict.value"-->
            <!--              >-->
            <!--                <svg-icon :icon-class="dict.label" />-->
            <!--                <span> {{ dict.label }}</span>-->
            <!--              </el-option>-->
            <!--            </el-select>-->
          </el-form-item>
          <el-form-item label="链接类型" prop="linkType">
            <el-select v-model="goodsForm.linkType">
              <el-option
                v-for="dict in linkType"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <div v-show="isLinkTypeChange" style="border:1px solid darkgray;padding: 10px;margin-bottom: 10px">
            当您更改<strong>链接类型</strong>时，请按照以下步骤操作：
            <p>1. <strong>直达一跳投放广告：</strong>请重新获取链接，并使用新链接搭建广告或替换现有链接。</p>
            <p>2. <strong>二跳链接：</strong>请重新保存二跳落地页，以便更改生效。</p>
            <p v-show="isPdd(goodsForm.platform)">3. <strong>从非礼金类型更换为礼金：</strong>在切换链接类型后，新建礼金根据投放链接的类型，执行上述直达一跳或二跳链接的操作。</p>
          </div>
          <template v-if="((isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform)) && goodsForm.linkType === '2')">
            <el-form-item label="自动生成UD落地页" label-width="140px">
              <el-radio-group v-model="goodsForm.itemSugarType">
                <el-radio :label="0">不生成</el-radio>
                <el-radio :label="1">真实销量&评价</el-radio>
                <el-radio :label="2">同款销量&评价</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <el-form-item v-if="isTjm && goodsForm.itemSugarType === 0" label="淘积木链接" prop="originalTjmUrl">
            <el-input v-model.trim="goodsForm.originalTjmUrl" placeholder="淘积木链接" clearable />
          </el-form-item>
          <el-form-item
            v-if="isPdd(goodsForm.platform) || isTbPro(goodsForm.platform) || isJd(goodsForm.platform) || isJdPro(goodsForm.platform)"
            label="全店归因"
            prop="shopAttribute"
          >
            <el-switch v-model="goodsForm.shopAttribute" />
            <el-tooltip
              class="item"
              effect="dark"
              content="当该项关闭时,用户通过该商品进入店铺后购买其他商品不会回传"
              placement="top"
            >
              <i class="el-icon-info ml10" />
            </el-tooltip>
          </el-form-item>
          <el-form-item v-if="(isPdd(goodsForm.platform) || isTbPro(goodsForm.platform) || isJd(goodsForm.platform) || isJdPro(goodsForm.platform)) && goodsForm.shopAttribute" label="回传上报" prop="enableWB">
            <div style="display: flex; gap: 10px;height: 36px;align-items: center">
              <el-radio-group
                v-model="goodsForm.enableWB"
                size="mini"
              >
                <el-radio-button
                  :label="0"
                >关闭</el-radio-button>
                <el-radio-button
                  :label="1"
                >白名单
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="白名单内的商品将被回传，名单外的商品不回传"
                    placement="top"
                  >
                    <i class="el-icon-question" />
                  </el-tooltip>
                </el-radio-button>
                <el-radio-button
                  :label="2"
                >黑名单
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="黑名单内的商品不会被回传，名单外的商品正常回传"
                    placement="top"
                  >
                    <i class="el-icon-question" />
                  </el-tooltip>
                </el-radio-button>
              </el-radio-group>
              <template v-if="goodsForm.enableWB">
                <el-input
                  v-model="goodsForm.WBList"
                  :placeholder="!isJdPro(goodsForm.platform) ? `请输入商品ID，多个ID用逗号分隔`: goodsForm.enableWB === 2 ? `自营商品入参sku，pop商品入参spu` : '输入 spu'"
                  size="mini"
                  style="width: 240px"
                >
                  <el-button slot="append" icon="el-icon-folder-opened" @click="wbExpandVisible = true" />
                </el-input>
                <span style="font-size: 12px;color: #999">{{ goodsForm.WBList.split(/[,，]/).filter(item => item).length }}/50</span>
                <el-button v-if="!isJdPro(goodsForm.platform)" size="mini" @click="goodsVisible = true">选择商品</el-button>
              </template>
            </div>
          </el-form-item>
          <el-form-item
            v-if="goodsForm.platform === '4'"
            label="自动跳转"
            prop="autoRedirect"
            label-width="100px"
          >
            <el-switch v-model="goodsForm.autoRedirect" :active-value="1" :inactive-value="0" />
          </el-form-item>
          <template v-if="goodsForm.platform === '6'">
            <el-form-item label="图片" prop="goodsThumbnailUrl">
              <el-input v-model.trim="goodsForm.goodsThumbnailUrl" placeholder="图片地址" clearable />
            </el-form-item>
            <el-form-item label="价格" prop="goodsName">
              <el-input-number v-model="goodsForm.minNormalPrice" placeholder="请输入商品价格" :controls="false" class="mr5 " /> 元
            </el-form-item>
            <el-form-item label="店铺名称" prop="mallName">
              <el-input v-model="goodsForm.mallName" placeholder="店铺名称" clearable />
            </el-form-item>
            <el-form-item label="店铺ID" prop="mallId">
              <el-input v-model.trim="goodsForm.mallId" placeholder="店铺ID" clearable />
            </el-form-item>
            <el-form-item label="PageID" prop="PageID">
              <el-input v-model.trim="goodsForm.numIid" placeholder="PageID" clearable />
            </el-form-item>
          </template>
          <template v-if="isTbPro(goodsForm.platform)">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="转化方式" prop="tbProSyn">
                  <el-radio-group v-model="goodsForm.tbProSyn">
                    <el-radio :label="3">全部</el-radio>
                    <el-radio :label="1">同步归因
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="点击唤端后产生的订单(类似老流量通)"
                        placement="top"
                      >
                        <i class="el-icon-info" />
                      </el-tooltip>
                    </el-radio>
                    <el-radio :label="2">异步归因
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="曝光或点击未唤端的订单"
                        placement="top"
                      >
                        <i class="el-icon-info" />
                      </el-tooltip>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-if="isJdPro(goodsForm.platform)">
            <el-form-item label="行为" prop="jdTraceEventType">
              <el-radio-group v-model="goodsForm.jdTraceEventType">
                <el-radio :label="10">下单</el-radio>
                <el-radio :label="11">支付
                  <TextHint content="订单支付才会归因，未支付订单不归因不回传" icon="el-icon-question" />
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="归因触点" prop="jdTracePoint">
              <el-radio-group v-model="goodsForm.jdTracePoint" @change="handleJdTracePointChange">
                <el-radio :label="1">曝光
                  <TextHint content="用户被曝光广告，下单后即会归因" icon="el-icon-question" />
                </el-radio>
                <el-radio :label="2">有效播放+点击
                  <TextHint content="用户进一步浏览广告大于3s,或点击广告，下单后才归因" icon="el-icon-question" />
                </el-radio>
                <el-radio :label="3">联盟点击
                  <TextHint content="用广更点击广告后，呼起了京东app/小程序/京东H5页面，下单后才会归因" icon="el-icon-question" />
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="去重归因" prop="jdTraceRepeat">
              <el-radio-group v-model="goodsForm.jdTraceRepeat">
                <el-radio :label="0">不去重
                  <TextHint content="即一个订单可同时归因CID投放，和京准通站内/站外广告，比如搜索广告" icon="el-icon-question" />
                </el-radio>
                <el-radio :label="1" :disabled="!showJdTraceRepeat">去重
                  <TextHint content="即一个订单如果归因给京准通站内/站外广告，则不再归因给CID投放，不予回传。" icon="el-icon-question" />
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="归因周期" prop="jdTracePeriod">
              <el-radio-group v-model="goodsForm.jdTracePeriod">
                <el-radio :label="1">1天</el-radio>
                <el-radio :label="7">7天</el-radio>
                <el-radio :label="15">15天</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>

        </el-form>
      </div>
    </div>
    <div slot="footer" class="dialog-footer-right">
      <el-button class="submit-btn" type="primary" @click="submitForm">保 存</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
    <GoodsSelector
      :visible.sync="goodsVisible"
      :query-list="['goodsName', 'goodsId','mediaPlatformType', 'platform']"
      :table-list="['goodsName', 'mediaPlatformType', 'remark', 'platform']"
      :auto-search="false"
      @select="handleGoodsSelect"
    />
    <el-dialog title="回报上传" :visible.sync="wbExpandVisible" append-to-body width="700px">
      <el-form :model="goodsForm" label-width="110px" style="padding: 10px 0 20px;">
        <el-form-item label="黑白名单设置">
          <el-radio-group
            v-model="goodsForm.enableWB"
            size="mini"
          >
            <el-radio-button
              :label="0"
            >关闭</el-radio-button>
            <el-radio-button
              :label="1"
            >白名单</el-radio-button>
            <el-radio-button
              :label="2"
            >黑名单</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="goodsForm.enableWB" label="商品ID">
          <el-input
            v-model="goodsForm.WBList"
            type="textarea"
            :rows="5"
            :placeholder="!isJdPro(goodsForm.platform) ? `请输入商品ID，多个ID用逗号分隔`: goodsForm.enableWB === 2 ? `自营商品入参sku，pop商品入参spu` : '输入 spu'"
            size="mini"
          />
          <el-button v-if="!isJdPro(goodsForm.platform)" style="margin-right: 10px;" size="mini" @click="goodsVisible = true">选择商品</el-button>
          <span style="font-size: 12px;color: #999;">{{ goodsForm.WBList.split(/[,，]/).filter(item => item).length }}/50</span>
        </el-form-item>
        <el-button class="fr" @click="wbExpandVisible=false">关闭</el-button>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.submit-btn {
  display: inline-block;
  margin-right: 10px;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>
