<template>
  <div>
    <el-form ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="80px">
      <el-form-item label="商品ID" prop="goodsId">
        <el-input
          v-model="queryParams.goodsId"
          placeholder="请输入商品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="goodsName">
        <el-input
          v-model="queryParams.goodsName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" height="500px" :data="goodsList">
      <el-table-column label="商品信息" align="left" prop="goodsName">
        <template #default="scope">
          <div class="table-base-info">
            <svg-icon v-if="scope.row.platform === '2' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="taobao" />
            <div v-else class="info-img">
              <image-preview :src="scope.row.goodsThumbnailUrl" :width="50" :height="50" />
            </div>
            <BaseInfoCell :id="scope.row.goodsId" class="info-wrap" style="flex:1" :name="scope.row.goodsName" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template #default="scope">
          <el-button
            type="primary"
            @click="selectGoods(scope.row)"
          >选择</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { listGoods } from '@/api/promotion/goods'

export default {
  name: 'GoodsList',
  components: { BaseInfoCell },
  props: {
    goods: {
      type: Object
    },
    platformType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      goodsList: [],
      total: 0,
      queryParams: {
        goodsId: '',
        goodsName: '',
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 查询商品信息列表 */
    getList() {
      this.loading = true
      this.queryParams.mediaPlatformType = this.platformType
      listGoods(this.queryParams).then(response => {
        this.goodsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    selectGoods(row) {
      if (this.goods && this.goods.id === row.id) {
        this.$message.warning('该商品与当前商品相同，请重新选择')
        return
      }
      this.$emit('select', row)
    }
  }
}
</script>

<style lang="scss" scoped>
.info-wrap {
  width: calc(100% - 60px);
  flex: 1;
}
</style>
