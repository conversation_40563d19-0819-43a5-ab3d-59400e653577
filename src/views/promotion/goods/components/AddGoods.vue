<script setup>
import {
  computed,
  onMounted,
  reactive,
  ref,
  nextTick,
  onBeforeUnmount
} from 'vue'
import useDicts from '@/hooks/useDicts'
import { formatPrice } from '@/utils'
import { checkPermi } from '@/utils/permission'
import { checkGoods, getDuoId, addGoods, udLandingPageCreate } from '@/api/promotion/goods'
import { Message } from 'element-ui'
import { getCompanyList } from '@/api/promotion/company'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import Upload from '@/components/Upload/index.vue'
import { isJd, isTbPro, isJdPro, isUDSmart } from '@/utils/judge/platform'
import CompanySelect from '@/components/CtreeSelect/CompanySelect.vue'
import TextHint from '@/components/TextHint/index.vue'
import GoodsSelector from '@/components/GoodsSelector/index.vue'

const { dicts, dictMap } = useDicts(
  [
    'platform_type',
    'media_type',
    'goods_link_type_pdd',
    'goods_link_type_jd',
    'goods_link_type_pangda',
    'goods_link_type_tb',
    'goods_link_type_mt',
    'goods_link_type_djk',
    'goods_link_type_uds',
    'goods_link_type_tbpro',
    'goods_link_type_jdpro'
  ],
  () => {
    addLoading.value = false
  }
)

const rules = {
  mediaPlatformTypeArr: [
    {
      type: 'array',
      required: true,
      message: '媒体类型不能为空',
      trigger: 'change'
    }
  ]
}

const emit = defineEmits(['cancel', 'success'])

const goodsTableRef = ref(null)
const goodsFormRef = ref(null)
const goodsForm = reactive({
  goodsSearchListDTO: [],
  mediaPlatformTypeArr: [],
  linkType: '1',
  duoId: '',
  companyId: '',
  platform: '1',
  mallId: '',
  shopAttribute: false,
  autoRedirect: true,
  jdTraceEventType: 10,
  jdTracePoint: 1,
  jdTracePeriod: 15,
  jdTraceRepeat: 0,
  tbProSyn: 3,
  enableWB: 0,
  WBList: '',
  itemSugarType: 0
})

const duoduoOptions = ref([])
const pddCompanyList = ref([])
const djkCompanyList = ref([])
const laterPay = ref(false)

const platformTypes = computed(() => {
  return dicts.value.platform_type.filter(item => (item.value !== '12' && item.value !== '2'))
})

const companyList = computed(() => {
  switch (goodsForm.platform) {
    case '1':
      return pddCompanyList.value
    case '8':
      return djkCompanyList.value
    default:
      return []
  }
})

const mediaType = computed(() => {
  const platformMediaTypes = {
    '6': ['1', '2', '4'],
    '7': ['1', '2'],
    '8': ['1', '2', '4', '5', '7', '8'],
    '9': ['1'],
    '10': ['1', '2', '3', '4', '5', '10'],
    '11': ['1', '4']
  }

  const allowedTypes = platformMediaTypes[goodsForm.platform]

  return allowedTypes
    ? dicts.value.media_type.filter(item => allowedTypes.includes(item.value))
    : dicts.value.media_type
})

const linkType = computed(() => {
  const platformLinkTypes = {
    '1': dicts.value.goods_link_type_pdd,
    '3': dicts.value.goods_link_type_jd,
    '4': dicts.value.goods_link_type_pangda,
    '6': dicts.value.goods_link_type_mt,
    '8': dicts.value.goods_link_type_djk,
    '9': dicts.value.goods_link_type_tbpro,
    '10': dicts.value.goods_link_type_tbpro,
    '11': dicts.value.goods_link_type_jdpro
  }

  return platformLinkTypes[goodsForm.platform] || dicts.value.goods_link_type_tb
})

const isTb = computed(() => ['2', '5', '7'].includes(goodsForm.platform))
const isPdd = computed(() => goodsForm.platform === '1')
const isDjk = computed(() => goodsForm.platform === '8')
const isTbpro = computed(() => goodsForm.platform === '10')

const showJdTraceRepeat = computed(() => {
  return [2, 3].includes(goodsForm.jdTracePoint)
})

const handleJdTracePointChange = () => {
  if (goodsForm.jdTracePoint === 1) {
    goodsForm.jdTraceRepeat = 0
  }
}

onMounted(() => {
  if (checkPermi(['promotion:goods:getDuoId'])) {
    getDuoId().then((res) => {
      duoduoOptions.value = res.data
    })
  }

  Promise.all([
    getCompanyList({ platform: 1 }),
    getCompanyList({ platform: 8 })
  ]).then(([pddData, djkData]) => {
    if (pddData.code === 200) {
      // 根据id去重
      const uniqueData = [...new Map(pddData.data.map(item => [item.id, item])).values()]
      pddCompanyList.value = uniqueData.map(item => ({
        id: item.id,
        label: item.shopName
      }))
    }
    if (djkData.code === 200) {
      djkCompanyList.value = djkData.data.map(item => ({
        id: item.id,
        label: item.shopName
      }))
    }
  })

  onScrollbarScroll()
  initGoodsItem()
})

onBeforeUnmount(() => {
  if (scrollbarRef.value) { scrollbarRef.value.wrap.removeEventListener('scroll', onScrollbarToBottom) }
})

const addLoading = ref(true)

const addGoodsItem = (num = 1) => {
  for (let i = 0; i < num; i++) {
    goodsForm.goodsSearchListDTO.push({
      goodsId: '',
      goodsUrl: '',
      numIid: '',
      originalTjmUrl: ''
    })
  }
}
const initGoodsItem = () => {
  if (goodsForm.goodsSearchListDTO.length > 10) return
  addGoodsItem(10)
}

const scrollbarRef = ref(null)
const onScrollbarScroll = () => {
  scrollbarRef.value.wrap.addEventListener('scroll', onScrollbarToBottom)
}

const onScrollbarToBottom = () => {
  const wrap = scrollbarRef.value.wrap
  const poor = wrap.scrollHeight - wrap.clientHeight - 10
  if (
    poor <= parseInt(wrap.scrollTop) ||
    poor <= Math.ceil(wrap.scrollTop) ||
    poor <= Math.floor(wrap.scrollTop)
  ) {
    addGoodsItem(2)
  }
}

const removeGoods = (index) => {
  goodsForm.goodsSearchListDTO.splice(index, 1)
  initGoodsItem()
}

// 处理拼多多商品链接，获取goodsId
let inputTimer = null
const handlePddGoodsUrl = (goods) => {
  clearError(goods)
  if (inputTimer) {
    clearTimeout(inputTimer)
  }

  // 获取goods_id
  goods.goodsId = '-'
  inputTimer = setTimeout(() => {
    goods.goodsId = getUrlGoodsId(goods.goodsUrl)
  }, 300)
}

// 处理淘宝链接，获取虚拟 ID itemId
// eslint-disable-next-line no-unused-vars
const handleTbGoodsUrl = (goods) => {
  if (isValidUrl(goods.numIid)) {
    goods.numIid = getUrlParam(goods.numIid, 'itemId') || goods.numIid
  }
}

const getUrlParam = (url, param) => {
  if (!isValidUrl(url)) {
    console.error('Invalid URL:', url)
    return ''
  }
  try {
    return new URLSearchParams(new URL(url).search).get(param) ?? ''
  } catch (error) {
    console.error('Invalid URL:', url)
    return ''
  }
}

const isValidUrl = (url) => {
  try {
    new URL(url)
    return true
  } catch (error) {
    return false
  }
}

const getUrlGoodsId = (url) => {
  return getUrlParam(url, 'goods_id')
}

const errorMap = ref({})
const clearError = (goods, index, state, event) => {
  if (errorMap.value[goods.goodsId]) {
    errorMap.value[goods.goodsId] = ''
  }
  // 平台类型-淘宝
  if (state) {
    const match = event.match(/itemId=(.*?)&/)
    if (match) {
      event = match[1]
    }
  }
}

function isAllDigits(str) {
  return /^\d+$/.test(str)
}
const checkGoodsId = () => {
  if ((goodsForm.platform === '1' || goodsForm.platform === '8') && !goodsForm.companyId) {
    Message.warning('请选择店铺')
    return
  }

  goodsFormRef.value.validate((valid) => {
    if (valid) {
      // 去除空值
      const searchList = goodsForm.goodsSearchListDTO.filter(
        (item) => item.goodsId
      )
      if (searchList.length === 0) {
        Message.warning('请填写商品信息')
        return
      }

      const err = {}
      searchList.forEach((item) => {
        if (isPdd.value) {
          if (!item.goodsUrl) {
            err[item.goodsId] = '请输入商品链接'
          }
          if (!isAllDigits(item.goodsId)) {
            err[item.goodsId] = '商品ID解析错误，请重新确认链接是否正确或联系技术人员'
          }
        } else if (isTb.value) {
          // if (!item.numIid) {
          //   err[item.goodsId] = '请输入商品虚拟ID '
          // } else if (isValidUrl(item.numIid)) {
          //   err[item.goodsId] = '商品链接未获取到虚拟ID，请检查链接是否正确'
          // }
          if (/\D/.test(item.goodsId)) {
            err[item.goodsId] = '商品ID只能填写数字'
          }

          if (
            goodsForm.platform === '2' &&
            goodsForm.linkType === '2' &&
            !item.originalTjmUrl
          ) {
            err[item.goodsId] = '请输入淘积木链接'
          }
        }

        if (goodsForm.platform === '6' && !item.mallId) {
          err[item.goodsId] = '请输入店铺ID'
        }

        if (isDjk.value && !item.goodsName) {
          err[item.goodsId] = '请输入商品名称'
        }
        if (isJdPro.value && !item.duoId) {
          err[item.goodsId] = '请输入登录Pin'
        }
      })
      if (Object.keys(err).length > 0) {
        errorMap.value = err
        Message.warning('商品信息出错, 请检查商品信息')
        return
      }

      addLoading.value = true
      const params = {
        platform: goodsForm.platform,
        goodsSearchListDTO: searchList,
        mediaPlatformTypeArr: goodsForm.mediaPlatformTypeArr,
        linkType: goodsForm.linkType
      }

      if (
        checkPermi(['promotion:goods:getDuoId']) &&
        goodsForm.platform === '1' &&
        goodsForm.duoId
      ) { params.duoId = goodsForm.duoId }

      if (isPdd.value || isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform) || isJd(goodsForm.platform) || isJdPro(goodsForm.platform)) {
        params.shopAttribute = goodsForm.shopAttribute
      }

      if (isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform)) {
        params.tbProSyn = goodsForm.tbProSyn
      }

      if (goodsForm.platform === '4') {
        params.autoRedirect = +goodsForm.autoRedirect
      }

      if (isJdPro(goodsForm.platform)) {
        params.jdTraceEventType = goodsForm.jdTraceEventType
        params.jdTracePoint = goodsForm.jdTracePoint
        params.jdTracePeriod = goodsForm.jdTracePeriod
        params.jdTraceRepeat = goodsForm.jdTraceRepeat
      }

      if ((isPdd.value || isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform) || isJd(goodsForm.platform) || isJdPro(goodsForm.platform)) && goodsForm.enableWB) {
        if (!goodsForm.WBList) {
          Message.warning(`${goodsForm.enableWB === 1 ? '白名单' : '黑名单'}商品id不能为空`)
          return
        }
        const list = goodsForm.WBList.replace(/，/g, ',')
        if (list.split(',').filter(item => item).length > 50) {
          Message.warning(`${goodsForm.enableWB === 1 ? '白名单' : '黑名单'}商品不能超过 50 个`)
          return
        }
        if (goodsForm.enableWB === 1) {
          params.goodsWhiteList = list
        } else if (goodsForm.enableWB === 2) {
          params.goodsBlackList = list
        }
      }

      if ((isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform)) && goodsForm.linkType === '2') {
        params.itemSugarType = goodsForm.itemSugarType
      }

      checkGoods(params)
        .then((data) => {
          if (data.code === 200) {
            const newList = []
            const key = isJd(goodsForm.platform) ? 'goodsSign' : 'goodsId'
            data.data.forEach((item) => {
              let goods = {}

              if (goodsForm.platform === '6') {
                goods = {
                  goodsName: '',
                  goodsThumbnailUrl: '',
                  goodsImageUrl: '',
                  minGroupPrice: '',
                  minNormalPrice: '',
                  mallName: ''
                }
              }
              if (!goodsMap.has(item[key])) {
                goodsMap.set(item[key], goods)
                goodsList.value.push(goods)
              } else {
                goods = goodsMap.get(item[key])
              }

              Object.assign(goods, {
                ...item,
                mediaPlatformTypeArr: [...goodsForm.mediaPlatformTypeArr]
              })

              if (goodsForm.platform === '1') { goods.companyId = goodsForm.companyId }

              if (goodsForm.platform === '8') {
                const mall = djkCompanyList.value.find(item => item.id === goodsForm.companyId)
                goods.mallId = mall.id
                goods.mallName = mall.label
              }
              newList.push(goods)
            })

            // 移除正确检测的商品
            const idList = newList.map((item) => item[key])
            goodsForm.goodsSearchListDTO = goodsForm.goodsSearchListDTO.filter(
              (goods) => goods.goodsId && !idList.includes(goods.goodsId)
            ) // 过滤掉空值和正确检测的商品

            // 重新添加空值
            initGoodsItem()
            // 滚动返回顶部
            scrollbarRef.value.wrap.scrollTop = 0

            nextTick(() => {
              goodsTableRef.value.doLayout()
              newList.forEach((item) => {
                goodsTableRef.value.toggleRowSelection(item, true)
              })
            })

            let msg = '检测成功'
            if (data.msg) {
              errorMap.value = JSON.parse(data.msg)
              msg += ', 有部分商品信息出错, 请检查商品信息'
            }
            Message.success(msg)
          }
        })
        .finally(() => {
          addLoading.value = false
        })
    }
  })
}

const goodsList = ref([])
const selectGoodsList = ref([])
const goodsMap = new Map()
const handleSelectionChange = (val) => {
  selectGoodsList.value = val
}

const downloadTemplate = () => {
  window.open('https://oss.pangdasc.com/template/goods.xls?t=' + new Date().getTime())
}

const templateImportUrl = '/promotion/goods/importGoodsExcel'
const handleUploadSuccess = (res) => {
  const list = res.data
  goodsForm.goodsSearchListDTO = goodsForm.goodsSearchListDTO
    .filter((item) => item.goodsId)
    .concat(list)
  initGoodsItem()
  if (list.length > 0) {
    if (list.some((item) => item.numIid)) {
      goodsForm.platform = '2'
      nextTick(() => {
        Message.success(
          '检测到数据含[商品虚拟ID](淘宝、淘宝客、UCID才填写此列),平台类型已切换到淘宝平台'
        )
      })
    }
    if (list.some((item) => item.goodsUrl)) {
      goodsForm.platform = '1'
      list.forEach((item) => {
        if (item.goodsUrl && !item.goodsId) {
          item.goodsId = getUrlGoodsId(item.goodsUrl)
        }
      })
      nextTick(() => {
        Message.success(
          '检测到数据含[商品链接](拼多多才填写此列), 平台类型已切换到拼多多平台'
        )
      })
    }
    if (list.some((item) => item.originalTjmUrl)) {
      goodsForm.linkType = '2'
      setTimeout(() => {
        Message.success(
          '检测到数据含[淘积木url](淘宝、淘宝客、UCID才填写此列),链接类型已切换到淘积木链接'
        )
      })
    }
  }

  checkGoodsId()
}

const handlePlatformChange = () => {
  goodsForm.linkType = '1'
  goodsForm.companyId = ''
}

// 编辑
const onEditing = ref(false)
const mtList = ref([])
let cloneGoodsListJSON = ''
const switchEdit = () => {
  if (!onEditing.value) {
    cloneGoodsListJSON = JSON.stringify(goodsList.value)
    mtList.value = goodsList.value.filter((item) => item.platform === '6')
    mtList.value.forEach((item) => {
      item.minNormalPrice = item.minNormalPrice / 100
      item.minGroupPrice = item.minGroupPrice / 100
    })
    onEditing.value = true
  } else {
    // 修改后提示用户是否保存
    goodsList.value = JSON.parse(cloneGoodsListJSON)
    onEditing.value = false
    setTimeout(() => {
      goodsList.value.forEach((item) => {
        goodsTableRef.value.toggleRowSelection(item, true)
      })
    })
  }
}
const saveEdit = () => {
  if (
    mtList.value.some(
      (item) => !item.goodsName || !item.mallName || !item.minNormalPrice
    )
  ) {
    Message.warning('请填写完整的商品信息')
    return
  }
  mtList.value.forEach((item) => {
    item.minGroupPrice = item.minNormalPrice = item.minNormalPrice * 100
    item.goodsImageUrl = item.goodsThumbnailUrl
  })
  onEditing.value = !onEditing.value
  setTimeout(() => {
    selectGoodsList.value.forEach((item) => {
      goodsTableRef.value.toggleRowSelection(item, true)
    })
  })
}

// 提交
const submitForm = () => {
  if (selectGoodsList.value.length === 0) {
    if (goodsForm.platform === '8' && goodsForm.linkType === '4') {
      Message.warning('请选择云台落地页')
    } else {
      Message.warning('请选择商品')
    }

    return
  }
  const unsetGoods = selectGoodsList.value.filter(
    (item) => item.platform === '6' && !item.goodsName
  )
  if (unsetGoods.length > 0) {
    Message.warning(
      `商品ID为${unsetGoods
        .map((item) => item.goodsId)
        .join(',')}的商品信息不完整, 请填写完整的商品信息或取消勾选该项后再提交`
    )
    return
  }
  addLoading.value = true
  addGoods(selectGoodsList.value)
    .then((response) => {
      Message.success('添加成功')
      emit('success')
    })
    .finally(() => {
      addLoading.value = false
    })
}
const saveAndRecord = () => {
  selectGoodsList.value.forEach((item) => {
    if (item.platform === '1' && !item.videoUrl) {
      item.videoUrl = item.goodsThumbnailUrl
    }
  })
  submitForm()
}
const cancel = () => {
  emit('cancel')
}

const goodsVisible = ref(false)
const wbExpandVisible = ref(false)
const handleGoodsSelect = (selections) => {
  goodsVisible.value = false
  const selectedIds = selections.map(item => item.goodsId).join(',')
  goodsForm.WBList = goodsForm.WBList ? `${goodsForm.WBList},${selectedIds}` : selectedIds
}

</script>

<template>
  <div v-loading="addLoading" class="dialog-body">
    <div class="flex gap-10">
      <div v-show="!onEditing" class="flex1">
        <el-form
          ref="goodsFormRef"
          :model="goodsForm"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label="平台类型" prop="platform">
            <el-radio-group v-model="goodsForm.platform" @change="handlePlatformChange">
              <el-radio
                v-for="dict in platformTypes"
                :key="dict.value"
                :label="dict.value"
                :value="dict.value"
                border
                style="margin:0 10px 5px 0;width: 120px;"
              >
                <svg-icon :icon-class="dict.label" />
                <span> {{ dict.label }}</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="媒体类型" prop="mediaPlatformTypeArr">
            <el-checkbox-group v-model="goodsForm.mediaPlatformTypeArr">
              <el-checkbox
                v-for="dict in mediaType"
                :key="dict.value"
                :label="dict.value"
                border
                style="margin:0 10px 5px 0;width: 105px;"
              >
                <svg-icon :icon-class="dict.label" />
                <span> {{ dict.label }}</span>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="链接类型" prop="linkType">
            <el-radio-group
              v-model="goodsForm.linkType"
              style="margin-bottom: -10px"
              size="small"
            >
              <el-radio
                v-for="dict in linkType"
                :key="dict.value"
                :label="dict.value"
                :value="dict.value"
                style="margin: 0 12px 10px 0"
              >{{ dict.label }}</el-radio>
            </el-radio-group>

            <el-button v-show="goodsForm.platform === '8' && goodsForm.linkType === '4'" type="primary" size="small" @click="showPageSelector">选择云台落地页</el-button>
          </el-form-item>
          <el-row :gutter="20">
            <el-col v-show="['1', '8'].includes(goodsForm.platform)" :span="12">
              <el-form-item label="店铺" prop="companyId">
                <CompanySelect v-model="goodsForm.companyId" :options="companyList" placeholder="请选择店铺" :is-search="false" />
              </el-form-item>
            </el-col>
            <el-col v-show="goodsForm.platform === '1'" :span="12">
              <el-form-item
                v-has-permi="['promotion:goods:getDuoId']"
                label="多多进宝"
                prop="duoId"
              >
                <el-select
                  v-model="goodsForm.duoId"
                  size="small"
                >
                  <el-option
                    v-for="o in duoduoOptions"
                    :key="o.duoId"
                    :label="o.duoName"
                    :value="o.duoId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col v-show="goodsForm.platform === '1'" :span="12">
              <el-form-item label="先用后付" prop="laterPay">
                <el-switch v-model="laterPay" disabled />
                <span
                  style="font-weight: bold; padding-left: 10px; color: #808080"
                >默认关闭</span>
              </el-form-item>
            </el-col>
            <el-col v-show="isPdd || isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform) || isJd(goodsForm.platform) || isJdPro(goodsForm.platform)" :span="12">
              <el-form-item
                label="全店归因"
                prop="shopAttribute"
              >
                <el-switch v-model="goodsForm.shopAttribute" />
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="当该项关闭时,用户通过该商品进入店铺后购买其他商品不会回传"
                  placement="top"
                >
                  <i class="el-icon-info ml10" />
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item v-if="(isPdd || isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform) || isJd(goodsForm.platform) || isJdPro(goodsForm.platform)) && goodsForm.shopAttribute" label="回传上报" prop="enableWB">
            <div style="display: flex; gap: 10px;height: 36px;align-items: center">
              <el-radio-group
                v-model="goodsForm.enableWB"
                size="mini"
              >
                <el-radio-button
                  :label="0"
                >关闭</el-radio-button>
                <el-radio-button
                  :label="1"
                >白名单
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="白名单内的商品将被回传，名单外的商品不回传"
                    placement="top"
                  >
                    <i class="el-icon-question" />
                  </el-tooltip>
                </el-radio-button>
                <el-radio-button
                  :label="2"
                >黑名单
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="黑名单内的商品不会被回传，名单外的商品正常回传"
                    placement="top"
                  >
                    <i class="el-icon-question" />
                  </el-tooltip>
                </el-radio-button>
              </el-radio-group>
              <template v-if="goodsForm.enableWB">
                <el-input
                  v-model="goodsForm.WBList"
                  :placeholder="!isJdPro(goodsForm.platform) ? `请输入商品ID，多个ID用逗号分隔`: goodsForm.enableWB === 2 ? `自营商品入参sku，pop商品入参spu` : '输入 spu'"
                  size="mini"
                  style="width: 240px"
                >
                  <el-button slot="append" icon="el-icon-folder-opened" @click="wbExpandVisible = true" />
                </el-input>
                <span style="font-size: 12px;color: #999">{{ goodsForm.WBList.split(/[,，]/).filter(item => item).length }}/50</span>
                <el-button v-if="!isJdPro(goodsForm.platform)" size="mini" @click="goodsVisible = true">选择商品</el-button>
              </template>
            </div>
          </el-form-item>
          <div v-show="goodsForm.platform === '4'">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="自动跳转"
                  prop="autoRedirect"
                  label-width="100px"
                >
                  <el-switch v-model="goodsForm.autoRedirect" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <template v-if="isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform)">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="转化方式" prop="tbProSyn">
                  <el-radio-group v-model="goodsForm.tbProSyn">
                    <el-radio :label="3">全部</el-radio>
                    <el-radio :label="1">同步归因
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="点击唤端后产生的订单(类似老流量通)"
                        placement="top"
                      >
                        <i class="el-icon-info" />
                      </el-tooltip>
                    </el-radio>
                    <el-radio :label="2">异步归因
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="曝光或点击未唤端的订单"
                        placement="top"
                      >
                        <i class="el-icon-info" />
                      </el-tooltip>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-if="((isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform)) && goodsForm.linkType === '2')">
            <el-form-item label="自动生成UD落地页" label-width="140px">
              <el-radio-group v-model="goodsForm.itemSugarType">
                <el-radio :label="0">不生成</el-radio>
                <el-radio :label="1">真实销量&评价</el-radio>
                <el-radio :label="2">同款销量&评价</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <template v-if="isJdPro(goodsForm.platform)">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="行为" prop="jdTraceEventType">
                  <el-radio-group v-model="goodsForm.jdTraceEventType">
                    <el-radio :label="10">下单</el-radio>
                    <el-radio :label="11">支付
                      <TextHint content="订单支付才会归因，未支付订单不归因不回传" icon="el-icon-question" />
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="归因周期" prop="jdTracePeriod">
                  <el-radio-group v-model="goodsForm.jdTracePeriod">
                    <el-radio :label="1">1天</el-radio>
                    <el-radio :label="7">7天</el-radio>
                    <el-radio :label="15">15天</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="归因触点" prop="jdTracePoint">
              <el-radio-group v-model="goodsForm.jdTracePoint" @change="handleJdTracePointChange">
                <el-radio :label="1">曝光
                  <TextHint content="用户被曝光广告，下单后即会归因" icon="el-icon-question" />
                </el-radio>
                <el-radio :label="2">有效播放+点击
                  <TextHint content="用户进一步浏览广告大于3s，或点击广告，下单后才归因" icon="el-icon-question" />
                </el-radio>
                <el-radio :label="3">联盟点击
                  <TextHint content="用户点击广告后，呼起了京东app/小程序/京东H5页面，下单后才会归因" icon="el-icon-question" />
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="去重归因" prop="jdTraceRepeat">
              <el-radio-group v-model="goodsForm.jdTraceRepeat">
                <el-radio :label="0">不去重
                  <TextHint content="即一个订单可同时归因CID投放，和京准通站内/站外广告，比如搜索广告" icon="el-icon-question" />
                </el-radio>
                <el-radio :label="1" :disabled="!showJdTraceRepeat">去重
                  <TextHint content="即一个订单如果归因给京准通站内/站外广告，则不再归因给CID投放，不予回传。" icon="el-icon-question" />
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>

        </el-form>
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="card-title">检测商品信息</span>
            <div v-if="!isJdPro(goodsForm.platform)" style="display: flex; float: right; padding: 3px 0; gap: 10px">
              <div style="display: flex; gap: 10px">
                <el-button
                  icon="el-icon-download"
                  type="text"
                  size="small"
                  @click="downloadTemplate"
                >下载模板</el-button>
                <Upload
                  :action="templateImportUrl"
                  :file-size="1"
                  filter-name
                  button-type="primary"
                  :file-type="['xls', 'xlsx']"
                  @successResult="handleUploadSuccess"
                >
                  <el-button
                    slot="trigger"
                    icon="el-icon-upload"
                    size="small"
                  >上传模板</el-button>
                </Upload>
              </div>
            </div>
          </div>
          <div v-if="isPdd" class="info-tips">
            <div>
              <span class="color-danger">商品链接</span>
              <span>获取方式:</span>
            </div>
            <a
              href="https://ziuqxc1rltx.feishu.cn/docx/PIeqdcNY4oeafwx8cdqcC3cnnTf#Evf2dHRGAovvNux7YkacfR6jnxc"
              class="color-primary"
              target="_blank"
            >
              <i class="el-icon-link" />
              链接
            </a>
          </div>
          <div v-if="isTb" class="info-tips">
            <!-- <div>
              <span>可复制流量通商品链接到虚拟ID输入框中自动解析，</span>
              <span class="color-danger">商品虚拟ID</span>
              <span>获取方式:</span>
            </div> -->
            <a
              href="https://ziuqxc1rltx.feishu.cn/docx/ZUBDd7LrboSKxPxsfnLciYkwnlf"
              class="color-primary"
              target="_blank"
            >
              <i class="el-icon-link" />
              链接
            </a>
          </div>

          <div v-if="isJdPro(goodsForm.platform)" class="info-tips">
            <div>
              <span class="color-danger">登录Pin </span>
              <span>获取方式:</span>
            </div>
            <a
              href="https://ziuqxc1rltx.feishu.cn/docx/BZQldp7Qgo3SsZxOkwqcNjvcnKb"
              class="color-primary"
              target="_blank"
            >
              <i class="el-icon-link" />
              链接
            </a>
          </div>
          <el-scrollbar
            ref="scrollbarRef"
            style="height: 120px"
          >
            <div
              v-for="(goods, i) in goodsForm.goodsSearchListDTO"
              :key="i"
              class="check-goods"
            >
              <div
                class="check-goods-info"
                :class="[errorMap[goods.goodsId] ? 'error-info' : '']"
              >
                <el-input
                  v-if="!isPdd"
                  v-model.trim="goods.goodsId"
                  clearable
                  :placeholder="goodsForm.platform === '3' || isJdPro(goodsForm.platform) ? 'SKUID' : '商品ID'"
                  size="small"
                  @input="clearError(goods)"
                />
                <template v-else-if="goods.goodsUrl">
                  <el-tag v-if="goods.goodsId">商品ID: {{ goods.goodsId }}</el-tag>
                  <el-tag v-if="!goods.goodsId" type="danger">未检测到商品ID，请检查商品链接是否正确</el-tag>
                </template>
                <template v-if="isPdd">
                  <el-input
                    v-model.trim="goods.goodsUrl"
                    clearable
                    placeholder="商品链接"
                    size="small"
                    @input="handlePddGoodsUrl(goods)"
                  />
                </template>
                <template v-if="(isTbPro(goodsForm.platform) || isUDSmart(goodsForm.platform)) && goodsForm.linkType === '2' && goodsForm.itemSugarType === 0">
                  <el-input
                    v-model.trim="goods.originalTjmUrl"
                    clearable
                    placeholder="ud 建站链接 或 淘积木链接"
                    size="small"
                  />
                </template>
                <template v-if="isTb">
                  <!-- <el-input
                    v-model.trim="goods.numIid"
                    clearable
                    size="small"
                    placeholder="商品虚拟ID，示例:9BxxxxBi5CeMxxxZs3BWxxt6-gKZ0nX0Ixxq52e4qxxx"
                    @input="clearError(goods,i,true,$event)"
                    @blur="handleTbGoodsUrl(goods)"
                  /> -->
                  <el-input
                    v-if="goodsForm.linkType === '2'"
                    v-model.trim="goods.originalTjmUrl"
                    placeholder="淘积木链接"
                    clearable
                    size="small"
                    @input="clearError(goods)"
                  />
                  <i
                    class="el-icon-delete delete-icon"
                    @click="removeGoods(i)"
                  />
                </template>
                <template v-if="isJdPro(goodsForm.platform)">
                  <el-input
                    v-model.trim="goods.duoId"
                    size="small"
                    clearable
                    placeholder="登录Pin"
                    @input="clearError(goods)"
                  />
                  <el-input
                    v-if="goodsForm.linkType === '2'"
                    v-model.trim="goods.originalTjmUrl"
                    size="small"
                    clearable
                    placeholder="大健康链接"
                    @input="clearError(goods)"
                  />
                  <i
                    class="el-icon-delete delete-icon"
                    @click="removeGoods(i)"
                  />
                </template>
                <template v-if="goodsForm.platform === '3' && goodsForm.linkType === '3'">
                  <el-input
                    v-model.trim="goods.originalTjmUrl"
                    size="small"
                    clearable
                    placeholder="通天塔链接"
                    @input="clearError(goods)"
                  />
                  <i
                    class="el-icon-delete delete-icon"
                    @click="removeGoods(i)"
                  />
                </template>
                <template v-if="goodsForm.platform === '3' && goodsForm.linkType === '5'">
                  <el-input
                    v-model.trim="goods.originalTjmUrl"
                    size="small"
                    clearable
                    placeholder="大健康链接"
                    @input="clearError(goods)"
                  />
                  <i
                    class="el-icon-delete delete-icon"
                    @click="removeGoods(i)"
                  />
                </template>
                <template v-if="goodsForm.platform === '6'">
                  <el-input
                    v-model.trim="goods.mallId"
                    size="small"
                    clearable
                    placeholder="店铺ID"
                    @input="clearError(goods)"
                  />
                  <el-input
                    v-model.trim="goods.numIid"
                    clearable
                    size="small"
                    placeholder="PageID(主商品可配置)"
                    @input="clearError(goods)"
                  />
                  <i
                    class="el-icon-delete delete-icon"
                    @click="removeGoods(i)"
                  />
                </template>
                <template v-if="isDjk">
                  <el-input
                    v-model.trim="goods.goodsName"
                    clearable
                    placeholder="商品名称"
                    size="small"
                    @input="clearError(goods)"
                  />
                </template>
              </div>
              <div class="error-msg">{{ errorMap[goods.goodsId] }}</div>
            </div>
          </el-scrollbar>
          <div class="check-btns">
            <el-button
              class="flex1"
              icon="el-icon-search"
              type="primary"
              plain
              @click="checkGoodsId"
            >检测</el-button>
          </div>
        </el-card>
      </div>
      <div class="goods-select-table">
        <div class="goods-select-title">
          <div>已检测的商品信息</div>
          <div>
            <el-button
              v-if="onEditing"
              type="primary"
              icon="el-icon-upload"
              size="mini"
              @click="saveEdit"
            >保存</el-button>
            <el-button
              :icon="onEditing ? 'el-icon-close' : 'el-icon-edit'"
              size="mini"
              @click="switchEdit"
            >{{ onEditing ? "取消" : "编辑" }}</el-button>
          </div>
        </div>
        <div class="flex1 width-full">
          <el-table
            v-if="onEditing"
            :data="mtList"
            border
            stripe
            size="mini"
          >
            <el-table-column type="selection" width="50" />
            <el-table-column prop="goodsName" label="商品信息">
              <template #default="{ row }">
                <el-input
                  v-model="row.goodsName"
                  class="mb5"
                  :placeholder="`商品ID:${row.goodsId}, 请输入商品名称`"
                />
                <el-input
                  v-model="row.goodsThumbnailUrl"
                  placeholder="请输入图片地址"
                />
              </template>
            </el-table-column>
            <el-table-column prop="platform" label="平台类型" width="80">
              <template slot-scope="{ row }">
                <div class="platform-icon">
                  <svg-icon :icon-class="dictMap.platform_type[row.platform]" />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="minNormalPrice" label="商品价格" width="250">
              <template slot-scope="{ row }">
                <div style="margin: 0 auto">
                  <el-input-number
                    v-model="row.minNormalPrice"
                    placeholder="请输入商品价格"
                    :controls="false"
                    class="mr5"
                  />
                  元
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="mallName" label="店铺名称">
              <template slot-scope="{ row }">
                <el-input
                  v-model="row.mallName"
                  :placeholder="`店铺ID:${row.mallId}, 请输入店铺名称`"
                />
              </template>
            </el-table-column>
          </el-table>
          <el-table
            v-else
            ref="goodsTableRef"
            :data="goodsList"
            border
            stripe
            size="mini"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" />
            <el-table-column
              prop="goodsName"
              label="商品信息"
              :width="onEditing ? 400 : 250"
            >
              <template #default="{ row }">
                <div class="table-base-info">
                  <svg-icon
                    v-if="row.platform === '2' && !row.goodsThumbnailUrl"
                    class="info-img"
                    icon-class="taobao"
                  />
                  <svg-icon
                    v-else-if="row.platform === '9' && !row.goodsThumbnailUrl"
                    class="info-img"
                    icon-class="UDSmart"
                  />
                  <div v-else class="info-img">
                    <image-preview
                      :src="row.goodsThumbnailUrl"
                      :width="50"
                      :height="50"
                    />
                  </div>
                  <BaseInfoCell
                    :id="row.goodsId"
                    class="info-wrap"
                    style="flex: 1"
                    :name="row.goodsName"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="platform" label="平台类型" width="80">
              <template slot-scope="{ row }">
                <div class="platform-icon">
                  <svg-icon :icon-class="dictMap.platform_type[row.platform]" />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="media" label="媒体类型" width="110">
              <template slot-scope="{ row }">
                <svg-icon
                  v-for="m in row.mediaPlatformTypeArr"
                  :key="m"
                  class="media-icon"
                  :icon-class="dictMap.media_type[m]"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="minGroupPrice"
              label="最小拼团价"
              width="100"
            >
              <template slot-scope="{ row }">
                {{ row.minGroupPrice ? formatPrice(row.minGroupPrice) : "-" }} 元
              </template>
            </el-table-column>
            <el-table-column prop="goodsName" label="最小单买价格" width="100">
              <template slot-scope="{ row }">
                {{ row.minNormalPrice ? formatPrice(row.minNormalPrice) : "-" }} 元
              </template>
            </el-table-column>
            <el-table-column prop="mallName" label="店铺名称" width="200">
              <template slot-scope="{ row }">
                {{ row.mallName }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div v-show="!onEditing" slot="footer" class="dialog-footer-right">
      <el-button
        v-if="goodsForm.platform === '1'"
        class="submit-btn"
        @click="saveAndRecord"
      >保存并自动备案
        <el-tooltip
          class="item"
          effect="dark"
          content="仅拼多多生效"
          placement="top"
        >
          <i class="el-icon-info" />
        </el-tooltip>
      </el-button>
      <el-button
        class="submit-btn"
        type="primary"
        @click="submitForm"
      >确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
    <GoodsSelector
      :visible.sync="goodsVisible"
      :query-list="['goodsName', 'goodsId','mediaPlatformType', 'platform']"
      :table-list="['goodsName', 'mediaPlatformType', 'remark', 'platform']"
      :auto-search="false"
      @select="handleGoodsSelect"
    />
    <el-dialog title="回报上传" :visible.sync="wbExpandVisible" append-to-body width="700px">
      <el-form :model="goodsForm" label-width="110px" style="padding: 10px 0 20px;">
        <el-form-item label="黑白名单设置">
          <el-radio-group
            v-model="goodsForm.enableWB"
            size="mini"
          >
            <el-radio-button
              :label="0"
            >关闭</el-radio-button>
            <el-radio-button
              :label="1"
            >白名单</el-radio-button>
            <el-radio-button
              :label="2"
            >黑名单</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="goodsForm.enableWB" label="商品ID">
          <el-input
            v-model="goodsForm.WBList"
            type="textarea"
            :rows="5"
            :placeholder="!isJdPro(goodsForm.platform) ? `请输入商品ID，多个ID用逗号分隔`: goodsForm.enableWB === 2 ? `自营商品入参sku，pop商品入参spu` : '输入 spu'"
            size="mini"
          />
          <el-button v-if="!isJdPro(goodsForm.platform)" style="margin-right: 10px;" size="mini" @click="goodsVisible = true">选择商品</el-button>
          <span style="font-size: 12px;color: #999;">{{ goodsForm.WBList.split(/[,，]/).filter(item => item).length }}/50</span>
        </el-form-item>
        <el-form-item>
          <el-button class="fr" @click="wbExpandVisible=false">关闭</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.check-goods {
  .check-goods-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
    margin-right: 5px;
    &:hover .delete-icon {
      display: block;
    }
  }
  .delete-icon {
    display: none;
    color: #ff4949;
    margin-right: 10px;
    font-size: 18px;
    line-height: 36px;
  }
  .error-info {
    border: 1px solid #ff4949;
    border-radius: 4px;
  }
  .error-msg {
    color: #ff4949;
    font-size: 12px;
    margin-bottom: 5px;
  }
}

.card-title {
  font-size: 14px;
  font-weight: bold;
  line-height: 32px;
}
.info-tips {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  font-size: 12px;
  margin-bottom: 5px;
}
.check-btns {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}
.goods-select-table {
  flex: 1;
  width: 0;
  display: flex;
  flex-direction: column;
  .goods-select-title {
    font-size: 14px;
    font-weight: bold;
    line-height: 32px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.submit-btn {
  display: inline-block;
  margin-right: 10px;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
.platform-icon {
  text-align: center;
  font-size: 28px;
}
.media-icon {
  margin-right: 5px;
  font-size: 16px;
}

::v-deep .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
::v-deep .el-form-item {
  margin-bottom: 10px;
}
</style>
