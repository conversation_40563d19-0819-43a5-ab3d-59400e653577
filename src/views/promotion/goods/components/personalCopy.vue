<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { Message } from 'element-ui'
import ClipboardJS from 'clipboard'
import UrlClipboard from '@/views/promotion/goods/components/UrlClipboard.vue'
const props = defineProps({
  visible: Boolean,
  info: {
    type: Object,
    default: () => {}
  },
  oneJumpUrl: {
    type: String,
    default: ''
  },
  // 二跳数组
  pageList: {
    type: Array,
    default: () => []
  }

})
const oneJumpUrl = computed(() => {
  // return props.oneJumpUrl
  let newStr = props.oneJumpUrl
  if (formData.value.title) {
    newStr = newStr + '&title=' + formData.value.title + '&advertisement=' + +formData.value.advertisement
  } else {
    newStr = newStr + '&advertisement=' + +formData.value.advertisement
  }
  return newStr
})
const emit = defineEmits(['success', 'close'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})
const formData = ref({
  title: '',
  advertisement: 1
})
const profitForm = reactive({

})
const formRef = ref()

const close = () => {
  open.value = false
}
const rules = {
  // title: [
  //   { required: true, message: '请输入自定义标题', trigger: 'change' }
  // ],
  // advertisement: [
  //   { required: true, message: '请选择是否展示', trigger: 'change' }
  // ]
}
function clipboardHandle(str) {
  const clipboard = new ClipboardJS('#copyNode', {
    text() {
      return str
    }
  })
  clipboard.on('success', () => {
    Message.success('链接已复制，赶快去分享吧！')
    clipboard.destroy()
  })
  clipboard.on('error', e => {
  })
}
</script>

<template>
  <el-dialog
    title="一跳落地页"
    :visible.sync="open"
    width="900px"
    top="10vh"
    append-to-body
  >
    <el-form ref="formRef" :rules="rules" :model="profitForm" label-width="68px">
      <div style="font-weight: bold;" class="mb5">
        <!-- 一跳落地页 -->

        <el-form-item label-width="150px">
          <template #label>导航栏标题：
            <el-tooltip class="item" effect="dark" content="不输入标题时会使用商品标题作为导航栏标题" placement="top">
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
          <el-input v-model="formData.title" />
        </el-form-item>
        <el-form-item label="是否展示'广告'标签：" prop="advertisement" label-width="150px" class="mg20">
          <el-radio-group v-model="formData.advertisement">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="copyBtn mg20">
          <url-clipboard :url="oneJumpUrl" />
          <!-- <el-button id="copyNode" type="text" size="small" @click="clipboardHandle(oneJumpUrl)">
            <i class="el-icon-document-copy" />复制
          </el-button> -->
        </div>
      </div>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">关闭</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.copyBtn{
  margin-left: 50px;
}
.mg20{
  margin: 10px 0;
}
</style>
