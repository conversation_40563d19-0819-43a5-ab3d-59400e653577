<template>
  <div>
    <template v-if="simple">
      <el-select
        key="1"
        v-model="selectAccounts"
        filterable
        remote
        collapse-tags
        reserve-keyword
        placeholder="请选择媒体账户"
        :remote-method="requestRemote"
        :loading="selectLoading"
        style="width: 100%;margin-bottom: 10px"
        size="small"
        @visible-change="handleHidden"
        @change="handleSelect"
      >
        <el-option
          v-for="item in mediaAccountOptions"
          :key="item.value"
          :label="item.label"
          :value="item"
        />
      </el-select>
    </template>
    <template v-else>
      <div v-if="size==='default'" class="media-title ml5">
        媒体账户
        <span v-if="showTips" class="color-danger" style="font-size: 14px;font-weight: normal">(使用二跳链接请勿选择媒体账户)</span>
      </div>
      <div class="flex mb10 gap10">
        <el-select
          key="2"
          v-model="selectAccounts"
          multiple
          filterable
          remote
          collapse-tags
          reserve-keyword
          placeholder="请选择"
          :remote-method="requestRemote"
          :loading="selectLoading"
          class="flex1"
          size="small"
          @visible-change="handleHidden"
          @change="handleSelect"
        >
          <el-option
            v-for="item in mediaAccountOptions"
            :key="item.value"
            :label="showId?`${item.label} (${item.value})`:item.label"
            :value="item"
          />
        </el-select>
        <el-button type="primary" size="mini" icon="el-icon-copy-document" @click="handleBatchAdd" />
      </div>
      <el-table
        :data="selectAccounts"
        :height="size==='default'?'420px': '240px'"
        stripe
        border
        size="mini"
      >
        <el-table-column
          prop="name"
          label="名称"
        >
          <template #default="scope">
            <BaseInfoCell :id="scope.row.value" :name="scope.row.label" no-copy />
          </template>
        </el-table-column>
        <el-table-column
          prop="action"
          label="操作"
          width="50"
          align="center"
        ><template #default="scope">
          <el-button type="text" icon="el-icon-delete" @click="removeAid(scope.row)" />
        </template>
        </el-table-column>
      </el-table>
    </template>
  </div>
</template>

<script>
export default {
  name: 'MediaAccountSelector'
}
</script>

<script setup>
import { ref, nextTick } from 'vue'
import useAccountSelect from '@/hooks/mediaAction/useAccountSelect'
import { getReplaceFindAid, getSwitchSourceId } from '@/api/promotion/goods'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { MessageBox, Message } from 'element-ui'

const props = defineProps({
  goods: {
    type: Object,
    default: null
  },
  defaultSelections: {
    type: Array,
    default: () => []
  },
  mediaPlatformType: {
    type: [Number, String],
    default: null
  },
  replaceData: {
    type: Object,
    default: null
  },
  replaceLink: {
    type: Boolean,
    default: null
  },
  size: {
    type: String,
    default: 'default'
  },
  simple: {
    type: Boolean,
    default: false
  },
  type: {
    type: Number,
    default: null
  },
  fullBack: {
    type: Boolean,
    default: false
  },
  // 是否显示标题旁提示
  showTips: {
    type: Boolean,
    default: true
  },
  showId: {
    type: Boolean,
    default: false
  }

})
const emit = defineEmits(['change'])

const selectAccounts = ref([])
const { options: mediaAccountOptions, requestRemote, selectLoading, getSelectAccounts } = useAccountSelect(props.mediaPlatformType || props.goods?.mediaPlatformType, false)

const initSelect = (ids) => {
  mediaAccountOptions.value.forEach(row => {
    if (ids.includes(row.value)) {
      selectAccounts.value.push(row)
    }
  })
  handleSelect()
}
if (props.defaultSelections.length > 0) {
  getSelectAccounts(props.defaultSelections.map(item => item.value)).then(data => {
    selectAccounts.value = data
    handleSelect()
  })
}
requestRemote().then(() => {
  if (props.replaceLink !== null) {
    if (props.replaceLink) {
      getReplaceFindAid(props.goods.id).then(data => {
        if (data.code === 200) {
          const ids = Object.keys(data.data)
          if (ids.length === 0) return
          initSelect(ids)
        }
      })
    }
  } else if (props.replaceData?.list) {
    selectAccounts.value = props.replaceData.list.map(item => {
      return {
        value: item.aid,
        label: item.advertiserName
      }
    })
    handleSelect()
  } else if (props.replaceData === null && props.goods) {
    const postData = {
      id: props.goods.id,
      pauseSwitch: 1
    }
    if (props.type !== null) {
      postData.type = props.type
    }
    getSwitchSourceId(postData).then(response => {
      if (response.data?.list) {
        selectAccounts.value = response.data.list.map(item => {
          return {
            value: item.aid,
            label: item.advertiserName
          }
        })
        handleSelect()
      }
    })
  }
})

const removeAid = (row) => {
  selectAccounts.value = selectAccounts.value.filter(item => item !== row)
  handleSelect()
}

const handleSelect = () => {
  if (props.simple) {
    emit('change', selectAccounts.value.value)
    return
  }
  emit('change', props.fullBack ? selectAccounts.value : selectAccounts.value.map(item => item.value))
}

const handleHidden = (visible) => {
  if (!visible) {
    requestRemote('')
  }
}

const handleBatchAdd = () => {
  MessageBox.prompt('请输入要添加的媒体账户ID', '批量添加媒体账户', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入媒体账户ID，多个ID用逗号或回车分隔',
    inputValidator: (value) => {
      const ids = value.split(/[\n,，]/).map(item => item.trim())
      if (ids.length === 0) {
        return '请输入媒体账户ID'
      }
      return true
    }
  }).then(({ value }) => {
    const ids = value.split(/[\n,，]/).map(item => item.trim())
    getSelectAccounts(ids).then(data => {
      const newData = data.filter(item => !selectAccounts.value.some(selectItem => selectItem.value === item.value))
      selectAccounts.value = [...selectAccounts.value, ...newData]
      handleSelect()
      Message.success(`添加成功，共添加${newData.length}个媒体账户`)
      if (newData.length < ids.length) {
        const notAddedIds = ids.filter(id => !selectAccounts.value.some(item => item.value === id))
        nextTick(() => {
          Message.warning(`以下媒体账户未添加：${notAddedIds.join(', ')}，请检查媒体账户ID是否正确`)
        })
      }
    })
  })
}
</script>

<style lang="scss" scoped>
.media-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
::v-deep .el-select__tags-text {
  max-width: 200px;
}
</style>
