<template>
  <div>
    <el-dialog
      title="商品提报列表"
      :visible.sync="tableVisible"
      width="1400px"
      top="10vh"
      append-to-body
    >
      <el-form
        ref="queryForm"
        class="search-form"
        :model="formQuery"
        size="small"
        :inline="true"
        label-width="88px"
        @submit.native.prevent
      >
        <el-form-item prop="goodsId" label="商品ID：">
          <el-input v-model.trim="formQuery.goodsId" placeholder="请输入商品ID" @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetQuery"
          >重置</el-button>
        </el-form-item></el-form>
      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%">
        <el-table-column
          prop="goodsId"
          label="商品ID"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            <BaseInfoCell :name="scope.row.goodsId" />
          </template>
        </el-table-column>
        <el-table-column
          prop="goodsName"
          label="商品名称"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            <BaseInfoCell :name="scope.row.goodsName" no-copy />
          </template>
        </el-table-column>
        <el-table-column prop="brandName" align="center" label="品牌名称">
          <template slot-scope="scope">
            <BaseInfoCell :name="scope.row.brandName" no-copy />
          </template>
        </el-table-column>
        <el-table-column prop="record" align="center" label="记录" />
        <el-table-column prop="recordReason" align="center" label="记录原因" />
        <el-table-column prop="createTime" align="center" label="创建时间" />
        <el-table-column prop="modifyTime" align="center" label="修改时间" />

      </el-table>

      <pagination
        v-show="formQuery.total>0"
        :total="formQuery.total"
        :page-sizes="[5,10]"
        :page.sync="formQuery.pageNum"
        :limit.sync="formQuery.pageSize"
        @pagination="getList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelJoin">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getGoodsBrandList } from '@/api/promotion/goods'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
export default {
  components: { BaseInfoCell },
  props: {
    // row
    // list: {
    //   type: Object,
    //   default: null
    // },
    // 是否显示弹窗
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      formQuery: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        goodsId: ''
      },
      tableLoading: false
    }
  },
  computed: {
    tableVisible: {
      get(val) {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    tableVisible(val) {
      if (val) {
        this.getList()
      } else {
        this.resetForm()
      }
    }
  },
  mounted() {},
  methods: {
    getList() {
      const data = {
        page: this.formQuery.pageNum,
        pageSize: this.formQuery.pageSize
      }
      if (this.formQuery.goodsId) {
        data['goodsId'] = this.formQuery.goodsId ? [this.formQuery.goodsId] : ''
      }
      this.tableLoading = true
      getGoodsBrandList(data).then((data) => {
        this.formQuery.total = data.data && data.data.length && data.data[0]?.totalCount || 0
        this.tableData = data.data
      }).finally(() => {
        this.tableLoading = false
      })
    },
    resetForm() {
      this.tableData = []
      this.formQuery = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        goodsId: ''
      }
      this.tableLoading = false
    },
    cancelJoin() {
      this.tableVisible = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.formQuery.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped></style>
