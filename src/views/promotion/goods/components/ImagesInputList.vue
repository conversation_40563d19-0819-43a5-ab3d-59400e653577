<template>
  <div>
    <Draggable tag="ul" :list="images" class="list-group" handle=".handle">
      <li
        v-for="(_, i) in images"
        :key="i"
        class="list-group-item"
      >
        <i class="el-icon-sort handle" />
        <el-input v-model="images[i]" type="text" class="form-control flex1" :placeholder="placeholder" clearable />
        <el-button size="mini" class="close-btn" :disabled="images.length === 0" type="danger" plain @click="removeImage(i)">
          <i class="el-icon-close close" />
        </el-button>
      </li>
    </Draggable>

    <div class="action-btns">
      <el-button class="flex1 add-btn" type="primary" :disabled="images.length === max" @click="addImage">+ 新增</el-button>
      <el-button @click="emitUpload">
        <i class="el-icon-upload" />
      </el-button>
    </div>
  </div>
</template>

<script>
import Draggable from 'vuedraggable'

export default {
  name: 'ImagesInputList',
  components: {
    Draggable
  },
  model: {
    prop: 'list',
    event: 'change'
  },
  props: {
    list: {
      type: Array,
      default: () => ([])
    },
    placeholder: {
      type: String,
      default: '请输入图片地址'
    },
    fileSize: {
      type: Number,
      default: 5
    },
    max: {
      type: Number,
      default: 10
    }
  },
  data: () => ({
    images: []
  }),
  watch: {
    list: {
      handler(val) {
        this.images = val
      },
      deep: true
    },
    images: {
      handler(val) {
        this.$emit('change', val)
      },
      deep: true
    }
  },
  mounted() {
    this.images = this.list
  },
  methods: {
    addImage() {
      this.images.push('')
    },
    removeImage(i) {
      this.$emit('remove', this.images.splice(i, 1))
    },
    // 上传结束处理
    handleUploadSuccess(list) {
      this.images.forEach((item, i) => {
        if (!item) {
          this.images[i] = list.shift().url
        }
      })
      this.images = this.images.concat(list.map(item => item.url))
    },
    emitUpload() {
      this.$emit('upload')
    }
  }
}
</script>

<style lang="scss" scoped>
.images-main-item {
  margin-right: 12px;
}
.images-item {
  margin-bottom: 10px;
}
.action-btns {
  display: flex;
}
.upload-btn {
  margin-left: 10px;
}

.list-group-item {
  display: flex;
  align-items: center;
  padding: 5px;
  border: 1px solid #ececec;
  border-radius: 5px;
  margin-bottom: 8px;
  &:last-of-type {
    //margin-bottom: 0px;
  }
  .close-btn {
    display: none;
  }
  &:hover {
    .close-btn {
      display: block;
    }
  }
  .form-control {
    margin: 0 10px;
  }
}
.handle {
  float: left;
  font-size: 16px;
  padding: 5px;
  cursor: move;
}
</style>
