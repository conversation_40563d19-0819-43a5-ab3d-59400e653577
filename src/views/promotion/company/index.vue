<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item prop="platform">
        <el-select v-model="queryParams.platform" placeholder="平台类型" clearable @change="handleQuery">
          <el-option
            v-for="dict in dict.type.platform_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="industryType">
        <el-select
          v-model="queryParams.industryType"
          placeholder="行业类型"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.industry_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="shopName">
        <el-input
          v-model.trim="queryParams.shopName"
          placeholder="店铺名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="taxNumber">
        <el-input
          v-model.trim="queryParams.taxNumber"
          placeholder="税务号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="legalPerson">
        <el-input
          v-model.trim="queryParams.legalPerson"
          placeholder="法人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <UserSearchInput
        :create-by.sync="queryParams.createBy"
        @query="handleQuery"
      />
      <el-form-item prop="deptIds">
        <DeptTreeSelect v-model="queryParams.deptIds" :options="deptOptions" />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          @search="handleQuery"
        />
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:company:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-tooltip
          class="item"
          effect="dark"
          content="请先选择公司"
          placement="top"
        >
          <el-button
            v-hasPermi="['promotion:company:transfer']"
            plain
            icon="el-icon-sort"
            size="mini"
            :disabled="multiple"
            @click="handleTransfer"
          >一键移交</el-button>
        </el-tooltip>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="companyList"
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50"
        />
        <el-table-column label="类型" align="center" prop="mediaPlatformType" width="70">
          <template slot-scope="scope">
            <svg-icon v-if="dict.label.platform_type[scope.row.platform]" style="font-size: 24px" :icon-class="dict.label.platform_type[scope.row.platform]" />
            <div class="type-label">{{ dict.label.platform_type[scope.row.platform] }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="行业类型"
          align="center"
          prop="industryType"
          width="120px"
        >
          <template #default="scope">
            <dict-tag
              :options="dict.type.industry_type"
              :value="scope.row.industryType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="公司名称"
          align="center"
          prop="companyName"
          show-overflow-tooltip
        />
        <el-table-column
          label="店铺名称"
          align="center"
          prop="shopName"
          show-overflow-tooltip
        />
        <el-table-column label="税务号" align="center" prop="taxNumber" />
        <el-table-column
          label="法人名称"
          align="center"
          prop="legalPerson"
          width="150px"
        />
        <el-table-column
          label="联系方式"
          align="center"
          prop="contact"
          width="150px"
        />
        <el-table-column
          label="注册地址"
          align="center"
          prop="registeredAddress"
          show-overflow-tooltip
        />
        <el-table-column
          label="营业执照"
          align="center"
          prop="businessLicense"
          width="100"
        >
          <template slot-scope="scope">
            <image-preview
              :src="scope.row.businessLicense"
              :width="50"
              :height="50"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="用户昵称"
          align="center"
          prop="nickName"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="负责人"
          align="center"
          prop="createBy"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          label="部门名称"
          align="center"
          prop="deptName"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属公司"
          align="center"
          prop="firstDeptName"
          width="160"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="170"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="100px"
        >
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['promotion:company:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              v-hasPermi="['promotion:company:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改店铺信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body top="5vh">
      <el-form ref="form" :model="form" :rules="rules" :label-width="form.platform===9?'96px':'80px'">
        <el-form-item label="平台类型" prop="platform">
          <el-select v-model="form.platform" placeholder="平台类型" @change="handleFormPlatformChange">
            <el-option
              v-for="dict in platformTypes"
              :key="dict.value"
              :label="dict.label"
              :value="+dict.value"
            >
              <svg-icon :icon-class="dict.label" />
              <span> {{ dict.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="行业类型" prop="industryType">
          <el-select v-model="form.industryType" placeholder="请选择行业类型">
            <el-option
              v-for="dict in dict.type.industry_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺名称" prop="shopName">
          <el-input v-model="form.shopName" placeholder="请输入店铺名称" />
        </el-form-item>
        <el-form-item v-if="form.platform === 8" label="店铺链接" prop="shopUrl">
          <el-input v-model="form.shopUrl" placeholder="请输入店铺链接" maxlength="100" />
        </el-form-item>
        <el-form-item v-if="form.platform === 9" label="淘宝店铺ID" prop="shopId">
          <el-input v-model="form.shopId" placeholder="请输入淘宝店铺ID" />
        </el-form-item>
        <el-form-item v-if="form.platform === 9" label="卖家昵称" prop="sellerNick">
          <el-input v-model="form.sellerNick" :disabled="form.id?true:false" placeholder="请输入卖家昵称" />
        </el-form-item>
        <el-form-item v-if="form.platform === 9" label="卖家ID" prop="sellerId">
          <el-input v-model="form.sellerId" :disabled="form.id?true:false" placeholder="请输入卖家ID" />
        </el-form-item>

        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="税务号" prop="taxNumber">
          <el-input v-model="form.taxNumber" placeholder="请输入税务号" />
        </el-form-item>
        <el-form-item label="法人名称" prop="legalPerson">
          <el-input v-model="form.legalPerson" placeholder="请输入法人名称" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="注册地址" prop="registeredAddress">
          <el-input
            v-model="form.registeredAddress"
            placeholder="请输入注册地址"
          />
        </el-form-item>
        <el-form-item v-if="form.platform !== 9" label="营业执照" prop="businessLicense">
          <FileResSelector
            v-model="form.businessLicense"
            :img-file-size="3"
            default-selected="店铺"
          />
          <div class="hint">· 请选择营业执照图片，不能上传身份证！</div>
          <div class="hint">· 个人店铺，请上传广告媒体账户的营业执照。</div>
        </el-form-item>
        <el-form-item v-if="form.platform === 9" label="营业执照">
          <FileResSelector
            v-model="form.businessLicense"
            :img-file-size="3"
            default-selected="店铺"
          />
          <div class="hint">· 请选择营业执照图片，不能上传身份证！</div>
          <div class="hint">· 个人店铺，请上传广告媒体账户的营业执照。</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-loading="submitLoading"
          type="primary"
          @click="submitForm"
        >确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!--  一键移交  -->
    <el-dialog
      title="选择一键移交目标用户"
      :visible.sync="transferVisible"
      width="50%"
      top="10vh"
      append-to-body
    ><TransferUser @select="handleTransferSelect" /></el-dialog>
  </div>
</template>

<script>
import {
  listCompany,
  getCompany,
  delCompany,
  addCompany,
  updateCompany,
  transfer
} from '@/api/promotion/company'
import FileResSelector from '@/components/FileResSelector/index.vue'
import TransferUser from '@/components/TransferUser/index.vue'
import DeptTreeSelect from '@/components/DeptTreeSelect/DeptTreeSelect.vue'
import { mapGetters } from 'vuex'
import { loadPageSize } from '@/utils/beforeList'

export default {
  name: 'Company',
  components: { DeptTreeSelect, TransferUser, FileResSelector },
  dicts: ['industry_type', 'platform_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 店铺信息表格数据
      companyList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        industryType: null,
        shopName: null,
        taxNumber: null,
        legalPerson: null,
        contact: null,
        deptIds: [],
        createBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        platform: [
          { required: true, message: '平台类型不能为空', trigger: 'blur' }
        ],
        shopName: [
          { required: true, message: '店铺名称不能为空', trigger: 'blur' }
        ],
        shopUrl: [
          { required: true, message: '店铺链接不能为空', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '公司名称不能为空', trigger: 'blur' }
        ],
        businessLicense: [
          { required: true, message: '营业执照不能为空', trigger: 'blur' }
        ],
        sellerNick: [
          { required: true, message: '卖家昵称不能为空', trigger: 'blur' }
        ],
        sellerId: [
          { required: true, message: '卖家ID不能为空', trigger: 'blur' }
        ],
        shopId: [
          { required: true, message: '店铺ID不能为空', trigger: 'blur' }
        ]
      },
      // 提交按钮loading
      submitLoading: false,

      transferVisible: false
    }
  },
  computed: {
    ...mapGetters(['tableHeight']),
    platformTypes() {
      return this.dict.type.platform_type.filter(item => item.value !== '2')
    }
  },
  created() {
    loadPageSize(this.queryParams)
    // this.getList()
  },
  methods: {
    /** 查询店铺信息列表 */
    getList() {
      this.loading = true
      listCompany(this.queryParams).then((response) => {
        this.companyList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        industryType: null,
        companyName: null,
        taxNumber: null,
        legalPerson: null,
        contact: null,
        registeredAddress: null,
        businessLicense: null,
        sellerNick: null,
        sellerId: null,
        shopId: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleTransfer() {
      this.transferVisible = true
    },
    handleTransferSelect(userId) {
      this.transferVisible = false
      transfer({
        ids: this.ids,
        userId
      }).then((response) => {
        this.$message({
          message: '移交成功',
          type: 'success'
        })
        this.getList()
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加店铺信息'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCompany(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改店铺信息'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.submitLoading = true
          const data = { ...this.form }
          if (data.platform !== 9) {
            data.shopId = ''
            data.sellerNick = ''
            data.sellerId = ''
          }
          if (data.id != null) {
            updateCompany(data)
              .then((response) => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.submitLoading = false
              })
          } else {
            addCompany(data)
              .then((response) => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.submitLoading = false
              })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除店铺信息编号为"' + ids + '"的数据项？')
        .then(function() {
          return delCompany(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    handleFormPlatformChange(val) {
      this.rules.businessLicense[0].required = val !== 8
    }
  }
}
</script>

<script setup>
import { getCurrentInstance, ref } from 'vue'
import useStoreTableScroller from '@/hooks/useStoreTableScroller'
import useDeptOptions, {
  getDefaultIds, setDefaultIds
} from '@/components/DeptTreeSelect/useDeptOptions'
import SavedSearches from '@/components/SavedSearches/index.vue'

const tableRef = ref(null)
useStoreTableScroller(tableRef)

const self = getCurrentInstance().proxy
const deptOptions = useDeptOptions(() => {
  setDefaultIds(self)
  self.getList()
})
</script>

<style lang="scss" scoped>
.hint {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 10px;
  margin-left: 10px;
  line-height: 14px;
}
</style>
