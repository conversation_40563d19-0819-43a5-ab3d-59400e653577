<template>
  <div>
    <el-dialog
      title="评论模板"
      :visible.sync="joinVisible"
      width="500px"
      top="10vh"
      append-to-body
    >
      <el-form v-if="joinVisible" ref="joinForm" :model="joinForm" label-width="80px">
        <el-form-item label="评控状态" prop="controlStatus">
          <el-radio-group v-model="joinForm.controlStatus">
            <el-radio-button label="0">
              {{
                mediaType === "4" ? "开启评论" : "未启用"
              }}
            </el-radio-button>
            <el-radio-button label="1">
              {{ mediaType === "4" ? "关闭评论" : "隐藏评论" }}
              <el-tooltip
                effect="dark"
                :content="`${
                  mediaType === '4'
                    ? '仅显示精选评论'
                    : '开启之后的评论会被隐藏'
                }`"
                placement="top"
              >
                <i class="el-icon-question color-primary" /> </el-tooltip></el-radio-button>
            <el-radio-button
              v-if="mediaType !== '4'"
              label="2"
            >自动回复评论
              <el-tooltip
                effect="dark"
                content="开启之后根据模板自动回复评论"
                placement="top"
              >
                <i class="el-icon-question color-primary" />
              </el-tooltip>
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-show="joinForm.controlStatus === '2'"
          label="优化模板"
          prop="templateIds"
        >
          <el-select
            v-model="joinForm.templateIds"
            multiple
            collapse-tags
            placeholder="请选择优化模板"
            style="width: 100%"
          >
            <el-option
              v-for="item in templateList"
              :key="item.id"
              :label="item.templateName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="mediaType !== '4'"
          v-show="joinForm.controlStatus != '0'"
          label="隐藏评论"
          prop="templateIds"
        >
          <el-checkbox-group v-model="commentList">
            <el-checkbox
              v-for="dict in dict.type.emotion_type"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="submitJoin"
        >确 定</el-button>
        <el-button @click="cancelJoin">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getJoinDetails,
  getTemplateList,
  saveCommentJoin
} from '@/api/system/template'
export default {
  props: {
    // 广告主id
    advertiserId: {
      type: [Number, String],
      default: 1
    },
    // row
    list: {
      type: Object,
      default: null
    },
    // 媒体类型
    mediaType: {
      type: [String, Number],
      default: 1
    },
    // 是否显示弹窗
    value: {
      type: Boolean,
      default: false
    }
  },
  dicts: ['emotion_type'],
  data() {
    return {
      joinForm: {
        advertiserId: null,
        templateIds: [],
        controlStatus: 0
      },
      submitLoading: false,
      // joinVisible: false,
      commentList: [],
      templateList: []
    }
  },
  computed: {
    joinVisible: {
      get(val) {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    joinVisible(val) {
      if (val) {
        this.init()
      } else {
        this.resetForm()
      }
    }
  },
  mounted() {

  },
  methods: {
    init() {
      this.getTemplateList()
      // this.isbatchJoin = Array.isArray(row)
      // if (this.isbatchJoin) {
      //   this.joinForm.advertiserId = this.advertiserId
      //   this.joinForm.controlStatus = '0'
      //   this.joinForm.templateIds = []
      //   this.commentList = []
      //   this.joinVisible = true

      //   if (this.mediaType !== '4') {
      //     this.commentList = this.dict.type.emotion_type.map(
      //       (item) => item.value
      //     )
      //   }
      // } else {
      getJoinDetails({ advertiserId: this.advertiserId, mediaType: this.mediaType }).then((data) => {
        this.joinForm = data.data
        if (this.mediaType !== '4') {
          if (!this.joinForm.commentList) {
            this.commentList = this.dict.type.emotion_type.map(
              (item) => item.value
            )
          } else this.commentList = this.joinForm.commentList
        }
        this.joinForm.advertiserId = this.advertiserId
      })
      // }
    },
    resetForm() {
      this.joinForm.controlStatus = 0
      this.commentList = []
      this.joinForm.templateIds = []
      this.joinForm.advertiserId = null
    },
    getTemplateList() {
      getTemplateList().then((response) => {
        this.templateList = response.rows
      })
    },
    submitJoin() {
      if (
        this.joinForm.controlStatus === '2' &&
        !this.joinForm.templateIds.length
      ) {
        this.$modal.msgError('请选择模板')
        return
      }

      const postData = {
        advertiserIds: Array.isArray(this.joinForm.advertiserId)
          ? this.joinForm.advertiserId
          : [this.joinForm.advertiserId],
        templateIds: this.joinForm.templateIds,
        controlStatus: this.joinForm.controlStatus,
        mediaType: +this.mediaType
      }
      if (this.mediaType !== '4') {
        if (this.joinForm.controlStatus !== '0') {
          if (this.commentList.length === 0) {
            this.$modal.msgError('请选择评论类型')
            return
          }
          postData.commentList = this.commentList
        }
      }

      this.submitLoading = true
      saveCommentJoin(postData)
        .then((data) => {
          this.$modal.msgSuccess('提交成功')
          this.joinVisible = false
          this.$emit('Refresh')
        })
        .finally(() => {
          this.submitLoading = false
        })
    },
    cancelJoin() { this.joinVisible = false }
  }
}
</script>

<style lang="scss" scoped>

</style>
