<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
      @submit.native.prevent
    >
      <el-form-item prop="advertiserId" label="账户ID：">
        <el-input v-model.trim="queryParams.advertiserId" placeholder="请输入账户ID" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="content" label="评论内容" label-width="88px">
        <el-input v-model.trim="queryParams.content" maxlength="10" placeholder="请输入评论内容" clearable show-word-limit @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="item_ids" label="广告视频ID" label-width="88px">
        <el-input v-model.trim="queryParams.item_ids" placeholder="请输入广告视频ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="levelType" label="评论层级">
        <el-select
          v-model="queryParams.levelType"
          placeholder="请选择"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in levelTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="hideStatus" label="隐藏状态">
        <el-select
          v-model="queryParams.hideStatus"
          placeholder="请选择"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in hideStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="emotionType" label="情感类型">
        <el-select
          v-model="queryParams.emotionType"
          placeholder="请选择"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in emotionTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="commentType" label="内容类型">
        <el-select
          v-model="queryParams.commentType"
          placeholder="请选择"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in commentTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="dateRange" label="评论时间">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :picker-options="pickerOptions"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          storage-key="jl-comment"
          @search="handleQuery"
        />
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          :disabled="multiple"
          @click="replyHandle()"
        >批量回复</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          icon="el-icon-sort"
          size="mini"
          :disabled="multiple"
          @click="handleHide()"
        >批量隐藏</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-s-tools"
          size="mini"
          @click="handleJoinComment()"
        >自动屏控</el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="tableList"
        row-key="rowKey"
        lazy
        stripe
        border
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" :selectable="selectable" />
        <template v-for="(c, i) in columns">
          <!-- 评论ID -->
          <template v-if="c.visible !== false">
            <el-table-column
              v-if="c.prop === 'comment_id'"
              :key="i"
              align="center"
              :label="c.label"
              :width="c.width"
              fixed="left"
            >
              <template slot-scope="scope">
                {{ scope.row.comment_id }}
              </template>
            </el-table-column>
            <!-- 评论内容 -->
            <el-table-column
              v-else-if="c.prop === 'text'"
              :key="i"
              align="center"
              :label="c.label"
              :width="c.width"
              fixed="left"
            >
              <template slot-scope="scope">
                <!-- {{ scope.row.text }} -->
                <BaseInfoCell :name="scope.row.text" />
              </template>
            </el-table-column>

            <!-- 视频标题 -->
            <el-table-column
              v-else-if="c.prop === 'item_title'"
              :key="i"
              align="center"
              :label="c.label"
              :width="c.width"
            >
              <template slot-scope="scope">
                <BaseInfoCell :name="scope.row.item_title" no-copy />
              </template>
            </el-table-column>
            <!-- 处理状态 -->
            <el-table-column
              v-else-if="c.prop === 'is_replied'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
              fixed="left"
            >
              <template slot-scope="scope">
                {{ scope.row.is_replied ? "是" : "否" }}
              </template>
            </el-table-column>
            <!-- 评论层级 -->
            <el-table-column
              v-else-if="c.prop === 'level_type'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ filterName(scope.row.level_type, levelTypeOptions) }}
              </template>
            </el-table-column>
            <!-- 隐藏状态 -->
            <el-table-column
              v-else-if="c.prop === 'hide_status'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ filterName(scope.row.hide_status, hideStatusOptions) }}
              </template>
            </el-table-column>
            <!-- 情感类型 -->
            <el-table-column
              v-else-if="c.prop === 'emotion_type'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ filterName(scope.row.emotion_type, emotionTypeOptions) }}
              </template>
            </el-table-column>
            <!-- 内容类型 -->
            <el-table-column
              v-else-if="c.prop === 'comment_type'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ filterName(scope.row.comment_type, commentTypeOptions) }}
              </template>
            </el-table-column>
            <!-- 是否置顶 -->
            <el-table-column
              v-else-if="c.prop === 'is_stick'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ scope.row.is_stick ? "是" : "否" }}
              </template>
            </el-table-column>
            <CustomTableColumn
              v-else
              :key="i"
              :sortable="c.sortable ? c.sortable : false"
              :data="c"
              :render-map="renderMap"
            />
          </template>
        </template>
        <el-table-column label="操作" align="center" fixed="right" :width="140">
          <template slot-scope="scope">
            <!-- <el-button
              v-if="scope.row.is_stick !== 1&&scope.row.hide_status ==='NOT_HIDE'"
              type="text"
              size="mini"
              @click="TopUpHandle(scope.row, 'top')"
            >置顶</el-button>
            <el-button
              v-if="scope.row.is_stick === 1&&scope.row.hide_status ==='NOT_HIDE'"
              type="text"
              size="mini"
              @click="TopUpHandle(scope.row, 'cancelTop')"
            >取消置顶</el-button> -->
            <el-button
              v-if="scope.row.hide_status ==='NOT_HIDE'&&scope.row.level_type ==='LEVEL_ONE'"
              type="text"
              size="mini"
              @click="replyHandle(scope.row)"
            >回复</el-button>
            <el-button
              v-if="scope.row.hide_status ==='NOT_HIDE'"
              type="text"
              size="mini"
              @click="handleHide(scope.row)"
            >隐藏</el-button>

          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <commentTemplate v-model="commentVisible" :media-type="mediaType" :list="commentlist" :advertiser-id="queryParams.advertiserId" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { mapGetters } from 'vuex'
import {
  getTableList,
  setTop,
  commentReply,
  commentHide
} from '@/api/promotion/comment'

import { loadPageSize } from '@/utils/beforeList'
import { dateRangePickerOptions } from '@/config'
import commentTemplate from '@/views/promotion/comment/components/commentTemplate.vue'
export default {
  name: 'JlComment',
  components: { BaseInfoCell, commentTemplate },
  props: {
    // 	广告主id
    advertiserId: {
      type: [String, Number],
      default: ''
    },
    // 媒体类型
    mediaType: {
      type: [String, Number],
      default: 1
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      templateLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // isReplied: '', // 处理状态
        advertiserId: '', // 账户ID
        content: '', // 评论内容
        item_ids: '', // 广告视频id
        levelType: 'LEVEL_ALL', // 评论层级
        hideStatus: 'ALL', // 隐藏状态
        emotionType: '', // 情感类型
        commentType: '', // 内容类型
        ad_ids: '', // 计划id
        dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      },
      pickerOptions: dateRangePickerOptions,
      isRepliedOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      levelTypeOptions: [
        {
          label: '全部',
          value: 'LEVEL_ALL'
        },
        {
          label: '一级评论',
          value: 'LEVEL_ONE'
        },
        {
          label: '二级评论',
          value: 'LEVEL_TWO'
        }
      ],
      hideStatusOptions: [
        {
          label: '全部',
          value: 'ALL'
        },
        {
          label: '已隐藏',
          value: 'HIDE'
        },
        {
          label: '未隐藏',
          value: 'NOT_HIDE'
        }
      ],
      emotionTypeOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '负向评论',
          value: 'NEGATIVE'
        },
        {
          label: '中性评论',
          value: 'NEUTRAL'
        },
        {
          label: '正向评论',
          value: 'POSITIVE'
        }
      ],
      commentTypeOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '图片评论',
          value: 'IMAGE_COMMENT'
        },
        {
          label: '图文评论',
          value: 'IMAGE_TEXT_COMMENT'
        },
        {
          label: '文字评论',
          value: 'TEXT_COMMENT'
        }
      ],
      // 评论控制弹窗
      commentVisible: false,
      // 当前选中的列
      commentlist: null
    }
  },
  computed: {
    ...mapGetters(['device', 'permissions', 'roles', 'tableHeight'])
  },
  created() {
    this.queryParams.advertiserId = this.advertiserId
    loadPageSize(this.queryParams)
    this.getList()
  },
  methods: {
    /**
     *
     * @param val 需要找寻的值
     * @param arr 找寻范围的数组
     */
    filterName(val, arr) {
      if (val) {
        const obj = arr.find((item) => item.value === val)
        return obj?.label || ''
      }
    },
    /** 查询模板列表 */
    getList() {
      if (this.isOverTime()) {
        return
      }
      this.loading = true
      const data = {
        page: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        mediaType: this.mediaType, // 媒体类型
        advertiserId: this.queryParams.advertiserId
      }
      if (this.queryParams.dateRange && this.queryParams.dateRange.length > 0) {
        data.startTime = this.queryParams.dateRange[0]
        data.endTime = this.queryParams.dateRange[1]
      }
      const obj = {
        // isReplied: this.queryParams.isReplied,
        levelType: this.queryParams.levelType,
        hideStatus: this.queryParams.hideStatus,
        commentType: this.queryParams.commentType,
        emotionType: this.queryParams.emotionType,
        content: this.queryParams.content
      }
      if (this.queryParams.ad_ids) {
        obj['adIds'] = [this.queryParams.ad_ids]
      }
      if (this.queryParams.item_ids) {
        obj['itemIds'] = [this.queryParams.item_ids]
      }
      const filtering = Object.fromEntries(
        Object.entries(obj).filter(([key, value]) => value && value.length)
      )

      if (Object.keys(filtering).length) {
        data.filtering = filtering
      }

      getTableList(data).then((response) => {
        this.tableList = response?.data?.comment_list || []
        this.total = response?.data?.page_info?.total_number || 0
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.comment_id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加模板'
    },
    TopUpHandle(row, name) {
      let text = ''
      const data = {
        advertiserId: this.queryParams.advertiserId,
        commentId: row.comment_id,
        mediaType: this.mediaType // 媒体类型
      }
      let messageText = ''
      if (name === 'cancelTop') {
        text = '确认将该条评论取消置顶?'
        data['stickType'] = 'CANCEL_STICK'
        messageText = '取消置顶成功!'
      } else {
        text = '确认将该条评论置顶?'
        data['stickType'] = 'STICK_ON_TOP'
        messageText = '置顶成功!'
      }

      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          setTop(data).then((res) => {
            if (res.code === 200 && res?.data?.data) {
              this.$message({
                type: 'success',
                message: messageText
              })
              this.getList()
            } else {
              this.$message({
                type: 'error',
                message: res?.data?.message
              })
            }
          })
        })
        .catch(() => {})
    },
    // 回复&批量回复
    replyHandle(row) {
      const data = {
        advertiserId: this.queryParams.advertiserId,
        mediaType: this.mediaType // 媒体类型
      }
      if (row) {
        data['commentIds'] = [row.comment_id]
      } else {
        data['commentIds'] = this.ids
      }
      if (data['commentIds'].length > 20) {
        this.$alert(
          '仅支持同时操作小于等于20个评论',
          '提示',
          {
            confirmButtonText: '确定',
            callback: (action) => {}
          }
        )
        return
      }
      this.$prompt('请输入回复内容', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value) {
            return '输入不能为空'
          } else if (value.length > 100) {
            return '输入内容不能大于100个字'
          }
        }
      })
        .then(({ value }) => {
          data['replyText'] = value
          commentReply(data).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '回复成功'
              })
              // setTimeout(() => {
              this.getList()
              // }, 1500)
            } else {
              this.$message({
                type: 'error',
                message: res?.msg
              })
            }
          })
        })
        .catch(() => {})
    },
    selectable(row) {
      return row.hide_status === 'NOT_HIDE' && row.level_type === 'LEVEL_ONE'
    },
    // 隐藏&批量隐藏
    handleHide(row) {
      const data = {
        advertiserId: this.queryParams.advertiserId,
        mediaType: this.mediaType // 媒体类型
      }
      if (row) {
        data['commentIds'] = [row.comment_id]
      } else {
        data['commentIds'] = this.ids
      }
      this.$confirm('确认将所选评论隐藏?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        commentHide(data).then((res) => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '隐藏成功！'
            })
            this.getList()
          } else {
            this.$message({
              type: 'error',
              message: res?.msg
            })
          }
        })
      }).catch(() => {

      })
    },
    isOverTime() {
      const overTime =
        dayjs(this.queryParams.dateRange[1]).diff(dayjs(this.queryParams.dateRange[0]), 'days') + 1 >
        31
      if (overTime) {
        this.$message.error('时间范围不能超过31天, 请重新选择')
      }
      return overTime
    },
    handleJoinComment() {
      this.commentVisible = true
    }
  }
}
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import TransferUser from '@/components/TransferUser/index.vue'
import { transfer } from '@/api/system/template'
import { Message, MessageBox } from 'element-ui'
import useColumns from '@/hooks/useColumns'
import { useRoute, useRouter } from 'vue-router/composables'
import {
  ControlStatusRender,
  MaxDangerRate,
  PercentValue
} from '@/utils/render'
import useStoreTableScroller from '@/hooks/useStoreTableScroller'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
const renderMap = {
  controlStatus: (row) => {
    return ControlStatusRender(row)
  },
  chargeBackRate: (row) => {
    return MaxDangerRate(row.chargeBackRate)
  }
}
const defaultColumns = [
  {
    label: `评论ID`,
    prop: 'comment_id',
    width: '180',
    sortable: false
  },
  {
    label: `评论内容`,
    prop: 'text',
    width: '180'
  },
  // {
  //   label: `处理状态(无)`,
  //   prop: 'isReplied',
  //   width: '120'
  // },
  {
    label: `评论层级`,
    prop: 'level_type',
    width: '120'
    // overflow: true
  },
  {
    label: `隐藏状态`,
    prop: 'hide_status',
    width: '120'
  },
  {
    label: `评论用户`,
    prop: 'aweme_name',
    width: '120'
  },
  {
    label: `相关评论数`,
    prop: 'reply_count',
    width: '120',
    sortable: true
  },
  {
    label: `点赞数`,
    prop: 'like_count',
    width: '160',
    sortable: true
    // overflow: true
  },
  {
    label: `情感类型`,
    prop: 'emotion_type',
    width: '120'
  },
  {
    label: `评论类型`,
    prop: 'comment_type',
    width: '120'
  },
  {
    label: `评论时间`,
    prop: 'create_time',
    width: '160',
    sortable: true
  },
  // {
  //   label: `视频ID（无）`,
  //   prop: 'materialId',
  //   width: '120'
  // },
  {
    label: `视频标题`,
    prop: 'item_title',
    width: '180'
  },
  {
    label: `广告视频ID`,
    prop: 'item_id',
    width: '180'
  },
  {
    label: `广告id`,
    prop: 'promotion_id',
    width: '200'
  },
  // {
  //   label: `评论来源创意（无）`,
  //   prop: 'settlementVariance',
  //   width: '120'
  // },
  {
    label: `是否置顶`,
    prop: 'is_stick',
    width: '120'
  }

  // {
  //   label: `来源计划名称（无）`,
  //   prop: 'settlementVariance',
  //   width: '120'
  // }
]
const tableRef = ref(null)
const {
  columnsInstance,
  columns,
  operatedColumns,
  customList,
  handleHeaderDragend
} = useColumns({
  defaultColumns,
  tableRef,
  name: useRoute().name + '_jlComment'
})
useStoreTableScroller(tableRef)

</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-bottom: 10px;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
</style>
