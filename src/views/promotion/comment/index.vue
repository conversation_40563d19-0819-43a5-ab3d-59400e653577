<template>
  <el-tabs v-model="activeName" style="margin-top: 0px" @tab-click="handleTabClick">
    <el-tab-pane v-if="mediaType==='1'" name="jlComment" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="table" />巨量引擎</div>
      </template>
      <jl-comment media-type="1" :advertiser-id="advertiserId" />
    </el-tab-pane>
    <el-tab-pane v-else-if="mediaType==='2'" name="clComment" lazy>
      <template slot="label">
        <div style="padding: 0 60px"><svg-icon icon-class="form" />快手</div>
      </template>
      <clComment media-type="2" :advertiser-id="advertiserId" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import jlComment from './jlComment.vue'
import clComment from './clComment.vue'
export default {
  name: 'Comment',
  components: {
    jlComment, clComment
  },
  data() {
    return {
      activeName: 'account',
      loading: false,
      advertiserId: null,
      mediaType: null,
      authList: { '1': 'jlComment',
        '2': 'clComment' }
    }
  },
  created() {
    this.setTabs()
  },
  activated() {
    this.setTabs()
  },
  methods: {
    setTabs() {
      const query = { ...this.$route.query }
      if (query && query.advertiserId) {
        this.advertiserId = query.advertiserId
        this.mediaType = query.mediaType
        this.activeName = this.authList[query.mediaType]
      }
    },
    handleTabClick() {
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__item{
  height: 42px;
  line-height: 42px;
  font-size: 16px;
  font-weight: bold;
}
</style>
