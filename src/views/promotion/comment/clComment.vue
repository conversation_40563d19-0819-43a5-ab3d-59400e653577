<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="88px"
      @submit.native.prevent
    >
      <el-form-item prop="advertiserId" label="账户ID：">
        <el-input v-model.trim="queryParams.advertiserId" placeholder="请输入账户ID" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="commentContent" label="评论内容：">
        <el-input v-model.trim="queryParams.commentContent" placeholder="请输入内容" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="replyStatus" label="处理状态：">
        <el-select
          v-model="queryParams.replyStatus"
          placeholder="请选择"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in replyStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="commentLevel" label="评论层级：">
        <el-select
          v-model="queryParams.commentLevel"
          placeholder="请选择"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in commentLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="shieldStatus" label="隐藏状态：">
        <el-select
          v-model="queryParams.shieldStatus"
          placeholder="请选择"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in hideStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item prop="dateRange" label="评论时间：">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :picker-options="pickerOptions"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          storage-key="cl-comment"
          @search="handleQuery"
        />
        <el-button
          icon="el-icon-refresh"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          :disabled="multiple"
          @click="replyHandle()"
        >批量回复</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          icon="el-icon-sort"
          size="mini"
          :disabled="multiple"
          @click="handleHide()"
        >批量隐藏</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-s-tools"
          size="mini"
          @click="handleJoinComment()"
        >自动屏控</el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="tableList"
        row-key="rowKey"
        lazy
        stripe
        border
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" :selectable="selectable" />
        <template v-for="(c, i) in columns">
          <!-- 评论ID -->
          <template v-if="c.visible !== false">
            <el-table-column
              v-if="c.prop === 'comment_id'"
              :key="i"
              align="center"
              :label="c.label"
              :width="c.width"
              fixed="left"
            >
              <template slot-scope="scope">
                {{ scope.row.comment_id }}
              </template>
            </el-table-column>
            <!-- 评论内容 -->
            <el-table-column
              v-else-if="c.prop === 'comment_content'"
              :key="i"
              align="center"
              :label="c.label"
              :width="c.width"
              fixed="left"
            >
              <template slot-scope="scope">

                <BaseInfoCell :name="scope.row.comment_content" />
              </template>
            </el-table-column>
            <!-- 处理状态 -->
            <el-table-column
              v-else-if="c.prop === 'reply_status'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ scope.row.reply_status==2 ? "已回复" : "未回复" }}
              </template>
            </el-table-column>
            <!-- 评论层级 -->
            <el-table-column
              v-else-if="c.prop === 'comment_level'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ filterName(scope.row.comment_level, commentLevelOptions) }}
              </template>
            </el-table-column>
            <!-- 隐藏状态 -->
            <el-table-column
              v-else-if="c.prop === 'shield_status'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ filterName(scope.row.shield_status, hideStatusOptions) }}
              </template>
            </el-table-column>
            <!-- 是否置顶 -->
            <el-table-column
              v-else-if="c.prop === 'is_top_comment'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                {{ scope.row.is_top_comment ? "是" : "否" }}
              </template>
            </el-table-column>
            <!-- 评论时间 -->
            <el-table-column
              v-else-if="c.prop === 'post_time'"
              :key="i"
              align="center"
              :label="c.label"
              :min-width="c.width"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.post_time">
                  {{ dayjs(parseInt(scope.row.post_time)).format('YYYY-MM-DD HH:mm:ss') }}
                </span>
              </template>
            </el-table-column>
            <CustomTableColumn
              v-else
              :key="i"
              :sortable="c.sortable ? c.sortable : false"
              :data="c"
              :render-map="renderMap"
            />
          </template>
        </template>
        <el-table-column label="操作" align="center" fixed="right" :width="140">
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.is_top_comment&&scope.row.shield_status ===1"
              type="text"
              size="mini"
              @click="TopUpHandle(scope.row, 'top')"
            >置顶</el-button>
            <el-button
              v-if="scope.row.is_top_comment&&scope.row.shield_status ===1"
              type="text"
              size="mini"
              @click="TopUpHandle(scope.row, 'cancelTop')"
            >取消置顶</el-button>

            <el-button
              v-if="scope.row.shield_status !==2&&scope.row.reply_status ===1&&scope.row.comment_level===1"
              type="text"
              size="mini"
              @click="replyHandle(scope.row)"
            >回复</el-button>
            <el-button
              v-if="scope.row.shield_status ===1"
              type="text"
              size="mini"
              @click="handleHide(scope.row)"
            >隐藏</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <commentTemplate v-model="commentVisible" :media-type="mediaType" :list="commentlist" :advertiser-id="queryParams.advertiserId" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import {
  getTableList,
  commentReply,
  commentHide, setTop
} from '@/api/promotion/comment'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { loadPageSize } from '@/utils/beforeList'
import { dateRangePickerOptions } from '@/config'
import commentTemplate from '@/views/promotion/comment/components/commentTemplate.vue'
export default {
  name: 'ClComment',
  components: { BaseInfoCell },
  props: {
    // 	广告主id
    advertiserId: {
      type: [String, Number],
      default: ''
    },
    // 媒体类型
    mediaType: {
      type: [String, Number],
      default: 2
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      templateLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        advertiserId: '', // 账户ID
        commentContent: '', // 评论内容
        replyStatus: '', // 处理状态
        commentLevel: '', // 评论层级
        shieldStatus: '', // 隐藏状态
        dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      },
      pickerOptions: dateRangePickerOptions,
      replyStatusOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未回复',
          value: 1
        },
        {
          label: '已回复',
          value: 2
        }
      ],
      commentLevelOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '一级评论',
          value: 1
        },
        {
          label: '二级评论',
          value: 2
        }
      ],
      hideStatusOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未隐藏',
          value: 1
        },
        {
          label: '已隐藏',
          value: 2
        }
      ],
      // 评论控制弹窗
      commentVisible: false,
      commentlist: null

    }
  },
  computed: {
    ...mapGetters(['device', 'permissions', 'roles', 'tableHeight'])
  },
  created() {
    this.queryParams.advertiserId = this.advertiserId
    loadPageSize(this.queryParams)
    this.getList()
  },
  methods: {
    /**
     *
     * @param val 需要找寻的值
     * @param arr 找寻范围的数组
     */
    filterName(val, arr) {
      if (val) {
        const obj = arr.find((item) => item.value === val)
        return obj?.label || ''
      }
    },
    /** 查询模板列表 */
    getList() {
      this.loading = true
      const data = {
        page: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        mediaType: this.mediaType, // 媒体类型
        advertiserId: this.queryParams.advertiserId,
        commentContent: this.queryParams.commentContent,
        replyStatus: this.queryParams.replyStatus,
        commentLevel: this.queryParams.commentLevel,
        shieldStatus: this.queryParams.shieldStatus

      }
      if (this.queryParams.dateRange && this.queryParams.dateRange.length > 0) {
        data.postTimeStart = this.queryParams.dateRange[0]
        data.postTimeEnd = this.queryParams.dateRange[1]
      }

      getTableList(data).then((response) => {
        this.tableList = response?.data?.details || []
        this.total = response?.data?.page_info?.total_number || 0
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加模板'
    },
    TopUpHandle(row, name) {
      let text = ''
      const data = {
        advertiserId: this.queryParams.advertiserId,
        commentId: row.comment_id,
        mediaType: this.mediaType, // 媒体类型
        photoId: row.photo_id
      }
      let messageText = ''

      if (name === 'cancelTop') {
        text = '确认将该条评论取消置顶?'
        messageText = '取消置顶成功!'
        data['stickType'] = 'CANCEL_STICK'
      } else {
        text = '确认将该条评论置顶?'
        messageText = '置顶成功!'
        data['stickType'] = 'STICK_ON_TOP'
      }

      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          setTop(data).then((res) => {
            if (res.code === 200 && res?.data?.code === 0) {
              this.$message({
                type: 'success',
                message: messageText
              })
              this.getList()
            } else {
              this.$message({
                type: 'error',
                message: res?.data?.message
              })
            }
          })
        })
        .catch(() => {})
    },
    // 回复&批量回复
    replyHandle(row) {
      this.$prompt('请输入回复内容', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value) {
            return '输入不能为空'
          } else if (value.length > 100) {
            return '输入内容不能大于100个字'
          }
        }
      })
        .then(({ value }) => {
          const data = {
            advertiserId: this.queryParams.advertiserId,
            mediaType: this.mediaType // 媒体类型
          }
          if (row) {
            data['replyList'] = [{
              'reply_to_comment_id': row.comment_id,
              'photo_id': row.photo_id,
              'photo_author_id': row.photo_author_id,
              'reply_to_user_id': row.comment_author_id,
              'reply_content': value
            }]
          } else {
            data['replyList'] = []
            this.ids.forEach(item => {
              data['replyList'].push({
                'reply_to_comment_id': item.comment_id,
                'photo_id': item.photo_id,
                'photo_author_id': item.photo_author_id,
                'reply_to_user_id': item.comment_author_id,
                'reply_content': value
              })
            })
          }
          commentReply(data).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '回复成功'
              })

              this.getList()
            } else {
              this.$message({
                type: 'error',
                message: res?.msg
              })
            }
          })
        })
        .catch(() => {})
    },
    selectable(row) {
      return row.reply_status === 1 && row.shield_status === 1
    },
    // 隐藏&批量隐藏
    handleHide(row) {
      const data = {
        advertiserId: this.queryParams.advertiserId,
        mediaType: this.mediaType // 媒体类型
      }
      if (row) {
        data['shieldList'] = [{
          'comment_id': row.comment_id,
          'photo_id': row.photo_id
        }]
      } else {
        data['shieldList'] = []
        this.ids.forEach(item => {
          data['shieldList'].push({
            'comment_id': item.comment_id,
            'photo_id': item.photo_id
          })
        })
      }
      this.$confirm('确认将所选评论隐藏?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        commentHide(data).then((res) => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '隐藏成功！'
            })
            this.getList()
          } else {
            this.$message({
              type: 'error',
              message: res?.msg
            })
          }
        })
      }).catch(() => {

      })
    },
    handleJoinComment() {
      this.commentVisible = true
    }
  }
}
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import TransferUser from '@/components/TransferUser/index.vue'
import { transfer } from '@/api/system/template'
import { Message, MessageBox } from 'element-ui'
import useColumns from '@/hooks/useColumns'
import { useRoute, useRouter } from 'vue-router/composables'
import {
  ControlStatusRender,
  MaxDangerRate,
  PercentValue
} from '@/utils/render'
import useStoreTableScroller from '@/hooks/useStoreTableScroller'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
const renderMap = {
  controlStatus: (row) => {
    return ControlStatusRender(row)
  },
  chargeBackRate: (row) => {
    return MaxDangerRate(row.chargeBackRate)
  }
}
const defaultColumns = [
  {
    label: `评论ID`,
    prop: 'comment_id',
    width: '180',
    sortable: false
  },
  {
    label: `评论内容`,
    prop: 'comment_content',
    width: '180'
  },
  {
    label: `处理状态`,
    prop: 'reply_status',
    width: '120'
  },
  {
    label: `评论层级`,
    prop: 'comment_level',
    width: '120'
    // overflow: true
  },
  {
    label: `隐藏状态`,
    prop: 'shield_status',
    width: '120'
  },

  {
    label: `评论用户`,
    prop: 'nickname',
    width: '120'
  },
  {
    label: `点赞数`,
    prop: 'fav_num',
    width: '120',
    sortable: true
  },

  {
    label: `评论时间`,
    prop: 'post_time',
    width: '160',
    sortable: true
  },
  {
    label: `视频ID`,
    prop: 'photo_id',
    width: '200'
  },
  // {
  //   label: `视频标题`,
  //   prop: 'item_title',
  //   width: '180'
  // },
  // {
  //   label: `评论来源计划ID（1.0）`,
  //   prop: 'item_id',
  //   width: '180'
  // },
  // {
  //   label: `评论来源创意（无）`,
  //   prop: 'settlementVariance',
  //   width: '120'
  // },
  {
    label: `是否置顶`,
    prop: 'is_top_comment',
    width: '120'
  }
  // {
  //   label: `评论来源计划ID（2.0）`,
  //   prop: 'promotion_id',
  //   width: '180'
  // }
  // {
  //   label: `来源计划名称（无）`,
  //   prop: 'settlementVariance',
  //   width: '120'
  // }
]
const tableRef = ref(null)
const {
  columnsInstance,
  columns,
  operatedColumns,
  customList,
  handleHeaderDragend
} = useColumns({
  defaultColumns,
  tableRef,
  name: useRoute().name + '_clComment'
})
useStoreTableScroller(tableRef)

</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-bottom: 10px;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
</style>
