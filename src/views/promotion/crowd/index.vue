<script setup>
import { ref } from 'vue'
import { useStore } from '@/store'
import router from '@/router'
import dayjs from 'dayjs'
import { Message, MessageBox } from 'element-ui'
import useDicts from '@/hooks/useDicts'
import { getIndustryList } from '@/api/promotion/crowd'
import { exportCrowd } from '@/api/data/crowd'
import DeptTreeSelect from '@/components/DeptTreeSelect/DeptTreeSelect.vue'
import useDeptOptions from '@/components/DeptTreeSelect/useDeptOptions'
import { dateRangePickerOptions } from '@/config'

const store = useStore()
const emit = defineEmits(['confirm', 'close'])

const rules = {
  lcy: [
    { required: true, type: 'array', message: '请选择匹配类型', trigger: 'blur' }
  ],
  fieldName: [
    { required: true, message: '请选择属性名', trigger: 'blur' }
  ]
}

const dateRange = ref([
  dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
  dayjs().subtract(1, 'day').format('YYYY-MM-DD')
])

const { dicts } = useDicts(['crowd_data_item'])

const disabledDate = (time) => {
  return time.getTime() > (Date.now() - 24 * 3600 * 1000)
}

const pickerOptions = {
  ...dateRangePickerOptions,
  disabledDate
}

const crowdForm = ref({
  advertiserIds: undefined,
  goodsIds: undefined,
  deptIds: [],
  priceStart: undefined,
  priceEnd: undefined,
  flcy: null,
  slcy: null,
  fieldName: null
})

const lcyOptions = ref([])
getIndustryList().then(res => {
  lcyOptions.value = res.data.map(item => {
    return {
      value: item.flcy,
      label: item.flcy,
      children: item.slcyList.map(child => {
        return {
          value: child,
          label: child
        }
      })
    }
  })
})

const clearPrice = () => {
  crowdForm.value.priceStart = undefined
  crowdForm.value.priceEnd = undefined
}

const formRef = ref(null)
const handleExport = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      const postData = {
        fieldName: crowdForm.value.fieldName,
        start: dateRange.value[0],
        end: dateRange.value[1]
      }

      if (crowdForm.value.lcy.length > 0) {
        const flcy = []
        const slcy = []
        crowdForm.value.lcy.forEach(c => {
          flcy.push(c[0])
          if (c[1]) slcy.push(c[1])
        })
        postData.flcy = Array.from(new Set(flcy)).join(',')
        postData.slcy = slcy.join(',')
      }

      if (crowdForm.value.priceStart) postData.priceStart = crowdForm.value.priceStart
      if (crowdForm.value.priceEnd) postData.priceEnd = crowdForm.value.priceEnd

      if (crowdForm.value.advertiserIds) postData.advertiserIds = crowdForm.value.advertiserIds.replace('，', ',')
      if (crowdForm.value.goodsIds) postData.goodsIds = crowdForm.value.goodsIds.replace('，', ',')

      if (crowdForm.value.deptIds?.length > 0) { postData.deptIds = crowdForm.value.deptIds }

      exportCrowd(postData).then(() => {
        Message.success('导出成功')
        emit('confirm')
      })
    }
  })
}
const close = () => {
  // tab.closePage(router.currentRoute).then(({ visitedViews }) => {
  //   toLastView(visitedViews, router.currentRoute)
  // })
  emit('close')
}

function toLastView(visitedViews, view) {
  const latestView = visitedViews.slice(-1)[0]
  if (latestView) {
    router.push(latestView.fullPath)
  } else {
    // now the default is to redirect to the home page if there is no tags-view,
    // you can adjust it according to your needs.
    if (view.name === 'Dashboard') {
      // to reload home page
      router.replace({ path: '/redirect' + view.fullPath })
    } else {
      router.push('/')
    }
  }
}

const deptOptions = useDeptOptions()
</script>

<template>
  <div class="app-container">
    <el-form ref="formRef" :model="crowdForm" :rules="rules" label-width="90px">
      <el-form-item label="账户ID" prop="advertiserIds">
        <el-input
          v-model.trim="crowdForm.advertiserIds"
          style="width: 280px"
          placeholder="使用逗号间隔可添加多个"
          clearable
        />
      </el-form-item>
      <el-form-item label="商品ID" prop="goodsIds">
        <el-input
          v-model.trim="crowdForm.goodsIds"
          style="width: 280px"
          placeholder="使用逗号间隔可添加多个"
          clearable
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptIds">
        <DeptTreeSelect v-model="crowdForm.deptIds" :options="deptOptions" :is-search="false" />
      </el-form-item>
      <el-form-item label="匹配类型" prop="fieldName">
        <el-select
          v-model="crowdForm.fieldName"
          style="width: 280px"
        >
          <el-option
            v-for="dict in dicts.crowd_data_item"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类目" prop="lcy">
        <el-cascader
          v-model="crowdForm.lcy"
          style="width: 280px"
          :options="lcyOptions"
          :props="{ checkStrictly: true, multiple: true }"
          collapse-tags
          clearable
        />
      </el-form-item>
      <el-form-item label="价格区间">
        <el-input-number v-model="crowdForm.priceStart" :min="0" :max="crowdForm.priceEnd" clearable :controls="false" style="margin-right:  10px;width: 150px" />
        -
        <el-input-number v-model="crowdForm.priceEnd" :min="crowdForm.priceStart" :controls="false" style="margin-left:  10px;width: 150px" />
        <el-button icon="el-icon-refresh" circle class="ml10" @click="clearPrice" />
      </el-form-item>

      <el-form-item label="日期范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          :popper-class="store.getters.device === 'mobile' ? 'mobile-date-picker' : ''"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :picker-options="store.getters.device === 'mobile' ? {disabledDate}: pickerOptions"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" size="mini" @click="handleExport">导出</el-button>
        <el-button type="danger" size="mini" @click="close">关闭</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<style scoped lang="scss">

</style>
