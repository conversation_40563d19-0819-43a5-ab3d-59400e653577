# 资源管理页面

这是一个全新设计的文件管理页面，采用现代化的Vue 2.7 + Element UI技术栈，并应用了iOS风格的设计。

## 功能特性

### 🎨 设计风格
- **iOS风格设计**：采用iOS设计语言，包含毛玻璃效果、圆角、阴影等
- **响应式布局**：适配桌面端、平板端和移动端
- **流畅动画**：丰富的过渡动画和交互效果

### 📁 文件管理
- **双视图模式**：网格视图和列表视图
- **文件预览**：支持图片、视频、文档预览
- **批量操作**：批量选择、删除、移动等
- **搜索功能**：实时搜索和高级搜索
- **排序筛选**：多种排序方式和筛选条件

### 📤 文件上传
- **拖拽上传**：支持文件拖拽到指定区域上传
- **批量上传**：支持多文件同时上传
- **上传进度**：实时显示上传进度和状态
- **类型限制**：根据分类自动应用文件类型限制
- **错误处理**：完善的错误处理和重试机制

### 🗂️ 分类管理
- **树形结构**：支持多级分类
- **分类操作**：创建、编辑、删除分类
- **文件统计**：显示每个分类的文件数量
- **权限控制**：共享分类和私有分类

### ⚡ 性能优化
- **虚拟滚动**：大量文件时的性能优化
- **图片懒加载**：缩略图按需加载
- **缓存策略**：智能缓存提升用户体验

## 技术架构

### 组件结构
```
ResourceManager/
├── index.vue                 # 主容器组件
├── components/
│   ├── Toolbar.vue          # 顶部工具栏
│   ├── Sidebar.vue          # 侧边栏分类树
│   ├── FileDisplay.vue      # 文件展示组件
│   ├── FileGrid.vue         # 网格视图（集成在FileDisplay中）
│   ├── FileList.vue         # 列表视图（集成在FileDisplay中）
│   ├── UploadArea.vue       # 上传区域
│   ├── FilePreview.vue      # 文件预览
│   ├── ContextMenu.vue      # 右键菜单
│   └── AdvancedSearch.vue   # 高级搜索
├── hooks/
│   ├── useFileOperations.js # 文件操作逻辑
│   ├── useUpload.js         # 上传逻辑
│   ├── useSearch.js         # 搜索逻辑
│   └── useSelection.js      # 选择逻辑
├── utils/
│   ├── fileUtils.js         # 文件工具函数
│   └── constants.js         # 常量定义
└── styles/
    └── resource.scss        # 样式文件
```

### 技术栈
- **Vue 2.7**：使用Composition API
- **Element UI**：UI组件库
- **SCSS**：样式预处理器
- **阿里云OSS**：文件存储服务

## 使用方法

### 路由配置
页面路由已配置为 `/resource/files`，需要 `promotion:fileres:list` 权限。

### API接口
复用现有的文件资源管理API：
- `listFileres` - 获取文件列表
- `delFileres` - 删除文件
- `updateFileres` - 更新文件信息
- `listCategory` - 获取分类列表
- `addCategory` - 创建分类
- `updateCategory` - 更新分类
- `delCategory` - 删除分类

### 上传组件
继续使用现有的 `AliUpload` 组件逻辑，但提供了更好的用户界面。

## 开发说明

### 环境要求
- Vue 2.7+
- Element UI
- Node.js 14+

### 开发模式
```bash
npm run dev
```

### 构建
```bash
npm run build
```

## 特色功能

### 1. iOS风格设计
- 毛玻璃效果（backdrop-filter）
- 圆角设计
- 柔和阴影
- 流畅动画

### 2. 智能文件识别
- 自动识别文件类型
- 生成缩略图
- 文件图标映射

### 3. 高级搜索
- 文件名搜索
- 文件类型筛选
- 文件大小范围
- 上传时间范围
- 快速筛选标签

### 4. 批量操作
- 多选支持
- 批量删除
- 批量移动
- 批量下载

### 5. 文件预览
- 图片预览（支持缩放、旋转）
- 视频播放
- PDF预览
- 文件信息展示

## 注意事项

1. **权限控制**：确保用户有相应的文件管理权限
2. **文件大小限制**：根据服务器配置调整上传限制
3. **浏览器兼容性**：部分CSS特性需要现代浏览器支持
4. **移动端适配**：在移动设备上提供简化的操作界面

## 文件选择器功能

### 🎯 新增功能
项目现在支持在其他页面中以弹窗形式打开文件选择器，让用户选择文件并获取文件信息。

### 📦 组件结构
```
src/views/resource/
├── components/
│   └── FileSelector.vue     # 文件选择器组件
├── utils/
│   └── fileSelector.js      # 选择器工具函数
├── demo/
│   └── FileSelectorDemo.vue # 使用演示页面
└── FILE_SELECTOR_USAGE.md   # 详细使用文档
```

### 🚀 快速使用
```javascript
import fileSelector from '@/views/resource/utils/fileSelector'

// 选择单个文件
const file = await fileSelector.selectSingle()

// 选择多个文件
const files = await fileSelector.selectMultiple()

// 选择图片文件
const images = await fileSelector.selectImages()
```

### 🎨 特性
- **多种选择模式**：单选、多选、类型限制
- **分类浏览**：按分类浏览文件
- **搜索功能**：实时搜索文件
- **双视图模式**：网格视图和列表视图
- **iOS风格设计**：与主页面保持一致

### 📖 详细文档
查看 [FILE_SELECTOR_USAGE.md](./FILE_SELECTOR_USAGE.md) 获取完整的使用指南和API文档。

### 🎮 演示页面
访问 `/resource/selector-demo` 查看文件选择器的各种使用方式。

## 后续优化

1. **虚拟滚动**：处理大量文件时的性能优化
2. **离线支持**：添加Service Worker支持
3. **快捷键**：添加更多键盘快捷键
4. **文件夹支持**：支持文件夹创建和管理
5. **版本控制**：文件版本历史管理
6. **文件选择器增强**：支持文件夹选择、批量操作等

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
