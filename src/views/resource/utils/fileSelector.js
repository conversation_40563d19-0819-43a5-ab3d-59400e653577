import Vue from 'vue'
import EnhancedFileSelector from '../components/EnhancedFileSelector.vue'



/**
 * 打开文件选择器
 * @param {Object} options - 选择器配置选项
 * @param {boolean} options.multiple - 是否多选，默认true
 * @param {Array} options.fileTypes - 文件类型限制，如['image', 'video']
 * @param {number} options.maxCount - 最大选择数量，0表示无限制
 * @param {string|number} options.defaultCategoryId - 默认分类ID
 * @returns {Promise} 返回选择的文件信息
 */
export function openFileSelector(options = {}) {
  return new Promise((resolve, reject) => {
    // 创建容器元素
    const container = document.createElement('div')
    document.body.appendChild(container)

    // 创建Vue实例
    const vm = new Vue({
      data() {
        return {
          visible: true,
          multiple: options.multiple !== false, // 默认为true
          fileTypes: options.fileTypes || [],
          maxCount: options.maxCount || 0,
          defaultCategoryId: options.defaultCategoryId || null
        }
      },
      methods: {
        handleConfirm(files) {
          resolve(files)
          this.cleanup()
        },
        handleClose() {
          reject(new Error('用户取消选择'))
          this.cleanup()
        },
        cleanup() {
          this.visible = false
          setTimeout(() => {
            this.$destroy()
            if (container.parentNode) {
              container.parentNode.removeChild(container)
            }
          }, 300)
        }
      },
      components: {
        EnhancedFileSelector
      },
      // 使用渲染函数而不是模板
      render(h) {
        return h('EnhancedFileSelector', {
          props: {
            visible: this.visible,
            title: '选择文件',
            multiple: this.multiple,
            fileTypes: this.fileTypes,
            maxCount: this.maxCount,
            defaultCategoryId: this.defaultCategoryId
          },
          on: {
            confirm: this.handleConfirm,
            close: this.handleClose
          }
        })
      }
    })

    // 挂载到容器
    vm.$mount(container)
  })
}

/**
 * Vue 2 版本的便捷方法
 */
export const fileSelector = {
  // 选择单个文件
  selectSingle(options = {}) {
    return openFileSelector({
      ...options,
      multiple: false
    })
  },

  // 选择多个文件
  selectMultiple(options = {}) {
    return openFileSelector({
      ...options,
      multiple: true
    })
  },

  // 选择图片
  selectImages(options = {}) {
    return openFileSelector({
      ...options,
      fileTypes: ['image']
    })
  },

  // 选择视频
  selectVideos(options = {}) {
    return openFileSelector({
      ...options,
      fileTypes: ['video']
    })
  },

  // 选择文档
  selectDocuments(options = {}) {
    return openFileSelector({
      ...options,
      fileTypes: ['document']
    })
  }
}

// 默认导出
export default fileSelector
