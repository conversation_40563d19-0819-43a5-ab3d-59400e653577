// 文件类型常量
export const FILE_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
  DOCUMENT: 'document',
  ARCHIVE: 'archive',
  AUDIO: 'audio',
  OTHER: 'other'
}

// 文件扩展名映射
export const FILE_EXTENSIONS = {
  // 图片
  jpg: FILE_TYPES.IMAGE,
  jpeg: FILE_TYPES.IMAGE,
  png: FILE_TYPES.IMAGE,
  gif: FILE_TYPES.IMAGE,
  webp: FILE_TYPES.IMAGE,
  svg: FILE_TYPES.IMAGE,
  bmp: FILE_TYPES.IMAGE,
  ico: FILE_TYPES.IMAGE,
  
  // 视频
  mp4: FILE_TYPES.VIDEO,
  avi: FILE_TYPES.VIDEO,
  mov: FILE_TYPES.VIDEO,
  wmv: FILE_TYPES.VIDEO,
  flv: FILE_TYPES.VIDEO,
  webm: FILE_TYPES.VIDEO,
  mkv: FILE_TYPES.VIDEO,
  
  // 文档
  pdf: FILE_TYPES.DOCUMENT,
  doc: FILE_TYPES.DOCUMENT,
  docx: FILE_TYPES.DOCUMENT,
  xls: FILE_TYPES.DOCUMENT,
  xlsx: FILE_TYPES.DOCUMENT,
  ppt: FILE_TYPES.DOCUMENT,
  pptx: FILE_TYPES.DOCUMENT,
  txt: FILE_TYPES.DOCUMENT,
  
  // 压缩包
  zip: FILE_TYPES.ARCHIVE,
  rar: FILE_TYPES.ARCHIVE,
  '7z': FILE_TYPES.ARCHIVE,
  tar: FILE_TYPES.ARCHIVE,
  gz: FILE_TYPES.ARCHIVE,
  
  // 音频
  mp3: FILE_TYPES.AUDIO,
  wav: FILE_TYPES.AUDIO,
  flac: FILE_TYPES.AUDIO,
  aac: FILE_TYPES.AUDIO,
  ogg: FILE_TYPES.AUDIO
}

// 视图模式
export const VIEW_MODES = {
  GRID: 'grid',
  LIST: 'list'
}

// 文件大小单位
export const SIZE_UNITS = ['B', 'KB', 'MB', 'GB', 'TB']

// 上传状态
export const UPLOAD_STATUS = {
  PENDING: 'pending',
  UPLOADING: 'uploading',
  SUCCESS: 'success',
  ERROR: 'error',
  CANCELLED: 'cancelled'
}

// 文件操作类型
export const FILE_ACTIONS = {
  PREVIEW: 'preview',
  DOWNLOAD: 'download',
  RENAME: 'rename',
  DELETE: 'delete',
  MOVE: 'move',
  COPY: 'copy'
}

// 排序类型
export const SORT_TYPES = {
  NAME_ASC: 'name_asc',
  NAME_DESC: 'name_desc',
  SIZE_ASC: 'size_asc',
  SIZE_DESC: 'size_desc',
  TIME_ASC: 'time_asc',
  TIME_DESC: 'time_desc',
  TYPE_ASC: 'type_asc',
  TYPE_DESC: 'type_desc'
}

// 默认配置
export const DEFAULT_CONFIG = {
  pageSize: 50,
  maxFileSize: 100, // MB
  allowedImageTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  allowedVideoTypes: ['mp4', 'avi', 'mov'],
  allowedDocumentTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  thumbnailSize: 150,
  previewImageSize: 800
}

// 错误消息
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: '文件大小超出限制',
  FILE_TYPE_NOT_ALLOWED: '不支持的文件类型',
  UPLOAD_FAILED: '上传失败',
  NETWORK_ERROR: '网络错误',
  PERMISSION_DENIED: '权限不足',
  FILE_NOT_FOUND: '文件不存在',
  CATEGORY_NOT_FOUND: '分类不存在'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  UPLOAD_SUCCESS: '上传成功',
  DELETE_SUCCESS: '删除成功',
  RENAME_SUCCESS: '重命名成功',
  MOVE_SUCCESS: '移动成功',
  COPY_SUCCESS: '复制成功'
}

// 键盘快捷键
export const KEYBOARD_SHORTCUTS = {
  SELECT_ALL: 'ctrl+a',
  DELETE: 'delete',
  COPY: 'ctrl+c',
  PASTE: 'ctrl+v',
  RENAME: 'f2',
  REFRESH: 'f5',
  SEARCH: 'ctrl+f'
}

// 文件图标映射
export const FILE_ICONS = {
  [FILE_TYPES.IMAGE]: 'el-icon-picture-outline',
  [FILE_TYPES.VIDEO]: 'el-icon-video-play',
  [FILE_TYPES.DOCUMENT]: 'el-icon-document',
  [FILE_TYPES.ARCHIVE]: 'el-icon-folder-opened',
  [FILE_TYPES.AUDIO]: 'el-icon-headset',
  [FILE_TYPES.OTHER]: 'el-icon-document-copy'
}

// 文件颜色映射
export const FILE_COLORS = {
  [FILE_TYPES.IMAGE]: '#67C23A',
  [FILE_TYPES.VIDEO]: '#E6A23C',
  [FILE_TYPES.DOCUMENT]: '#409EFF',
  [FILE_TYPES.ARCHIVE]: '#909399',
  [FILE_TYPES.AUDIO]: '#F56C6C',
  [FILE_TYPES.OTHER]: '#909399'
}

// 网格视图配置
export const GRID_CONFIG = {
  itemWidth: 160,
  itemHeight: 180,
  gap: 16,
  minColumns: 2,
  maxColumns: 8
}

// 列表视图配置
export const LIST_CONFIG = {
  rowHeight: 60,
  columns: [
    { prop: 'fileName', label: '文件名', width: 'auto', sortable: true },
    { prop: 'fileSize', label: '大小', width: 100, sortable: true },
    { prop: 'fileType', label: '类型', width: 100, sortable: true },
    { prop: 'createTime', label: '上传时间', width: 160, sortable: true },
    { prop: 'actions', label: '操作', width: 200, sortable: false }
  ]
}

// 动画配置
export const ANIMATION_CONFIG = {
  duration: 300,
  easing: 'cubic-bezier(0.25, 0.8, 0.25, 1)',
  stagger: 50
}

// 响应式断点
export const BREAKPOINTS = {
  xs: 480,
  sm: 768,
  md: 992,
  lg: 1200,
  xl: 1920
}
