import { FILE_EXTENSIONS, FILE_TYPES, SIZE_UNITS, DEFAULT_CONFIG } from './constants'

/**
 * 获取文件扩展名
 * @param {string} fileName - 文件名
 * @returns {string} 扩展名（小写）
 */
export function getFileExtension(fileName) {
  if (!fileName || typeof fileName !== 'string') return ''
  const lastDotIndex = fileName.lastIndexOf('.')
  if (lastDotIndex === -1) return ''
  return fileName.slice(lastDotIndex + 1).toLowerCase()
}

/**
 * 根据文件名获取文件类型
 * @param {string} fileName - 文件名
 * @returns {string} 文件类型
 */
export function getFileType(fileName) {
  const extension = getFileExtension(fileName)
  return FILE_EXTENSIONS[extension] || FILE_TYPES.OTHER
}

/**
 * 检查是否为图片文件
 * @param {string} fileName - 文件名
 * @returns {boolean}
 */
export function isImageFile(fileName) {
  return getFileType(fileName) === FILE_TYPES.IMAGE
}

/**
 * 检查是否为视频文件
 * @param {string} fileName - 文件名
 * @returns {boolean}
 */
export function isVideoFile(fileName) {
  return getFileType(fileName) === FILE_TYPES.VIDEO
}

/**
 * 检查是否为文档文件
 * @param {string} fileName - 文件名
 * @returns {boolean}
 */
export function isDocumentFile(fileName) {
  return getFileType(fileName) === FILE_TYPES.DOCUMENT
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes, decimals = 2) {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + SIZE_UNITS[i]
}

/**
 * 验证文件大小
 * @param {number} fileSize - 文件大小（字节）
 * @param {number} maxSize - 最大大小（MB）
 * @returns {boolean}
 */
export function validateFileSize(fileSize, maxSize = DEFAULT_CONFIG.maxFileSize) {
  const maxBytes = maxSize * 1024 * 1024
  return fileSize <= maxBytes
}

/**
 * 验证文件类型
 * @param {string} fileName - 文件名
 * @param {string[]} allowedTypes - 允许的文件类型
 * @returns {boolean}
 */
export function validateFileType(fileName, allowedTypes = []) {
  if (!allowedTypes || allowedTypes.length === 0) return true
  const extension = getFileExtension(fileName)
  return allowedTypes.includes(extension)
}

/**
 * 生成文件缩略图URL
 * @param {string} fileUrl - 文件URL
 * @param {number} size - 缩略图大小
 * @returns {string}
 */
export function generateThumbnailUrl(fileUrl, size = DEFAULT_CONFIG.thumbnailSize) {
  if (!fileUrl) return ''
  
  // 如果是阿里云OSS，添加缩略图参数
  if (fileUrl.includes('aliyuncs.com')) {
    return `${fileUrl}?x-oss-process=image/resize,w_${size},h_${size},m_fill`
  }
  
  // 其他情况直接返回原URL
  return fileUrl
}

/**
 * 生成预览图URL
 * @param {string} fileUrl - 文件URL
 * @param {number} size - 预览图大小
 * @returns {string}
 */
export function generatePreviewUrl(fileUrl, size = DEFAULT_CONFIG.previewImageSize) {
  if (!fileUrl) return ''
  
  // 如果是阿里云OSS，添加预览图参数
  if (fileUrl.includes('aliyuncs.com')) {
    return `${fileUrl}?x-oss-process=image/resize,w_${size},h_${size},m_lfit`
  }
  
  return fileUrl
}

/**
 * 检查文件名是否包含特殊字符
 * @param {string} fileName - 文件名
 * @returns {boolean}
 */
export function hasSpecialChars(fileName) {
  const specialChars = /[\\/:*?"<>|]/
  return specialChars.test(fileName)
}

/**
 * 清理文件名中的特殊字符
 * @param {string} fileName - 文件名
 * @returns {string}
 */
export function sanitizeFileName(fileName) {
  if (!fileName) return ''
  return fileName.replace(/[\\/:*?"<>|]/g, '_')
}

/**
 * 生成唯一文件名
 * @param {string} originalName - 原始文件名
 * @returns {string}
 */
export function generateUniqueFileName(originalName) {
  const extension = getFileExtension(originalName)
  const nameWithoutExt = originalName.slice(0, originalName.lastIndexOf('.'))
  const timestamp = Date.now()
  const random = Math.random().toString(36).slice(-6)
  
  return `${sanitizeFileName(nameWithoutExt)}_${timestamp}_${random}.${extension}`
}

/**
 * 解析文件路径
 * @param {string} filePath - 文件路径
 * @returns {object}
 */
export function parseFilePath(filePath) {
  if (!filePath) return { dir: '', name: '', ext: '' }
  
  const lastSlashIndex = filePath.lastIndexOf('/')
  const lastDotIndex = filePath.lastIndexOf('.')
  
  const dir = lastSlashIndex >= 0 ? filePath.slice(0, lastSlashIndex) : ''
  const fullName = lastSlashIndex >= 0 ? filePath.slice(lastSlashIndex + 1) : filePath
  const name = lastDotIndex > lastSlashIndex ? fullName.slice(0, lastDotIndex - lastSlashIndex - 1) : fullName
  const ext = lastDotIndex > lastSlashIndex ? fullName.slice(lastDotIndex - lastSlashIndex) : ''
  
  return { dir, name, ext, fullName }
}

/**
 * 计算网格布局
 * @param {number} containerWidth - 容器宽度
 * @param {number} itemWidth - 项目宽度
 * @param {number} gap - 间距
 * @returns {object}
 */
export function calculateGridLayout(containerWidth, itemWidth, gap) {
  const availableWidth = containerWidth - gap
  const columns = Math.floor(availableWidth / (itemWidth + gap))
  const actualItemWidth = (availableWidth - (columns - 1) * gap) / columns
  
  return {
    columns: Math.max(1, columns),
    itemWidth: actualItemWidth,
    gap
  }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间
 * @returns {Function}
 */
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间
 * @returns {Function}
 */
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any}
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期
 * @param {string} format - 格式
 * @returns {string}
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 获取相对时间
 * @param {Date|string|number} date - 日期
 * @returns {string}
 */
export function getRelativeTime(date) {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) return '刚刚'
  if (diff < hour) return `${Math.floor(diff / minute)}分钟前`
  if (diff < day) return `${Math.floor(diff / hour)}小时前`
  if (diff < week) return `${Math.floor(diff / day)}天前`
  if (diff < month) return `${Math.floor(diff / week)}周前`
  if (diff < year) return `${Math.floor(diff / month)}个月前`
  return `${Math.floor(diff / year)}年前`
}
