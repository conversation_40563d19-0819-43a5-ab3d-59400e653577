// 文件选择器 Mixin
// 提供一个更简单的方式在组件中使用文件选择器

export default {
  data() {
    return {
      fileSelectorVisible: false,
      fileSelectorOptions: {},
      fileSelectorResolve: null,
      fileSelectorReject: null
    }
  },
  
  methods: {
    /**
     * 打开文件选择器
     * @param {Object} options - 选择器配置选项
     * @returns {Promise}
     */
    $openFileSelector(options = {}) {
      return new Promise((resolve, reject) => {
        this.fileSelectorOptions = {
          multiple: options.multiple !== false,
          fileTypes: options.fileTypes || [],
          maxCount: options.maxCount || 0,
          defaultCategoryId: options.defaultCategoryId || null
        }
        this.fileSelectorResolve = resolve
        this.fileSelectorReject = reject
        this.fileSelectorVisible = true
      })
    },

    /**
     * 选择单个文件
     */
    $selectSingleFile(options = {}) {
      return this.$openFileSelector({
        ...options,
        multiple: false
      })
    },

    /**
     * 选择多个文件
     */
    $selectMultipleFiles(options = {}) {
      return this.$openFileSelector({
        ...options,
        multiple: true
      })
    },

    /**
     * 选择图片文件
     */
    $selectImages(options = {}) {
      return this.$openFileSelector({
        ...options,
        fileTypes: ['image']
      })
    },

    /**
     * 选择视频文件
     */
    $selectVideos(options = {}) {
      return this.$openFileSelector({
        ...options,
        fileTypes: ['video']
      })
    },

    /**
     * 选择文档文件
     */
    $selectDocuments(options = {}) {
      return this.$openFileSelector({
        ...options,
        fileTypes: ['document']
      })
    },

    /**
     * 处理文件选择确认
     */
    handleFileSelectorConfirm(files) {
      if (this.fileSelectorResolve) {
        this.fileSelectorResolve(files)
      }
      this.closeFileSelector()
    },

    /**
     * 处理文件选择取消
     */
    handleFileSelectorClose() {
      if (this.fileSelectorReject) {
        this.fileSelectorReject(new Error('用户取消选择'))
      }
      this.closeFileSelector()
    },

    /**
     * 关闭文件选择器
     */
    closeFileSelector() {
      this.fileSelectorVisible = false
      this.fileSelectorResolve = null
      this.fileSelectorReject = null
      this.fileSelectorOptions = {}
    }
  }
}
