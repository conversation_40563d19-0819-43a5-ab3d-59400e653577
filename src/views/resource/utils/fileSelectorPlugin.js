// 增强版文件选择器插件
// 提供全局方法来使用增强版文件选择器
// 基于ResourceManager组件，保持与主页面一致的设计和功能

import Vue from 'vue'
import EnhancedFileSelector from '../components/EnhancedFileSelector.vue'

// 全局文件选择器实例
let fileSelectorInstance = null

/**
 * 创建文件选择器实例
 */
function createFileSelectorInstance() {
  if (fileSelectorInstance) {
    return fileSelectorInstance
  }

  // 创建容器
  const container = document.createElement('div')
  document.body.appendChild(container)

  // 创建Vue实例
  const FileSelectorConstructor = Vue.extend({
    components: {
      EnhancedFileSelector
    },
    data() {
      return {
        visible: false,
        title: '选择文件',
        multiple: true,
        fileTypes: [],
        maxCount: 0,
        defaultCategoryId: null,
        currentResolve: null,
        currentReject: null
      }
    },
    methods: {
      show(options = {}) {
        return new Promise((resolve, reject) => {
          this.title = options.title || '选择文件'
          this.multiple = options.multiple !== false
          this.fileTypes = options.fileTypes || []
          this.maxCount = options.maxCount || 0
          this.defaultCategoryId = options.defaultCategoryId || null
          this.currentResolve = resolve
          this.currentReject = reject
          this.visible = true
        })
      },
      handleConfirm(files) {
        if (this.currentResolve) {
          this.currentResolve(files)
        }
        this.hide()
      },
      handleClose() {
        if (this.currentReject) {
          this.currentReject(new Error('用户取消选择'))
        }
        this.hide()
      },
      hide() {
        this.visible = false
        this.currentResolve = null
        this.currentReject = null
      }
    },
    render(h) {
      return h('EnhancedFileSelector', {
        props: {
          visible: this.visible,
          title: this.title,
          multiple: this.multiple,
          fileTypes: this.fileTypes,
          maxCount: this.maxCount,
          defaultCategoryId: this.defaultCategoryId
        },
        on: {
          confirm: this.handleConfirm,
          close: this.handleClose
        }
      })
    }
  })

  fileSelectorInstance = new FileSelectorConstructor()
  fileSelectorInstance.$mount(container)

  return fileSelectorInstance
}

/**
 * 文件选择器方法
 */
const fileSelector = {
  /**
   * 打开文件选择器
   */
  open(options = {}) {
    const instance = createFileSelectorInstance()
    return instance.show(options)
  },

  /**
   * 选择单个文件
   */
  selectSingle(options = {}) {
    return this.open({
      title: '选择单个文件',
      ...options,
      multiple: false
    })
  },

  /**
   * 选择多个文件
   */
  selectMultiple(options = {}) {
    return this.open({
      title: '选择多个文件',
      ...options,
      multiple: true
    })
  },

  /**
   * 选择图片文件
   */
  selectImages(options = {}) {
    return this.open({
      title: '选择图片文件',
      ...options,
      fileTypes: ['image']
    })
  },

  /**
   * 选择视频文件
   */
  selectVideos(options = {}) {
    return this.open({
      title: '选择视频文件',
      ...options,
      fileTypes: ['video']
    })
  },

  /**
   * 选择文档文件
   */
  selectDocuments(options = {}) {
    return this.open({
      title: '选择文档文件',
      ...options,
      fileTypes: ['document']
    })
  },

  /**
   * 选择媒体文件（图片和视频）
   */
  selectMedia(options = {}) {
    return this.open({
      title: '选择媒体文件',
      ...options,
      fileTypes: ['image', 'video']
    })
  },

  /**
   * 选择指定数量的文件
   */
  selectWithLimit(maxCount, options = {}) {
    return this.open({
      title: `选择文件（最多${maxCount}个）`,
      ...options,
      maxCount: maxCount
    })
  },

  /**
   * 选择指定分类的文件
   */
  selectFromCategory(categoryId, options = {}) {
    return this.open({
      title: '选择文件',
      ...options,
      defaultCategoryId: categoryId
    })
  }
}

/**
 * Vue插件安装函数
 */
function install(Vue) {
  // 添加全局方法
  Vue.prototype.$fileSelector = fileSelector
  
  // 添加全局属性
  Vue.fileSelector = fileSelector
}

// 自动安装
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export default {
  install,
  fileSelector
}

export { fileSelector }
