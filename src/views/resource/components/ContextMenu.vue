<template>
  <Teleport to="body">
    <div
      v-if="visible"
      :style="menuStyle"
      class="context-menu"
      @click.stop
    >
      <div class="menu-item" @click="handleAction('preview')">
        <i class="el-icon-view" />
        <span>预览</span>
      </div>

      <div class="menu-item" @click="handleAction('download')">
        <i class="el-icon-download" />
        <span>下载</span>
      </div>

      <div class="menu-item" @click="handleAction('copy-url')">
        <i class="el-icon-link" />
        <span>复制链接</span>
      </div>

      <div class="menu-divider" />

      <div class="menu-item" @click="handleAction('rename')">
        <i class="el-icon-edit" />
        <span>重命名</span>
      </div>

      <div class="menu-item" @click="handleAction('move')">
        <i class="el-icon-folder" />
        <span>移动到...</span>
      </div>

      <div class="menu-item" @click="handleAction('copy')">
        <i class="el-icon-document-copy" />
        <span>复制到...</span>
      </div>

      <div class="menu-divider" />

      <div class="menu-item danger" @click="handleAction('delete')">
        <i class="el-icon-delete" />
        <span>删除</span>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import Teleport from 'vue2-teleport'

const props = defineProps({

  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  file: {
    type: Object,
    default: null
  }
})

const emit = defineEmits([
  'close',
  'action'
])

// 计算菜单样式
const menuStyle = computed(() => {
  const { x, y } = props.position
  const menuWidth = 160
  const menuHeight = 280

  // 获取视口尺寸
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // 计算菜单位置，确保不超出视口
  let left = x
  let top = y

  if (x + menuWidth > viewportWidth) {
    left = x - menuWidth
  }

  if (y + menuHeight > viewportHeight) {
    top = y - menuHeight
  }

  return {
    position: 'fixed',
    left: `${Math.max(0, left)}px`,
    top: `${Math.max(0, top)}px`,
    zIndex: 9999
  }
})

// 处理菜单项点击
const handleAction = async(action) => {
  if (!props.file) return

  switch (action) {
    case 'copy-url':
      await copyFileUrl()
      break
    default:
      emit('action', action, props.file)
      break
  }

  emit('close')
}

// 复制文件URL
const copyFileUrl = async() => {
  if (!props.file?.url) return

  try {
    await navigator.clipboard.writeText(props.file.url)
    // 这里应该使用全局的消息提示
    console.log('链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  if (props.visible) {
    emit('close')
  }
}

// 按ESC键关闭菜单
const handleKeyDown = (event) => {
  if (event.key === 'Escape' && props.visible) {
    emit('close')
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeyDown)
})

</script>

<style lang="scss" scoped>
.context-menu {
  background: var(--ios-background-secondary);
  border: 1px solid var(--ios-border-color-light);
  border-radius: var(--ios-border-radius);
  box-shadow: 0 8px 24px var(--ios-shadow-medium);
  backdrop-filter: blur(20px);
  min-width: 160px;
  overflow: hidden;
  user-select: none;

  .menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    font-size: 14px;
    color: var(--ios-text-primary);
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--ios-fill-quaternary);
    }

    &.danger {
      color: var(--ios-red);

      &:hover {
        background: rgba(255, 59, 48, 0.1);
      }
    }

    i {
      width: 16px;
      font-size: 16px;
      flex-shrink: 0;
    }

    span {
      flex: 1;
      white-space: nowrap;
    }
  }

  .menu-divider {
    height: 1px;
    background: var(--ios-separator-color);
    margin: 4px 0;
  }
}

// 动画效果
.context-menu {
  animation: contextMenuFadeIn 0.15s ease-out;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
