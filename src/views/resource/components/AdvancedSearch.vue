<template>
  <div class="advanced-search">
    <el-form
      ref="searchFormRef"
      :model="searchForm"
      label-width="100px"
      size="medium"
    >
      <!-- 文件名 -->
      <el-form-item label="文件名">
        <el-input
          v-model="searchForm.fileName"
          placeholder="请输入文件名关键词"
          clearable
        />
      </el-form-item>

      <!-- 文件类型 -->
      <el-form-item label="文件类型">
        <el-input
          v-model="searchForm.fileType"
          placeholder="请输入文件类型，如：image/jpeg, application/pdf"
          clearable
        />
      </el-form-item>

      <!-- 文件后缀 -->
      <el-form-item label="文件后缀">
        <el-input
          v-model="searchForm.fileSuffix"
          placeholder="请输入单个文件后缀，如：jpg 或 pdf 或 mp4"
          clearable
        />
      </el-form-item>

      <!-- 快速筛选 -->
      <el-form-item label="快速筛选">
        <div class="quick-filters">
          <el-tag
            v-for="filter in quickFilters"
            :key="filter.key"
            :type="filter.type"
            class="filter-tag"
            @click="applyQuickFilter(filter)"
          >
            {{ filter.label }}
          </el-tag>
        </div>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="search-actions">
      <el-button @click="resetForm">重置</el-button>
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdvancedSearch'
}
</script>

<script setup>
import { ref, reactive } from 'vue'

const emit = defineEmits(['search', 'close'])

// 搜索表单
const searchForm = reactive({
  fileName: '',
  fileType: '',
  fileSuffix: ''
})

// 快速筛选选项
const quickFilters = ref([
  { key: 'images', label: '图片文件', type: 'info' },
  { key: 'videos', label: '视频文件', type: 'warning' },
  { key: 'documents', label: '文档文件', type: 'success' },
  { key: 'audio', label: '音频文件', type: 'primary' },
  { key: 'pdf', label: 'PDF文件', type: 'danger' },
  { key: 'excel', label: 'Excel文件', type: '' },
  { key: 'word', label: 'Word文件', type: 'info' }
])

// 表单引用
const searchFormRef = ref(null)

/**
 * 应用快速筛选
 */
const applyQuickFilter = (filter) => {
  switch (filter.key) {
    case 'images':
      searchForm.fileType = 'image/jpeg'
      searchForm.fileSuffix = 'jpg'
      break
    case 'videos':
      searchForm.fileType = 'video/mp4'
      searchForm.fileSuffix = 'mp4'
      break
    case 'documents':
      searchForm.fileType = 'text/plain'
      searchForm.fileSuffix = 'txt'
      break
    case 'audio':
      searchForm.fileType = 'audio/mpeg'
      searchForm.fileSuffix = 'mp3'
      break
    case 'pdf':
      searchForm.fileType = 'application/pdf'
      searchForm.fileSuffix = 'pdf'
      break
    case 'excel':
      searchForm.fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      searchForm.fileSuffix = 'xlsx'
      break
    case 'word':
      searchForm.fileType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      searchForm.fileSuffix = 'docx'
      break
  }
}

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(searchForm, {
    fileName: '',
    fileType: '',
    fileSuffix: ''
  })
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  const searchParams = { ...searchForm }

  // 清理空值
  Object.keys(searchParams).forEach(key => {
    if (searchParams[key] === '' || searchParams[key] === null || searchParams[key] === undefined) {
      delete searchParams[key]
    }
  })

  emit('search', searchParams)
}

</script>

<style lang="scss" scoped>
.advanced-search {
  .quick-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .filter-tag {
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .search-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--ios-separator-color);
  }
}

::v-deep .el-form-item {
  margin-bottom: 20px;

  .el-form-item__label {
    color: var(--ios-text-primary);
    font-weight: 500;
  }
}

::v-deep .el-input,
::v-deep .el-select,
::v-deep .el-date-editor {
  .el-input__inner {
    border-radius: var(--ios-border-radius-small);
    border: 1px solid var(--ios-border-color);
    transition: all 0.2s ease;

    &:focus {
      border-color: var(--ios-blue);
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
    }
  }
}

::v-deep .el-input-number {
  .el-input__inner {
    text-align: left;
  }
}

::v-deep .el-tag {
  border-radius: var(--ios-border-radius-small);
  border: none;
  font-weight: 500;
}
</style>
