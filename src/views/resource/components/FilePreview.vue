<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="file?.fileName || '文件预览'"
    width="80%"
    top="5vh"
    append-to-body
    destroy-on-close
    custom-class="file-preview-dialog"
    @close="handleClose"
  >
    <div v-if="file" class="preview-container">
      <!-- 图片预览 -->
      <div v-if="isImageFile(file.fileName)" class="image-preview">
        <img
          :src="generatePreviewUrl(file.url)"
          :alt="file.fileName"
          class="preview-image"
          @load="handleImageLoad"
          @error="handleImageError"
        />
        <div class="image-controls">
          <el-button-group>
            <el-button icon="el-icon-zoom-in" @click="zoomIn" title="放大" />
            <el-button icon="el-icon-zoom-out" @click="zoomOut" title="缩小" />
            <el-button icon="el-icon-refresh-left" @click="rotateLeft" title="左转" />
            <el-button icon="el-icon-refresh-right" @click="rotateRight" title="右转" />
            <el-button icon="el-icon-full-screen" @click="toggleFullscreen" title="全屏" />
          </el-button-group>
        </div>
      </div>

      <!-- 视频预览 -->
      <div v-else-if="isVideoFile(file.fileName)" class="video-preview">
        <video
          ref="videoRef"
          :src="file.url"
          controls
          class="preview-video"
          @loadedmetadata="handleVideoLoad"
          @error="handleVideoError"
        >
          您的浏览器不支持视频播放
        </video>
      </div>

      <!-- 文档预览 -->
      <div v-else-if="isDocumentFile(file.fileName)" class="document-preview">
        <div v-if="canPreviewDocument" class="document-viewer">
          <!-- PDF预览 -->
          <iframe
            v-if="file.fileSuffix === 'pdf'"
            :src="file.url"
            class="pdf-viewer"
            frameborder="0"
          />
          <!-- 其他文档类型 -->
          <div v-else class="document-placeholder">
            <i class="el-icon-document document-icon"></i>
            <p>此文档类型不支持在线预览</p>
            <el-button type="primary" @click="handleDownload">下载查看</el-button>
          </div>
        </div>
        <div v-else class="document-placeholder">
          <i class="el-icon-document document-icon"></i>
          <p>文档预览功能暂不可用</p>
          <el-button type="primary" @click="handleDownload">下载文件</el-button>
        </div>
      </div>

      <!-- 其他文件类型 -->
      <div v-else class="unsupported-preview">
        <div class="file-info-card">
          <div class="file-icon-large">
            <i :class="getFileIcon(file.fileName)" :style="{ color: getFileColor(file.fileName) }"></i>
          </div>
          <div class="file-details">
            <h3 class="file-name">{{ file.fileName }}</h3>
            <div class="file-meta">
              <div class="meta-item">
                <span class="meta-label">文件大小:</span>
                <span class="meta-value">{{ formatFileSize(file.fileSize) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">文件类型:</span>
                <span class="meta-value">{{ getFileTypeLabel(file.fileName) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">上传时间:</span>
                <span class="meta-value">{{ formatDate(file.createTime) }}</span>
              </div>
              <div v-if="file.remark" class="meta-item">
                <span class="meta-label">备注:</span>
                <span class="meta-value">{{ file.remark }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="preview-actions">
          <el-button type="primary" icon="el-icon-download" @click="handleDownload">
            下载文件
          </el-button>
          <el-button icon="el-icon-link" @click="copyUrl">
            复制链接
          </el-button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <el-loading text="加载中..." />
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-overlay">
        <div class="error-content">
          <i class="el-icon-warning error-icon"></i>
          <p class="error-message">{{ error }}</p>
          <el-button @click="retry">重试</el-button>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div slot="footer" class="preview-footer">
      <div class="file-actions">
        <el-button icon="el-icon-download" @click="handleDownload">下载</el-button>
        <el-button icon="el-icon-link" @click="copyUrl">复制链接</el-button>
        <el-button icon="el-icon-edit" @click="handleRename">重命名</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="handleDelete">删除</el-button>
      </div>
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue'
import {
  isImageFile,
  isVideoFile,
  isDocumentFile,
  formatFileSize,
  formatDate,
  generatePreviewUrl,
  getFileType
} from '../utils/fileUtils'
import { FILE_ICONS, FILE_COLORS, FILE_TYPES } from '../utils/constants'

export default {
  name: 'FilePreview',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    file: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'download', 'rename', 'delete'],
  setup(props, { emit }) {
    // 响应式数据
    const dialogVisible = ref(false)
    const loading = ref(false)
    const error = ref(null)
    const imageScale = ref(1)
    const imageRotation = ref(0)
    const videoRef = ref(null)

    // 计算属性
    const canPreviewDocument = computed(() => {
      if (!props.file) return false
      return props.file.fileSuffix === 'pdf'
    })

    const imageRotationDeg = computed(() => {
      return imageRotation.value + 'deg'
    })

    // 监听visible变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal
      if (newVal && props.file) {
        resetPreviewState()
      }
    })

    // 监听dialogVisible变化
    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('close')
      }
    })

    // 方法
    const resetPreviewState = () => {
      loading.value = false
      error.value = null
      imageScale.value = 1
      imageRotation.value = 0
    }

    const handleClose = () => {
      dialogVisible.value = false
    }

    const handleImageLoad = () => {
      loading.value = false
    }

    const handleImageError = () => {
      loading.value = false
      error.value = '图片加载失败'
    }

    const handleVideoLoad = () => {
      loading.value = false
    }

    const handleVideoError = () => {
      loading.value = false
      error.value = '视频加载失败'
    }

    const zoomIn = () => {
      imageScale.value = Math.min(imageScale.value * 1.2, 5)
    }

    const zoomOut = () => {
      imageScale.value = Math.max(imageScale.value / 1.2, 0.1)
    }

    const rotateLeft = () => {
      imageRotation.value -= 90
    }

    const rotateRight = () => {
      imageRotation.value += 90
    }

    const toggleFullscreen = () => {
      const element = document.querySelector('.preview-image')
      if (element) {
        if (document.fullscreenElement) {
          document.exitFullscreen()
        } else {
          element.requestFullscreen()
        }
      }
    }

    const handleDownload = () => {
      emit('download', props.file)
    }

    const handleRename = () => {
      emit('rename', props.file)
    }

    const handleDelete = () => {
      emit('delete', props.file)
    }

    const copyUrl = async () => {
      if (!props.file?.url) return
      
      try {
        await navigator.clipboard.writeText(props.file.url)
        // 这里需要使用全局消息提示
        console.log('链接已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        console.log('复制失败')
      }
    }

    const retry = () => {
      error.value = null
      loading.value = true
      // 重新加载资源
      nextTick(() => {
        const img = document.querySelector('.preview-image')
        const video = document.querySelector('.preview-video')
        
        if (img) {
          img.src = img.src
        } else if (video) {
          video.load()
        }
      })
    }

    const getFileIcon = (fileName) => {
      const fileType = getFileType(fileName)
      return FILE_ICONS[fileType] || FILE_ICONS[FILE_TYPES.OTHER]
    }

    const getFileColor = (fileName) => {
      const fileType = getFileType(fileName)
      return FILE_COLORS[fileType] || FILE_COLORS[FILE_TYPES.OTHER]
    }

    const getFileTypeLabel = (fileName) => {
      const fileType = getFileType(fileName)
      const labels = {
        [FILE_TYPES.IMAGE]: '图片',
        [FILE_TYPES.VIDEO]: '视频',
        [FILE_TYPES.DOCUMENT]: '文档',
        [FILE_TYPES.AUDIO]: '音频',
        [FILE_TYPES.ARCHIVE]: '压缩包',
        [FILE_TYPES.OTHER]: '其他'
      }
      return labels[fileType] || '其他'
    }

    return {
      // 响应式数据
      dialogVisible,
      loading,
      error,
      imageScale,
      imageRotation,
      imageRotationDeg,
      videoRef,
      
      // 计算属性
      canPreviewDocument,
      
      // 方法
      handleClose,
      handleImageLoad,
      handleImageError,
      handleVideoLoad,
      handleVideoError,
      zoomIn,
      zoomOut,
      rotateLeft,
      rotateRight,
      toggleFullscreen,
      handleDownload,
      handleRename,
      handleDelete,
      copyUrl,
      retry,
      isImageFile,
      isVideoFile,
      isDocumentFile,
      formatFileSize,
      formatDate,
      generatePreviewUrl,
      getFileIcon,
      getFileColor,
      getFileTypeLabel
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .file-preview-dialog {
  .el-dialog__body {
    padding: 0;
  }
}

.preview-container {
  position: relative;
  min-height: 400px;
  background: var(--ios-background);

  .image-preview {
    position: relative;
    text-align: center;
    padding: 20px;

    .preview-image {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
      transition: transform 0.3s ease;
      transform: scale(v-bind(imageScale)) rotate(v-bind(imageRotationDeg));
    }

    .image-controls {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.7);
      border-radius: var(--ios-border-radius);
      padding: 8px;
    }
  }

  .video-preview {
    text-align: center;
    padding: 20px;

    .preview-video {
      width: 100%;
      max-height: 70vh;
      border-radius: var(--ios-border-radius);
    }
  }

  .document-preview {
    height: 70vh;

    .document-viewer {
      height: 100%;

      .pdf-viewer {
        width: 100%;
        height: 100%;
      }
    }

    .document-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;

      .document-icon {
        font-size: 64px;
        color: var(--ios-blue);
        margin-bottom: 16px;
      }

      p {
        font-size: 16px;
        color: var(--ios-text-secondary);
        margin: 0 0 20px 0;
      }
    }
  }

  .unsupported-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;

    .file-info-card {
      background: var(--ios-background-secondary);
      border-radius: var(--ios-border-radius);
      padding: 30px;
      box-shadow: 0 4px 12px var(--ios-shadow-light);
      margin-bottom: 24px;
      max-width: 400px;

      .file-icon-large {
        margin-bottom: 20px;

        i {
          font-size: 64px;
        }
      }

      .file-details {
        .file-name {
          font-size: 18px;
          font-weight: 600;
          color: var(--ios-text-primary);
          margin: 0 0 16px 0;
          word-break: break-all;
        }

        .file-meta {
          text-align: left;

          .meta-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;

            .meta-label {
              color: var(--ios-text-secondary);
              font-weight: 500;
            }

            .meta-value {
              color: var(--ios-text-primary);
            }
          }
        }
      }
    }

    .preview-actions {
      display: flex;
      gap: 12px;
    }
  }

  .loading-overlay,
  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
  }

  .error-overlay {
    .error-content {
      text-align: center;

      .error-icon {
        font-size: 48px;
        color: var(--ios-red);
        margin-bottom: 16px;
      }

      .error-message {
        font-size: 16px;
        color: var(--ios-text-secondary);
        margin: 0 0 20px 0;
      }
    }
  }
}

.preview-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid var(--ios-separator-color);

  .file-actions {
    display: flex;
    gap: 8px;
  }
}
</style>
