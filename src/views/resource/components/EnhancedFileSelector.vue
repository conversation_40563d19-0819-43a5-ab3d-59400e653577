<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="title"
    width="90%"
    top="5vh"
    append-to-body
    destroy-on-close
    custom-class="enhanced-file-selector-dialog"
    @close="handleClose"
  >
    <ResourceManager
      :selector-mode="true"
      :selector-title="title"
      :multiple="multiple"
      :max-count="maxCount"
      :file-types="fileTypes"
      :default-category-id="defaultCategoryId"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </el-dialog>
</template>

<script>
import { ref, watch } from 'vue'
import ResourceManager from '../index.vue'

export default {
  name: 'EnhancedFileSelector',
  components: {
    ResourceManager
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '选择文件'
    },
    multiple: {
      type: Boolean,
      default: true
    },
    maxCount: {
      type: Number,
      default: 0 // 0表示无限制
    },
    fileTypes: {
      type: Array,
      default: () => [] // ['image', 'video', 'document'] 等
    },
    defaultCategoryId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['close', 'confirm'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)

    // 监听visible变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal
    }, { immediate: true })

    // 监听dialogVisible变化
    watch(dialogVisible, (newVal) => {
      if (!newVal && props.visible) {
        emit('close')
      }
    })

    const handleClose = () => {
      dialogVisible.value = false
      emit('close')
    }

    const handleConfirm = (selectedFiles) => {
      emit('confirm', selectedFiles)
      handleClose()
    }

    const handleCancel = () => {
      handleClose()
    }

    return {
      dialogVisible,
      handleClose,
      handleConfirm,
      handleCancel
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.enhanced-file-selector-dialog) {
  .el-dialog {
    overflow: hidden;
  }
  
  .el-dialog__header {
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: var(--ios-text-primary);
    }
  }
  
  .el-dialog__body {

    border-radius: var(--ios-border-radius);
    padding-top: 0;
    max-height: 80vh;
    overflow: hidden;
  }
  
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    
    .el-dialog__close {
      color: var(--ios-text-secondary);
      font-size: 18px;
      
      &:hover {
        color: var(--ios-text-primary);
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  :deep(.enhanced-file-selector-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 0 auto;
    }
  }
}
</style>
