<template>
  <div class="upload-area-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="upload-area" @click.stop>
      <!-- 头部 -->
      <div class="upload-header">
        <h3 class="upload-title">上传文件</h3>
        <el-button
          type="text"
          icon="el-icon-close"
          @click="handleClose"
          class="close-btn"
        />
      </div>

      <!-- 拖拽上传区域 -->
      <div
        :class="[
          'drop-zone',
          { 'drag-over': isDragOver }
        ]"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
      >
        <div class="drop-content">
          <i class="el-icon-upload drop-icon"></i>
          <p class="drop-text">
            拖拽文件到此处，或
            <span class="click-text">点击选择文件</span>
          </p>
          <p class="drop-hint">
            支持多文件上传，单个文件最大 {{ maxFileSize }}MB
          </p>
        </div>
        
        <!-- 隐藏的文件输入框 -->
        <input
          ref="fileInput"
          type="file"
          multiple
          :accept="acceptTypes"
          @change="handleFileSelect"
          style="display: none"
        />
      </div>

      <!-- 文件类型选择 -->
      <div class="file-type-selector">
        <el-radio-group v-model="uploadType" @change="handleTypeChange">
          <el-radio-button label="all">全部文件</el-radio-button>
          <el-radio-button label="image">图片</el-radio-button>
          <el-radio-button label="video">视频</el-radio-button>
          <el-radio-button label="document">文档</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 上传队列 -->
      <div v-if="uploadQueue.length > 0" class="upload-queue">
        <div class="queue-header">
          <span class="queue-title">上传队列 ({{ uploadQueue.length }})</span>
          <div class="queue-actions">
            <el-button
              v-if="!uploadStatus.isUploading"
              type="primary"
              size="mini"
              @click="startUpload"
              :disabled="!hasValidFiles"
            >
              开始上传
            </el-button>
            <el-button
              v-else
              size="mini"
              @click="pauseUpload"
            >
              暂停上传
            </el-button>
            <el-button
              size="mini"
              @click="clearQueue"
            >
              清空队列
            </el-button>
          </div>
        </div>

        <!-- 总体进度 -->
        <div v-if="uploadStatus.isUploading" class="overall-progress">
          <el-progress
            :percentage="uploadStatus.progress"
            :status="uploadStatus.progress === 100 ? 'success' : ''"
          />
          <div class="progress-text">
            {{ uploadStatus.completedFiles }}/{{ uploadStatus.totalFiles }} 已完成
          </div>
        </div>

        <!-- 文件列表 -->
        <div class="file-list">
          <div
            v-for="item in uploadQueue"
            :key="item.id"
            class="file-item"
          >
            <div class="file-info">
              <div class="file-icon">
                <i :class="getFileIcon(item.fileName)"></i>
              </div>
              <div class="file-details">
                <div class="file-name" :title="item.fileName">
                  {{ item.fileName }}
                </div>
                <div class="file-meta">
                  <span class="file-size">{{ formatFileSize(item.fileSize) }}</span>
                  <span class="file-status" :class="getStatusClass(item.status)">
                    {{ getStatusText(item.status) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 进度条 -->
            <div v-if="item.status === 'uploading'" class="file-progress">
              <el-progress
                :percentage="item.progress"
                :show-text="false"
                stroke-width="4"
              />
            </div>

            <!-- 错误信息 -->
            <div v-if="item.error" class="file-error">
              <i class="el-icon-warning"></i>
              {{ item.error }}
            </div>

            <!-- 操作按钮 -->
            <div class="file-actions">
              <el-button
                v-if="item.status === 'error'"
                type="text"
                size="mini"
                @click="retryUpload(item.id)"
              >
                重试
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="removeUploadItem(item.id)"
              >
                移除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="upload-footer">
        <div class="upload-tips">
          <i class="el-icon-info"></i>
          <span>{{ getUploadTips() }}</span>
        </div>
        <div class="footer-actions">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useUpload } from '../hooks/useUpload'
import { formatFileSize, getFileType, validateFileSize, validateFileType } from '../utils/fileUtils'
import { FILE_ICONS, FILE_TYPES, DEFAULT_CONFIG } from '../utils/constants'

export default {
  name: 'UploadArea',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    categoryId: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'upload-success'],
  setup(props, { emit }) {
    // 使用上传hook
    const {
      uploadQueue,
      uploadStatus,
      addFilesToQueue,
      startUpload: startUploadHook,
      pauseUpload: pauseUploadHook,
      removeUploadItem: removeUploadItemHook,
      clearQueue: clearQueueHook,
      retryUpload: retryUploadHook
    } = useUpload()

    // 响应式数据
    const isDragOver = ref(false)
    const uploadType = ref('all')
    const fileInput = ref(null)

    // 计算属性
    const maxFileSize = computed(() => {
      switch (uploadType.value) {
        case 'image':
          return 10
        case 'video':
          return 100
        case 'document':
          return 50
        default:
          return DEFAULT_CONFIG.maxFileSize
      }
    })

    const acceptTypes = computed(() => {
      switch (uploadType.value) {
        case 'image':
          return 'image/*'
        case 'video':
          return 'video/*'
        case 'document':
          return '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx'
        default:
          return '*/*'
      }
    })

    const allowedTypes = computed(() => {
      switch (uploadType.value) {
        case 'image':
          return DEFAULT_CONFIG.allowedImageTypes
        case 'video':
          return DEFAULT_CONFIG.allowedVideoTypes
        case 'document':
          return DEFAULT_CONFIG.allowedDocumentTypes
        default:
          return []
      }
    })

    const hasValidFiles = computed(() => {
      return uploadQueue.value.some(item => 
        item.status === 'pending' || item.status === 'error'
      )
    })

    // 监听上传完成
    watch(() => uploadStatus.completedFiles, (newVal) => {
      if (newVal > 0 && newVal === uploadStatus.totalFiles) {
        emit('upload-success')
      }
    })

    // 拖拽事件处理
    const handleDragEnter = (e) => {
      e.preventDefault()
      isDragOver.value = true
    }

    const handleDragOver = (e) => {
      e.preventDefault()
    }

    const handleDragLeave = (e) => {
      e.preventDefault()
      if (!e.currentTarget.contains(e.relatedTarget)) {
        isDragOver.value = false
      }
    }

    const handleDrop = (e) => {
      e.preventDefault()
      isDragOver.value = false
      
      const files = Array.from(e.dataTransfer.files)
      handleFiles(files)
    }

    // 文件选择处理
    const triggerFileInput = () => {
      fileInput.value?.click()
    }

    const handleFileSelect = (e) => {
      const files = Array.from(e.target.files)
      handleFiles(files)
      // 清空input值，允许重复选择同一文件
      e.target.value = ''
    }

    const handleFiles = (files) => {
      if (!files || files.length === 0) return

      addFilesToQueue(files, {
        categoryId: props.categoryId,
        maxFileSize: maxFileSize.value,
        allowedTypes: allowedTypes.value,
        autoUpload: false
      })
    }

    // 上传控制
    const startUpload = () => {
      startUploadHook()
    }

    const pauseUpload = () => {
      pauseUploadHook()
    }

    const retryUpload = (uploadId) => {
      retryUploadHook(uploadId)
    }

    const removeUploadItem = (uploadId) => {
      removeUploadItemHook(uploadId)
    }

    const clearQueue = () => {
      clearQueueHook()
    }

    // 其他方法
    const handleTypeChange = () => {
      // 类型改变时可以重新验证队列中的文件
    }

    const handleClose = () => {
      emit('close')
    }

    const handleOverlayClick = () => {
      emit('close')
    }

    const getFileIcon = (fileName) => {
      const fileType = getFileType(fileName)
      return FILE_ICONS[fileType] || FILE_ICONS[FILE_TYPES.OTHER]
    }

    const getStatusClass = (status) => {
      return `status-${status}`
    }

    const getStatusText = (status) => {
      const statusTexts = {
        pending: '等待上传',
        uploading: '上传中',
        success: '上传成功',
        error: '上传失败',
        cancelled: '已取消'
      }
      return statusTexts[status] || '未知状态'
    }

    const getUploadTips = () => {
      switch (uploadType.value) {
        case 'image':
          return '支持 JPG、PNG、GIF 等图片格式'
        case 'video':
          return '支持 MP4、AVI、MOV 等视频格式'
        case 'document':
          return '支持 PDF、Word、Excel、PPT 等文档格式'
        default:
          return '支持多种文件格式上传'
      }
    }

    return {
      // 响应式数据
      isDragOver,
      uploadType,
      fileInput,
      uploadQueue,
      uploadStatus,
      
      // 计算属性
      maxFileSize,
      acceptTypes,
      allowedTypes,
      hasValidFiles,
      
      // 方法
      handleDragEnter,
      handleDragOver,
      handleDragLeave,
      handleDrop,
      triggerFileInput,
      handleFileSelect,
      handleTypeChange,
      handleClose,
      handleOverlayClick,
      startUpload,
      pauseUpload,
      retryUpload,
      removeUploadItem,
      clearQueue,
      formatFileSize,
      getFileIcon,
      getStatusClass,
      getStatusText,
      getUploadTips
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-area-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.upload-area {
  background: var(--ios-background-secondary);
  border-radius: var(--ios-border-radius-large);
  box-shadow: 0 12px 32px var(--ios-shadow-dark);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .upload-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--ios-separator-color);

    .upload-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--ios-text-primary);
    }

    .close-btn {
      color: var(--ios-text-secondary);
      font-size: 18px;
      padding: 4px;
      
      &:hover {
        color: var(--ios-text-primary);
        background: var(--ios-fill-quaternary);
        border-radius: var(--ios-border-radius-small);
      }
    }
  }

  .drop-zone {
    margin: 24px;
    padding: 40px 20px;
    border: 2px dashed var(--ios-border-color);
    border-radius: var(--ios-border-radius);
    background: var(--ios-fill-quaternary);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;

    &:hover,
    &.drag-over {
      border-color: var(--ios-blue);
      background: var(--ios-selection-background);
    }

    .drop-content {
      .drop-icon {
        font-size: 48px;
        color: var(--ios-blue);
        margin-bottom: 16px;
      }

      .drop-text {
        font-size: 16px;
        color: var(--ios-text-primary);
        margin: 0 0 8px 0;

        .click-text {
          color: var(--ios-blue);
          font-weight: 500;
        }
      }

      .drop-hint {
        font-size: 14px;
        color: var(--ios-text-secondary);
        margin: 0;
      }
    }
  }

  .file-type-selector {
    padding: 0 24px 16px;
    text-align: center;
  }

  .upload-queue {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin: 0 24px 16px;
    border: 1px solid var(--ios-border-color-light);
    border-radius: var(--ios-border-radius);

    .queue-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: var(--ios-light-gray);
      border-bottom: 1px solid var(--ios-separator-color);

      .queue-title {
        font-weight: 500;
        color: var(--ios-text-primary);
      }

      .queue-actions {
        display: flex;
        gap: 8px;
      }
    }

    .overall-progress {
      padding: 12px 16px;
      border-bottom: 1px solid var(--ios-separator-color);

      .progress-text {
        text-align: center;
        font-size: 12px;
        color: var(--ios-text-secondary);
        margin-top: 4px;
      }
    }

    .file-list {
      flex: 1;
      overflow-y: auto;
      max-height: 300px;

      .file-item {
        padding: 12px 16px;
        border-bottom: 1px solid var(--ios-separator-color);

        &:last-child {
          border-bottom: none;
        }

        .file-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .file-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--ios-fill-quaternary);
            border-radius: var(--ios-border-radius-small);

            i {
              font-size: 18px;
              color: var(--ios-blue);
            }
          }

          .file-details {
            flex: 1;
            min-width: 0;

            .file-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--ios-text-primary);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 2px;
            }

            .file-meta {
              display: flex;
              gap: 12px;
              font-size: 12px;

              .file-size {
                color: var(--ios-text-secondary);
              }

              .file-status {
                &.status-pending {
                  color: var(--ios-text-secondary);
                }
                &.status-uploading {
                  color: var(--ios-blue);
                }
                &.status-success {
                  color: var(--ios-green);
                }
                &.status-error {
                  color: var(--ios-red);
                }
                &.status-cancelled {
                  color: var(--ios-text-tertiary);
                }
              }
            }
          }
        }

        .file-progress {
          margin: 8px 0 4px 44px;
        }

        .file-error {
          margin: 4px 0 0 44px;
          font-size: 12px;
          color: var(--ios-red);
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .file-actions {
          margin-top: 8px;
          text-align: right;
        }
      }
    }
  }

  .upload-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-top: 1px solid var(--ios-separator-color);
    background: var(--ios-light-gray);

    .upload-tips {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 13px;
      color: var(--ios-text-secondary);

      i {
        color: var(--ios-blue);
      }
    }
  }
}
</style>
