<template>
  <div class="resource-sidebar">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <h3 class="sidebar-title">文件分类</h3>
      <el-button
        type="text"
        icon="el-icon-plus"
        size="small"
        title="新建分类"
        class="add-category-btn"
        @click="showCreateDialog = true"
      />
    </div>

    <!-- 分类列表 -->
    <div v-loading="loading" class="category-list">
      <div
        v-for="category in categories"
        :key="category.id"
        :class="[
          'category-item',
          { 'active': currentCategory?.id === category.id }
        ]"
        @click="handleCategoryClick(category)"
        @contextmenu.prevent="handleContextMenu($event, category)"
      >
        <div class="category-content">
          <div class="category-info">
            <i class="el-icon-folder category-icon" />
            <span class="category-name" :title="category.categoryName">
              {{ category.categoryName }}
            </span>
          </div>

          <div class="category-meta">
            <span v-if="category.fileCount !== undefined" class="file-count">
              {{ category.fileCount }}
            </span>
            <el-dropdown
              v-if="!category.communal"
              trigger="click"
              @command="handleCategoryAction"
              @click.stop
            >
              <i class="el-icon-more category-more" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="`edit_${category.id}`">
                  <i class="el-icon-edit" /> 编辑
                </el-dropdown-item>
                <el-dropdown-item :command="`delete_${category.id}`" divided>
                  <i class="el-icon-delete" /> 删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <!-- 共享标识 -->
        <div v-if="category.communal" class="shared-badge">
          <el-tag size="mini" type="info">共享</el-tag>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && categories.length === 0" class="empty-state">
        <p class="el-icon-folder-opened empty-icon" />
        <p class="empty-text">暂无分类</p>
        <el-button
          type="primary"
          size="small"
          @click="showCreateDialog = true"
        >
          创建分类
        </el-button>
      </div>
    </div>

    <!-- 创建分类对话框 -->
    <el-dialog
      title="创建分类"
      :visible.sync="showCreateDialog"
      width="400px"
      append-to-body
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="categoryName">
          <el-input
            v-model="createForm.categoryName"
            placeholder="请输入分类名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="createForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateCategory">确定</el-button>
      </div>
    </el-dialog>

    <!-- 编辑分类对话框 -->
    <el-dialog
      title="编辑分类"
      :visible.sync="showEditDialog"
      width="400px"
      append-to-body
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="categoryName">
          <el-input
            v-model="editForm.categoryName"
            placeholder="请输入分类名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="editForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateCategory">确定</el-button>
      </div>
    </el-dialog>

    <!-- 右键菜单 -->
    <Teleport to="body">
      <div
        v-if="contextMenuVisible"
        :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
        class="context-menu"
        @click.stop
      >
        <div class="context-menu-item" @click="handleEditCategory(contextMenuCategory)">
          <i class="el-icon-edit" /> 编辑分类
        </div>
        <div class="context-menu-item danger" @click="handleDeleteCategory(contextMenuCategory)">
          <i class="el-icon-delete" /> 删除分类
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script>
export default {
  name: 'SidebarComponent'
}
</script>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import Teleport from 'vue2-teleport'

const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  currentCategory: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'category-change',
  'category-create',
  'category-update',
  'category-delete'
])

// 响应式数据
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const contextMenuVisible = ref(false)
const contextMenuPosition = reactive({ x: 0, y: 0 })
const contextMenuCategory = ref(null)

// 创建表单
const createForm = reactive({
  categoryName: '',
  remark: ''
})

// 编辑表单
const editForm = reactive({
  id: null,
  categoryName: '',
  remark: ''
})

// 表单验证规则
const createRules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

const editRules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 表单引用
const createFormRef = ref(null)
const editFormRef = ref(null)

// 事件处理
const handleCategoryClick = (category) => {
  emit('category-change', category)
}

const handleCategoryAction = (command) => {
  const [action, categoryId] = command.split('_')
  const category = props.categories.find(c => c.id === categoryId)

  if (action === 'edit') {
    handleEditCategory(category)
  } else if (action === 'delete') {
    handleDeleteCategory(category)
  }
}

const handleCreateCategory = () => {
  console.log(createFormRef.value)
  createFormRef.value?.validate((valid) => {
    if (valid) {
      emit('category-create', { ...createForm })
      resetCreateForm()
      showCreateDialog.value = false
    }
  })
}

const handleEditCategory = (category) => {
  if (!category || category.communal) return

  editForm.id = category.id
  editForm.categoryName = category.categoryName
  editForm.remark = category.remark || ''
  showEditDialog.value = true
  contextMenuVisible.value = false
}

const handleUpdateCategory = () => {
  editFormRef.value.validate((valid) => {
    if (valid) {
      emit('category-update', { ...editForm })
      resetEditForm()
      showEditDialog.value = false
    }
  })
}

const handleDeleteCategory = (category) => {
  if (!category || category.communal) return

  emit('category-delete', category)
  contextMenuVisible.value = false
}

const handleContextMenu = (event, category) => {
  if (category.communal) return

  event.preventDefault()

  // 获取菜单尺寸（估算）
  const menuWidth = 120
  const menuHeight = 80

  // 获取视口尺寸
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // 计算初始位置
  let x = event.clientX
  let y = event.clientY

  // 确保菜单不会超出视口右边界
  if (x + menuWidth > viewportWidth) {
    x = viewportWidth - menuWidth - 10
  }

  // 确保菜单不会超出视口下边界
  if (y + menuHeight > viewportHeight) {
    y = viewportHeight - menuHeight - 10
  }

  // 确保菜单不会超出视口左边界和上边界
  x = Math.max(10, x)
  y = Math.max(10, y)

  contextMenuPosition.x = x
  contextMenuPosition.y = y
  contextMenuCategory.value = category
  contextMenuVisible.value = true
}

const resetCreateForm = () => {
  createForm.categoryName = ''
  createForm.remark = ''
  createFormRef.value?.resetFields()
}

const resetEditForm = () => {
  editForm.id = null
  editForm.categoryName = ''
  editForm.remark = ''
  editFormRef.value?.resetFields()
}

// 点击其他地方关闭右键菜单
const handleClickOutside = (event) => {
  if (contextMenuVisible.value) {
    // 检查点击的元素是否在右键菜单内
    const contextMenu = document.querySelector('.context-menu')
    if (contextMenu && !contextMenu.contains(event.target)) {
      contextMenuVisible.value = false
    }
  }
}

// 按ESC键关闭菜单
const handleKeyDown = (event) => {
  if (event.key === 'Escape' && contextMenuVisible.value) {
    contextMenuVisible.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeyDown)
})

</script>

<style lang="scss" scoped>
.resource-sidebar {
  width: 280px;
  background: var(--ios-background-secondary);
  border-radius: var(--ios-border-radius);
  box-shadow: 0 2px 8px var(--ios-shadow-light);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-separator-color);

    .sidebar-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--ios-text-primary);
    }

    .add-category-btn {
      color: var(--ios-blue);
      padding: 4px;

      &:hover {
        background: var(--ios-fill-quaternary);
        border-radius: var(--ios-border-radius-small);
      }
    }
  }

  .category-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;

    .category-item {
      position: relative;
      margin: 2px 12px;
      padding: 12px 16px;
      border-radius: var(--ios-border-radius-small);
      cursor: pointer;
      transition: all 0.2s ease;
      user-select: none;

      &:hover {
        background: var(--ios-fill-quaternary);
      }

      &.active {
        background: var(--ios-blue);
        color: white;
        box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);

        .category-icon,
        .category-name,
        .file-count {
          color: white !important;
        }

        .category-more {
          color: rgba(255, 255, 255, 0.8);

          &:hover {
            color: white;
          }
        }
      }

      .category-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .category-info {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;

          .category-icon {
            font-size: 16px;
            color: var(--ios-blue);
            margin-right: 8px;
            flex-shrink: 0;
          }

          .category-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--ios-text-primary);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .category-meta {
          display: flex;
          align-items: center;
          gap: 8px;

          .file-count {
            font-size: 12px;
            color: var(--ios-text-secondary);
            background: var(--ios-fill-secondary);
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 20px;
            text-align: center;
          }

          .category-more {
            font-size: 14px;
            color: var(--ios-text-tertiary);
            padding: 4px;
            border-radius: 4px;
            opacity: 0;
            transition: all 0.2s ease;

            &:hover {
              background: var(--ios-fill-tertiary);
              color: var(--ios-text-primary);
            }
          }
        }
      }

      &:hover .category-more {
        opacity: 1;
      }

      .shared-badge {
        position: absolute;
        top: 4px;
        right: 4px;
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      text-align: center;

      .empty-icon {
        font-size: 48px;
        color: var(--ios-text-quaternary);
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 14px;
        color: var(--ios-text-secondary);
        margin: 0 0 16px 0;
      }
    }
  }
}

.context-menu {
  position: fixed;
  background: var(--ios-background-secondary);
  border: 1px solid var(--ios-border-color-light);
  border-radius: var(--ios-border-radius);
  box-shadow: 0 8px 24px var(--ios-shadow-medium);
  backdrop-filter: blur(20px);
  z-index: 99999;
  min-width: 120px;
  overflow: hidden;
  animation: contextMenuFadeIn 0.15s ease-out;

  .context-menu-item {
    padding: 8px 16px;
    font-size: 14px;
    color: var(--ios-text-primary);
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--ios-fill-quaternary);
    }

    &.danger {
      color: var(--ios-red);
    }

    i {
      margin-right: 8px;
    }
  }
}

// 动画效果
@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resource-sidebar {
    width: 100%;
    height: auto;
    max-height: 300px;
  }
}
</style>
