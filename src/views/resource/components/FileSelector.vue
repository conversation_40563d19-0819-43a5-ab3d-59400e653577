<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="选择文件"
    width="80%"
    top="5vh"
    append-to-body
    destroy-on-close
    custom-class="file-selector-dialog"
    @close="handleClose"
  >
    <div class="file-selector">
      <!-- 工具栏 -->
      <div class="selector-toolbar">
        <div class="toolbar-left">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文件..."
            size="small"
            clearable
            @input="handleSearch"
            style="width: 300px;"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="handleSearch"
            />
          </el-input>
        </div>
        
        <div class="toolbar-right">
          <el-radio-group
            v-model="viewMode"
            size="small"
          >
            <el-radio-button label="grid">
              <i class="el-icon-menu"></i>
            </el-radio-button>
            <el-radio-button label="list">
              <i class="el-icon-s-grid"></i>
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="selector-content">
        <!-- 侧边栏 -->
        <div class="selector-sidebar">
          <div class="category-list">
            <div
              v-for="category in categories"
              :key="category.id"
              :class="[
                'category-item',
                { 'active': currentCategory?.id === category.id }
              ]"
              @click="handleCategoryChange(category)"
            >
              <i class="el-icon-folder category-icon"></i>
              <span class="category-name">{{ category.categoryName }}</span>
              <span v-if="category.fileCount !== undefined" class="file-count">
                {{ category.fileCount }}
              </span>
            </div>
          </div>
        </div>

        <!-- 文件列表 -->
        <div class="selector-main">
          <FileDisplay
            :files="files"
            :loading="filesLoading"
            :view-mode="viewMode"
            :selected-files="selectedFiles"
            :selector-mode="true"
            @file-select="handleFileSelect"
            @selection-change="handleSelectionChange"
          />

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              :current-page="pagination.pageNum"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              :page-sizes="[20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>

      <!-- 选择信息 -->
      <div v-if="selectedFiles.length > 0" class="selection-info">
        <div class="selection-summary">
          <span class="selection-count">已选择 {{ selectedFiles.length }} 个文件</span>
          <div class="selected-files">
            <el-tag
              v-for="file in selectedFiles.slice(0, 3)"
              :key="file.id"
              size="small"
              closable
              @close="removeSelectedFile(file)"
            >
              {{ file.fileName }}
            </el-tag>
            <span v-if="selectedFiles.length > 3" class="more-files">
              等{{ selectedFiles.length }}个文件
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <div slot="footer" class="dialog-footer">
      <div class="footer-info">
        <span v-if="multiple">
          {{ selectedFiles.length > 0 ? `已选择 ${selectedFiles.length} 个文件` : '请选择文件' }}
        </span>
        <span v-else>
          {{ selectedFiles.length > 0 ? `已选择: ${selectedFiles[0].fileName}` : '请选择文件' }}
        </span>
      </div>
      <div class="footer-actions">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="selectedFiles.length === 0"
          @click="handleConfirm"
        >
          确定 {{ selectedFiles.length > 0 ? `(${selectedFiles.length})` : '' }}
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import FileDisplay from './FileDisplay.vue'
import { useFileOperations } from '../hooks/useFileOperations'
import { useSelection } from '../hooks/useSelection'
import { useSearch } from '../hooks/useSearch'

export default {
  name: 'FileSelector',
  components: {
    FileDisplay
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    fileTypes: {
      type: Array,
      default: () => [] // ['image', 'video', 'document'] 等
    },
    maxCount: {
      type: Number,
      default: 0 // 0表示无限制
    },
    defaultCategoryId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['close', 'confirm'],
  setup(props, { emit }) {
    // 响应式数据
    const dialogVisible = ref(false)
    const viewMode = ref('grid')
    const currentCategory = ref(null)
    const categories = ref([])
    const files = ref([])
    const searchKeyword = ref('')
    const filesLoading = ref(false)
    
    // 分页
    const pagination = reactive({
      pageNum: 1,
      pageSize: 50,
      total: 0
    })

    // 使用组合式函数
    const { selectedFiles, handleSelectionChange, clearSelection } = useSelection()
    const { searchFiles } = useSearch()
    const { loadCategories, loadFiles } = useFileOperations()

    // 监听visible变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal
      if (newVal) {
        initSelector()
      } else {
        clearSelection()
      }
    })

    // 监听dialogVisible变化
    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('close')
      }
    })

    // 监听分类变化
    watch(currentCategory, (newCategory) => {
      if (newCategory) {
        loadFileList()
      }
    })

    // 初始化选择器
    const initSelector = async () => {
      await loadCategoryList()
      
      // 设置默认分类
      if (props.defaultCategoryId && categories.value.length > 0) {
        const defaultCategory = categories.value.find(c => c.id === props.defaultCategoryId)
        if (defaultCategory) {
          currentCategory.value = defaultCategory
        }
      }
      
      // 如果没有默认分类，选择第一个
      if (!currentCategory.value && categories.value.length > 0) {
        currentCategory.value = categories.value[0]
      }
    }

    // 加载分类列表
    const loadCategoryList = async () => {
      try {
        const result = await loadCategories()
        categories.value = result.rows || []
      } catch (error) {
        console.error('加载分类列表失败:', error)
      }
    }

    // 加载文件列表
    const loadFileList = async () => {
      if (!currentCategory.value) return
      
      filesLoading.value = true
      try {
        const params = {
          pageNum: pagination.pageNum,
          pageSize: pagination.pageSize,
          fileCategory: currentCategory.value.id,
          fileName: searchKeyword.value
        }

        // 如果指定了文件类型，添加筛选
        if (props.fileTypes.length > 0) {
          // 这里可以根据实际API调整参数名
          params.fileTypes = props.fileTypes.join(',')
        }

        const result = await loadFiles(params)
        files.value = result.rows || []
        pagination.total = result.total || 0
      } catch (error) {
        console.error('加载文件列表失败:', error)
      } finally {
        filesLoading.value = false
      }
    }

    // 事件处理
    const handleCategoryChange = (category) => {
      currentCategory.value = category
      clearSelection()
      pagination.pageNum = 1
    }

    const handleFileSelect = (file, options = {}) => {
      if (!props.multiple) {
        // 单选模式
        selectedFiles.value = [file]
      } else {
        // 多选模式
        const isSelected = selectedFiles.value.some(f => f.id === file.id)
        
        if (isSelected) {
          // 取消选择
          const index = selectedFiles.value.findIndex(f => f.id === file.id)
          selectedFiles.value.splice(index, 1)
        } else {
          // 检查最大数量限制
          if (props.maxCount > 0 && selectedFiles.value.length >= props.maxCount) {
            // 可以显示提示信息
            return
          }
          
          // 添加选择
          selectedFiles.value.push(file)
        }
      }
    }

    const removeSelectedFile = (file) => {
      const index = selectedFiles.value.findIndex(f => f.id === file.id)
      if (index !== -1) {
        selectedFiles.value.splice(index, 1)
      }
    }

    const handleSearch = () => {
      pagination.pageNum = 1
      loadFileList()
    }

    const handleSizeChange = (size) => {
      pagination.pageSize = size
      pagination.pageNum = 1
      loadFileList()
    }

    const handleCurrentChange = (page) => {
      pagination.pageNum = page
      loadFileList()
    }

    const handleClose = () => {
      dialogVisible.value = false
    }

    const handleConfirm = () => {
      if (selectedFiles.value.length === 0) return
      
      // 返回选择的文件信息
      const result = props.multiple ? selectedFiles.value : selectedFiles.value[0]
      emit('confirm', result)
      handleClose()
    }

    return {
      // 响应式数据
      dialogVisible,
      viewMode,
      currentCategory,
      categories,
      files,
      searchKeyword,
      filesLoading,
      pagination,
      selectedFiles,
      
      // 方法
      handleCategoryChange,
      handleFileSelect,
      handleSelectionChange,
      removeSelectedFile,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      handleClose,
      handleConfirm
    }
  }
}
</script>

<style lang="scss" scoped>
.file-selector-dialog {
  .el-dialog__body {
    padding: 0;
  }
}

.file-selector {
  display: flex;
  flex-direction: column;
  height: 70vh;

  .selector-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-separator-color);
    background: var(--ios-background-secondary);
  }

  .selector-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .selector-sidebar {
      width: 200px;
      border-right: 1px solid var(--ios-separator-color);
      background: var(--ios-background-secondary);
      overflow-y: auto;

      .category-list {
        padding: 8px 0;

        .category-item {
          display: flex;
          align-items: center;
          padding: 8px 16px;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: var(--ios-fill-quaternary);
          }

          &.active {
            background: var(--ios-blue);
            color: white;
          }

          .category-icon {
            margin-right: 8px;
            font-size: 14px;
          }

          .category-name {
            flex: 1;
            font-size: 14px;
          }

          .file-count {
            font-size: 12px;
            color: var(--ios-text-secondary);
            background: var(--ios-fill-secondary);
            padding: 2px 6px;
            border-radius: 10px;
          }
        }
      }
    }

    .selector-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .pagination-wrapper {
        padding: 16px;
        border-top: 1px solid var(--ios-separator-color);
        background: var(--ios-background-secondary);
        text-align: center;
      }
    }
  }

  .selection-info {
    padding: 12px 20px;
    border-top: 1px solid var(--ios-separator-color);
    background: var(--ios-light-gray);

    .selection-summary {
      display: flex;
      align-items: center;
      gap: 12px;

      .selection-count {
        font-size: 14px;
        font-weight: 500;
        color: var(--ios-text-primary);
      }

      .selected-files {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .more-files {
          font-size: 12px;
          color: var(--ios-text-secondary);
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;

  .footer-info {
    font-size: 14px;
    color: var(--ios-text-secondary);
  }

  .footer-actions {
    display: flex;
    gap: 12px;
  }
}
</style>
