# 文件选择器安装指南

## 问题解决

### Vue模板编译错误

如果遇到以下错误：
```
[Vue warn]: You are using the runtime-only build of Vue where the template compiler is not available.
```

这是因为Vue 2.7的运行时版本不包含模板编译器。我们提供了两种解决方案：

## 解决方案一：使用插件方式（推荐）

### 1. 导入插件
```javascript
import { fileSelector } from '@/views/resource/utils/fileSelectorPlugin'
```

### 2. 直接使用
```javascript
// 选择单个文件
const file = await fileSelector.selectSingle()

// 选择多个文件
const files = await fileSelector.selectMultiple()

// 选择图片
const images = await fileSelector.selectImages()
```

### 优点
- 无需模板编译
- 使用简单
- 全局单例，性能更好

## 解决方案二：使用Mixin方式

### 1. 导入Mixin和组件
```javascript
import fileSelectorMixin from '@/views/resource/utils/fileSelectorMixin'
import FileSelector from '@/views/resource/components/FileSelector.vue'

export default {
  mixins: [fileSelectorMixin],
  components: {
    FileSelector
  }
}
```

### 2. 在模板中添加组件
```vue
<template>
  <div>
    <!-- 你的页面内容 -->
    
    <!-- 文件选择器组件 -->
    <FileSelector
      :visible="fileSelectorVisible"
      :multiple="fileSelectorOptions.multiple"
      :file-types="fileSelectorOptions.fileTypes"
      :max-count="fileSelectorOptions.maxCount"
      :default-category-id="fileSelectorOptions.defaultCategoryId"
      @confirm="handleFileSelectorConfirm"
      @close="handleFileSelectorClose"
    />
  </div>
</template>
```

### 3. 使用Mixin提供的方法
```javascript
// 选择单个文件
const file = await this.$selectSingleFile()

// 选择多个文件
const files = await this.$selectMultipleFiles()

// 选择图片
const images = await this.$selectImages()
```

### 优点
- 更灵活的控制
- 可以自定义组件属性
- 适合复杂场景

## 推荐使用方式

对于大多数场景，推荐使用**插件方式**，因为：

1. **简单易用**：无需在每个组件中添加模板
2. **性能更好**：全局单例，避免重复创建
3. **无编译问题**：使用渲染函数，避免模板编译错误

## 完整示例

### 插件方式示例
```vue
<template>
  <div>
    <el-button @click="selectFile">选择文件</el-button>
    <div v-if="selectedFile">
      选择的文件: {{ selectedFile.fileName }}
    </div>
  </div>
</template>

<script>
import { fileSelector } from '@/views/resource/utils/fileSelectorPlugin'

export default {
  data() {
    return {
      selectedFile: null
    }
  },
  methods: {
    async selectFile() {
      try {
        this.selectedFile = await fileSelector.selectSingle()
        this.$message.success('文件选择成功')
      } catch (error) {
        console.log('用户取消选择')
      }
    }
  }
}
</script>
```

### Mixin方式示例
```vue
<template>
  <div>
    <el-button @click="selectFile">选择文件</el-button>
    <div v-if="selectedFile">
      选择的文件: {{ selectedFile.fileName }}
    </div>
    
    <FileSelector
      :visible="fileSelectorVisible"
      :multiple="fileSelectorOptions.multiple"
      :file-types="fileSelectorOptions.fileTypes"
      :max-count="fileSelectorOptions.maxCount"
      :default-category-id="fileSelectorOptions.defaultCategoryId"
      @confirm="handleFileSelectorConfirm"
      @close="handleFileSelectorClose"
    />
  </div>
</template>

<script>
import fileSelectorMixin from '@/views/resource/utils/fileSelectorMixin'
import FileSelector from '@/views/resource/components/FileSelector.vue'

export default {
  mixins: [fileSelectorMixin],
  components: {
    FileSelector
  },
  data() {
    return {
      selectedFile: null
    }
  },
  methods: {
    async selectFile() {
      try {
        this.selectedFile = await this.$selectSingleFile()
        this.$message.success('文件选择成功')
      } catch (error) {
        console.log('用户取消选择')
      }
    }
  }
}
</script>
```

## 注意事项

1. **权限检查**：确保用户有访问文件资源的权限
2. **依赖组件**：确保项目中已正确配置Element UI
3. **样式兼容**：确保iOS样式变量已正确定义
4. **API兼容**：确保文件资源相关的API接口可用

## 故障排除

### 1. 组件找不到
确保正确导入了FileSelector组件：
```javascript
import FileSelector from '@/views/resource/components/FileSelector.vue'
```

### 2. 样式问题
确保项目中包含了iOS样式文件：
```scss
@import '@/styles/ios-style.scss';
```

### 3. API错误
检查文件资源相关的API接口是否正常工作：
- `listFileres` - 获取文件列表
- `listCategory` - 获取分类列表

### 4. 权限问题
确保用户有以下权限：
- `promotion:fileres:list` - 查看文件列表权限
