<template>
  <div :class="['resource-manager', { 'selector-mode': selectorMode }]">
    <!-- 顶部工具栏 -->
    <Toolbar
      :current-category="currentCategory"
      :view-mode="viewMode"
      :search-keyword="searchKeyword"
      :selected-files="selectedFiles"
      :refreshing="refreshing"
      :selector-mode="selectorMode"
      @upload="handleUpload"
      @search="handleSearch"
      @view-mode-change="handleViewModeChange"
      @batch-delete="handleBatchDelete"
      @refresh="handleRefresh"
      @advanced-search="handleAdvancedSearch"
    />

    <div class="resource-content">
      <!-- 侧边栏 -->
      <Sidebar
        :categories="categories"
        :current-category="currentCategory"
        :loading="categoriesLoading"
        @category-change="handleCategoryChange"
        @category-create="handleCategoryCreate"
        @category-update="handleCategoryUpdate"
        @category-delete="handleCategoryDelete"
      />

      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 上传区域 -->
        <UploadArea
          v-if="showUploadArea"
          :category-id="currentCategory?.id"
          :visible="showUploadArea"
          @upload-success="handleUploadSuccess"
          @close="showUploadArea = false"
        />

        <!-- 文件列表 -->
        <FileDisplay
          :files="files"
          :loading="filesLoading"
          :view-mode="viewMode"
          :selected-files="selectedFiles"
          :selector-mode="selectorMode"
          @file-select="handleFileSelect"
          @file-preview="handleFilePreview"
          @file-download="handleFileDownload"
          @file-delete="handleFileDelete"
          @file-rename="handleFileRename"
          @selection-change="handleSelectionChange"
        />

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            :current-page="pagination.pageNum"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[20, 50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 文件预览弹窗 -->
    <FilePreview :visible="previewVisible" :file="previewFile" @close="previewVisible = false" />

    <!-- 选择器模式的底部操作栏 -->
    <div v-if="selectorMode" class="selector-footer">
      <div v-if="selectedFiles.length > 0" class="selected-files-preview">
        <div class="preview-title">已选择的文件：</div>
        <div class="selected-files-list">
          <el-tag
            v-for="file in selectedFiles.slice(0, 5)"
            :key="file.id"
            size="small"
            closable
            @close="removeSelectedFile(file)"
          >
            <span class="file-name">{{ file.fileName }}</span>

          </el-tag>
          <span v-if="selectedFiles.length > 5" class="more-files">
            等{{ selectedFiles.length }}个文件
          </span>
        </div>
      </div>
      <div v-else class="selected-files-preview" />

      <div class="selector-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :disabled="selectedFiles.length === 0 || (maxCount > 0 && selectedFiles.length > maxCount)"
          @click="handleConfirm"
        >
          确定 {{ selectedFiles.length > 0 ? `(${selectedFiles.length})` : '' }}
        </el-button>
      </div>
    </div>

    <!-- 右键菜单 -->
    <ContextMenu
      :visible="contextMenuVisible"
      :position="contextMenuPosition"
      :file="contextMenuFile"
      @close="contextMenuVisible = false"
      @action="handleContextMenuAction"
    />
  </div>
</template>

<script>
export default {
  name: 'ResourceManager'
}
</script>

<script setup>
import { ref, reactive, onMounted, computed, watch, getCurrentInstance } from 'vue'

// 组件导入
import Toolbar from './components/Toolbar.vue'
import Sidebar from './components/Sidebar.vue'
import FileDisplay from './components/FileDisplay.vue'
import UploadArea from './components/UploadArea.vue'
import FilePreview from './components/FilePreview.vue'
import ContextMenu from './components/ContextMenu.vue'

// hooks导入
import { useFileOperations } from './hooks/useFileOperations'
import { useSelection } from './hooks/useSelection'

// 获取props
const props = defineProps({
  // 选择器模式
  selectorMode: {
    type: Boolean,
    default: false
  },
  // 选择器标题
  selectorTitle: {
    type: String,
    default: '选择文件'
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 最大选择数量
  maxCount: {
    type: Number,
    default: 0 // 0表示无限制
  },
  // 文件类型限制
  fileTypes: {
    type: Array,
    default: () => [] // ['image', 'video', 'document'] 等
  },
  // 默认分类ID
  defaultCategoryId: {
    type: [String, Number],
    default: null
  }
})

// 定义emits
const emit = defineEmits(['confirm', 'cancel'])

const { proxy } = getCurrentInstance()
// 响应式数据
// 从localStorage读取视图模式，默认为grid
const getStoredViewMode = () => {
  try {
    return localStorage.getItem('resource_view_mode') || 'grid'
  } catch (error) {
    console.warn('读取视图模式失败:', error)
    return 'grid'
  }
}

const viewMode = ref(getStoredViewMode()) // 'grid' | 'list'
const currentCategory = ref(null)
const categories = ref([])
const files = ref([])
const searchKeyword = ref('')
const showUploadArea = ref(false)

// 高级搜索相关
const advancedSearchParams = ref(null) // 存储高级搜索参数
const isAdvancedSearchActive = ref(false) // 标识是否正在使用高级搜索

// 加载状态
const categoriesLoading = ref(false)
const filesLoading = ref(false)
const refreshing = ref(false)

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
})

// 预览相关
const previewVisible = ref(false)
const previewFile = ref(null)

// 右键菜单
const contextMenuVisible = ref(false)
const contextMenuPosition = reactive({ x: 0, y: 0 })
const contextMenuFile = ref(null)

// 使用组合式函数
const { selectedFiles, handleSelectionChange, clearSelection } = useSelection()
const {
  loadCategories,
  loadFiles,
  deleteFile,
  renameFile,
  downloadFile,
  createCategory,
  updateCategory,
  deleteCategory
} = useFileOperations()

// 计算属性
const hasSelectedFiles = computed(() => selectedFiles.value.length > 0)

// 监听分类变化
watch(currentCategory, (newCategory) => {
  if (newCategory) {
    loadFileList()
  }
})

// 监听搜索关键词
watch(searchKeyword, (keyword) => {
  if (keyword.trim()) {
    performSearch(keyword)
  } else {
    loadFileList()
  }
})

// 监听视图模式变化并保存到localStorage
watch(viewMode, (newMode) => {
  try {
    localStorage.setItem('resource_view_mode', newMode)
  } catch (error) {
    console.warn('保存视图模式失败:', error)
  }
})

// 方法
const loadFileList = async(customParams = {}) => {
  if (!currentCategory.value) return

  filesLoading.value = true
  try {
    // 构建查询参数
    const queryParams = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      fileCategory: currentCategory.value.id,
      ...customParams
    }

    // 如果有普通搜索关键词，添加到参数中
    if (searchKeyword.value && !isAdvancedSearchActive.value) {
      queryParams.fileName = searchKeyword.value
    }

    // 如果有高级搜索参数，合并进去
    if (isAdvancedSearchActive.value && advancedSearchParams.value) {
      Object.assign(queryParams, advancedSearchParams.value)
    }

    const result = await loadFiles(queryParams)
    files.value = result.rows || []
    pagination.total = result.total || 0
  } catch (error) {
    console.error('加载文件列表失败:', error)
    proxy.$modal.msgError('加载文件列表失败')
  } finally {
    filesLoading.value = false
  }
}

const loadCategoryList = async() => {
  categoriesLoading.value = true
  try {
    const result = await loadCategories()
    categories.value = result.rows || []
    // 默认选择第一个分类
    if (categories.value.length > 0 && !currentCategory.value) {
      currentCategory.value = categories.value[0]
    }
  } catch (error) {
    console.error('加载分类列表失败:', error)
    proxy.$modal.msgError('加载分类列表失败')
  } finally {
    categoriesLoading.value = false
  }
}

// 执行搜索
const performSearch = async(keyword) => {
  if (!keyword.trim()) {
    loadFileList()
    return
  }

  // 清除高级搜索状态
  advancedSearchParams.value = null
  isAdvancedSearchActive.value = false

  filesLoading.value = true
  try {
    const result = await loadFiles({
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      fileCategory: currentCategory.value?.id,
      fileName: keyword.trim()
    })
    files.value = result.rows || []
    pagination.total = result.total || 0
  } catch (error) {
    console.error('搜索文件失败:', error)
    proxy.$modal.msgError('搜索文件失败')
  } finally {
    filesLoading.value = false
  }
}

// 事件处理
const handleUpload = () => {
  if (!currentCategory.value) {
    proxy.$modal.msgWarning('请先选择文件分类')
    return
  }
  showUploadArea.value = true
}

const handleSearch = (keyword) => {
  searchKeyword.value = keyword
}

const handleViewModeChange = (mode) => {
  viewMode.value = mode
}

const handleRefresh = async() => {
  if (refreshing.value) return // 防止重复刷新

  refreshing.value = true

  try {
    // 清空搜索关键词和高级搜索
    searchKeyword.value = ''
    advancedSearchParams.value = null
    isAdvancedSearchActive.value = false
    // 清空选择的文件
    clearSelection()
    // 重置分页到第一页
    pagination.pageNum = 1

    // 重新加载分类列表
    await loadCategoryList()

    // 重新加载文件列表
    if (currentCategory.value) {
      await loadFileList()
    }

    // 显示刷新成功提示
    proxy.$modal.msgSuccess('刷新成功')
  } catch (error) {
    console.error('刷新失败:', error)
    proxy.$modal.msgError('刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 处理高级搜索
const handleAdvancedSearch = (searchParams) => {
  // 保存高级搜索参数
  advancedSearchParams.value = searchParams
  isAdvancedSearchActive.value = true

  // 清空普通搜索关键词
  searchKeyword.value = ''

  // 重置分页
  pagination.pageNum = 1

  // 执行搜索
  loadFileList()
}

const handleCategoryChange = (category) => {
  currentCategory.value = category
  clearSelection()
  pagination.pageNum = 1
}

const handleFileSelect = (file, options = {}) => {
  if (props.selectorMode) {
    // 选择器模式下的文件选择逻辑
    if (props.multiple) {
      // 多选模式
      const index = selectedFiles.value.findIndex(f => f.id === file.id)
      if (index > -1) {
        // 已选择，取消选择
        selectedFiles.value.splice(index, 1)
      } else {
        // 未选择，添加选择
        if (props.maxCount > 0 && selectedFiles.value.length >= props.maxCount) {
          proxy.$modal.msgWarning(`最多只能选择 ${props.maxCount} 个文件`)
          return
        }
        selectedFiles.value.push(file)
      }
    } else {
      // 单选模式
      selectedFiles.value = [file]
    }
  } else {
    // 普通模式下的文件选择逻辑
    console.log('选择文件:', file)
  }
}

const handleFilePreview = (file) => {
  previewFile.value = file
  previewVisible.value = true
}

const handleFileDownload = async(file) => {
  try {
    await downloadFile(file)
  } catch (error) {
    console.error('下载失败:', error)
    proxy.$modal.msgError('下载失败')
  }
}

const handleFileDelete = async(file) => {
  try {
    await proxy.$modal.confirm(`确定删除文件 "${file.fileName}" 吗？`)
    await deleteFile(file.id)
    proxy.$modal.msgSuccess('删除成功')
    loadFileList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      proxy.$modal.msgError('删除失败')
    }
  }
}

const handleFileRename = async(file, newName) => {
  try {
    await renameFile(file.id, newName)
    proxy.$modal.msgSuccess('重命名成功')
    loadFileList()
  } catch (error) {
    console.error('重命名失败:', error)
    proxy.$modal.msgError('重命名失败')
  }
}

const handleBatchDelete = async() => {
  if (!hasSelectedFiles.value) return

  try {
    await proxy.$modal.confirm(`确定删除选中的 ${selectedFiles.value.length} 个文件吗？`)
    const deletePromises = selectedFiles.value.map(file => deleteFile(file.id))
    await Promise.all(deletePromises)
    proxy.$modal.msgSuccess('批量删除成功')
    clearSelection()
    loadFileList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      proxy.$modal.msgError('批量删除失败')
    }
  }
}

const handleUploadSuccess = () => {
  showUploadArea.value = false
  loadFileList()
}

const handleCategoryCreate = async(categoryData) => {
  try {
    await createCategory(categoryData)
    proxy.$modal.msgSuccess('创建分类成功')
    loadCategoryList()
  } catch (error) {
    console.error('创建分类失败:', error)
    proxy.$modal.msgError('创建分类失败')
  }
}

const handleCategoryUpdate = async(categoryData) => {
  try {
    await updateCategory(categoryData)
    proxy.$modal.msgSuccess('更新分类成功')
    loadCategoryList()
  } catch (error) {
    console.error('更新分类失败:', error)
    proxy.$modal.msgError('更新分类失败')
  }
}

const handleCategoryDelete = async(category) => {
  try {
    await proxy.$modal.confirm(`确定删除分类 "${category.categoryName}" 吗？`)
    await deleteCategory(category.id)
    proxy.$modal.msgSuccess('删除分类成功')
    loadCategoryList()
    if (currentCategory.value?.id === category.id) {
      currentCategory.value = null
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      proxy.$modal.msgError('删除分类失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  // 根据当前搜索状态决定调用哪个方法
  if (isAdvancedSearchActive.value) {
    loadFileList() // 高级搜索状态下直接调用loadFileList
  } else if (searchKeyword.value.trim()) {
    performSearch(searchKeyword.value)
  } else {
    loadFileList()
  }
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  // 根据当前搜索状态决定调用哪个方法
  if (isAdvancedSearchActive.value) {
    loadFileList() // 高级搜索状态下直接调用loadFileList
  } else if (searchKeyword.value.trim()) {
    performSearch(searchKeyword.value)
  } else {
    loadFileList()
  }
}

const handleContextMenuAction = (action, file) => {
  contextMenuVisible.value = false
  switch (action) {
    case 'preview':
      handleFilePreview(file)
      break
    case 'download':
      handleFileDownload(file)
      break
    case 'rename':
      // 触发重命名
      break
    case 'delete':
      handleFileDelete(file)
      break
  }
}

// 选择器相关方法
const removeSelectedFile = (file) => {
  const index = selectedFiles.value.findIndex(f => f.id === file.id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  }
}

const handleConfirm = () => {
  if (selectedFiles.value.length === 0) {
    proxy.$modal.msgWarning('请选择文件')
    return
  }

  if (props.maxCount > 0 && selectedFiles.value.length > props.maxCount) {
    proxy.$modal.msgWarning(`最多只能选择 ${props.maxCount} 个文件`)
    return
  }

  const result = props.multiple ? selectedFiles.value : selectedFiles.value[0]
  emit('confirm', result)
}

const handleCancel = () => {
  clearSelection()
  emit('cancel')
}

// 生命周期
onMounted(() => {
  loadCategoryList()
})

</script>

<style lang="scss" scoped>
@import './styles/resource.scss';

.resource-manager {
  height: calc(100vh - 145px);
  display: flex;
  flex-direction: column;
}

.resource-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.main-content {
  margin-left: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 16px;
}

// 选择器模式样式
.resource-manager.selector-mode {
  height: calc(80vh);
  display: flex;
  flex-direction: column;

  .selector-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-border-color-light);
    background: var(--ios-background-secondary);
    flex-shrink: 0;

    .selector-title {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--ios-text-primary);
    }

    .selector-info {
      font-size: 14px;
      color: var(--ios-text-secondary);

      .count-limit {
        color: var(--ios-orange);
        font-weight: 500;
      }
    }
  }

  .resource-content {
    flex: 1;
    overflow: hidden;
    display: flex;

    .sidebar {
      flex-shrink: 0;
      width: 200px;
      border-right: 1px solid var(--ios-border-color-light);
    }

    .main-content {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }

  .selector-footer {
    padding: 16px 20px;
    margin-top: 10px;
    border-top: 1px solid var(--ios-border-color-light);
    background: var(--ios-background-secondary);
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 20px;
    flex-shrink: 0;

    .selected-files-preview {
      flex: 1;

      .preview-title {
        font-size: 14px;
        color: var(--ios-text-secondary);
        margin-bottom: 8px;
      }

      .selected-files-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;

        .el-tag {
          .file-name {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .more-files {
          font-size: 12px;
          color: var(--ios-text-secondary);
          background: var(--ios-fill-quaternary);
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
    }

    .selector-actions {
      display: flex;
      gap: 12px;
      flex-shrink: 0;
    }
  }
}

@media (max-width: 768px) {
  .resource-content {
    flex-direction: column;
  }

  .main-content {
    padding: 12px;
  }

  .resource-manager.selector-mode {
    .resource-content {
      flex-direction: column;

    }

    .main-content {
      padding: 12px;
    }

    .selector-footer {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .selector-actions {
        justify-content: center;
      }
    }
  }
}
</style>
