# 文件选择器使用指南

文件选择器组件允许在其他页面中以弹窗形式打开资源管理页面，让用户选择文件并获取文件信息。

## 功能特性

- 🎯 **多种选择模式**：单选、多选、类型限制
- 📁 **分类浏览**：按分类浏览文件
- 🔍 **搜索功能**：实时搜索文件
- 👀 **双视图模式**：网格视图和列表视图
- 📊 **选择统计**：显示已选择的文件信息
- 🎨 **iOS风格**：与主页面保持一致的设计风格

## 基本使用

### 方式一：使用插件（推荐）

```javascript
import { fileSelector } from '@/views/resource/utils/fileSelectorPlugin'
```

### 方式二：使用Mixin

```javascript
import fileSelectorMixin from '@/views/resource/utils/fileSelectorMixin'

export default {
  mixins: [fileSelectorMixin],
  // 在模板中添加组件
  // <FileSelector :visible="fileSelectorVisible" ... />
}
```

### 1. 选择单个文件

```javascript
// 使用插件方式
try {
  const file = await fileSelector.selectSingle()
  console.log('选择的文件:', file)
  // file 包含: { id, fileName, fileSize, url, fileType, createTime, ... }
} catch (error) {
  console.log('用户取消选择')
}

// 使用Mixin方式
try {
  const file = await this.$selectSingleFile()
  console.log('选择的文件:', file)
} catch (error) {
  console.log('用户取消选择')
}
```

### 3. 选择多个文件

```javascript
try {
  const files = await fileSelector.selectMultiple()
  console.log('选择的文件列表:', files)
  // files 是一个数组，包含多个文件对象
} catch (error) {
  console.log('用户取消选择')
}
```

### 4. 按类型选择文件

```javascript
// 选择图片文件
try {
  const images = await fileSelector.selectImages()
  console.log('选择的图片:', images)
} catch (error) {
  console.log('用户取消选择')
}

// 选择视频文件
try {
  const videos = await fileSelector.selectVideos()
  console.log('选择的视频:', videos)
} catch (error) {
  console.log('用户取消选择')
}

// 选择文档文件
try {
  const documents = await fileSelector.selectDocuments()
  console.log('选择的文档:', documents)
} catch (error) {
  console.log('用户取消选择')
}
```

## 高级配置

### 配置选项

```javascript
const options = {
  multiple: true,              // 是否多选，默认true
  fileTypes: ['image'],        // 文件类型限制，可选值: 'image', 'video', 'document'
  maxCount: 5,                 // 最大选择数量，0表示无限制
  defaultCategoryId: 'cat123'  // 默认打开的分类ID
}

try {
  const files = await openFileSelector(options)
  console.log('选择的文件:', files)
} catch (error) {
  console.log('用户取消选择')
}
```

### 限制选择数量

```javascript
// 最多选择3个文件
try {
  const files = await openFileSelector({
    multiple: true,
    maxCount: 3
  })
  console.log('选择的文件:', files)
} catch (error) {
  console.log('用户取消选择')
}
```

### 指定默认分类

```javascript
// 打开时默认显示指定分类的文件
try {
  const files = await openFileSelector({
    defaultCategoryId: 'category-id-123'
  })
  console.log('选择的文件:', files)
} catch (error) {
  console.log('用户取消选择')
}
```

## 在Vue组件中使用

### 在模板中使用

```vue
<template>
  <div>
    <el-button @click="selectFile">选择文件</el-button>
    <el-button @click="selectImages">选择图片</el-button>
    <el-button @click="selectMultipleFiles">选择多个文件</el-button>
    
    <!-- 显示选择的文件 -->
    <div v-if="selectedFile">
      <h3>选择的文件:</h3>
      <p>文件名: {{ selectedFile.fileName }}</p>
      <p>文件大小: {{ formatFileSize(selectedFile.fileSize) }}</p>
      <p>文件URL: {{ selectedFile.url }}</p>
    </div>
    
    <div v-if="selectedFiles.length > 0">
      <h3>选择的文件列表:</h3>
      <ul>
        <li v-for="file in selectedFiles" :key="file.id">
          {{ file.fileName }} ({{ formatFileSize(file.fileSize) }})
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import fileSelector from '@/views/resource/utils/fileSelector'
import { formatFileSize } from '@/views/resource/utils/fileUtils'

export default {
  data() {
    return {
      selectedFile: null,
      selectedFiles: []
    }
  },
  methods: {
    async selectFile() {
      try {
        this.selectedFile = await fileSelector.selectSingle()
        this.$message.success('文件选择成功')
      } catch (error) {
        console.log('用户取消选择')
      }
    },
    
    async selectImages() {
      try {
        this.selectedFiles = await fileSelector.selectImages({
          multiple: true,
          maxCount: 5
        })
        this.$message.success(`选择了 ${this.selectedFiles.length} 个图片`)
      } catch (error) {
        console.log('用户取消选择')
      }
    },
    
    async selectMultipleFiles() {
      try {
        this.selectedFiles = await fileSelector.selectMultiple({
          maxCount: 10
        })
        this.$message.success(`选择了 ${this.selectedFiles.length} 个文件`)
      } catch (error) {
        console.log('用户取消选择')
      }
    },
    
    formatFileSize
  }
}
</script>
```

### 在Composition API中使用

```vue
<script setup>
import { ref } from 'vue'
import fileSelector from '@/views/resource/utils/fileSelector'

const selectedFile = ref(null)
const selectedFiles = ref([])

const selectFile = async () => {
  try {
    selectedFile.value = await fileSelector.selectSingle()
    console.log('选择的文件:', selectedFile.value)
  } catch (error) {
    console.log('用户取消选择')
  }
}

const selectImages = async () => {
  try {
    selectedFiles.value = await fileSelector.selectImages()
    console.log('选择的图片:', selectedFiles.value)
  } catch (error) {
    console.log('用户取消选择')
  }
}
</script>
```

## 返回的文件对象结构

```javascript
{
  id: "file123",                    // 文件ID
  fileName: "example.jpg",          // 文件名
  fileSize: 1024000,               // 文件大小（字节）
  url: "https://...",              // 文件URL
  fileType: "image",               // 文件类型
  fileSuffix: "jpg",               // 文件扩展名
  createTime: "2023-12-01 10:00:00", // 创建时间
  fileCategory: "cat123",          // 所属分类ID
  remark: "备注信息"                // 备注
}
```

## 注意事项

1. **权限检查**：确保用户有访问文件资源的权限
2. **网络状态**：文件选择器需要网络请求，注意处理网络错误
3. **文件大小**：大文件可能影响加载速度
4. **浏览器兼容性**：确保目标浏览器支持相关特性

## 错误处理

```javascript
try {
  const files = await fileSelector.selectMultiple()
  // 处理选择的文件
} catch (error) {
  if (error.message === '用户取消选择') {
    // 用户主动取消，通常不需要特殊处理
    console.log('用户取消了文件选择')
  } else {
    // 其他错误，如网络错误等
    console.error('文件选择出错:', error)
    this.$message.error('文件选择失败，请重试')
  }
}
```

## 自定义样式

文件选择器使用了与主页面相同的iOS风格样式，如需自定义，可以通过CSS覆盖：

```css
/* 自定义文件选择器对话框样式 */
.file-selector-dialog .el-dialog {
  border-radius: 12px;
}

/* 自定义工具栏样式 */
.file-selector .selector-toolbar {
  background: #f8f9fa;
}
```
