import { ref, reactive, computed } from 'vue'
import { debounce } from '../utils/fileUtils'
import { useFileOperations } from './useFileOperations'

/**
 * 搜索相关的组合式函数
 */
export function useSearch() {
  const { searchFiles: searchFilesAPI } = useFileOperations()
  
  // 搜索状态
  const searchState = reactive({
    keyword: '',
    isSearching: false,
    hasSearched: false,
    results: [],
    total: 0,
    error: null
  })
  
  // 高级搜索参数
  const advancedSearch = reactive({
    fileName: '',
    fileType: '',
    fileSize: {
      min: null,
      max: null
    },
    dateRange: {
      start: null,
      end: null
    },
    categoryId: null
  })
  
  // 搜索历史
  const searchHistory = ref([])
  
  // 搜索建议
  const searchSuggestions = ref([])
  
  // 计算属性
  const hasResults = computed(() => searchState.results.length > 0)
  const hasKeyword = computed(() => searchState.keyword.trim().length > 0)
  const isAdvancedSearchActive = computed(() => {
    return advancedSearch.fileName ||
           advancedSearch.fileType ||
           advancedSearch.fileSize.min !== null ||
           advancedSearch.fileSize.max !== null ||
           advancedSearch.dateRange.start ||
           advancedSearch.dateRange.end
  })

  /**
   * 防抖搜索函数
   */
  const debouncedSearch = debounce(async (keyword, options = {}) => {
    await performSearch(keyword, options)
  }, 300)

  /**
   * 执行搜索
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   */
  const performSearch = async (keyword, options = {}) => {
    if (!keyword.trim() && !isAdvancedSearchActive.value) {
      clearSearch()
      return
    }

    searchState.isSearching = true
    searchState.error = null

    try {
      const searchParams = {
        pageNum: options.pageNum || 1,
        pageSize: options.pageSize || 50,
        fileName: keyword.trim() || advancedSearch.fileName,
        fileType: advancedSearch.fileType || options.fileType,
        fileCategory: advancedSearch.categoryId || options.categoryId
      }

      // 添加文件大小筛选
      if (advancedSearch.fileSize.min !== null) {
        searchParams.fileSizeMin = advancedSearch.fileSize.min
      }
      if (advancedSearch.fileSize.max !== null) {
        searchParams.fileSizeMax = advancedSearch.fileSize.max
      }

      // 添加日期范围筛选
      if (advancedSearch.dateRange.start) {
        searchParams.createTimeStart = advancedSearch.dateRange.start
      }
      if (advancedSearch.dateRange.end) {
        searchParams.createTimeEnd = advancedSearch.dateRange.end
      }

      const response = await searchFilesAPI(searchParams)
      
      searchState.results = response.rows || []
      searchState.total = response.total || 0
      searchState.hasSearched = true

      // 保存搜索历史
      if (keyword.trim()) {
        addToSearchHistory(keyword.trim())
      }

    } catch (error) {
      console.error('搜索失败:', error)
      searchState.error = error.message || '搜索失败'
      searchState.results = []
      searchState.total = 0
    } finally {
      searchState.isSearching = false
    }
  }

  /**
   * 简单搜索
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   */
  const searchFiles = (keyword, options = {}) => {
    searchState.keyword = keyword
    debouncedSearch(keyword, options)
  }

  /**
   * 高级搜索
   * @param {Object} params - 高级搜索参数
   */
  const advancedSearchFiles = async (params = {}) => {
    // 更新高级搜索参数
    Object.assign(advancedSearch, params)
    
    await performSearch(searchState.keyword, {
      pageNum: 1,
      pageSize: 50
    })
  }

  /**
   * 清除搜索
   */
  const clearSearch = () => {
    searchState.keyword = ''
    searchState.results = []
    searchState.total = 0
    searchState.hasSearched = false
    searchState.error = null
    clearAdvancedSearch()
  }

  /**
   * 清除高级搜索
   */
  const clearAdvancedSearch = () => {
    advancedSearch.fileName = ''
    advancedSearch.fileType = ''
    advancedSearch.fileSize.min = null
    advancedSearch.fileSize.max = null
    advancedSearch.dateRange.start = null
    advancedSearch.dateRange.end = null
    advancedSearch.categoryId = null
  }

  /**
   * 添加到搜索历史
   * @param {string} keyword - 搜索关键词
   */
  const addToSearchHistory = (keyword) => {
    if (!keyword || keyword.length < 2) return

    // 移除重复项
    const index = searchHistory.value.indexOf(keyword)
    if (index !== -1) {
      searchHistory.value.splice(index, 1)
    }

    // 添加到开头
    searchHistory.value.unshift(keyword)

    // 限制历史记录数量
    if (searchHistory.value.length > 10) {
      searchHistory.value = searchHistory.value.slice(0, 10)
    }

    // 保存到本地存储
    try {
      localStorage.setItem('resource_search_history', JSON.stringify(searchHistory.value))
    } catch (error) {
      console.warn('保存搜索历史失败:', error)
    }
  }

  /**
   * 从本地存储加载搜索历史
   */
  const loadSearchHistory = () => {
    try {
      const saved = localStorage.getItem('resource_search_history')
      if (saved) {
        searchHistory.value = JSON.parse(saved)
      }
    } catch (error) {
      console.warn('加载搜索历史失败:', error)
      searchHistory.value = []
    }
  }

  /**
   * 清除搜索历史
   */
  const clearSearchHistory = () => {
    searchHistory.value = []
    try {
      localStorage.removeItem('resource_search_history')
    } catch (error) {
      console.warn('清除搜索历史失败:', error)
    }
  }

  /**
   * 生成搜索建议
   * @param {string} input - 输入内容
   * @param {Array} allFiles - 所有文件列表
   */
  const generateSuggestions = (input, allFiles = []) => {
    if (!input || input.length < 2) {
      searchSuggestions.value = []
      return
    }

    const suggestions = new Set()
    const inputLower = input.toLowerCase()

    // 从文件名中提取建议
    allFiles.forEach(file => {
      if (file.fileName && file.fileName.toLowerCase().includes(inputLower)) {
        suggestions.add(file.fileName)
      }
    })

    // 从搜索历史中提取建议
    searchHistory.value.forEach(keyword => {
      if (keyword.toLowerCase().includes(inputLower)) {
        suggestions.add(keyword)
      }
    })

    searchSuggestions.value = Array.from(suggestions).slice(0, 8)
  }

  /**
   * 按文件类型筛选
   * @param {string} fileType - 文件类型
   */
  const filterByType = (fileType) => {
    advancedSearch.fileType = fileType
    performSearch(searchState.keyword)
  }

  /**
   * 按文件大小筛选
   * @param {number} minSize - 最小大小（字节）
   * @param {number} maxSize - 最大大小（字节）
   */
  const filterBySize = (minSize, maxSize) => {
    advancedSearch.fileSize.min = minSize
    advancedSearch.fileSize.max = maxSize
    performSearch(searchState.keyword)
  }

  /**
   * 按日期范围筛选
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   */
  const filterByDateRange = (startDate, endDate) => {
    advancedSearch.dateRange.start = startDate
    advancedSearch.dateRange.end = endDate
    performSearch(searchState.keyword)
  }

  /**
   * 获取搜索统计信息
   */
  const getSearchStats = () => {
    const stats = {
      totalResults: searchState.total,
      currentResults: searchState.results.length,
      hasKeyword: hasKeyword.value,
      isAdvanced: isAdvancedSearchActive.value,
      historyCount: searchHistory.value.length
    }

    // 按文件类型统计
    const typeStats = {}
    searchState.results.forEach(file => {
      const type = file.fileType || 'unknown'
      typeStats[type] = (typeStats[type] || 0) + 1
    })
    stats.typeStats = typeStats

    return stats
  }

  // 初始化
  loadSearchHistory()

  return {
    // 响应式数据
    searchState,
    advancedSearch,
    searchHistory,
    searchSuggestions,
    
    // 计算属性
    hasResults,
    hasKeyword,
    isAdvancedSearchActive,
    
    // 方法
    searchFiles,
    advancedSearchFiles,
    clearSearch,
    clearAdvancedSearch,
    addToSearchHistory,
    loadSearchHistory,
    clearSearchHistory,
    generateSuggestions,
    filterByType,
    filterBySize,
    filterByDateRange,
    getSearchStats
  }
}
