import { ref, computed } from 'vue'

/**
 * 文件选择相关的组合式函数
 */
export function useSelection() {
  // 选中的文件列表
  const selectedFiles = ref([])
  
  // 最后选中的文件（用于范围选择）
  const lastSelectedFile = ref(null)
  
  // 计算属性
  const hasSelection = computed(() => selectedFiles.value.length > 0)
  const selectionCount = computed(() => selectedFiles.value.length)
  const isAllSelected = computed(() => {
    // 需要传入总文件列表来判断是否全选
    return false
  })

  /**
   * 选择单个文件
   * @param {Object} file - 文件对象
   * @param {boolean} isCtrlPressed - 是否按住Ctrl键
   * @param {boolean} isShiftPressed - 是否按住Shift键
   * @param {Array} allFiles - 所有文件列表（用于范围选择）
   */
  const selectFile = (file, isCtrlPressed = false, isShiftPressed = false, allFiles = []) => {
    if (!file) return

    const fileIndex = selectedFiles.value.findIndex(f => f.id === file.id)
    const isSelected = fileIndex !== -1

    if (isShiftPressed && lastSelectedFile.value && allFiles.length > 0) {
      // 范围选择
      selectRange(file, allFiles)
    } else if (isCtrlPressed) {
      // 多选模式
      if (isSelected) {
        // 取消选择
        selectedFiles.value.splice(fileIndex, 1)
      } else {
        // 添加选择
        selectedFiles.value.push(file)
        lastSelectedFile.value = file
      }
    } else {
      // 单选模式
      if (isSelected && selectedFiles.value.length === 1) {
        // 如果只选中了这一个文件，则取消选择
        clearSelection()
      } else {
        // 选择这个文件，清除其他选择
        selectedFiles.value = [file]
        lastSelectedFile.value = file
      }
    }
  }

  /**
   * 范围选择文件
   * @param {Object} endFile - 结束文件
   * @param {Array} allFiles - 所有文件列表
   */
  const selectRange = (endFile, allFiles) => {
    if (!lastSelectedFile.value || !endFile || !allFiles.length) return

    const startIndex = allFiles.findIndex(f => f.id === lastSelectedFile.value.id)
    const endIndex = allFiles.findIndex(f => f.id === endFile.id)

    if (startIndex === -1 || endIndex === -1) return

    const minIndex = Math.min(startIndex, endIndex)
    const maxIndex = Math.max(startIndex, endIndex)

    // 选择范围内的所有文件
    const rangeFiles = allFiles.slice(minIndex, maxIndex + 1)
    
    // 合并选择，去重
    const newSelection = [...selectedFiles.value]
    rangeFiles.forEach(file => {
      if (!newSelection.find(f => f.id === file.id)) {
        newSelection.push(file)
      }
    })
    
    selectedFiles.value = newSelection
  }

  /**
   * 切换文件选择状态
   * @param {Object} file - 文件对象
   */
  const toggleFileSelection = (file) => {
    if (!file) return

    const fileIndex = selectedFiles.value.findIndex(f => f.id === file.id)
    if (fileIndex !== -1) {
      selectedFiles.value.splice(fileIndex, 1)
    } else {
      selectedFiles.value.push(file)
      lastSelectedFile.value = file
    }
  }

  /**
   * 检查文件是否被选中
   * @param {Object} file - 文件对象
   * @returns {boolean}
   */
  const isFileSelected = (file) => {
    if (!file) return false
    return selectedFiles.value.some(f => f.id === file.id)
  }

  /**
   * 全选文件
   * @param {Array} allFiles - 所有文件列表
   */
  const selectAll = (allFiles) => {
    if (!allFiles || !allFiles.length) return
    
    selectedFiles.value = [...allFiles]
    lastSelectedFile.value = allFiles[allFiles.length - 1]
  }

  /**
   * 反选文件
   * @param {Array} allFiles - 所有文件列表
   */
  const invertSelection = (allFiles) => {
    if (!allFiles || !allFiles.length) return

    const newSelection = allFiles.filter(file => 
      !selectedFiles.value.some(selected => selected.id === file.id)
    )
    
    selectedFiles.value = newSelection
    lastSelectedFile.value = newSelection.length > 0 ? newSelection[newSelection.length - 1] : null
  }

  /**
   * 清除所有选择
   */
  const clearSelection = () => {
    selectedFiles.value = []
    lastSelectedFile.value = null
  }

  /**
   * 批量选择文件
   * @param {Array} files - 要选择的文件列表
   * @param {boolean} append - 是否追加到现有选择
   */
  const selectFiles = (files, append = false) => {
    if (!files || !files.length) return

    if (append) {
      // 追加选择，去重
      const newFiles = files.filter(file => 
        !selectedFiles.value.some(selected => selected.id === file.id)
      )
      selectedFiles.value.push(...newFiles)
    } else {
      // 替换选择
      selectedFiles.value = [...files]
    }
    
    lastSelectedFile.value = files[files.length - 1]
  }

  /**
   * 根据条件选择文件
   * @param {Array} allFiles - 所有文件列表
   * @param {Function} predicate - 筛选条件函数
   */
  const selectByCondition = (allFiles, predicate) => {
    if (!allFiles || !allFiles.length || typeof predicate !== 'function') return

    const matchedFiles = allFiles.filter(predicate)
    selectFiles(matchedFiles)
  }

  /**
   * 选择指定类型的文件
   * @param {Array} allFiles - 所有文件列表
   * @param {string} fileType - 文件类型
   */
  const selectByType = (allFiles, fileType) => {
    selectByCondition(allFiles, file => file.fileType === fileType)
  }

  /**
   * 选择指定大小范围的文件
   * @param {Array} allFiles - 所有文件列表
   * @param {number} minSize - 最小大小（字节）
   * @param {number} maxSize - 最大大小（字节）
   */
  const selectBySize = (allFiles, minSize, maxSize) => {
    selectByCondition(allFiles, file => {
      const size = file.fileSize || 0
      return size >= minSize && size <= maxSize
    })
  }

  /**
   * 选择指定时间范围的文件
   * @param {Array} allFiles - 所有文件列表
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   */
  const selectByDateRange = (allFiles, startDate, endDate) => {
    selectByCondition(allFiles, file => {
      const fileDate = new Date(file.createTime)
      return fileDate >= startDate && fileDate <= endDate
    })
  }

  /**
   * 获取选中文件的总大小
   * @returns {number} 总大小（字节）
   */
  const getSelectedTotalSize = () => {
    return selectedFiles.value.reduce((total, file) => {
      return total + (file.fileSize || 0)
    }, 0)
  }

  /**
   * 获取选中文件的类型统计
   * @returns {Object} 类型统计对象
   */
  const getSelectedTypeStats = () => {
    const stats = {}
    selectedFiles.value.forEach(file => {
      const type = file.fileType || 'unknown'
      stats[type] = (stats[type] || 0) + 1
    })
    return stats
  }

  /**
   * 处理表格选择变化（兼容Element UI表格）
   * @param {Array} selection - 选中的行
   */
  const handleSelectionChange = (selection) => {
    selectedFiles.value = selection || []
    lastSelectedFile.value = selection && selection.length > 0 
      ? selection[selection.length - 1] 
      : null
  }

  return {
    // 响应式数据
    selectedFiles,
    lastSelectedFile,
    
    // 计算属性
    hasSelection,
    selectionCount,
    isAllSelected,
    
    // 方法
    selectFile,
    selectRange,
    toggleFileSelection,
    isFileSelected,
    selectAll,
    invertSelection,
    clearSelection,
    selectFiles,
    selectByCondition,
    selectByType,
    selectBySize,
    selectByDateRange,
    getSelectedTotalSize,
    getSelectedTypeStats,
    handleSelectionChange
  }
}
