import { ref, reactive } from 'vue'
import { UPLOAD_STATUS, ERROR_MESSAGES } from '../utils/constants'
import { validateFileSize, validateFileType, generateUniqueFileName } from '../utils/fileUtils'

/**
 * 文件上传相关的组合式函数
 */
export function useUpload() {
  // 上传队列
  const uploadQueue = ref([])

  // 上传状态
  const uploadStatus = reactive({
    isUploading: false,
    totalFiles: 0,
    completedFiles: 0,
    failedFiles: 0,
    progress: 0
  })

  /**
   * 添加文件到上传队列
   * @param {FileList|Array} files - 文件列表
   * @param {Object} options - 上传选项
   */
  const addFilesToQueue = (files, options = {}) => {
    const {
      categoryId,
      maxFileSize = 100, // MB
      allowedTypes = [],
      autoUpload = false
    } = options

    if (!files || files.length === 0) return

    const fileArray = Array.from(files)
    const validFiles = []

    fileArray.forEach((file, index) => {
      const uploadItem = {
        id: `upload_${Date.now()}_${index}`,
        file,
        fileName: file.name,
        fileSize: file.size,
        categoryId,
        status: UPLOAD_STATUS.PENDING,
        progress: 0,
        error: null,
        url: null
      }

      // 验证文件大小
      if (!validateFileSize(file.size, maxFileSize)) {
        uploadItem.status = UPLOAD_STATUS.ERROR
        uploadItem.error = ERROR_MESSAGES.FILE_TOO_LARGE
      }
      // 验证文件类型
      else if (allowedTypes.length > 0 && !validateFileType(file.name, allowedTypes)) {
        uploadItem.status = UPLOAD_STATUS.ERROR
        uploadItem.error = ERROR_MESSAGES.FILE_TYPE_NOT_ALLOWED
      }
      else {
        validFiles.push(uploadItem)
      }

      uploadQueue.value.push(uploadItem)
    })

    // 更新状态
    uploadStatus.totalFiles = uploadQueue.value.length

    // 自动上传
    if (autoUpload && validFiles.length > 0) {
      startUpload()
    }

    return validFiles
  }

  /**
   * 开始上传
   */
  const startUpload = async () => {
    if (uploadStatus.isUploading) return

    const pendingFiles = uploadQueue.value.filter(item =>
      item.status === UPLOAD_STATUS.PENDING
    )

    if (pendingFiles.length === 0) return

    uploadStatus.isUploading = true
    uploadStatus.completedFiles = 0
    uploadStatus.failedFiles = 0

    // 并发上传（限制并发数）
    const concurrency = 3
    const chunks = []
    for (let i = 0; i < pendingFiles.length; i += concurrency) {
      chunks.push(pendingFiles.slice(i, i + concurrency))
    }

    for (const chunk of chunks) {
      await Promise.all(chunk.map(uploadFile))
    }

    uploadStatus.isUploading = false
    updateProgress()
  }

  /**
   * 上传单个文件
   * @param {Object} uploadItem - 上传项
   */
  const uploadFile = async (uploadItem) => {
    if (!uploadItem || uploadItem.status !== UPLOAD_STATUS.PENDING) return

    uploadItem.status = UPLOAD_STATUS.UPLOADING
    uploadItem.progress = 0

    try {
      // 导入上传相关的API函数
      const { getAliOssUploadSignature, saveUploadFiles } = await import('@/api/promotion/fileres')

      // 1. 获取OSS上传凭证
      const res = await getAliOssUploadSignature({ categoryId: uploadItem.categoryId })
      if (res.code !== 200) {
        throw new Error(res.msg || '获取上传凭证失败')
      }
      const ossConfig = res.data

      // 2. 构建表单数据
      const formData = new FormData()
      const extension = uploadItem.fileName.substring(uploadItem.fileName.lastIndexOf('.'))
      const fileName = generateUniqueFileName(uploadItem.fileName)
      const filePath = `${ossConfig.dir}/${fileName}`

      formData.append('name', fileName)
      formData.append('key', filePath)
      formData.append('policy', ossConfig.policy)
      formData.append('OSSAccessKeyId', ossConfig.accessKeyId)
      formData.append('success_action_status', '200')
      formData.append('signature', ossConfig.signature)
      formData.append('file', uploadItem.file)

      // 3. 上传到OSS
      const host = ossConfig.host.startsWith('http:')
        ? ossConfig.host.replace('http:', 'https:')
        : ossConfig.host

      const uploadResponse = await fetch(host, {
        method: 'POST',
        body: formData
      })

      if (!uploadResponse.ok) {
        throw new Error('上传失败')
      }

      // 4. 构建文件信息
      const fileInfo = {
        fileCategory: uploadItem.categoryId,
        fileName: fileName,
        fileSuffix: extension.slice(1),
        newFileName: fileName,
        originalFileName: uploadItem.fileName,
        bucketName: ossConfig.bucketName,
        filePath: `${ossConfig.bucketDomain}/${filePath}`,
        fileSize: uploadItem.fileSize,
        fileType: uploadItem.file.type,
        url: `${ossConfig.bucketDomain}/${filePath}`
      }

      // 5. 保存文件信息
      const saveRes = await saveUploadFiles({
        uploadFiles: [fileInfo]
      })

      if (saveRes.code !== 200) {
        throw new Error(saveRes.msg || '保存文件信息失败')
      }

      // 上传成功
      uploadItem.status = UPLOAD_STATUS.SUCCESS
      uploadItem.progress = 100
      uploadItem.url = fileInfo.url
      uploadStatus.completedFiles++

    } catch (error) {
      // 上传失败
      uploadItem.status = UPLOAD_STATUS.ERROR
      uploadItem.error = error.message || ERROR_MESSAGES.UPLOAD_FAILED
      uploadStatus.failedFiles++
    }

    updateProgress()
  }

  /**
   * 更新总体进度
   */
  const updateProgress = () => {
    const total = uploadStatus.totalFiles
    if (total === 0) {
      uploadStatus.progress = 0
      return
    }

    const completed = uploadStatus.completedFiles + uploadStatus.failedFiles
    uploadStatus.progress = Math.round((completed / total) * 100)
  }

  /**
   * 暂停上传
   */
  const pauseUpload = () => {
    uploadStatus.isUploading = false
    // 将正在上传的文件状态改为暂停
    uploadQueue.value.forEach(item => {
      if (item.status === UPLOAD_STATUS.UPLOADING) {
        item.status = UPLOAD_STATUS.PENDING
      }
    })
  }

  /**
   * 取消上传
   * @param {string} uploadId - 上传项ID
   */
  const cancelUpload = (uploadId) => {
    const item = uploadQueue.value.find(item => item.id === uploadId)
    if (item) {
      item.status = UPLOAD_STATUS.CANCELLED
      if (item.status === UPLOAD_STATUS.UPLOADING) {
        uploadStatus.failedFiles++
        updateProgress()
      }
    }
  }

  /**
   * 重试上传
   * @param {string} uploadId - 上传项ID
   */
  const retryUpload = async (uploadId) => {
    const item = uploadQueue.value.find(item => item.id === uploadId)
    if (item && item.status === UPLOAD_STATUS.ERROR) {
      item.status = UPLOAD_STATUS.PENDING
      item.error = null
      item.progress = 0
      uploadStatus.failedFiles--
      await uploadFile(item)
    }
  }

  /**
   * 移除上传项
   * @param {string} uploadId - 上传项ID
   */
  const removeUploadItem = (uploadId) => {
    const index = uploadQueue.value.findIndex(item => item.id === uploadId)
    if (index !== -1) {
      const item = uploadQueue.value[index]
      if (item.status === UPLOAD_STATUS.SUCCESS) {
        uploadStatus.completedFiles--
      } else if (item.status === UPLOAD_STATUS.ERROR) {
        uploadStatus.failedFiles--
      }
      uploadQueue.value.splice(index, 1)
      uploadStatus.totalFiles--
      updateProgress()
    }
  }

  /**
   * 清空上传队列
   */
  const clearQueue = () => {
    uploadQueue.value = []
    uploadStatus.isUploading = false
    uploadStatus.totalFiles = 0
    uploadStatus.completedFiles = 0
    uploadStatus.failedFiles = 0
    uploadStatus.progress = 0
  }

  /**
   * 获取上传统计
   */
  const getUploadStats = () => {
    const stats = {
      total: uploadStatus.totalFiles,
      pending: 0,
      uploading: 0,
      success: uploadStatus.completedFiles,
      error: uploadStatus.failedFiles,
      cancelled: 0
    }

    uploadQueue.value.forEach(item => {
      switch (item.status) {
        case UPLOAD_STATUS.PENDING:
          stats.pending++
          break
        case UPLOAD_STATUS.UPLOADING:
          stats.uploading++
          break
        case UPLOAD_STATUS.CANCELLED:
          stats.cancelled++
          break
      }
    })

    return stats
  }

  /**
   * 检查是否有上传中的文件
   */
  const hasUploadingFiles = () => {
    return uploadQueue.value.some(item =>
      item.status === UPLOAD_STATUS.UPLOADING ||
      item.status === UPLOAD_STATUS.PENDING
    )
  }

  return {
    // 响应式数据
    uploadQueue,
    uploadStatus,

    // 方法
    addFilesToQueue,
    startUpload,
    pauseUpload,
    cancelUpload,
    retryUpload,
    removeUploadItem,
    clearQueue,
    getUploadStats,
    hasUploadingFiles
  }
}
