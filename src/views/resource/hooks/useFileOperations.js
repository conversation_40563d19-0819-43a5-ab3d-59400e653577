import { ref } from 'vue'
import {
  listFileres,
  delFileres,
  updateFileres
} from '@/api/promotion/fileres'

import { listCategory, delCategory, addCategory, updateCategory } from '@/api/promotion/category'
import { downloadFile as downloadFileUtil } from '@/utils'

/**
 * 文件操作相关的组合式函数
 */
export function useFileOperations() {
  const loading = ref(false)
  const error = ref(null)

  /**
   * 加载文件列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>}
   */
  const loadFiles = async(params = {}) => {
    loading.value = true
    error.value = null

    try {
      const response = await listFileres(params)
      return response
    } catch (err) {
      error.value = err
      console.error('加载文件列表失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除文件
   * @param {string|number} fileId - 文件ID
   * @returns {Promise<void>}
   */
  const deleteFile = async(fileId) => {
    loading.value = true
    error.value = null

    try {
      await delFileres(fileId)
    } catch (err) {
      error.value = err
      console.error('删除文件失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 重命名文件
   * @param {string|number} fileId - 文件ID
   * @param {string} newName - 新文件名
   * @returns {Promise<void>}
   */
  const renameFile = async(fileId, newName) => {
    loading.value = true
    error.value = null

    try {
      await updateFileres({ id: fileId, fileName: newName })
    } catch (err) {
      error.value = err
      console.error('重命名文件失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 下载文件
   * @param {Object} file - 文件对象
   * @returns {Promise<void>}
   */
  const downloadFile = async(file) => {
    loading.value = true
    error.value = null

    try {
      // 根据文件类型和URL来源选择下载方式
      if ((~file.fileType?.indexOf('image') || file.fileType === 'jpeg') &&
          !~file.url?.indexOf('aliyun')) {
        // 对于非阿里云的图片文件，使用工具函数下载
        downloadFileUtil(file.url, file.fileName)
      } else {
        // 对于其他文件或阿里云文件，使用表单提交方式
        const form = document.createElement('form')
        form.setAttribute('action', file.url)
        form.setAttribute('method', 'get')
        form.setAttribute('target', '_blank')
        form.setAttribute('style', 'display:none')
        document.body.appendChild(form)
        form.submit()
        document.body.removeChild(form)
      }
    } catch (err) {
      error.value = err
      console.error('下载文件失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 移动文件到指定分类
   * @param {string|number} fileId - 文件ID
   * @param {string|number} categoryId - 分类ID
   * @returns {Promise<void>}
   */
  const moveFile = async(fileId, categoryId) => {
    loading.value = true
    error.value = null

    try {
      await updateFileres({ id: fileId, fileCategory: categoryId })
    } catch (err) {
      error.value = err
      console.error('移动文件失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量删除文件
   * @param {Array<string|number>} fileIds - 文件ID数组
   * @returns {Promise<void>}
   */
  const batchDeleteFiles = async(fileIds) => {
    loading.value = true
    error.value = null

    try {
      const deletePromises = fileIds.map(id => delFileres(id))
      await Promise.all(deletePromises)
    } catch (err) {
      error.value = err
      console.error('批量删除文件失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量移动文件
   * @param {Array<string|number>} fileIds - 文件ID数组
   * @param {string|number} categoryId - 分类ID
   * @returns {Promise<void>}
   */
  const batchMoveFiles = async(fileIds, categoryId) => {
    loading.value = true
    error.value = null

    try {
      const movePromises = fileIds.map(id =>
        updateFileres({ id, fileCategory: categoryId })
      )
      await Promise.all(movePromises)
    } catch (err) {
      error.value = err
      console.error('批量移动文件失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 分类相关操作
  /**
   * 加载分类列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>}
   */
  const loadCategories = async(params = {}) => {
    loading.value = true
    error.value = null

    try {
      const response = await listCategory(params)
      return response
    } catch (err) {
      error.value = err
      console.error('加载分类列表失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建分类
   * @param {Object} categoryData - 分类数据
   * @returns {Promise<void>}
   */
  const createCategory = async(categoryData) => {
    loading.value = true
    error.value = null

    try {
      await addCategory(categoryData)
    } catch (err) {
      error.value = err
      console.error('创建分类失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新分类
   * @param {Object} categoryData - 分类数据
   * @returns {Promise<void>}
   */
  const updateCategoryInfo = async(categoryData) => {
    loading.value = true
    error.value = null

    try {
      await updateCategory(categoryData)
    } catch (err) {
      error.value = err
      console.error('更新分类失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除分类
   * @param {string|number} categoryId - 分类ID
   * @returns {Promise<void>}
   */
  const deleteCategory = async(categoryId) => {
    loading.value = true
    error.value = null

    try {
      await delCategory(categoryId)
    } catch (err) {
      error.value = err
      console.error('删除分类失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取文件统计信息
   * @param {string|number} categoryId - 分类ID
   * @returns {Promise<Object>}
   */
  const getFileStats = async(categoryId) => {
    loading.value = true
    error.value = null

    try {
      const response = await listFileres({
        fileCategory: categoryId,
        pageNum: 1,
        pageSize: 1
      })

      return {
        total: response.total || 0,
        categoryId
      }
    } catch (err) {
      error.value = err
      console.error('获取文件统计失败:', err)
      return { total: 0, categoryId }
    } finally {
      loading.value = false
    }
  }

  /**
   * 搜索文件
   * @param {Object} searchParams - 搜索参数
   * @returns {Promise<Object>}
   */
  const searchFiles = async(searchParams) => {
    loading.value = true
    error.value = null

    try {
      const response = await listFileres(searchParams)
      return response
    } catch (err) {
      error.value = err
      console.error('搜索文件失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,

    // 文件操作
    loadFiles,
    deleteFile,
    renameFile,
    downloadFile,
    moveFile,
    batchDeleteFiles,
    batchMoveFiles,
    searchFiles,

    // 分类操作
    loadCategories,
    createCategory,
    updateCategory: updateCategoryInfo,
    deleteCategory,
    getFileStats
  }
}
