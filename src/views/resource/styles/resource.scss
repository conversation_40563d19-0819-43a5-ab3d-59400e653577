// 资源管理页面专用样式
// 基于iOS设计风格

// 导入iOS样式变量
@import '@/styles/ios-style.scss';

// 资源管理容器
.resource-manager {

  // 工具栏样式增强
  .resource-toolbar {
    background: var(--ios-background-secondary);
    // backdrop-filter: blur(20px);
    // border: 1px solid var(--ios-border-color-light);
  }

  // 侧边栏样式增强
  .resource-sidebar {
    background: var(--ios-background-secondary);
    backdrop-filter: blur(20px);
    border: 1px solid var(--ios-border-color-light);

    .category-item {
      &:hover {
        background: var(--ios-fill-quaternary);
        transform: translateX(2px);
      }

      &.active {
        background: linear-gradient(135deg, var(--ios-blue), var(--ios-blue-light));
        box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
      }
    }
  }

  // 文件展示区域样式增强
  .file-display {
    background: var(--ios-background-secondary);
    backdrop-filter: blur(20px);
    border: 1px solid var(--ios-border-color-light);

    // 网格视图增强
    .grid-view {
      .file-item {
        background: var(--ios-background);
        border: 2px solid transparent;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

        &:hover {
          border-color: var(--ios-blue);
          transform: translateY(-4px) scale(1.02);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        &.selected {
          border-color: var(--ios-blue);
          background: var(--ios-selection-background);
          box-shadow: 0 4px 16px rgba(0, 122, 255, 0.2);
        }

        .file-thumbnail {
          background: var(--ios-fill-quaternary);
          border-radius: var(--ios-border-radius-small);
          overflow: hidden;
          position: relative;

          .thumbnail-image {
            transition: transform 0.3s ease;
          }

          &:hover .thumbnail-image {
            transform: scale(1.05);
          }
        }

        // 网格视图操作按钮增强
        .grid-action-buttons {
          .action-btn {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.08);

            &:hover {
              background: rgba(255, 255, 255, 1);
              border-color: rgba(0, 0, 0, 0.12);
            }
          }
        }
      }
    }

    // 列表视图增强
    .list-view {
      .el-table {
        background: transparent;

        .el-table__header {
          background: var(--ios-light-gray);

          th {
            background: transparent;
            border-bottom: 1px solid var(--ios-separator-color);
            color: var(--ios-text-primary);
            font-weight: 600;
          }
        }

        .el-table__body {
          tr {
            background: transparent;
            transition: background-color 0.2s ease;

            &:hover {
              background: var(--ios-fill-quaternary);
            }

            td {
              border-bottom: 1px solid var(--ios-separator-color);
            }
          }
        }
      }
    }
  }
}

// 上传区域样式增强
.upload-area-overlay {
  backdrop-filter: blur(8px);

  .upload-area {
    background: var(--ios-background-secondary);
    backdrop-filter: blur(20px);
    border: 1px solid var(--ios-border-color-light);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);

    .drop-zone {
      border: 2px dashed var(--ios-border-color);
      background: var(--ios-fill-quaternary);
      transition: all 0.3s ease;

      &:hover,
      &.drag-over {
        border-color: var(--ios-blue);
        background: var(--ios-selection-background);
        transform: scale(1.02);
      }
    }

    .file-item {
      background: var(--ios-background);
      border-radius: var(--ios-border-radius-small);
      border: 1px solid var(--ios-border-color-light);
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 文件预览样式增强
.file-preview-dialog {
  .el-dialog {
    background: var(--ios-background-secondary);
    backdrop-filter: blur(20px);
    border: 1px solid var(--ios-border-color-light);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);

    .el-dialog__header {
      background: var(--ios-light-gray);
      border-bottom: 1px solid var(--ios-separator-color);
    }

    .el-dialog__body {
      background: var(--ios-background);
    }

    .el-dialog__footer {
      background: var(--ios-light-gray);
      border-top: 1px solid var(--ios-separator-color);
    }
  }

  .preview-image {
    border-radius: var(--ios-border-radius);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .image-controls {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--ios-border-radius);
  }
}

// 右键菜单样式增强
.context-menu {
  background: var(--ios-background-secondary);
  backdrop-filter: blur(20px);
  border: 1px solid var(--ios-border-color-light);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

  .menu-item {
    transition: all 0.2s ease;

    &:hover {
      background: var(--ios-fill-quaternary);
      transform: translateX(2px);
    }

    &.danger:hover {
      background: rgba(255, 59, 48, 0.1);
      color: var(--ios-red);
    }
  }
}

// 响应式设计增强
@media (max-width: 768px) {
  .resource-manager {
    .resource-content {
      flex-direction: column;
      gap: 12px;
    }

    .resource-sidebar {
      width: 100%;
      max-height: 200px;
      overflow-y: auto;
    }

    .file-display {
      .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 12px;

        .file-item {
          .file-thumbnail {
            height: 80px;
          }
        }
      }
    }
  }

  .upload-area {
    width: 95% !important;
    max-height: 90vh;

    .drop-zone {
      padding: 20px 16px;
    }
  }
}

// 动画增强
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.resource-manager {
  animation: fadeInUp 0.5s ease-out;

  .resource-sidebar {
    animation: slideInLeft 0.5s ease-out;
  }

  .file-display {
    animation: fadeInUp 0.5s ease-out 0.1s both;
  }
}

// 滚动条样式
.resource-manager {
  * {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: var(--ios-fill-quaternary);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--ios-fill-secondary);
      border-radius: 4px;

      &:hover {
        background: var(--ios-fill-primary);
      }
    }
  }
}