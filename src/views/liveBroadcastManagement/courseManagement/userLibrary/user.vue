<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd">
          新增用户身份
        </el-button>
        <el-button type="primary" plain icon="el-icon-plus" @click="handleBatchAdd">
          批量新增
        </el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="序号" type="index" width="55" align="center" />
      <el-table-column
        label="用户昵称"
        align="center"
        prop="nickname"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="用户头像"
        align="center"
        prop="avatarUrl"
      >
        <template #default="scope">
          <el-image
            class="pic"
            v-if="scope.row.avatarUrl"
            :src="scope.row.avatarUrl"
            :preview-src-list="[scope.row.avatarUrl]"
            fit="contain"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" align="center" class-name="small-padding">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 用户新增 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="formUser" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="用户昵称" prop="nickname" required>
          <el-input v-model="form.nickname" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="用户头像" prop="avatarUrl" required>
          <div class="boxImage">
            <el-image
              class="pic"
              v-if="form.avatarUrl"
              :src="form.avatarUrl"
              fit="contain"
            />
            <el-button
              type="text"
              icon="el-icon-plus"
              @click="chooeseImageShow = true"
              >选择图片</el-button
            >
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户批量新增 -->
    <el-dialog title="批量新增用户身份" :visible.sync="openBatch" width="600px" append-to-body>
      <el-form ref="batchForm" :model="batchForm" :rules="rules" label-width="80px">
        <el-form-item label="新增数量" prop="count" required>
          <el-input v-model="batchForm.count" type="number" :max="1000" :min="0" placeholder="请输入新增数量" />
        </el-form-item>

        <el-form-item label="" required>
          <div style="font-size: 12px;">
            <span style="color: red;">*</span>
            请输入1～1000（系统会从客户列表中随机抽取客户微信头像和昵称生成用户身份）
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchForm">确 定</el-button>
        <el-button @click="cancelBatch">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 选择图片 -->
    <chooeseImage
      :isCreateTemplate="true" 
      :visible.sync="chooeseImageShow"
      @chooeseImageSuccess="chooeseSuccess"
    ></chooeseImage>
  </div>
</template>

<script>
import {
  batchAddUser,
  addUser,
  editeUser,
  deleteUser,
  getListUser
} from "@/api/liveBroadcastManagement";
import chooeseImage from "../../components/chooeseImage.vue";
export default {
  name: "courseGroup",
  dicts: ["category_type"],
  components: { chooeseImage },
  data() {
    return {
      activeName: "first",

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单参数
      form: {
        avatarUrl: null,
        nickname: true,
      },
      batchForm:{
        count: null,
      },
      // 表单校验
      rules: {
        nickname: [
          { required: true, message: "用户昵称不能为空", trigger: ['blur', 'change'] },
        ],
        avatarUrl: [
          { required: true, message: "请选择头像图片", trigger: "change" },
        ],
        count:[
          { required: true, message: "请输入新增数量", trigger: ['blur', 'change'] },
        ]
      },
      // 用户选择器
      userSelectorVisible: false,
      chooeseImageShow: false,
      openBatch:false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 选择图片
    chooeseSuccess(url) {
      this.form.avatarUrl = url;
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      getListUser(this.queryParams).then((response) => {
        if(response.code == 200){
          this.dataList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        avatarUrl: null,
        nickname: null
      };
      this.batchForm = {
        count: null
      }
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null,
      };
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.noticeId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户";
    },
    
    // 批量新增
    cancelBatch(){
      this.reset();
      this.openBatch = false;
    },

    // 批量新增
    handleBatchAdd(){
      this.reset();
      this.openBatch = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let data = JSON.parse(JSON.stringify(row))
      this.form = data;
      this.open = true;
      this.title = "修改用户";
    },

    // 批量新增
    submitBatchForm: function () {
      if(this.batchForm.count <= 0 || !this.batchForm.count || this.batchForm.count > 1000){
        this.$modal.msgWarning("请正确输入新增数量");
        return
      }
      this.$refs["batchForm"].validate((valid) => {
        if (valid) {
          batchAddUser(this.batchForm).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.batchForm = {
              count: null
            }
            this.openBatch = false;
            this.getList();
          });
        }
      });
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["formUser"].validate((valid) => {
        if (valid) {
          if (this.form.id !== undefined) {
            editeUser(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUser(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let parmas = {
        robotId: row.id
      }
      this.$modal
        .confirm('是否确认删除用户"' + row.nickname + '"？')
        .then(function () {
          return deleteUser(parmas);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.boxImage {
  display: flex;
  align-items: center;
}
.pic {
  width: 60px;
  height: 60px;
}
</style>

