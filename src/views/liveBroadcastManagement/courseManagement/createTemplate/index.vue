<template>
  <div class="containerBox">
    <div class="left">
      <div style="margin-bottom: 10px">
        <el-input v-model="templateData.templateName" placeholder="请输入标题"></el-input>
      </div>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="课程视频" name="first">
          <div style="width: 100%;">
            <!-- :poster="videoData.coverURL" -->
            <div class="pb-3 mb-5">{{ videoData.title }}</div>
            <video
              v-if="videoData.playURL"
              ref="videoRef"
              :src="videoData.playURL"
              controls
              width="100%"
              @timeupdate="onTimeUpdate"
              style="border-radius: 8px;max-height: 270px;"
            >
              您的浏览器不支持 video 标签。
            </video>
          </div>
          <div>
            <div
              style="
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
              "
            >
              <!-- 时间前进后退 -->
              <timeAdjuster
                @timeChange="videoTimeChange"
                :currentTime="currentTime"
                :maxcurrentTime="Number(videoData.duration) || 1024"
              />
              <el-button type="primary" @click="lookMessage">
                查看此刻消息
              </el-button>
              <el-button type="primary" @click="currentAddMsg">
                在此刻添加消息
              </el-button>
            </div>
            <div>总时长：{{ formatSecondsToHHMMSS(videoData.duration) }}</div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="PC端视频" name="second">
          <div style="width: 100%;">
            <div class="pb-3 mb-5">{{ videoData.title }}</div>
            <video
              v-if="videoData.playURL"
              ref="videoRef"
              :src="videoData.playURL"
              controls
              width="100%"
              @timeupdate="onTimeUpdate"
              style="border-radius: 8px;max-height: 270px;"
            >
              您的浏览器不支持 video 标签。
            </video>
          </div>
          <div>
            <div
              style="
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
              "
            >
              <!-- 时间前进后退 -->
              <timeAdjuster />
              <el-button type="primary" @click="lookMessage">
                查看此刻消息
              </el-button>
              <el-button type="primary" @click="currentAddMsg">
                在此刻添加消息
              </el-button>
            </div>
            <div>总时长：{{ formatSecondsToHHMMSS(videoData.duration) }}</div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="right">
      <div class="queryBox">
        <div class="titleBox">
          <div class="titleName">消息内容</div>
          <el-tooltip class="item" effect="light" content="在模板启用期间修改消息后，使用该模板且已开播/即将开播的录播课，将仍执行修改前消息，若要执行修改后消息，需在该录播课编辑页重新保存一次课程设置" placement="top" popper-class="custom-tooltip">
            <div>修改消息须知</div>
          </el-tooltip>
        </div>
        <el-form ref="queryForm" :model="queryParams" :inline="true">
          <el-form-item>
            <el-input
              v-model="queryParams.messageContent"
              placeholder="请输入消息内容"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div>
          <el-button type="primary" @click="addCommentFunc">
            添加消息
          </el-button>
          <el-button type="primary" @click="deleteSelected">批量删除</el-button>
        </div>
        <div>
          <el-button type="primary" @click="importMsg">导入消息</el-button>
          <el-button type="primary" @click="downloadMessageFunc">下载消息</el-button>
        </div>
      </div>
      <div class="rightTable">
        <el-table
          v-loading="loading"
          :data="dataList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            align="center"
            :selectable="handleSelectable"
            width="55"
          />
          <el-table-column
            label="时间"
            align="center"
            prop="sendTime"
            width="150"
          >
            <template slot-scope="scope">
              <div>{{ scope.row.sendTime }}</div>
              <div>{{ scope.row.messageType == 1 ? "文本" : "图片" }}</div>
            </template>
          </el-table-column>
          <el-table-column label="消息内容" align="center" prop="templateName">
            <template slot-scope="scope">
              <div class="userInfoBox">
                <el-image
                  :src="scope.row.senderHeadImg || userDefault"
                  class="headImg"
                ></el-image>
                <div class="userIdentity">
                  {{ scope.row.senderType == 1 ? "学员" : "助教" }}
                </div>
                <div class="uname">{{ scope.row.senderName }}</div>
              </div>
              <div class="msgContent" v-if="scope.row.messageContent.length < 70">{{ scope.row.messageContent }}</div>
              <el-tooltip v-else class="item" effect="dark" :content="scope.row.messageContent" placement="top" popper-class="custom-tooltip">
                <div class="msgContent">{{ scope.row.messageContent }}</div>
              </el-tooltip>
              <!-- 回复消息内容展示 -->
              <div v-if="scope.row.replayMessageScript" class="replayInfoBox">
                <div class="replayBox">
                  <div class="userInfoBox">
                    <el-image
                      :src="scope.row.replayMessageScript.senderHeadImg || userDefault"
                      class="headImg"
                    ></el-image>
                    <div class="userIdentity">
                      {{ scope.row.replayMessageScript.senderType == 1 ? "学员" : "助教" }}
                    </div>
                    <div class="uname">{{ scope.row.replayMessageScript.senderName }}</div>
                  </div>
                  <div class="messageId">消息ID: {{ scope.row.replayMessageScript.id }}</div>
                </div>
                <div class="msgContent" v-if="scope.row.replayMessageScript.messageContent.length < 70">{{ scope.row.replayMessageScript.messageContent }}</div>
                <el-tooltip v-else class="item" effect="dark" :content="scope.row.replayMessageScript.messageContent" placement="top" popper-class="custom-tooltip">
                  <div class="msgContent">{{ scope.row.replayMessageScript.messageContent }}</div>
                </el-tooltip>
              </div>
              <div class="msgContent messageId">消息ID：{{ scope.row.id }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="right" width="220">
            <template slot-scope="scope">
              <el-button type="text" v-if="!scope.row.replayMessageScript && scope.row.senderType == 1" @click="addReplyFunc(scope.row)">添加回复消息</el-button>
              <el-button type="text" @click="toEdite(scope.row)">编辑</el-button>
              <el-button type="text" @click="toCopy(scope.row)">复制</el-button>
              <el-button type="text" @click="deleteMsg(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="loadMessageList"
      />
    </div>

    <!-- 添加评论消息 -->
    <addCommentMsg
      :visible.sync="showAddMsg"
      :chooeseVideoId="chooeseTemplateId"
      @loadList="handleQuery"
      :chooeseTime="chooeseTime"
      :copyOrEdite="copyOrEdite"
      :addReplyData="replyInfo"
    />

    <!-- 导入消息 -->
    <el-dialog
      title="导入消息"
      :visible.sync="showImportMsg"
      width="45%"
      @close="showImportMsg = false"
    >
      <el-form ref="msgForm" :model="importForm" :rules="rules" label-width="120px">
        <el-form-item label="模板">
          <el-button type="primary" @click="downloadTemplate">下载模版</el-button>
        </el-form-item>
        <el-form-item label="最近导入时间">
          <div>2025-08-12 22:49:32</div>
        </el-form-item>
        <el-form-item label="文件上传">
          <el-upload ref="upload2" class="upload-demo" v-model:file-list="fileLists" :show-file-list="false"
            :http-request="uploadFile" action="" :limit="limit" multiple :on-exceed="handleExceed">
            <el-button type="primary" link><el-icon>
                <Upload />
              </el-icon>本地上传</el-button>
          </el-upload>
          <p v-if="file">选中文件:{{ file.name }}</p>
        </el-form-item>
        <el-form-item label="">
          <div style="font-size: 14px; color: #606266;line-height: 22px;">
            <div style="margin-top: 8px">导入前须知：</div>
            <div>1、每条消息都需要有ID且ID必须唯一，范围在10000~99999，若有ID重复的消息，则仅保留第一条；</div>
            <div>2、消息内容字数限制300字以内；</div>
            <div>3、消息可以指定发言者昵称，系统将随机为发言者补充头像，若有消息使用重复的昵称，系统将显示为同一人发言；</div>
            <div>4、仅助教消息能以回复消息的形式导入，若消息为学员消息，则即使设定了回复ID，消息形式也会被生成为学员普通消息；</div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importCancel">取 消</el-button>
        <el-button type="primary" @click="importConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getExistingObj } from '@/utils'
import timeAdjuster from "../../components/timeAdjuster.vue";
import addCommentMsg from "../../components/addCommentMsg.vue";
import {
  getMessageList,
  templateInfo,
  removeMessage,
  downloadMessage,
} from "@/api/liveBroadcastManagement/messageTemplate";
import { getVideoSrc } from "@/api/sourceMaterial/index";
import axios from 'axios'
import { getToken } from '@/utils/auth'
import userDefault from '@/assets/images/userDefault.png'
export default {
  name: "createTemplate",
  components: { timeAdjuster, addCommentMsg },
  data() {
    return {
      userDefault,
      activeName: "first",
      input: null,
      total: 0,
      loading: false,
      dataList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateIdStr: "",
        sendTime: null,
        messageContent: null,
      },
      selectedRows: [],
      showAddMsg: false,
      videoData: {}, //视频数据
      chooeseTemplateId: "",
      currentTime: 0, //当前时间
      chooeseTime: "",
      copyOrEdite: false,

      showImportMsg: false, //导入消息
      importForm: {
        sendTime: null,
      },
      rules: {
        sendTime: [
          { required: true, message: "请选择发送时间", trigger: ['blur', 'change'] },
        ],
      },
      fileLists: [], //上传的文件列表
      uploadPercent: 0, //上传进度
      images: [], //存储上传的图片
      limit: 1, //上传限制
      file: null, //上传的文件
      templateData: {}, //模版数据
      replyInfo: {},
    };
  },
  created() {
    this.chooeseTemplateId = localStorage.getItem("createTemplateId") || null;
    this.queryParams.templateIdStr = this.chooeseTemplateId
    this.loadTemplateInfo(); //通过创建的模版消息id加载视频
    this.loadMessageList(); //加载右侧消息列表
  },
  methods: {
    addCommentFunc(){
      this.replyInfo = {}
      this.showAddMsg = true
    },
    handleExceed(){
      this.$message.warning("当前只允许选择 " + this.limit + " 个文件！");
    },
    // 上传文件
    uploadFile(params) {
      this.file = params.file
    },
    // 导入消息
    importMsg(){
      this.showImportMsg = true
    },
    // 取消导入
    importCancel() {
      this.showImportMsg = false;
      this.importForm.sendTime = null;
    },
    // 下载模版
    downloadTemplate(){
      this.download('/liveAuiMessageTemplateScriptBiz/downloadTemplate', {}, `消息模版_${new Date().getTime()}.xlsx`)
    },
    // 确认导入
    importConfirm() {
      if (!this.file) {
        this.$message.warning("请先选择文件");
        return;
      }
      let that = this;
      const formdata = new FormData();
      formdata.append("file", that.file);
      const headers = { ...that.headers, "Content-Type": "multipart/form-data",'Authorization': 'Bearer ' + getToken() };
      axios({
        url:`${import.meta.env.VITE_APP_BASE_API}/liveAuiMessageTemplateScriptBiz/upload/${that.chooeseTemplateId}`,
        method: 'post',
        data: formdata,
        headers,
      }).then((res) => {
        if (res.data.code == 200) {
          that.$message.success("上传成功！");
          that.loadMessageList()//重新加载右侧消息
          that.showImportMsg = false;
          that.file = null; // 清空文件
          that.fileLists = []; // 清空上传列表
          this.$refs.upload2.clearFiles();
        } else {
          that.file = null; // 清空文件
          that.fileLists = []; // 清空上传列表
          this.$refs.upload2.clearFiles();
          that.$message.error(res.data.msg);
        }
      })
    },
    //下载消息
    downloadMessageFunc(){
      this.downloadJSON('/liveAuiMessageTemplateScriptBiz/download', {'templateIdStr':this.chooeseTemplateId}, `消息_${new Date().getTime()}.xlsx`)
    },
    // 监听当前视频播放到几时几分几秒了
    onTimeUpdate(event) {
      this.currentTime = Math.floor(event.target.currentTime);
    },
    // 改变进度条
    videoTimeChange(newTime) {
      const video = this.$refs.videoRef;
      if (video && video.readyState >= 1) {
        video.currentTime = newTime;
      }
    },
    // 搜索消息
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.loadMessageList();
    },
    // 重置
    resetQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.sendTime = null;
      this.queryParams.messageContent = null;
      this.loadMessageList();
    },
    // 切换移动端还是pc端视频
    handleClick() {},
    // 查看此刻消息
    lookMessage() {
      let num = this.formatSecondsToHHMMSS(this.currentTime);
      this.queryParams.sendTime = num;
      this.handleQuery();
    },
    // 在此刻添加消息
    currentAddMsg() {
      let num = this.formatSecondsToHHMMSS(this.currentTime);
      this.chooeseTime = num;
      setTimeout( () => {
        this.showAddMsg = true;
      },500)
    },
    // 加载模版的视频信息
    loadTemplateInfo() {
      let that = this
      let params = {
        id: that.chooeseTemplateId,
      };
      templateInfo(params).then((res) => {
        if (res.code === 200) {
          that.templateData = res.data;
          that.loadVideoInfo(res.data.appVideoId)
        }
      });
    },
    loadVideoInfo(id){
      getVideoSrc({id:id}).then(res => {
        if(res.code == 200){
          this.videoData = {
            ...res.data.playInfoList[0],
            ...res.data.requestId,
            ...res.data.videoBase,
          }
        }
      })
    },
    // 加载右侧table展示的消息
    loadMessageList() {
      this.loading = true;
      getMessageList(this.queryParams).then((response) => {
        this.dataList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 选择表格行
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 勾选消息
    handleSelectable(row) {
      return row.convertFlag !== 1;
    },
    // 添加回复消息
    addReplyFunc(data){
      this.replyInfo = data
      this.showAddMsg = true;
    },
    // 去编辑
    toEdite(data){
      data['isEdite'] = true
      localStorage.setItem("msgData",JSON.stringify(data));
      this.copyOrEdite = true
      this.showAddMsg = true;
    },
    // 复制消息
    toCopy(data){
      data['isEdite'] = false
      localStorage.setItem("msgData",JSON.stringify(data));
      this.copyOrEdite = true
      this.showAddMsg = true;
    },
    // 删除消息
    deleteSelected() {
      if (!this.selectedRows.length) {
        this.$message.warning("请先勾选消息");
        return;
      }
      let ids = this.selectedRows.map((item) => item.id);
      this.deleteFunc(ids)
    },
    // 单条消息删除
    deleteMsg(data){
      this.deleteFunc([data.id])
    },
    // 删除请求
    deleteFunc(ids){
      let that = this
      that.$modal
        .confirm('是否确认删除消息？')
        .then(function () {
          removeMessage(ids).then((res) => {
            if (res.code == 200) {
              that.loadMessageList();
              that.$message.success("删除消息成功");
            }
          });
        })
        .catch(() => {});
    },
    // 总时长展示优化
    formatSecondsToHHMMSS(seconds) {
      let str = "00:00:00";
      if (seconds) {
        const sec = parseInt(seconds, 10);
        const h = Math.floor(sec / 3600);
        const m = Math.floor((sec % 3600) / 60);
        const s = sec % 60;
        const pad = (n) => n.toString().padStart(2, "0");
        str = `${pad(h)}:${pad(m)}:${pad(s)}`;
      }
      return str;
    },
  },
};
</script>
<style lang="scss" scoped>
.containerBox {
  width: 100%;
  display: flex;
  height: calc(100vh - 180px);
}
.left {
  width: 35%;
  min-width: 465px;
  margin-right: 10px;
}
.rightTable{
  max-height: calc(100vh - 352px);
  overflow-y: scroll;
}
.right {
  flex: 1;
  .queryBox {
    margin-bottom: 10px;
    cursor: pointer;
    height: 40px;
    display: flex;
    justify-content: space-between;
    .titleBox{
      display: flex;
      justify-content: space-between;
      .titleName {
        font-size: 16px;
        font-weight: bold;
        color: #000;
        margin-right: 20px;
      }
    }
  }
  .btnBox {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .msgContent {
    padding: 5px 0;
    text-align: left;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .userInfoBox {
    display: flex;
    justify-content: flex-start;
    height: 30px;
    .userIdentity {
      padding: 0px 6px;
      line-height: 30px;
      height: 30px;
      border-radius: 3px;
      margin: 0 5px;
      background-color: #ddf0eb;
      color: #00c088;
    }
    .headImg {
      width: 30px;
      height: 30px;
    }
    .uname {
      text-align: left;
      line-height: 30px;
      height: 30px;
      flex: 1;
      color: gray;
    }
  }
}
.replayInfoBox{
  padding: 10px;
  background-color: #f5f5f5;
  .replayBox{
    display: flex;
    justify-content: space-between;
  }
}
.messageId{
  color: gray;
}
</style>
<style>
.custom-tooltip {
  max-width: 500px !important;         /* 限宽 */
  white-space: normal !important;       /* 允许换行 */
  word-break: break-all;                /* 极长单词也断开 */
  overflow-wrap: anywhere;              /* 兜底换行 */
}
</style>