<template>
  <div class="w-100">
    <div style="width: 80%">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基础配置 -->
        <div class="titleBox">基础配置</div>
        <el-form-item label="直播名称" prop="title" required>
          <el-input
            v-model="form.title"
            placeholder="请输入直播名称"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="直播公告" prop="notice" required>
          <el-input
            type="textarea"
            :rows="4"
            v-model="form.notice"
            placeholder="请输入直播公告"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="课程分组" prop="courseGroupId" required>
          <el-select
            v-model="form.courseGroupId"
            placeholder="请选择课程分组"
            style="width: 100%"
          >
            <el-option
              v-for="(dict,index) in courseSelectData"
              :key="index"
              :label="dict.categoryName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="直播简介" prop="liveIntroduction" required>
          <editor
            v-model="form.liveIntroduction"
            :min-height="200"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" required>
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="stopTime" required>
          <el-date-picker
            v-model="form.stopTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="直播封面" prop="liveImageId" required>
          <div class="flex">
            <img :src="form.liveImageId ? chooeseImg : posterImg" style="width: 318px; height: 172px" />
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                @click="handleChooeseImg"
              >
                添加图片
              </el-button>
              <div class="tips">
                <div>*（建议按照下方要求添加图片）</div>
                <div>图片尺寸：“750px*420px”</div>
                <div>图片比例：16:9</div>
                <div>图片格式：png、jpg、jpeg格式</div>
                <div>图片大小：小5M</div>
                <div>暖场视频</div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="暖场视频" prop="warmUpVideoId" required>
          <div class="flex">
            <div v-if="form.warmUpVideoId">
              <video
                ref="videoRef"
                :src="videoSrc"
                :poster="videoPoster"
                controls
                width="100%"
                style="max-height: 270px;"
              >
                您的浏览器不支持 video 标签。
              </video>
            </div>
            <img v-else :src="videoImg" style="width: 318px; height: 172px" />
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                @click="handleChooeseVideo('warmUpVideoId')"
              >
                添加暖场视频
              </el-button>
              <div class="tips">
                <div>
                  支持mp4、avi、wmv、mov、flv、rmvb、3gp、m4v、mkv格式；
                </div>
                <div>文件最大不超过20G，新视频上传后需要转码；</div>
                <div>请尽可能提前上传，转码未完成时学员端无法观看</div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="讲师设置" prop="anchorId" required>
          <el-select
            v-model="form.anchorId"
            placeholder="请选择课程分组"
            style="width: 100%"
            @change="changeAnchorId"
          >
            <el-option
              v-for="(dict,index) in dropdownList"
              :key="index"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="助教设置" required>
          <div class="flex">
            <div>
              <el-tag style="margin-right: 6px; cursor: pointer;" v-for="(item,index) in checkedNodes" :key="index">{{ item.label }}</el-tag>
            </div>
            <el-button
              class="addBtn"
              type="primary"
              @click="addTeachingAssistant = true"
            >
              选择助教
            </el-button>
          </div>
          <div style="color: gray; fontSize: 12px;">* 单场直播可配置多位助教</div>
        </el-form-item>
        <!-- 直播内容设置 -->
        <div class="titleBox">直播内容设置</div>
        <el-form-item label="内容来源" prop="contentSource" required>
          <el-radio-group v-model="form.contentSource" @change="changeContentSource">
            <el-radio :label="0">自定义</el-radio>
            <el-radio :label="1">直播模版</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="课程视频" prop="courseVideoId" required v-if="form.contentSource === 0">
          <div class="flex">
            <div v-if="form.courseVideoId">
              <video
                ref="videoRef"
                :src="courseVideoSrc"
                :poster="courseVideoPoster"
                controls
                width="100%"
                style="max-height: 270px;"
              >
                您的浏览器不支持 video 标签。
              </video>
            </div>
            <img v-else :src="videoImg" style="width: 318px; height: 172px" />
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                @click="handleChooeseVideo('courseVideoId')"
              >
                添加课程视频
              </el-button>
              <div class="tips">
                <div>
                  1、支持mp4、avi、wmv、mov、flv、rmvb、3gp、m4v、mkv格式；
                </div>
                <div>2、文件最大不超过20G，新视频上传后需要转码；</div>
                <div>
                  请至少提60分钟创建课程，否则可能会影响视频按时开始播放
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="消息内容设置" v-if="form.contentSource === 0">
          <el-select
            v-model="form.categoryType"
            placeholder="请选择消息内容"
            style="width: 100%"
          >
            <el-option
              v-for="(item,index) in messageList"
              :key="index"
              :label="item.roomName"
              :value="item.liveId"
            />
          </el-select>
          <div class="tips">
            <div>
              *
              若使用真实直播下载的的视频回放生成录播直播课，则使用该直播的【直播消息下载】功能下载的消息，原则上可以直接用于生成用于录播直播的消息内容
            </div>
            <div>
              *
              若原直播中间有中断（主要为OBS断流，螳螂直播原则上不受影响），则回放视频会自动截除中断时间的视频
            </div>
            <div>
              *
              直播中断，但IM消息不会停止，故若视频中断，则课程回放可能与直播IM消息产生无法对应的情况，故若直播课程有中断，建议不要使用该课程回放用作生成录播直播
            </div>
            <div>
              *
              若坚持使用该直播回放生成录播直播，建议按实际回放效果，调整直播IM消息时间（【直播消息下载】下载的IM消息中，直播中断时的IM将会被标记）
            </div>
          </div>
        </el-form-item>
        <el-form-item label="模版设置" prop="liveTemplateId" required v-if="form.contentSource === 1">
          <el-select
            v-model="form.liveTemplateId"
            placeholder="请选择直播模版"
            style="width: 100%"
          >
            <el-option
              v-for="(dict,index) in templateListData"
              :key="index"
              :label="dict.templateName"
              :value="dict.liveTemplateId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div style="margin-top: 50px;margin-left:150px;">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>

    <!-- 助教选择 -->
    <el-dialog
      title="添加助教"
      :visible="addTeachingAssistant"
      width="60%"
      @close="handleClose"
      :close-on-click-modal="false"
    >
      <div class="flex myBox">
        <div
          class="flex-1"
          style="border: 1px solid #ccc; margin-right: 10px; padding: 20px"
        >
          <div style="border-bottom: 1px solid #ccc; margin-bottom: 10px">
            <el-input
              v-model="form.categoryName"
              prefix-icon="el-icon-search"
              placeholder="请搜索"
              style="width: 100%; border: none !important"
            />
          </div>
          <el-tree
            :data="userTreeData"
            show-checkbox
            node-key="id"
            ref="userTree"
            filter
            :default-checked-keys="defaultChecked"
            :props="defaultProps"
            :check-strictly="true"
            @check="handleCheckChange"
          >
          </el-tree>
        </div>
        <div class="flex-1" style="border: 1px solid #ccc; padding: 20px">
          <div
            style="
              border-bottom: 1px solid #ccc;
              line-height: 36px;
              margin-bottom: 10px;
            "
          >
            已选择的助教(0)
          </div>
          <div>
            <p v-for="(item,index) in checkedNodes" :key="index">{{ item.label }}</p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择视频 -->
     <chooeseVideo :visible.sync="showCreate" :isCreateTemplate="false" @chooeseVideo="chooeseVideoFunc" />

    <!-- 选择图片 -->
    <chooeseImage :visible.sync="chooeseImageShow" :isCreateTemplate="false" @chooeseImageSuccess="chooeseSuccess" />
  </div>
</template>

<script>
import { addRecordCourseData,getUserTreeList,searchTemplateList, courseSelectList, msgListData } from "@/api/liveBroadcastManagement/messageTemplate";
import chooeseImage from '@/views/liveBroadcastManagement/components/chooeseImage.vue'
import chooeseVideo from "../../components/chooeseVideo.vue";
import posterImg from "@/assets/images/courseImg.png";
import videoImg from "@/assets/images/courseVideo.png";
import { getVideoSrc } from "@/api/sourceMaterial/index";
export default {
  name: "createLiveRecord",
  dicts: ["category_type"],
  components:{chooeseVideo,chooeseImage},
  data() {
    return {
      courseVideoSrc: "", // 课程视频地址
      courseVideoPoster: "", // 课程视频地址
      videoSrc: "", // 暖场视频地址
      videoPoster: "", // 暖场视频封面
      chooeseImageShow: false, // 选择图片弹窗
      showCreate: false, // 选择课程弹窗
      chooeseImg: "", // 选择的图片
      posterImg,
      videoImg,
      total: 0,
      dataList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null,
      },
      defaultChecked: [],
      // 表单参数
      form: {
        title: "",//直播标题
        notice: "",//直播公告
        courseGroupId: "",//课程分组
        liveIntroduction: "",//直播简介
        startTime: "",//直播开始时间
        stopTime: "",//直播结束时间
        liveImageId: "",//直播封面id
        warmUpVideoId: "",//暖场视频ID
        anchorId: null,//主播Id
        anchorNick: null,//主播nick
        assistantIds: [],//助教信息 --- 助教用户ID列表
        contentSource: 0,//内容来源 0-自定义,1-直播模板
        courseVideoId: null,//课程视频ID(内容来源是自定义的时候设置)
        liveTemplateId: null,//直播模板ID(内容来源是直播模板的时候设置)
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: "直播标题不能为空", trigger: ['blur', 'change'] },
        ],
        notice: [
          { required: true, message: "直播公告不能为空", trigger: ['blur', 'change'] },
        ],
        courseGroupId: [
          { required: true, message: "课程分组不能为空", trigger: "change" },
        ],
        liveIntroduction: [
          { required: true, message: "直播简介不能为空", trigger: ['blur', 'change'] },
        ],
        startTime: [
          { required: true, message: "请选择直播开始时间", trigger: "change" },
        ],
        stopTime: [
          { required: true, message: "请选择直播结束时间", trigger: "change" },
        ],
        liveImageId: [
          { required: true, message: "请选择直播封面", trigger: "change" },
        ],
        warmUpVideoId: [
          { required: true, message: "请选择暖场视频", trigger: "change" },
        ],
        anchorId: [
          { required: true, message: "请选择讲师", trigger: "change" },
        ],
        assistantIds: [
          { required: true, message: "请选择助教", trigger: "change" },
        ],
        contentSource: [
          { required: true, message: "请选择内容来源", trigger: "change" },
        ],
        courseVideoId: [
          { required: true, message: "请选择课程视频", trigger: "change" },
        ],
        liveTemplateId: [
          { required: true, message: "请选择直播模版", trigger: "change" },
        ],
      },
      addTeachingAssistant: false,//助教弹窗
      userTreeData: [],//树形数据
      dropdownList: [],//下拉
      defaultProps: {
        children: "children",
        label: "label",
        disabled: (data, node) => {
          return data.type !== 1; // 非 type === 1 的节点禁用勾选
        }
      },
      checkedNodes: [],//已勾选助教数据
      checkedKeys: [],//已勾选助教ID
      templateListData: [],//直播模版
      courseSelectData: [],
      chooeseVideoType: '',//判断当前是选择暖场视频还是课程视频
      chooeseVideoId: null,
      messageList: []
    };
  },
  created() {
    this.getUserTree1();//讲师
    this.getUserTree2();//助教
    this.getTemplate()//可用直播模版
    this.getCourseSelectList()//课程下拉列表
  },
  watch:{
    'form.assistantIds':{
      handler(newVal){
        this.getUserTree1();//讲师
      },
      deep: true
    },
    'form.anchorId':{
      handler(newVal){
        this.getUserTree2();//助教
      },
      deep: true
    },
  },
  methods: {
    // 切换内容来源
    changeContentSource(){
      if(this.form.contentSource === 0){
        this.form.courseVideoId = null
        this.form.liveTemplateId = null
      }else if(this.form.contentSource === 1){
        this.form.courseVideoId = null
      }
    },
    // 选择课程分组
    getCourseSelectList(){
      courseSelectList().then(res => {
        this.courseSelectData = res.data
      })
    },
    // 选择图片
    handleChooeseImg() {
      this.chooeseImageShow = true
    },
    // 选择图片回调
    chooeseSuccess(data) {
      this.form.liveImageId = data.id
      this.chooeseImg = data.url
    },
    // 选择视频
    handleChooeseVideo(str) {
      this.chooeseVideoType = str
      this.showCreate = true
    },
    // 选择视频回调
    chooeseVideoFunc(data){
      if(this.chooeseVideoType === 'warmUpVideoId'){
        if(data.videoId){
          this.form.warmUpVideoId = data.id
          getVideoSrc({videoId:data.videoId}).then(res => {
            if(res.code == 200){
              this.videoSrc = res.data?.playInfoList[0]?.playURL
              this.videoPoster = res.data?.videoBase?.coverURL
            }
          })
        }
      }else if(this.chooeseVideoType === 'courseVideoId'){
        if(data.videoId){
          this.chooeseVideoId = data.videoId
          this.loadMessageList()
          this.form.courseVideoId = data.id
          getVideoSrc({videoId:data.videoId}).then(res => {
            if(res.code == 200){
              this.courseVideoSrc = res.data?.playInfoList[0]?.playURL
              this.courseVideoPoster = res.data?.videoBase?.coverURL
            }
          })
        }
      }
    },
    // 消息内容下拉列表
    loadMessageList(){
      msgListData(this.chooeseVideoId).then(res => {
        this.messageList = res.rows
      })
    },
    // 直播模版下拉
    getTemplate(){
      searchTemplateList().then(res => {
        this.templateListData = res.data
      })
    },
    // 讲师下拉选择
    changeAnchorId(e){
      let obj = this.dropdownList.filter(item => item.value === e)
      if(obj.length){
        this.form.anchorNick = obj[0].label
      }
    },
    // 助教选择确认
    handleConfirm() {
      console.log(this.checkedNodes, this.checkedKeys, "===checkedKeys")
      this.form.assistantIds = this.checkedKeys
      this.addTeachingAssistant = false;
    },
    // 助教选择取消
    handleCancel() {
      this.addTeachingAssistant = false;
    },
    handleClose(){
      this.addTeachingAssistant = false
    },
    // 勾选 选择
    handleCheckChange(checkedNodes, checkedKeys){
      this.checkedNodes = checkedKeys['checkedNodes']
      this.checkedKeys = checkedKeys['checkedKeys']
    },
    /** 讲师 */
    getUserTree1() {
      let parmas;
      if(this.form.assistantIds){
        parmas = {
          userIds: this.form.assistantIds
        }
      }else{
        parmas = {}
      }
      getUserTreeList(parmas).then((response) => {
        this.dropdownList = []
        this.collectType1Nodes(response.data[0])
      });
    },
    /** 助教 */
    getUserTree2() {
      let parmas;
      if(this.form.anchorId){
        parmas = {
          userIds: [this.form.anchorId]
        }
      }else{
        parmas = {}
      }
      getUserTreeList(parmas).then((response) => {
        this.userTreeData = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        
      };
      this.resetForm("form");
    },
    /** 提交按钮 */
    submitForm: function () {
      console.log(JSON.parse(JSON.stringify(this.form)), "===form🔥🔥🔥🔥🔥🔥🔥")
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id !== undefined) {
            editeCourseCategory(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
            });
          } else {
            console.log(JSON.parse(JSON.stringify(this.form)), "===form🔥🔥🔥🔥🔥🔥🔥")
            addRecordCourseData(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.$router.push('/courseManagement/liveCourse')
            });
          }
        }
      });
    },
    // 组装讲师下拉数据
    collectType1Nodes(node) {
      if (node.type === 1) {
        this.dropdownList.push({
          label: node.label,
          value: node.id
        });
      }
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => this.collectType1Nodes(child));
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.tips {
  font-size: 12px;
  color: gray;
  line-height: 20px;
  margin-top: 10px;
}
.titleBox {
  border-left: 3px solid #007aff;
  text-indent: 10px;
  font-weight: bold;
  margin-bottom: 20px;
}
.myBox{
  min-height: 300px;
}
::v-deep .myBox .el-input__inner {
  border: none !important;
  outline: none !important;
}
</style>

