<template>
  <div style="width: 40%;">
    <el-form ref="form" :model="form" :rules="rules" label-width="140px">
      <el-form-item label="应用名称" required prop="appName">
        <el-input
          v-model="form.appName"
          placeholder="请输入应用名称"
          :maxlength="26"
          :minlength="2"
          clearable
        />
      </el-form-item>
      <el-form-item label="消息群组回调地址" required prop="eventCallbackUrl">
        <el-input
          v-model="form.eventCallbackUrl"
          placeholder="请输入消息群组回调地址"
          :maxlength="256"
          type="textarea"
          :rows="5"
          show-word-limit
          clearable
        />
      </el-form-item>
      <el-form-item label="消息实效" required prop="msgLifeCycle">
        <el-select
          v-model="form.msgLifeCycle"
          placeholder="请选择消息实效"
          style="width:100%"
          clearable
        >
          <el-option label="30天" :value="0">30天</el-option>
          <el-option label="60天" :value="1">60天</el-option>
          <el-option label="90天" :value="2">90天</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { applicationConfigSave,getApplicationConfig,editeApplicationConfig } from '@/api/liveBroadcastManagement'
export default {
  name: 'applicationConfig',
  dicts: ['category_type'],
  data() {
    return {
      // 表单参数
      form: {
        appName: null,
        eventCallbackUrl: null,
        msgLifeCycle: null,
      },
      // 表单校验
      rules: {
        appName: [
          { required: true, message: '应用名称不能为空', trigger: ['blur', 'change'] }
        ],
        eventCallbackUrl: [
          { required: true, message: '消息群组回调地址不能为空', trigger: ['blur', 'change'] }
        ],
        msgLifeCycle: [
          { required: true, message: '请选择消息实效', trigger: 'change' }
        ]
      },
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    /** 查询公告列表 */
    getConfig() {
      getApplicationConfig().then(response => {
        this.form = response.data
      })
    },
    cancel(){

    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            editeApplicationConfig(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.getConfig()
            })
          } else {
            applicationConfigSave(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.getConfig()
            })
          }
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
</style>

