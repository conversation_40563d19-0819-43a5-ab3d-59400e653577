<template>
  <div>
    <div class="flex align-center justify-between">
      <div class="flex align-center">
        <i class="el-icon-data-line" style="font-size: 50px"></i>
        <div class="ml-5">
          <p style="font-weight: bold;">直播课程</p>
          <p>快速创建课程，公开课直播｜低价课直播｜0元课直播｜正价课直播｜大班课｜小班课</p>
          <p>营销直播间/服务直播间，优惠券｜精品课推荐｜快捷卡片</p>
        </div>
      </div>
      <div>
        <el-button type="primary">违规通知</el-button>
        <el-button type="primary">直播概览</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="addLive">新建直播</el-button>
      </div>
    </div>
    
    <el-tag class="mt-10" type="success">请仔细核查课程内容，遵守相关规定，禁止虚假宣传、夸大功效、诱导消费、网络水军等非法营销行为。关于规范私域直播秩序的公告</el-tag>
    
    <div class="boxContent">
      <div class="menuBox">
        <Category @selectCategory="chooeseCategory" />
      </div>
      <div class="content">
        <el-form ref="queryForm" class="mt-10" :model="queryParams" :inline="true">
          <el-form-item label="分类名称">
            <el-input
              v-model="queryParams.categoryName"
              placeholder="请输入分类名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="直播状态">
            <el-select
              v-model="queryParams.categoryType"
              placeholder="请选择"
              style="width:160px"
              clearable
            >
              <el-option
                v-for="dict in dict.type.category_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              >
              {{ dict.label }}
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上架状态">
            <el-select
              v-model="queryParams.systemed"
              placeholder="请选择"
              style="width:160px"
              clearable
            >
              <el-option :value="true" label="是">是</el-option>
              <el-option :value="false" label="否">否</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属流量池">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择"
              style="width:160px"
              clearable
            >
              <el-option :value="0" label="启用">启用</el-option>
              <el-option :value="1" label="禁用">禁用</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="课程类型">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择"
              style="width:160px"
              clearable
            >
              <el-option :value="0" label="启用">启用</el-option>
              <el-option :value="1" label="禁用">禁用</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="名称">
            <el-input
              v-model="queryParams.categoryName"
              placeholder="请输入课程名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
    
        <el-table v-loading="loading" :data="dataList">
          <el-table-column fixed="left" label="序号" type="index" width="55" align="center" />
          <el-table-column
            fixed="left"
            label="直播名称"
            align="center"
            prop="title"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="课程类型" align="center" prop="courseTypeName" />
          <!-- <el-table-column label="直播类型" align="center" prop="systemed" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.systemed">是</el-tag>
              <el-tag type="danger" v-else>否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="课程分组" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="直播模式" align="center" prop="createBy"/> -->
          <el-table-column label="直播状态" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag type="primary" :options="dict.type.live_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="直播开始时间" align="center" prop="startedAt"/>
          <el-table-column label="直播结束时间" align="center" prop="stoppedAt"/>
          <!-- <el-table-column label="上架状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="上/下架时间" align="center" prop="createTime" width="100">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所属流量池" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column label="创建人" align="center" prop="createBy" />
          <el-table-column label="创建时间" align="center" prop="createTime" />
          <!-- <el-table-column label="更新人" align="center" prop="status">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="更新时间" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" align="center" width="240" class-name="small-padding" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="handleUpdate(scope.row)">去直播</el-button>
              <el-button type="text" @click="handleUpdate(scope.row)">我的邀课链接</el-button>
              <el-button type="text" @click="handleUpdate(scope.row)">分享链接</el-button>
              <el-button type="text" @click="handleUpdate(scope.row)">服务配置</el-button>
              <el-dropdown>
                <span class="el-dropdown-link">
                  <el-button
                    type="text"
                  >更多</el-button>
                  <i class="el-icon-arrow-down el-icon--right" style="color:#409efe;"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <el-button type="text">编辑直播间</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text">复制课程</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text">下载直播消息</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text">下架</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text">删除</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text">直播统计</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text">我的直播统计</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text">直播大屏</el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
    
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="分类名称" prop="categoryName" required>
          <el-input v-model="form.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分组类型" prop="categoryType" required>
          <el-select v-model="form.categoryType" placeholder="请选择分组类型">
            <el-option
              v-for="dict in dict.type.category_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否系统类型" required>
          <el-radio-group v-model="form.systemed">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="启用禁用" required>
          <el-radio-group v-model="form.status">
            <el-radio :label="0">启用</el-radio>
            <el-radio :label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addCourseCategory,deleteCourseCategory,editeCourseCategory,detailCourseCategory,queryCourseCategory } from '@/api/liveBroadcastManagement'
import { courseListData,addRecordCourseData } from '@/api/liveBroadcastManagement/messageTemplate'
import Category from './category.vue'
export default {
  name: 'courseGroup',
  dicts: ['category_type','live_status'],
  components: {
    Category
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null,
        courseGroupId: null,
      },
      // 表单参数
      form: {
        status: 0,
        systemed:true,
        categoryName: null,
        categoryType: null,
        deleted:0,
      },
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: '分类名称不能为空', trigger: ['blur', 'change'] }
        ],
        categoryType: [
          { required: true, message: '分组类型不能为空', trigger: 'change' }
        ]
      },
      // 用户选择器
      userSelectorVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    chooeseCategory(data){
      this.queryParams.courseGroupId = data.id
      this.getList()
    },
    // 新建直播
    addLive(){
      this.$router.push('/courseManagement/createLiveRecord')
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      courseListData(this.queryParams).then(response => {
        this.dataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        status: 0,
        systemed:true,
        categoryName: null,
        categoryType: null,
        deleted:0,
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null,
      }
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加分类'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      let id = row.id
      detailCourseCategory(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改分类'
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            editeCourseCategory(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addCourseCategory(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.boxContent{
  display: flex;
  margin-top: 10px;
}
.menuBox{
  margin-right: 10px;
}
.content{
  flex: 1;
}
</style>

