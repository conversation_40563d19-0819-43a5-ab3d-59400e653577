<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button plain icon="el-icon-search" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          @click="handleAdd"
        >创建模板</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          text
          @click="toDeletePath"
        >已删除模板日志记录</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="60"
      />
      <el-table-column
        label="模版名称"
        align="center"
        prop="templateName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="视频来源"
        align="center"
        prop="appVideoName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="视频时长"
        align="center"
        prop="appVideoDuration"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <el-tag type="success" v-if="!scope.row.appVideoDuration">无</el-tag>
          <el-tag type="success" v-else>{{scope.row.appVideoDuration}}s</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="模板状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status == 0">关闭</el-tag>
          <el-tag type="danger" v-if="scope.row.status == 1">开启</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column
        label="创建人"
        align="center"
        prop="createBy"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="修改人"
        align="center"
        prop="updateBy"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="修改时间"
        align="center"
        prop="updateTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" align="center" class-name="small-padding">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleEdite(scope.row)"
          >编辑</el-button>
          <el-button
            type="text"
            @click="handleCopy(scope.row)"
          >复制</el-button>
          <el-button
            type="text"
             v-if="scope.row.status == 0"
            @click="handleOpenOrClose(scope.row,1)"
          >开启</el-button>
          <el-button
            type="text"
             v-if="scope.row.status == 1"
            @click="handleOpenOrClose(scope.row,0)"
          >关闭</el-button>
          <el-button
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增模版 -->
     <chooeseVideo :visible.sync="showCreate" :isCreateTemplate="true" />

    <!-- 复制模版 -->
    <el-dialog :title="editeOrCopyTitle" :visible.sync="openEdite" width="600px" append-to-body>
      <el-form ref="form" :model="copyForm" :rules="rules" label-width="80px">
        <el-row>
          <el-col>
            <el-form-item label="模版名称" prop="templateName">
              <el-input v-model="copyForm.templateName" placeholder="请输入模版名称" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditeForm">确 定</el-button>
        <el-button @click="openEdite = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="公告标题" prop="noticeTitle">
              <el-input v-model="form.noticeTitle" placeholder="请输入公告标题" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="覆盖用户">
              <el-button @click="userSelectorVisible = true">选择</el-button>
              <span v-if="form.userIds.length || (form.deptIds && form.deptIds.length)" class="selected-tip">已选{{ form.deptIds ? form.deptIds.length : 0 }}个部门，已选{{ form.userIds.length }}个用户</span>
              <span v-else class="selected-tip">如果不选择，则默认全部用户可见</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容">
              <editor v-model="form.noticeContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-drawer
      title="选择用户"
      size="100%"
      :visible.sync="userSelectorVisible"
    >
      <userSelector v-if="open" :default-user-ids="form.userIds" @confirm="handleSelectUser" />
    </el-drawer>
  </div>
</template>

<script>
import { listNotice, getNotice, delNotice, addNotice, updateNotice } from '@/api/system/notice'
import userSelector from '@/views/system/notice/components/UserSelector.vue'
import { queryMessageTemplateList,templateDelete,templateEdite,templateOpenOrClose,templateCopy } from '@/api/liveBroadcastManagement/messageTemplate'
import chooeseVideo from "../../components/chooeseVideo.vue";
export default {
  name: 'liveTemplate',
  components: { userSelector, chooeseVideo },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null,
      },
      // 表单参数
      form: {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        userIds: [],
        deptIds: [],
        status: '0'
      },
      // 表单校验
      rules: {
        noticeTitle: [
          { required: true, message: '公告标题不能为空', trigger: ['blur', 'change'] }
        ],
        noticeType: [
          { required: true, message: '公告类型不能为空', trigger: 'change' }
        ],
        templateName: [
          { required: true, message: '请输入模版名称', trigger: 'change' }
        ]
      },
      // 用户选择器
      userSelectorVisible: false,

      // 创建消息
      showCreate: false,
      editeOrCopyTitle: '',
      openEdite: false,
      copyForm:{
        templateName: '',
        idStr: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 删除记录
    toDeletePath(){
      this.$router.push('/courseManagement/deleteRecord')
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      queryMessageTemplateList(this.queryParams).then(response => {
        this.dataList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.showCreate = true
    },
    // 修改
    handleEdite(row){
      let parmas = {
        "idStr": row.id,
        "templateName": row.templateName,
        "appVideoIdStr": row.appVideoId,
        "pcVideoIdStr": row.pcVideoId,
        "status": row.status
      }
      templateEdite(parmas).then(res => {
        this.$router.push({
          path: '/courseManagement/createTemplate'
        })
        localStorage.setItem('createTemplateId',res.data.id)
      })
    },
    // 复制模版提交
    submitEditeForm(){
      templateCopy(this.copyForm).then(res => {
        if(res.code == 200){
          this.$router.push({
            path: '/courseManagement/createTemplate'
          })
          localStorage.setItem('createTemplateId',res.data)
        }
      })
    },
    // 复制
    handleCopy(row){
      this.editeOrCopyTitle = '复制编辑模版名称'
      this.copyForm.templateName = row.templateName
      this.copyForm.idStr = row.id
      this.openEdite = true
    },
    // 开启或者关闭
    handleOpenOrClose(row,status){
      if(status == 1){
        this.$modal.confirm('是否确认开启？').then(function() {
          let parmas = {
            idStr:row.id,
            openStatus: status
          }
          templateOpenOrClose(parmas).then(res => {
            if(res.code == 200){
              this.$modal.msgSuccess('开启操作成功')
              this.getList()
            }
          })
        })
      }else{
        this.$modal.confirm('是否确认关闭？').then(function() {
          let parmas = {
            idStr:row.id,
            openStatus: status
          }
          templateOpenOrClose(parmas).then(res => {
            if(res.code == 200){
              this.$modal.msgSuccess('关闭操作成功')
              this.getList()
            }
          })
        })
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const noticeId = row.noticeId || this.ids
      getNotice(noticeId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改公告'
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          console.log(this.form)
          if (this.form.noticeId !== undefined) {
            updateNotice(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addNotice(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名为"' + row.templateName + '"的直播模版？').then(function() {
        return templateDelete([row.id])
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    // 选择用户
    handleSelectUser(res) {
      this.userSelectorVisible = false
      this.form.userIds = res.userIds
      this.form.deptIds = res.deptIds
    }
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
</style>