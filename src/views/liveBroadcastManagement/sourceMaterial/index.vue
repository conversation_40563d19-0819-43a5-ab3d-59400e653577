<template>
  <div class="sourceMaterialBox">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="图片库" name="picture"></el-tab-pane>
      <el-tab-pane label="视频库" name="video"></el-tab-pane>
      <!-- <el-tab-pane label="回收站" name="recycleBin"></el-tab-pane> -->
    </el-tabs>
    <!-- 分类+内容区域 -->
    <div v-if="activeName !== 'recycleBin'" class="pageBox">
      <Category :type="activeName" :activeName="activeName" />
      <div class="pageRight">
        <component :is="activeComponent" />
      </div>
    </div>
    <!-- 回收站区域 -->
    <RecycleBin v-else />
  </div>
</template>

<script>
import Picture from "./components/picture.vue";
import Video from "./components/video.vue";
import RecycleBin from "./components/recycleBin.vue";
import Category from "./components/category.vue";
import {bus} from "@/utils/bus.js"

export default {
  name: "sourceMaterial",
  components: {
    Picture,
    Video,
    RecycleBin,
    Category,
  },
  data() {
    return {
      activeName: "picture",
    };
  },
  computed: {
    activeComponent() {
      return this.activeName === "picture" ? "Picture" : "Video";
    },
  },
  mounted() {
    // 监听事件
    this.$nextTick(() => {
      setTimeout(() => {
        bus.$emit("loadPicture",true);
      },100)
    });
  },
  methods: {
    handleClick(tab) {
      this.activeName = tab.name;
      if(tab.name == "picture") {
        this.$nextTick(() => {
          setTimeout(() => {
            bus.$emit("loadPicture",true);
          },100)
        });
      } else if(tab.name == "video") {
        this.$nextTick(() => {
          setTimeout(() => {
            bus.$emit("loadVideo",true);
          },100)
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.pageBox {
  display: flex;
  height: 100%;
}
.pageRight {
  flex: 1;
  max-height: calc(100vh - 260px);
  overflow-y: scroll;
  margin-left: 20px;
}
</style>
