<template>
  <div class="categoryBox">
    <el-input
      v-model="queryParams.cateName"
      placeholder="请输入分组名称"
      @keyup.enter.native="handleQuery"
    >
    </el-input>
    <el-button class="addBtn" plain icon="el-icon-plus" @click="handleAddCate">
      新建分组
    </el-button>
    <div class="categoryContainer">
      <div
        @click="groupClick(item, index)"
        v-for="(item, index) in categoryList"
        :key="index"
        class="boxItem"
        :class="activeIndex === index ? 'boxItemActive' : ''"
      >
        <template v-if="item.cateName.length < 10">
          {{ item.cateName }}
        </template>
        <template v-else>
          <el-popover
            placement="left-start"
            title=""
            width="200"
            trigger="hover"
            :content="item.cateName"
          >
            <div slot="reference" class="textOne">{{ item.cateName }}</div>
          </el-popover>
        </template>
        <i class="el-icon-edit editeIcon" @click="handleEdite(item)"></i>
        <i class="el-icon-delete deleteIcon" @click="handleDelete(item)"></i>
      </div>
    </div>
    <!-- 添加或修改资源类别对话框 -->
    <el-dialog :title="title" :visible.sync="openCate" width="500px" append-to-body>
      <el-form
        ref="form"
        :model="addQueryParams"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="分组名称" prop="cateName">
          <el-input
            v-model="addQueryParams.cateName"
            placeholder="请输入类别名称"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCateForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addGroup,
  deleteGroup,
  editeGroup,
  queryGroup,
} from "@/api/sourceMaterial/index";
import { bus } from "@/utils/bus.js"

export default {
  name: "Category",
  props: {
    type: {
      type: String,
      default: "picture", // 默认类型为图片
    },
    activeName: {
      type: String,
      default: "picture", // 默认激活的标签页
    },
  },
  data() {
    return {
      showDelete: false,
      activeIndex: 0,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资源类别表格数据
      categoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      openCate: false,
      // 查询参数
      queryParams: {
        cateName: null,
      },
      addQueryParams: {
        cateName: null,
        parentId: null,
        type: "",
      },
      // 表单校验
      rules: {
        cateName: [
          { required: true, message: "分组名称不能为空", trigger: ['blur', 'change'] },
        ],
      },
      isEdit: false,
      currentRow: null,
    };
  },
  mounted() {
    this.queryParams.cateName = null;
    this.getCateDataList();
  },
  methods: {
    groupClick(data, index) {
      this.activeIndex = index;
      if(this.activeName === "picture") {
        bus.$emit("selectPicture", data);
      } else {
        bus.$emit("selectVideo", data);
      }
      localStorage.setItem('cateIdChooese',data.id)
    },
    /** 查询资源类别列表 */
    getCateDataList() {
      this.loading = true;
      queryGroup(this.queryParams).then((response) => {
        this.categoryList = response.data || [];
        this.loading = false;
        localStorage.setItem('cateIdChooese','全部')
      });
    },
    // 取消按钮
    cancel() {
      this.openCate = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        cateName: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getCateDataList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 100,
        cateName: null,
      };
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAddCate() {
      this.reset();
      if (this.type === "picture") {
        this.title = "添加图片资源分组";
      } else {
        this.title = "添加视频资源分组";
      }
      this.openCate = true;
    },
    /** 修改按钮操作 */
    handleEdite(row) {
      this.addQueryParams = row;
      this.reset();
      if (this.type === "picture") {
        this.title = "修改图片资源分组";
      } else {
        this.title = "修改视频资源分组";
      }
      this.openCate = true;
    },
    /** 提交按钮 */
    submitCateForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.addQueryParams.id != null) {
            editeGroup(this.addQueryParams).then((response) => {
              if (this.type === "picture") {
                this.$modal.msgSuccess("修改图片分组成功");
              } else {
                this.$modal.msgSuccess("修改视频分组成功");
              }
              this.openCate = false;
              this.getCateDataList();
            });
          } else {
            addGroup(this.addQueryParams).then((response) => {
              if (this.type === "picture") {
                this.$modal.msgSuccess("新增图片分组成功");
              } else {
                this.$modal.msgSuccess("新增视频分组成功");
              }
              this.openCate = false;
              this.getCateDataList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除 " + row.cateName + " 分组？")
        .then(function () {
          return deleteGroup(row.id);
        })
        .then(() => {
          this.getCateDataList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.categoryContainer {
  height: calc(100vh - 370px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px;
  box-sizing: border-box;
}
.categoryBox {
  width: 260px;
  border: 1px solid #eee;
  padding: 10px;
  height: calc(100vh - 260px);
  box-sizing: border-box;
}
.addBtn {
  margin-top: 10px;
  margin-bottom: 10px;
}
.boxItem {
  position: relative;
  height: 40px;
  width: 100%;
  line-height: 40px;
  cursor: pointer;
  padding: 0 50px 0 10px;
  border-radius: 6px;
  box-sizing: border-box;
}

.textOne {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.boxItem .deleteIcon,
.boxItem .editeIcon {
  display: none;
}

.boxItem:hover .deleteIcon,
.boxItem:hover .editeIcon {
  display: block;
}

.deleteIcon {
  position: absolute;
  right: 6px;
  color: #007aff;
  top: 12px;
}
.editeIcon {
  position: absolute;
  right: 30px;
  color: #007aff;
  top: 12px;
}

.boxItemActive {
  background-color: #ecfaf6;
  color: #0abf89;
}
.boxItem:hover{
  background-color: #ecfaf6;
  color: #0abf89;
}
.category-container {
  min-width: 355px;
  padding: 10px;
  border: 1px solid #eaeaea;
}
.operation-header {
  display: flex;
  margin-bottom: 22px;
}

.category-table-wrap {
  height: calc(100vh - 250px);
}
.category-col {
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: opacity 0.3s;
  &:hover .category-name {
    text-decoration: underline;
  }
  .category-name {
    flex: 1;
    //line-height: 56.5px;
    padding: 0 10px;
  }
  .category-r {
    display: flex;
    align-items: center;
  }
  .category-operation {
    opacity: 0;
  }
  .el-icon-arrow-right {
    opacity: 1;
  }
  &:hover {
    .category-operation {
      opacity: 1;
    }
    .el-icon-arrow-right {
      opacity: 0;
    }
  }
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
::v-deep .el-form-item--small.el-form-item {
  margin: 0;
}
</style>
