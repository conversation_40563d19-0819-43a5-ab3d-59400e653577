<template>
  <el-dialog :title="titleName" :visible.sync="visible" width="30%" @close="handleClose">
    <div style="margin-bottom: 20px;">
      <el-input v-model="queryParams.nickname" placeholder="请输入用户昵称" clearable />
    </div>
    <div v-if="type === 'user'">
      <el-radio-group v-model="chooeseUser">
        <el-radio class="radioBox" :label="item.id" v-for="(item,index) in dataList" :key="index">
          <img class="userLogo" :src="item.avatarUrl" alt="">
          <span>{{item.nickname}}</span>
        </el-radio>
      </el-radio-group>
      <div class="pageBox">
        <el-pagination
          @current-change="handleCurrentChange"
          layout="prev, pager, next"
          :total="total">
        </el-pagination>
      </div>
    </div>
    <div v-else>
      <el-tree
        :data="userTreeData"
        show-checkbox
        node-key="id"
        ref="assistantTree"
        filter
        :default-checked-keys="defaultChecked"
        :props="defaultProps"
        @check="handleCheckChange"
      >
      </el-tree>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { userList,assistantList } from '@/api/liveBroadcastManagement/messageTemplate'
export default {
  name: "createVideo",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    type: {
      type: String,
      default: "user",
    },
  },
  data() {
    return {
      titleName: "请选择用户",
      queryParams:{
        nickname: null,
        pageSize: 10,
        pageNum: 1,
      },
      chooeseUser: null,
      currentPage: 1,
      total: 67,
      dataList: [],
      userTreeData: [],
      defaultChecked: [],
      defaultProps: {
        children: "children",
        label: "label",
        disabled: (data, node) => {
          return data.type !== 1; // 非 type === 1 的节点禁用勾选
        }
      },
      checkedNodes: [],
      checkedKeys: [],
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadVideoListData();
      }
    },
    type:{
      handler(val) {
        if (val === "user") {
          this.titleName = "请选择用户";
        } else if (val === "assistant") {
          this.titleName = "请选择助教";
        }
      },
      immediate: true,
      deep: true,
    }
  },
  methods: {
    // 勾选选择
    handleCheckChange(checkedNodes, checkedKeys){
      this.checkedNodes = checkedKeys['checkedNodes']
      this.checkedKeys = checkedKeys['checkedKeys']
      if (this.checkedKeys.length > 1) {
        this.checkedKeys = [checkedNodes.id]
        this.checkedNodes = [checkedNodes];
        // 取消所有选中
        this.$refs.assistantTree.setCheckedKeys([]);
        // 只选中当前点击的节点
        this.$refs.assistantTree.setCheckedKeys([checkedNodes.id]);
      }
    },
    // 分页
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.loadVideoListData();
    },
    // 加载数据
    loadVideoListData(){
      this.loading = true
      if(this.type === 'user'){
        userList(this.queryParams).then(res => {
          this.dataList = res.rows
          this.total = res.total
          this.loading = false
        })
      }else{
        assistantList(this.queryParams).then(res => {
          this.userTreeData = res.data
          this.loading = false
        })
      }
    },
    // 预览
    handleView(){

    },
    // 搜索
    handleQuery(){
      this.loadVideoListData()
    },
    // 重置
    resetQuery(){
      this.loadVideoListData()
    },
    handleClose() {
      this.$emit("update:visible", false);
    },
    handleCancel() {
      this.$emit("update:visible", false);
    },
    // 确认选择
    handleConfirm() {
      if (this.type === "user") {
        if (!this.chooeseUser) {
          this.$message.error("请选择用户");
          return;
        }
        let userInfo = this.dataList.find(item => item.id === this.chooeseUser);
        this.$emit("chooeseUserInfo", userInfo);
        this.$emit("update:visible", false);
      } else {
        if (this.checkedKeys.length === 0) {
          this.$message.error("请选择助教");
          return;
        }
        this.$emit("chooeseAssistant", this.checkedNodes[0]);
        this.$emit("update:visible", false);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.pageBox{
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.radioBox{
  display: flex;
  width: 100%;
  align-items: center;
}
.userLogo{
  width: 30px;
  height: 30px;
  display: block;
  border-radius: 30px;
  margin-left: 8px;
  margin-right: 8px;
}
::v-deep .el-radio__label{
  display: flex;
  align-items: center;
}
::v-deep .el-radio__input{
  display: flex;
  align-items: center;
}
</style>
