<template>
  <div>
    <!-- 添加评论消息 -->
    <el-dialog
      :title="title"
      :visible.sync="localVisible"
      width="45%"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="replayBox" v-if="addReplyData&&addReplyData.id">
        <div>{{ addReplyData.senderType == 1 ? '学员消息':'助教消息' }}({{ addReplyData.senderId }}): {{ addReplyData.messageContent }}</div>
      </div>
      <el-form ref="msgForm" :model="msgForm" :rules="rules" label-width="100px">
        <el-form-item label="发送时间" prop="sendTime" required>
          <el-time-picker
            v-model="msgForm.sendTime"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="请选择消息发送时间"
          />
        </el-form-item>
        <!-- 如果是回复消息 则只需要选择助教身份就行-->
        <el-form-item label="发送者类型" prop="senderType" required v-if="!addReplyData.id">
          <el-radio-group v-model="msgForm.senderType">
            <el-radio :label="1">学员</el-radio>
            <el-radio :label="2">助教</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 回复消息只能是文本 且 学员不能发送图片-->
        <el-form-item label="消息类型" prop="messageContentType" required>
          <el-radio-group v-model="msgForm.messageContentType">
            <el-radio :label="1">文本</el-radio>
            <el-radio :label="2" v-if="msgForm.senderType != 1 && !addReplyData.id">图片</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="msgForm.messageContentType === 1"
          label="文本"
          prop="messageContent"
          required
        >
          <el-input
            v-model="msgForm.messageContent"
            type="textarea"
            :rows="6"
            :maxlength="512"
            show-word-limit
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item
          v-if="msgForm.messageContentType === 2"
          label="图片"
          prop="imageUrl"
          required
        >
          <div class="boxImage">
            <el-image
              v-if="msgForm.imageUrl"
              class="pic"
              :src="msgForm.imageUrl"
              fit="contain"
            />
            <el-button
              type="text"
              icon="el-icon-plus"
              @click="chooeseImageShow = true"
            >选择图片</el-button>
          </div>
        </el-form-item>
        <el-form-item :label="msgForm.senderType === 1 ? '学员身份' : '助教身份'">
          <el-button type="primary" @click="chooeseUser">选择身份</el-button>
          <div v-if="msgForm.senderId">所选{{ msgForm.senderType === 1 ? '学员' : '助教' }}名称:{{ msgForm.senderName }}</div>
          <div class="tips">
            *
            可以选择用户身份库的身份，选择后昵称可修改，若不选择，系统将自动分配一个用户身份。
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择身份 -->
    <chooeseIdentity :visible.sync="showChooeseIdentity" :type="userType" @chooeseUserInfo="chooeseUserInfo" @chooeseAssistant="chooeseAssistant"></chooeseIdentity>
    <!-- 选择图片 -->
    <chooeseImage :visible.sync="chooeseImageShow" :isCreateTemplate="true" @chooeseImageSuccess="chooeseSuccess" />
  </div>
</template>

<script>
import { createMessage, editeMessage } from '@/api/liveBroadcastManagement/messageTemplate'
import chooeseIdentity from '@/views/liveBroadcastManagement/components/chooeseIdentity.vue'
import chooeseImage from '@/views/liveBroadcastManagement/components/chooeseImage.vue'
export default {
  name: 'addCommentMsg',
  components: { chooeseIdentity, chooeseImage },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    chooeseVideoId: {
      type: [Number, String]
    },
    chooeseTime: {
      type: [Number, String]
    },
    copyOrEdite:{
      type: Boolean,
      required: false
    },
    addReplyData:{
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      localVisible: this.visible,
      title: '添加评论消息',
      chooeseImageShow: false,
      dataList: [],
      loading: true,
      total: 0,
      queryParams: {
        categoryName: null,
        status: null,
        pageSize: 10,
        pageNum: 1
      },
      msgForm: {
        templateIdStr: this.chooeseVideoId,
        messageType: 1, // 评论消息
        messageContentType: 1,
        messageContent: '',
        senderType: 1,
        sendTime: '',
        imageUrl: ''
      },
      showChooeseIdentity: false,
      rules: {
        sendTime: [
          { required: true, message: '请选择消息发送时间', trigger: 'change' }
        ],
        senderType: [
          { required: true, message: '请选择发送者类型', trigger: 'change' }
        ],
        messageContentType: [
          { required: true, message: '请选择消息类型', trigger: 'change' }
        ],
        messageContent: [
          { required: true, message: '请输入消息内容', trigger: ['blur', 'change'] }
        ],
        imageUrl: [
          { required: true, message: '请选择图片上传', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          if (this.$refs.msgForm) {
            this.$refs.msgForm.resetFields();
          }
        });
        this.localVisible = val
        this.loadVideoListData()
        if(this.copyOrEdite){
          this.dealInfo()
        }
        if(this.addReplyData?.id){
          this.title = '添加回复消息'
          this.msgForm.senderType = 2
          this.msgForm['replayMessageId'] = this.addReplyData?.id
        }else{
          this.title = '添加评论消息'
        }
      }
    },
    localVisible(val) {
      this.$emit('update:visible', val)
    },
    chooeseTime: {
      handler(newVal) {
        setTimeout( () => {
          this.msgForm.sendTime = newVal
        },1000)
      },
      deep: true,
      immediate: true
    },
    'msgForm.senderType':{
      handler(newVal) {
        this.userType = newVal === 1 ? 'user' : 'assistant'
        if(newVal === 1) {
          this.msgForm.messageContentType = 1 // 学员只能发送文本消息
        }
      },
      immediate: true
    }
  },
  methods: {
    // 选择助教
    chooeseAssistant(data){
      this.msgForm.senderId = data.id
      this.msgForm.senderName = data.label
    },
    // 选择学员
    chooeseUserInfo(data){
      this.msgForm.senderId = data.id
      this.msgForm.senderName = data.nickname
    },
    // 处理编辑或者复制回显数据
    dealInfo(){
      let info =  localStorage.getItem("msgData")
      let msgInfo = info ? JSON.parse(info) : {};
      if(msgInfo.isEdite){
        this.msgForm.id = msgInfo.id
        this.title = '编辑评论消息'
      }else{
        this.title = '复制添加评论消息'
      }
      this.msgForm.messageType = msgInfo.messageType
      this.msgForm.messageContentType = Number(msgInfo.messageContentType)
      this.msgForm.messageContent = msgInfo.messageContent
      this.msgForm.senderType = Number(msgInfo.senderType)
      this.msgForm.sendTime = msgInfo.sendTime
      this.msgForm.imageUrl = msgInfo.imageUrl
    },
    // 选择图片
    chooeseSuccess(url) {
      this.msgForm.imageUrl = url
    },
    // 加载数据
    loadVideoListData() {
      this.loading = true
      // queryMessageTemplateList(this.queryParams).then((response) => {
      //   this.dataList = response.data.rows;
      //   this.total = response.data.total;
      //   this.loading = false;
      // });
    },
    chooeseUser() {
      this.showChooeseIdentity = true
    },
    handleClose() {
      this.reset()
      this.localVisible = false
      this.$emit('update:visible', false)
    },
    handleCancel() {
      this.reset()
      this.localVisible = false
      this.$emit('update:visible', false)
    },
    handleConfirm() {
      this.$refs['msgForm'].validate((valid) => {
        if (valid) {
          // 回复评论消息
          if(this.addReplyData.id){
            createMessage(this.msgForm).then((response) => {
              this.$modal.msgSuccess('回复消息成功')
              this.$emit('loadList', true)
            })
          }else{
            // 修改评论消息
            if (this.msgForm.id !== undefined) {
              editeMessage(this.msgForm).then((response) => {
                this.$modal.msgSuccess('修改评论成功')
                this.$emit('loadList', true)
              })
            } else {
            // 新增评论消息
              createMessage(this.msgForm).then((response) => {
                this.$modal.msgSuccess('新增评论成功')
                this.$emit('loadList', true)
              })
            }
          }
          this.reset()
          this.localVisible = false
          this.$emit('update:visible', false)
        }
      })
    },
    reset(){
     this.msgForm =  {
        templateIdStr: this.chooeseVideoId,
        messageType: 1, // 评论消息
        messageContentType: 1,
        messageContent: null,
        senderType: 1,
        sendTime: null,
        imageUrl: null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tips {
  font-size: 12px;
  margin-top: 10px;
  color: #909399;
}
.boxImage {
  display: flex;
  align-items: center;
}
.pic {
  width: 60px;
  height: 60px;
}
.replayBox{
  padding: 10px 0 20px 20px;
}
</style>
