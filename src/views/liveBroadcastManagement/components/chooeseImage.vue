<template>
  <div>
    <el-dialog
      title="图片库"
      :visible.sync="visible"
      width="70%"
      @close="handleClose"
      :close-on-click-modal="false"
    >
      <div>
        <el-form ref="queryForm" :model="queryParams" :inline="true">
          <el-form-item label="图片分组">
            <el-select
              v-model="queryParams.fileCategory"
              placeholder="请选择图片分组"
              clearable
            >
              <el-option
                v-for="(item, index) in categoryList"
                :key="index"
                :label="item.cateName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="图片名称">
            <el-input
              v-model="queryParams.fileName"
              placeholder="请输入图片名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <el-radio-group v-model="selectedRadio" class="radioBox">
          <div class="imgBox">
            <div class="imgItem" v-show="dataList.length" v-for="(item, index) in dataList" :key="index">
              <el-image class="pic" :src="item.url" fit="contain" />
              <div class="boxRadio">
                <el-radio :label="index+1"><div class="imgName">{{ item.title }}</div></el-radio>
              </div>
            </div>
            <div class="emptyBox">
              <el-empty v-show="!dataList.length" description="暂无数据..."></el-empty>
            </div>
          </div>
        </el-radio-group>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadImageListData"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryImageList,
  queryGroup,
} from "@/api/sourceMaterial/index";
export default {
  name: "chooeseImage",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    // 是否是新增模版
    isCreateTemplate:{
      type: Boolean,
      required: true,
    }
  },
  data() {
    return {
      selectedRows: [],
      categoryList: [],
      dataList: [],
      loading: true,
      total: 0,
      queryParams: {
        fileName: null,
        fileCategory: null,
        pageSize: 10,
        pageNum: 1,
      },
      videoData: {},
      showVideo: false,
      selectedRadio: null,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadImageListData();
        this.loadCategory();
      }
    },
  },
  methods: {
    // 加载数据
    loadImageListData() {
      this.loading = true;
      queryImageList(this.queryParams).then((response) => {
        this.dataList = response.rows || [];
        this.total = response.total;
        console.log(this.dataList,"==========1231231231")
        this.loading = false;
      });
    },
    // 加载视频分组数据
    loadCategory() {
      let parmas = {
        cateName: null,
      };
      queryGroup(parmas).then((response) => {
        this.categoryList = response.data || [];
      });
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.loadImageListData();
    },
    // 重置
    resetQuery() {
      this.queryParams.fileCategory = null;
      this.queryParams.fileName = null;
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.loadImageListData();
    },
    handleClose() {
      this.$emit("update:visible", false);
    },
    handleCancel() {
      this.$emit("update:visible", false);
    },
    handleConfirm() {
      if(this.isCreateTemplate){
        let url = this.dataList[this.selectedRadio-1]?.url
        if (this.selectedRadio) {
          this.$emit('chooeseImageSuccess', url);
          this.$emit("update:visible", false);
        } else {
          this.$message.warning("请先勾选图片");
        }
      }else{
        let chooeseData = this.dataList[this.selectedRadio-1]
        if (this.selectedRadio) {
          this.$emit('chooeseImageSuccess', chooeseData);
          this.$emit("update:visible", false);
        } else {
          this.$message.warning("请先勾选图片");
        }
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.tips {
  margin-top: 10px;
  margin-bottom: 20px;
  p {
    margin: 0;
    font-size: 12px;
    line-height: 20px;
  }
}
.imgBox {
  display: flex;
  flex-wrap: wrap;
  .imgItem {
    padding: 5px;
    box-sizing: border-box;
    width: 180px;
    height: 235px;
    margin-right: 15px;
    margin-bottom: 10px;
    background-color: #f2f2f2;
    .pic{
      width: 170px;
      height: 170px;
      margin-bottom: 4px;
    }
    .imgName {
      max-width: 100%;
      white-space: wrap;
      align-items: center;
    }
    .boxRadio{
      display: flex;
      align-items:center;
      height:30px;
      width:100%;
    }
  }
}
::v-deep .boxRadio .el-radio{
  width: 100% !important;
}
.emptyBox{
  width: 100%;
  display: flex;
  justify-content: center;
}
.radioBox{
  width: 100% !important;
}
</style>
