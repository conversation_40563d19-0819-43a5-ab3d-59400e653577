<template>
  <div class="app-container">
    <!-- 搜索条件 -->
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="dateRangeOptions"
        />
      </el-form-item>

      <el-form-item prop="advertiserId">
        <el-input
          v-model="queryParams.advertiserId"
          placeholder="账户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item prop="advertiserName">
        <el-input
          v-model="queryParams.advertiserName"
          placeholder="账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item prop="projectId">
        <el-input v-model="queryParams.projectId" placeholder="项目ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="项目名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item prop="promotionId">
        <el-input v-model="queryParams.promotionId" placeholder="广告ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item prop="promotionName">
        <el-input
          v-model="queryParams.promotionName"
          placeholder="广告名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <DeptTreeSelector v-model="queryParams.deptIds" :default-ids="queryParams.deptIds" multiple placeholder="请选择部门" />

      <BusinessSelector v-model="queryParams.business" />

      <el-form-item prop="createBy">
        <el-input v-model="queryParams.createBy" placeholder="负责人" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-date-range.sync="dateRange"
          storage-key="jl_plan_statistics"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb-4">
      <!-- <el-col :span="1.5">
        <el-button v-hasPermi="['promotion:ocean:export']" type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport">导出</el-button>
      </el-col> -->
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <!-- 数据表格 -->
    <div>
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="statisticsList"
        :summary-method="getSummaries"
        show-summary
        row-key="cdpPromotionId"
        stripe
        border
        @header-dragend="handleHeaderDragend"
      >
        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <!-- 数值类型列 -->
            <el-table-column
              :key="`col-${i}`"
              :label="c.label"
              :prop="c.prop"
              :width="c.width"
              :align="c.align || 'center'"
              :sortable="c.sortable"
            >
              <template slot-scope="scope">
                <span v-if="c.render">{{ c.render(scope.row[c.prop], scope.row) }}</span>
                <BaseInfoCell v-else-if="c.info" :id="scope.row[c.info.id]" :name="scope.row[c.info.name]" :label="c.info.label" :sub-label="c.info.subLabel" />
                <TableColumnSet v-else-if="c.set" :set="c.set" :row="scope.row" :label-width="c.labelWidth" />
                <span v-else>{{ scope.row[c.prop] ?? '-' }}</span>
              </template>
            </el-table-column>
          </template>
        </template>
      </el-table>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import useJlStatistics from '../hooks/useJlStatistics'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import DeptTreeSelector from '@/components/DeptTreeSelect/DeptTreeSelector.vue'
import BusinessSelector from '@/components/BusinessSelector/index.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import RightToolbar from '@/components/RightToolbar/index.vue'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import useColumns from '@/hooks/useColumns'
import { planColumns } from '../config/config'

// Props
const props = defineProps({
  initialParams: {
    type: Object,
    default: () => ({})
  }
})

// Table ref
const tableRef = ref(null)

// Columns
const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({
  defaultColumns: planColumns,
  tableRef,
  name: 'jl_plan_statistics'
})

// Statistics
const {
  loading,
  statisticsList,
  total,
  showSearch,
  queryParams,
  dateRange,
  dateRangeOptions,
  getList,
  handleQuery,
  resetQuery,
  handlePagination,
  getSummaries
} = useJlStatistics('promotionId', props.initialParams)

// 初始化数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
</style>
