<template>
  <div class="app-container">
    <!-- 搜索条件 -->
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="dateRangeOptions"
          @change="handleQuery"
        />
      </el-form-item>

      <el-form-item prop="advertiserId">
        <el-input
          v-model="queryParams.advertiserId"
          placeholder="账户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item prop="advertiserName">
        <el-input
          v-model="queryParams.advertiserName"
          placeholder="账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <DeptTreeSelector v-model="queryParams.deptIds" :default-ids="queryParams.deptIds" multiple />

      <BusinessSelector v-model="queryParams.business" />

      <el-form-item prop="createBy">
        <el-input v-model="queryParams.createBy" placeholder="负责人" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-date-range.sync="dateRange"
          storage-key="jl_account_statistics"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb-4">
      <!-- <el-col :span="1.5">
        <el-button v-hasPermi="['promotion:ocean:export']" type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport">导出</el-button>
      </el-col> -->
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <!-- 数据表格 -->
    <div>
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="statisticsList"
        :summary-method="getSummaries"
        show-summary
        row-key="advertiserId"
        stripe
        border
        @header-dragend="handleHeaderDragend"
      >
        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <!-- 基础信息列 -->
            <el-table-column
              v-if="c.prop === 'advertiserName'"
              :key="i"
              label="账户信息"
              align="left"
              :width="c.width || 200"
            >
              <template slot-scope="scope">
                <BaseInfoCell :id="scope.row.advertiserId" :name="scope.row.advertiserName" />
                <div class="drill-down-actions" style="margin-top: 8px;">
                  <el-button type="text" size="mini" @click="drillDown(scope.row, 'project')">查看项目</el-button>
                  <el-button type="text" size="mini" @click="drillDown(scope.row, 'plan')">查看广告</el-button>
                </div>
              </template>
            </el-table-column>

            <!-- 数值类型列 -->
            <el-table-column
              v-else
              :key="`col-${i}`"
              :label="c.label"
              :prop="c.prop"
              :width="c.width"
              :align="c.align || 'center'"
              :sortable="c.sortable"
            >
              <template slot-scope="scope">
                <span v-if="c.render">{{ c.render(scope.row[c.prop], scope.row) }}</span>
                <BaseInfoCell v-else-if="c.info" :id="scope.row[c.info.id]" :name="scope.row[c.info.name]" :label="c.info.label" :sub-label="c.info.subLabel" />
                <TableColumnSet v-else-if="c.set" :set="c.set" :row="scope.row" :label-width="c.labelWidth" />
                <span v-else>{{ scope.row[c.prop] ?? '-' }}</span>
              </template>
            </el-table-column>
          </template>
        </template>
      </el-table>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import useJlStatistics from '../hooks/useJlStatistics'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import DeptTreeSelector from '@/components/DeptTreeSelect/DeptTreeSelector.vue'
import BusinessSelector from '@/components/BusinessSelector/index.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import RightToolbar from '@/components/RightToolbar/index.vue'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import useColumns from '@/hooks/useColumns'
import { accountColumns } from '../config/config'

// Props
const props = defineProps({
  initialParams: {
    type: Object,
    default: () => ({})
  }
})

// Table ref
const tableRef = ref(null)

// Columns
const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({
  defaultColumns: accountColumns,
  tableRef,
  name: 'jl_account_statistics'
})

// Statistics
const {
  loading,
  statisticsList,
  total,
  showSearch,
  queryParams,
  dateRange,
  dateRangeOptions,
  getList,
  handleQuery,
  resetQuery,
  handlePagination,
  /* handleExport, */
  getSummaries,
  drillDown
} = useJlStatistics('advertiserId', props.initialParams)

// 初始化数据
onMounted(() => {
  getList()
})
</script>
