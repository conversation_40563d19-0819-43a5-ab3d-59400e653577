
import { applyRenderersToColumns } from '@/utils/formatters'
const baseColumns = [

  { prop: 'deptInfo', label: '公司信息', width: 160, labelWidth: 40,
    set: [
      { label: `公司`, prop: 'firstDeptName' },
      { label: `部门`, prop: 'deptName' }
    ]
  },
  { label: `用户信息`, prop: 'UserInfo', width: '200', labelWidth: 50,
    set: [
      { label: `昵称`, prop: 'createByNickName' },
      { label: `商务`, prop: 'businessName' }
    ]
  },
  { label: '账户余额', prop: 'fund', width: 120, sortable: true },
  { label: '消耗', prop: 'statCost', width: 120, sortable: true },
  { label: '展示数', prop: 'showCnt', width: 120, sortable: true },
  { label: '平均千次展现费用', prop: 'cpmPlatform', width: 150, sortable: true },
  { label: '点击数', prop: 'clickCnt', width: 120, sortable: true },
  { label: '点击率', prop: 'ctr', width: 120, sortable: true },
  { label: '平均点击单价', prop: 'cpcPlatform', width: 130, sortable: true },
  { prop: 'conversionInfo', label: '转化信息', width: 180, labelWidth: 60,
    set: [
      { label: '转化数', prop: 'convertCnt' },
      { label: '转化成本', prop: 'conversionCost' },
      { label: '转化率', prop: 'conversionRate' }
    ]
  },
  { prop: 'deepConversionInfo', label: '深度转化信息', width: 180, labelWidth: 60,
    set: [
      { label: '转化数', prop: 'deepConvertCnt' },
      { label: '转化率', prop: 'deepConvertRate' },
      { label: '转化成本', prop: 'deepConvertCost' }
    ]
  },
  { prop: 'realTotalConvertCost', label: '实际转化成本', width: 180, labelWidth: 40,
    set: [
      { label: '提交', prop: 'realTotalSubConvertCost' },
      { label: '支付', prop: 'realTotalPayConvertCost' },
      { label: '加微', prop: 'realTotalAddConvertCost' }
    ]
  },
  { label: '播放量', prop: 'totalPlay', width: 100 },
  { label: '3秒播放数', prop: 'playDuration3s', width: 110 },
  { prop: 'validPlayInfo', label: '有效播放信息', width: 160, labelWidth: 60,
    set: [
      { label: '播放数', prop: 'validPlay' },
      { label: '播放率', prop: 'validPlayRate' },
      { label: '播放成本', prop: 'validPlayCost' }
    ]
  },
  { label: '千次有效播放成本', prop: 'validPlayCostOfMille', width: 140 },
  { label: '25%进度播放数', prop: 'play25FeedBreak', width: 140 },
  { label: '50%进度播放数', prop: 'play50FeedBreak', width: 140 },
  { label: '75%进度播放数', prop: 'play75FeedBreak', width: 140 },
  { label: '99%进度播放数', prop: 'play99FeedBreak', width: 140 },
  { label: '完播率', prop: 'playOverRate', width: 100 },
  { label: '平均单次播放时长', prop: 'averagePlayTimePerPlay', width: 140 },
  { label: '点赞数', prop: 'dyLike', width: 100 },
  { label: '评论量', prop: 'dyComment', width: 100 },
  { label: '分享量', prop: 'dyShare', width: 100 },
  { label: '不感兴趣数', prop: 'adDislikeCnt', width: 120 },
  { label: '举报数', prop: 'adReportCnt', width: 100 },
  { label: '表单展示数', prop: 'formShowCount', width: 120 },
  { prop: 'formSubInfo', label: '表单提交信息', width: 160, labelWidth: 60,
    set: [
      { label: '提交数', prop: 'formSubCount' },
      { label: '回传数', prop: 'formSubConvertCount' },
      { label: '扣回传数', prop: 'formSubDeductionCount' }
    ]
  },
  { prop: 'formAddInfo', label: '表单加微信息', width: 160, labelWidth: 60,
    set: [
      { label: '加微数', prop: 'formAddCount' },
      { label: '回传数', prop: 'formAddConvertCount' },
      { label: '扣回传数', prop: 'formAddDeductionCount' }
    ]
  },
  { prop: 'formPayInfo', label: '支付信息(含退款)', width: 180, labelWidth: 60,
    set: [
      { label: '支付数', prop: 'formPayCount' },
      { label: '支付金额', prop: 'formPayAmount' },
      { label: '回传数', prop: 'formPayConvertCount' },
      { label: '扣回传数', prop: 'formPayDeductionCount' }
    ]
  },
  { prop: 'formPayRealInfo', label: '支付信息(不含退款)', width: 160, labelWidth: 60,
    set: [
      { label: '支付数', prop: 'formPayRealCount' },
      { label: '支付金额', prop: 'formPayRealAmount' }
    ]
  },
  { prop: 'formRefundInfo', label: '表单退款信息', width: 180, labelWidth: 60,
    set: [
      { label: '退款数', prop: 'formRefundCount' },
      { label: '退款金额', prop: 'formRefundAmount' },
      { label: '回传数', prop: 'formRefundConvertCount' },
      { label: '扣回传数', prop: 'formRefundDeductionCount' }
    ]
  }

]

const defaultColumns = applyRenderersToColumns(baseColumns)

export const accountColumns = [
  { label: '账户信息', prop: 'advertiserInfo', width: 240, align: 'left',
    info: {
      id: 'advertiserId',
      name: 'advertiserName'
    }
  },
  ...defaultColumns
]

export const projectColumns = [
  { label: '账户信息', prop: 'advertiserInfo', width: 240, align: 'left',
    info: {
      id: 'advertiserId',
      name: 'advertiserName'
    }
  },
  { label: '项目信息', prop: 'cdpProjectInfo', width: 240, align: 'left',
    info: {
      id: 'cdpProjectId',
      name: 'cdpProjectName'
    }
  },
  ...defaultColumns
]
export const planColumns = [
  { label: '账户信息', prop: 'advertiserInfo', width: 240, align: 'left',
    info: {
      id: 'advertiserId',
      name: 'advertiserName'
    }
  },
  { label: '项目信息', prop: 'cdpProjectInfo', width: 240, align: 'left',
    info: {
      id: 'cdpProjectId',
      name: 'cdpProjectName'
    }
  },
  { label: '广告信息', prop: 'cdpPromotionInfo', width: 240, align: 'left',
    info: {
      id: 'cdpPromotionId',
      name: 'cdpPromotionName'
    }
  },
  ...defaultColumns
]
