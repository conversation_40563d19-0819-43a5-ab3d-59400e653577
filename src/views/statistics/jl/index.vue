<template>
  <!-- 巨量数据报表 -->
  <el-tabs v-model="activeName" class="tabs_style" style="margin-top: 0px" @tab-click="handleTabClick">
    <el-tab-pane name="account" lazy>
      <template slot="label">
        <div style="padding: 0 60px">
          <svg-icon icon-class="table" />账户报表
        </div>
      </template>
      <AccountStatistics :initial-params="initialParams" />
    </el-tab-pane>
    <el-tab-pane name="project" lazy>
      <template slot="label">
        <div style="padding: 0 60px">
          <svg-icon icon-class="form" />项目报表
        </div>
      </template>
      <ProjectStatistics :initial-params="initialParams" />
    </el-tab-pane>
    <el-tab-pane name="plan" lazy>
      <template slot="label">
        <div style="padding: 0 60px">
          <svg-icon icon-class="cascader" />广告报表
        </div>
      </template>
      <PlanStatistics :initial-params="initialParams" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import AccountStatistics from './components/AccountStatistics.vue'
import ProjectStatistics from './components/ProjectStatistics.vue'
import PlanStatistics from './components/PlanStatistics.vue'

export default {
  name: 'JlStatistics',
  components: {
    AccountStatistics,
    ProjectStatistics,
    PlanStatistics
  },
  data() {
    return {
      activeName: 'account',
      loading: false,
      initialParams: {},
      pageList: [
        {
          key: 'account',
          label: '账户报表',
          icon: 'table'
        },
        {
          key: 'project',
          label: '项目报表',
          icon: 'form'
        },
        {
          key: 'plan',
          label: '广告报表',
          icon: 'cascader'
        }
      ],
      firstIn: {
        account: false,
        project: false,
        plan: false
      }
    }
  },
  created() {
    this.setTabs()
  },
  activated() {
    this.setTabs()
  },
  methods: {
    setTabs() {
      // 根据路由参数设置默认激活的tab
      const { tab, ...otherParams } = this.$route.query
      if (tab && ['account', 'project', 'plan'].includes(tab)) {
        this.activeName = tab
      }

      // 设置初始查询参数
      this.initialParams = { ...otherParams }
    },
    handleTabClick(tab) {
      const tabName = tab.name
      // 更新路由参数
      this.$router.replace({
        query: {
          ...this.$route.query,
          tab: tabName
        }
      })

      // 标记该tab已被访问
      this.firstIn[tabName] = true
    }
  }
}
</script>

<style scoped>
/* ::v-deep .el-tabs__header {
  margin: 0 0 20px 0;
} */

::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

::v-deep .el-tabs__item {
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 500;
}

::v-deep .el-tabs__item.is-active {
  color: #409EFF;
}

::v-deep .el-tabs__active-bar {
  background-color: #409EFF;
}
</style>
