<template>
  <div class="adaption-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" class="search-form" size="small" :inline="true" label-width="68px">
      <el-form-item prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <UserSearchInput
        :create-by.sync="queryParams.createBy"
        @query="handleQuery"
      />
      <BusinessSelector :business.sync="queryParams.business" @query="handleQuery" />
      <DeptTreeSelector :dept-ids.sync="queryParams.deptIds" />
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="dateRange"
          :popper-class="device === 'mobile' ? 'mobile-date-picker' : ''"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :picker-options="device === 'mobile' ? {} : dateRangePickerOptions"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-dateRange.sync="dateRange"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['wx:wxLink:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['wx:wxLink:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <div class="table-wrapper">
      <el-table
        v-loading="loading"
        v-bind="tableHeight"
        :data="wxLinkList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="短链接" align="center" prop="schemeLink" width="165">
          <template #default="{row}">
            {{ row.schemeLink }}
            <ClipboardButton :value="row.schemeLink" />
          </template>
        </el-table-column>
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="描述" align="center" prop="describes" show-overflow-tooltip />
        <el-table-column label="头像" align="center" prop="avatar">
          <template #default="{row}">
            <image-preview :src="row.avatar" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="二维码地址" align="center" prop="qrCode">
          <template #default="{row}">
            <image-preview :src="row.qrCode" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="小程序或公众号ID" align="center" prop="appId" />
        <el-table-column label="小程序密钥" align="center" prop="secret" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '有效' : '失效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="用户信息" align="center" prop="deptName" min-width="160px">
          <template #default="{row}">
            <TableColumnSet
              :set="[{ label: `昵称`, prop: 'nickName' },
                     { label: `负责人`, prop: 'createBy' }]"
              :row="row"
              label-width="50"
            />
          </template>
        </el-table-column>
        <el-table-column label="公司部门信息" align="center" prop="deptName" min-width="160px">
          <template #default="{row}">
            <TableColumnSet
              :set="[{ label: `公司`, prop: 'firstDeptName' },
                     { label: `部门`, prop: 'deptName' }]"
              :row="row"
              label-width="40"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="checkPermi(['promotion:config:duoduo'])" label="商务负责人" align="center" min-width="160px" prop="business">
          <template #default="{row}">
            <TableColumnSet
              :set="[{ label: `昵称`, prop: 'personInCharge' },
                     { label: `商务`, prop: 'business' }]"
              :row="row"
              label-width="40"
            />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
        <el-table-column label="创建时间" align="center" prop="createTime" width="100" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['wx:wxLink:edit']"
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="handleUpdate(scope.row)"
            >刷新</el-button>
            <el-button
              v-hasPermi="['wx:wxLink:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改短链信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="88px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="描述" prop="describes">
          <el-input v-model="form.describes" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <div class="flex">
            <el-input v-model="form.avatar" class="flex1 mr10" placeholder="请输入头像" />
            <FileResSelector v-model="form.avatar" width="80" height="30" />
          </div>
        </el-form-item>
        <el-form-item label="二维码地址" prop="qrCode">
          <div class="flex">
            <el-input v-model="form.qrCode" class="flex1 mr10" placeholder="请输入二维码地址" />
            <FileResSelector v-model="form.qrCode" width="80" height="30" />
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWxLink, getWxLink, delWxLink, addWxLink, updateWxLink } from '@/api/wx/wxLink'
import { mapGetters } from 'vuex'
import FileResSelector from '@/components/FileResSelector/index.vue'
import ClipboardButton from '@/components/ClipboardButton/index.vue'
import BusinessSelector from '@/components/BusinessSelector/index.vue'
import { dateRangePickerOptions } from '@/config'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import DeptTreeSelector from '@/components/DeptTreeSelect/DeptTreeSelector.vue'
import { checkPermi } from '@/utils/permission'
import SavedSearches from '@/components/SavedSearches/index.vue'

export default {
  name: 'WxLink',
  components: { SavedSearches, DeptTreeSelector, TableColumnSet, BusinessSelector, ClipboardButton, FileResSelector },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短链信息表格数据
      wxLinkList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        schemeLink: null,
        name: null,
        describes: null,
        avatar: null,
        qrCode: null,
        appId: null,
        secret: null,
        status: null,
        deleted: null,
        deptIds: []
      },
      // 表单参数
      dateRange: [],
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  computed: {
    dateRangePickerOptions() {
      return dateRangePickerOptions
    },
    ...mapGetters([
      'tableHeight', 'device'
    ])
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    /** 查询短链信息列表 */
    getList() {
      this.loading = true
      listWxLink(
        this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.wxLinkList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        schemeLink: null,
        name: null,
        describes: null,
        avatar: null,
        qrCode: null,
        appId: null,
        secret: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        deleted: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加短链信息'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getWxLink(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改短链信息'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWxLink(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addWxLink(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除短链信息编号为"' + ids + '"的数据项？').then(function() {
        return delWxLink(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/wxLink/export', {
        ...this.queryParams
      }, `wxLink_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
