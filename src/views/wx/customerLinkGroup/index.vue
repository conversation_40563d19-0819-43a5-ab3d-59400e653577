<template>
  <div>
    <el-form v-show="showSearch" ref="queryForm" class="search-form" :model="queryParams" :inline="true" label-width="68px">
      <el-form-item prop="groupName">
        <el-input
          v-model="queryParams.groupName"
          placeholder="链接组名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="groupType">
        <el-select v-model="queryParams.groupType" placeholder="链接类型" clearable size="small" @change="handleQuery">
          <el-option v-for="item in groupTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['wx:customerLinkGroup:save']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col v-if="!isSelector" :span="1.5">
        <el-button
          v-hasPermi="['wx:customerLinkGroup:delete']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleBatchDelete"
        >批量删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="groupList" border stripe @selection-change="handleSelectionChange">
      <el-table-column v-if="!isSelector" type="selection" width="55" align="center" />
      <el-table-column label="链接组名称" align="center" prop="groupName">
        <template slot-scope="scope">
          <div class="cell-edit">
            <span>{{ scope.row.groupName }}</span>
            <el-button
              v-hasPermi="['wx:customerLinkGroup:save']"
              type="text"
              icon="el-icon-edit"
              class="edit-icon"
              @click="handleUpdate(scope.row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="获客链接数量" align="center" prop="linkCount" width="110" />
      <el-table-column v-if="!isSelector" label="创建成功数量" align="center" prop="successCount" width="110" />
      <el-table-column v-if="!isSelector" label="创建失败数量" align="center" prop="failCount" width="110" />
      <el-table-column v-if="!isSelector" label="待创建数量" align="center" prop="pendingCount" width="110" />
      <el-table-column label="状态" align="center" prop="status" width="110">
        <template slot-scope="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createByName" />
      <el-table-column label="备注" align="center" prop="remark">
        <template slot-scope="scope">
          <div class="cell-edit">
            <span>{{ scope.row.remark || '-' }}</span>
            <el-button
              v-hasPermi="['wx:customerLinkGroup:save']"
              type="text"
              icon="el-icon-edit"
              class="edit-icon"
              @click="handleUpdate(scope.row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            v-if="isSelector"
            size="mini"
            type="text"
            icon="el-icon-circle-check"
            @click="selectGroup(scope.row)"
          >选择</el-button>
          <el-button
            v-hasPermi="['wx:customerLinkGroup:query']"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            v-if="!isSelector"
            v-hasPermi="['wx:customerLinkGroup:delete']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            class="color-danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加获客链接组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="groupName">
          <el-input v-model="form.groupName" placeholder="请输入链接组名称" />
        </el-form-item>
        <el-form-item label="授权企业" prop="corpId">
          <el-select v-model="form.corpId" filterable style="width: 370px" @change="handleCorpChange">
            <el-option
              v-for="(item, i) in authCorpList"
              :key="i"
              :label="item.corpName"
              :value="item.corpId"
              :disabled="!(item.isAppAuth && item.isCustomerAuth)"
            >
              {{ item.corpName }}
              <span v-if="!item.isAppAuth" class="text-danger"> <i class="el-icon-warning-outline" />应用未授权</span>
              <span v-if="!item.isCustomerAuth" class="text-danger"><i class="el-icon-warning-outline" />获客助手未授权</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="链接类型" prop="groupType">
          <el-radio-group v-model="form.groupType" :disabled="!!form.id">
            <el-radio-button :label="1">获客助手</el-radio-button>
            <el-radio-button :label="2">渠道活码
              <el-tooltip content="用户通过小程序内二维码加粉，无法精准回传" placement="top">
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="接粉账号" prop="items">
          <div class="user-list">
            <div v-for="(item, index) in form.items" :key="index" class="user-item">
              <template v-if="item.userName">
                <el-tag class="flex1">{{ item.userName }}</el-tag>
                <el-input-number
                  v-model="item.maxAddCount"
                  :min="1"
                  size="mini"
                  :controls="false"
                  label="每天最大接粉数量"
                  style="width: 90px"
                />
                <el-button type="danger" icon="el-icon-delete" size="mini" @click="removeUser(index)" />
              </template>
            </div>
          </div>
          <el-button type="primary" size="small" @click="openUserSelector">选择用户</el-button>
          <span>已选{{ form.items.length }}个用户</span>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 编辑获客链接组对话框 -->
    <el-dialog title="编辑获客链接组" :visible.sync="editOpen" width="500px" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="80px">
        <el-form-item label="名称" prop="groupName">
          <el-input v-model="editForm.groupName" placeholder="请输入链接组名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="editForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog :title="`链接组详情-${detailData.groupName}`" :visible.sync="detailOpen" width="1300px" append-to-body>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['wx:customerLinkGroup:save']"
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAddItem"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['wx:customerLinkGroup:save']"
            type="primary"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="detailMultiple"
            @click="handleBatchUpdateItem"
          >批量编辑</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['wx:customerLinkGroup:delete']"
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="detailMultiple"
            @click="handleBatchDeleteItem"
          >批量删除</el-button>
        </el-col>
      </el-row>
      <el-table v-loading="loading" :data="detailData.items" max-height="500" @selection-change="handleDetailSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="用户名称" align="center" prop="userName" />
        <el-table-column label="用户状态" align="center" prop="userStatus" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.userStatus === 1 ? 'danger' : 'success'">
              {{ scope.row.userStatus === 1 ? '无效' : '有效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="互通许可" align="center" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.licenseActivate === 1 ? 'success' : 'info'">
              {{ scope.row.licenseActivate === 1 ? '已激活' : '未激活' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="激活时间" align="center" prop="activeTime" width="160">
          <template slot-scope="scope">
            {{ scope.row.activeTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="过期时间" align="center" prop="expireTime" width="160">
          <template slot-scope="scope">
            {{ scope.row.expireTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="今日加粉数量" align="center" prop="todayCount" width="110" />
        <el-table-column label="每天最大接粉数量" align="center" prop="maxAddCount" width="140">
          <template slot-scope="scope">
            <div v-if="scope.row.isEdit" class="cell-edit">
              <el-input-number
                v-model="scope.row.maxAddCount"
                :min="1"
                :controls="false"
                size="mini"
                style="width: 90px"
              />
              <el-button
                type="text"
                icon="el-icon-check"
                class="edit-icon"
                style="display: inline-block;"
                @click="handleSaveItem(scope.row)"
              />
              <el-button
                type="text"
                icon="el-icon-close"
                class="edit-icon"
                style="display: inline-block;"
                @click="handleCancelItem(scope.row)"
              />
            </div>
            <div v-else class="cell-edit">
              <span>{{ scope.row.maxAddCount }}</span>
              <el-button
                v-hasPermi="['wx:customerLinkGroup:save']"
                type="text"
                icon="el-icon-edit"
                class="edit-icon"
                @click="handleEditItem(scope.row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template slot-scope="scope">
            <el-tooltip
              v-if="scope.row.failMsg"
              :content="scope.row.failMsg"
              placement="top"
            >
              <dict-tag :options="itemStatusOptions" :value="scope.row.status" />
            </el-tooltip>
            <dict-tag v-else :options="itemStatusOptions" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['wx:customerLinkGroup:delete']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              class="color-danger"
              @click="handleDeleteItem(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="detailData.total>0"
        :total="detailData.total"
        :page.sync="detailData.pageNum"
        :limit.sync="detailData.pageSize"
        @pagination="handleDetailPagination"
      />
    </el-dialog>

    <!-- 新增获客链接组明细对话框 -->
    <el-dialog title="新增获客链接" :visible.sync="itemOpen" width="500px" append-to-body>
      <el-form ref="itemForm" :model="itemForm" :rules="itemRules" label-width="80px">
        <el-form-item label="接粉账号" prop="items">
          <div class="user-list">
            <div v-for="(item, index) in itemForm.items" :key="index" class="user-item">
              <template v-if="item.userName">
                <el-tag class="flex1">{{ item.userName }}</el-tag>
                <el-input-number
                  v-model="item.maxAddCount"
                  :min="1"
                  size="mini"
                  :controls="false"
                  label="每天最大接粉数量"
                  style="width: 90px"
                />
                <el-button type="danger" icon="el-icon-delete" size="mini" @click="removeItemUser(index)" />
              </template>
            </div>
          </div>
          <el-button type="primary" size="small" @click="openItemUserSelector">选择用户</el-button>
          <span>已选{{ itemForm.items.length }}个用户</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitItemForm">确 定</el-button>
        <el-button @click="cancelItem">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量编辑获客链接组明细对话框 -->
    <el-dialog title="批量编辑每天最大接粉数量" :visible.sync="batchItemEditOpen" width="500px" append-to-body>
      <el-form ref="batchItemEditForm" :model="batchItemEditForm" :rules="batchItemEditRules" label-width="120px">
        <el-form-item label="每天最大接粉数量" prop="maxAddCount">
          <el-input-number
            v-model="batchItemEditForm.maxAddCount"
            :min="1"
            :controls="false"
            style="width: 200px"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchItemEditForm">确 定</el-button>
        <el-button @click="cancelBatchItemEdit">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户选择器弹窗 -->
    <customer-link-user-selector
      ref="userSelector"
      :corp-id="selectorCorpId"
      @confirm="handleUserSelect"
    />
    <!-- 用户选择器弹窗 -->
    <customer-link-user-selector
      ref="itemUserSelector"
      :corp-id="selectorCorpId"
      @confirm="handleItemUserSelect"
    />
  </div>
</template>

<script>
import { listCustomerLinkGroup, addCustomerLinkGroup, getCustomerLinkGroupDetail, deleteCustomerLinkGroup, deleteCustomerLinkGroupItem, addCustomerLinkGroupItem, editCustomerLinkGroup, editCustomerLinkGroupItem } from '@/api/wx/customerLinkGroup'
import { listAuthCorp } from '@/api/wx/authCorp'
import CustomerLinkUserSelector from '@/components/CustomerLinkUserSelector/index.vue'

export default {
  name: 'CustomerLinkGroup',
  components: {
    CustomerLinkUserSelector
  },
  props: {
    isSelector: {
      type: Boolean,
      default: false
    },
    formType: {
      type: Number,
      default: 1
    }
  },
  emits: ['select'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 获客链接组表格数据
      groupList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示编辑弹出层
      editOpen: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示新增获客链接组明细弹出层
      itemOpen: false,
      // 是否显示批量编辑获客链接组明细弹出层
      batchItemEditOpen: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 详情选中数组
      detailIds: [],
      // 详情非单个禁用
      detailSingle: true,
      // 详情非多个禁用
      detailMultiple: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: undefined,
        status: undefined
      },
      // 授权企业列表
      authCorpList: [],
      // 用户选择器企业ID
      selectorCorpId: undefined,
      // 表单参数
      form: {
        groupName: undefined,
        corpId: undefined,
        corpName: undefined,
        items: [],
        remark: undefined,
        groupType: 1
      },
      groupTypeOptions: [
        { label: '获客助手', value: 1 },
        { label: '渠道活码', value: 2 }
      ],
      // 表单校验
      rules: {
        groupName: [
          { required: true, message: '链接组名称不能为空', trigger: 'blur' }
        ],
        corpId: [
          { required: true, message: '请选择授权企业', trigger: 'blur' }
        ],
        items: [
          { required: true, message: '请至少添加一个接粉账号', trigger: 'change' }
        ]
      },
      // 状态数据字典
      statusOptions: [
        { value: '0', label: '创建中', raw: { listClass: 'primary' }},
        { value: '1', label: '创建成功', raw: { listClass: 'success' }},
        { value: '2', label: '创建失败', raw: { listClass: 'danger' }},
        { value: '3', label: '部分成功', raw: { listClass: 'warning' }}
      ],
      // 明细状态数据字典
      itemStatusOptions: [
        { value: '0', label: '未创建', raw: { listClass: 'info' }},
        { value: '1', label: '创建成功', raw: { listClass: 'success' }},
        { value: '2', label: '创建失败', raw: { listClass: 'danger' }}
      ],
      // 用户列表
      userList: [],
      // 详情数据
      detailData: {
        groupName: '',
        items: [],
        total: 0,
        pageNum: 1,
        pageSize: 10
      },
      // 获客链接组明细表单参数
      itemForm: {
        groupId: undefined,
        items: []
      },
      // 获客链接组明细表单校验
      itemRules: {
        items: [
          { required: true, message: '请至少添加一个接粉账号', trigger: 'change' }
        ]
      },
      // 编辑表单参数
      editForm: {
        id: undefined,
        groupName: undefined,
        remark: undefined
      },
      // 编辑表单校验
      editRules: {
        groupName: [
          { required: true, message: '链接组名称不能为空', trigger: 'blur' }
        ]
      },
      // 批量编辑获客链接组明细表单参数
      batchItemEditForm: {
        maxAddCount: 100
      },
      // 批量编辑获客链接组明细表单校验
      batchItemEditRules: {
        maxAddCount: [
          { required: true, message: '每天最大接粉数量不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getAuthCorpList()
  },
  methods: {
    /** 查询获客链接组列表 */
    getList() {
      this.loading = true
      const query = {
        ...this.queryParams
      }
      if (this.isSelector) {
        query.groupType = this.formType
      }
      listCustomerLinkGroup(query).then(response => {
        this.groupList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 获取授权企业列表 */
    getAuthCorpList() {
      listAuthCorp({
        pageNum: 1,
        pageSize: 9999
      }).then(response => {
        this.authCorpList = response.rows
      })
    },
    /** 处理授权企业变更 */
    handleCorpChange(corpId) {
      const corp = this.authCorpList.find(item => item.corpId === corpId)
      if (corp) {
        this.form.corpName = corp.corpName
      }
      this.selectorCorpId = corpId
      // 切换企业时清空已选用户
      this.form.items = []
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        groupName: undefined,
        corpId: undefined,
        corpName: undefined,
        items: [],
        remark: undefined,
        groupType: 1
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加获客链接组'
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.loading = true
      this.selectorCorpId = row.corpId
      this.detailData.groupName = row.groupName
      this.detailData.groupId = row.id
      this.detailData.pageNum = 1
      this.detailData.pageSize = 10
      this.getDetailList(row.id)
    },
    /** 获取详情列表 */
    getDetailList(groupId) {
      this.loading = true
      this.detailData.groupId = groupId
      getCustomerLinkGroupDetail({
        groupId: groupId,
        pageNum: this.detailData.pageNum,
        pageSize: this.detailData.pageSize
      }).then(response => {
        this.detailData.items = response.rows.map(item => {
          const user = item.users[0]
          return {
            ...item,
            ...user,
            isEdit: false,
            oldMaxAddCount: item.maxAddCount
          }
        })
        this.detailData.total = response.total
        this.detailOpen = true
        this.loading = false
      })
    },
    /** 详情分页操作 */
    handleDetailPagination(pagination) {
      this.detailData.pageNum = pagination.page
      this.detailData.pageSize = pagination.limit
      this.getDetailList(this.detailData.groupId)
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.editForm = {
        id: row.id,
        groupName: row.groupName,
        remark: row.remark
      }
      this.editOpen = true
    },
    /** 提交编辑表单 */
    submitEditForm() {
      this.$refs['editForm'].validate(valid => {
        if (valid) {
          editCustomerLinkGroup(this.editForm).then(response => {
            this.$modal.msgSuccess('修改成功')
            this.editOpen = false
            this.getList()
          })
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          addCustomerLinkGroup(this.form).then(response => {
            this.$modal.msgSuccess('新增成功')
            this.open = false
            this.getList()
          })
        }
      })
    },
    /** 取消编辑按钮 */
    cancelEdit() {
      this.editOpen = false
      this.resetEdit()
    },
    /** 重置编辑表单 */
    resetEdit() {
      this.editForm = {
        id: undefined,
        groupName: undefined,
        remark: undefined
      }
      this.resetForm('editForm')
    },
    /** 打开用户选择器 */
    openUserSelector() {
      if (!this.form.corpId) {
        this.$message.warning('请先选择授权企业')
        return
      }
      const selectedUsers = this.form.items
        .filter(item => item.userId)
        .map(item => ({
          userId: item.userId,
          userName: item.userName
        }))
      this.$refs.userSelector.open(selectedUsers)
    },
    /** 处理用户选择确认 */
    handleUserSelect(users) {
      // 移除未选中的用户
      this.form.items = this.form.items.filter(item =>
        users.some(user => user.userId === item.userId)
      )
      // 添加新选中的用户
      users.forEach(user => {
        if (!this.form.items.some(item => item.userId === user.userId)) {
          this.form.items.push({
            userId: user.userId,
            userName: user.userName,
            maxAddCount: 100
          })
        }
      })
    },
    /** 移除用户 */
    removeUser(index) {
      this.form.items.splice(index, 1)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除获客链接组"' + row.groupName + '"？').then(() => {
        return deleteCustomerLinkGroup([row.id])
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 删除详情列表中的项目 */
    handleDeleteItem(row) {
      this.$modal.confirm('是否确认删除该获客链接？').then(() => {
        return deleteCustomerLinkGroupItem([row.id], this.detailData.groupId)
      }).then(() => {
        this.getDetailList(this.detailData.groupId)
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 详情多选框选中数据 */
    handleDetailSelectionChange(selection) {
      this.detailIds = selection.map(item => item.id)
      this.detailSingle = selection.length !== 1
      this.detailMultiple = !selection.length
    },
    /** 批量删除按钮操作 */
    handleBatchDelete() {
      const groupNames = this.groupList
        .filter(item => this.ids.includes(item.id))
        .map(item => item.groupName)
        .join('、')
      this.$modal.confirm('是否确认删除获客链接组"' + groupNames + '"？').then(() => {
        return deleteCustomerLinkGroup(this.ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 批量删除详情按钮操作 */
    handleBatchDeleteItem() {
      this.$modal.confirm('是否确认删除选中的获客链接？').then(() => {
        return deleteCustomerLinkGroupItem(this.detailIds, this.detailData.groupId)
      }).then(() => {
        this.getDetailList(this.detailData.groupId)
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 新增获客链接组明细按钮操作 */
    handleAddItem() {
      this.itemForm = {
        groupId: this.detailData.groupId,
        items: []
      }
      this.itemOpen = true
    },
    /** 打开获客链接组明细用户选择器 */
    openItemUserSelector() {
      const selectedUsers = this.itemForm.items
        .filter(item => item.userId)
        .map(item => ({
          userId: item.userId,
          userName: item.userName
        }))
      this.$refs.itemUserSelector.open(selectedUsers)
    },
    /** 处理获客链接组明细用户选择确认 */
    handleItemUserSelect(users) {
      // 移除未选中的用户
      this.itemForm.items = this.itemForm.items.filter(item =>
        users.some(user => user.userId === item.userId)
      )
      // 添加新选中的用户
      users.forEach(user => {
        if (!this.itemForm.items.some(item => item.userId === user.userId)) {
          this.itemForm.items.push({
            userId: user.userId,
            userName: user.userName,
            maxAddCount: 100
          })
        }
      })
    },
    /** 移除获客链接组明细用户 */
    removeItemUser(index) {
      this.itemForm.items.splice(index, 1)
    },
    /** 提交获客链接组明细表单 */
    submitItemForm() {
      this.$refs['itemForm'].validate(valid => {
        if (valid) {
          addCustomerLinkGroupItem(this.itemForm).then(response => {
            this.$modal.msgSuccess('新增成功')
            this.itemOpen = false
            this.getDetailList(this.detailData.groupId)
          })
        }
      })
    },
    /** 取消获客链接组明细按钮 */
    cancelItem() {
      this.itemOpen = false
      this.resetItem()
    },
    /** 重置获客链接组明细表单 */
    resetItem() {
      this.itemForm = {
        groupId: undefined,
        items: []
      }
      this.resetForm('itemForm')
    },
    /** 编辑获客链接组明细按钮操作 */
    handleEditItem(row) {
      this.$set(row, 'isEdit', true)
      this.$set(row, 'oldMaxAddCount', row.maxAddCount)
    },
    /** 保存获客链接组明细按钮操作 */
    handleSaveItem(row) {
      editCustomerLinkGroupItem([{
        id: row.id,
        maxAddCount: row.maxAddCount
      }], this.detailData.groupId).then(response => {
        this.$modal.msgSuccess('修改成功')
        this.$set(row, 'isEdit', false)
        delete row.oldMaxAddCount
      })
    },
    /** 取消获客链接组明细编辑按钮操作 */
    handleCancelItem(row) {
      this.$set(row, 'maxAddCount', row.oldMaxAddCount)
      this.$set(row, 'isEdit', false)
      delete row.oldMaxAddCount
    },
    /** 批量编辑获客链接组明细按钮操作 */
    handleBatchUpdateItem() {
      this.batchItemEditForm = {
        maxAddCount: 100
      }
      this.batchItemEditOpen = true
    },
    /** 提交批量编辑获客链接组明细表单 */
    submitBatchItemEditForm() {
      this.$refs['batchItemEditForm'].validate(valid => {
        if (valid) {
          const items = this.detailData.items
            .filter(item => this.detailIds.includes(item.id))
            .map(item => ({
              id: item.id,
              maxAddCount: this.batchItemEditForm.maxAddCount
            }))
          editCustomerLinkGroupItem(items, this.detailData.groupId).then(response => {
            this.$modal.msgSuccess('修改成功')
            this.batchItemEditOpen = false
            this.getDetailList(this.detailData.groupId)
          })
        }
      })
    },
    /** 取消批量编辑获客链接组明细按钮 */
    cancelBatchItemEdit() {
      this.batchItemEditOpen = false
      this.resetBatchItemEdit()
    },
    /** 重置批量编辑获客链接组明细表单 */
    resetBatchItemEdit() {
      this.batchItemEditForm = {
        maxAddCount: 100
      }
      this.resetForm('batchItemEditForm')
    },
    /** 选择获客链接组 */
    selectGroup(row) {
      this.$emit('select', row)
    }
  }
}
</script>

<style scoped>
.user-list {
  overflow: auto;
  max-height: 200px;
}
.user-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}
.text-danger {
  color: #F56C6C;
  margin-left: 5px;
}
.mt20 {
  margin-top: 20px;
}
.cell-edit {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.cell-edit:hover .edit-icon {
  display: inline-block;
}
.edit-icon {
  display: none;
  padding: 0;
  font-size: 14px;
}
</style>
