<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      class="search-form"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="企微昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="mobile">
        <el-input v-model="queryParams.mobile" placeholder="企微手机号码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="corpId">
        <el-input v-model="queryParams.corpId" placeholder="企业主体" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <UserSearchInput :create-by.sync="queryParams.createBy" @query="handleQuery" />
      <el-form-item>
        <SavedSearches v-model="queryParams" @search="handleQuery" />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">授权企微员工</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['live:corpUser:delete']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        border
        :data="corpUsersList"
        @selection-change="handleSelectionChange"
        @header-dragend="handleHeaderDragend"
      >
        <el-table-column type="selection" width="55" align="center" />
        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <!-- 头像特殊处理 -->
            <template v-if="c.prop === 'avatar'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <el-avatar v-if="scope.row.avatar" :size="40" :src="scope.row.avatar" fit="cover" />
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </template>

            <!-- 性别特殊处理 -->
            <template v-else-if="c.prop === 'gender'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <span v-if="scope.row.gender === 1">男</span>
                  <span v-else-if="scope.row.gender === 2">女</span>
                  <span v-else>未知</span>
                </template>
              </el-table-column>
            </template>

            <!-- 许可激活状态特殊处理 -->
            <template v-else-if="c.prop === 'licenseActivate'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <el-tag :type="scope.row.licenseActivate === 1 ? 'success' : 'info'">
                    {{ scope.row.licenseActivate === 1 ? '已激活' : '未激活' }}
                  </el-tag>
                </template>
              </el-table-column>
            </template>

            <!-- 个人二维码特殊处理 -->
            <template v-else-if="c.prop === 'qrCode'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <ImagePreview v-if="scope.row.qrCode" :src="scope.row.qrCode" :width="60" :height="60" />
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </template>

            <!-- 机器人ID特殊处理 -->
            <template v-else-if="c.prop === 'robotId'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <div style="display: flex; align-items: center; justify-content: center;">
                    <div v-if="scope.row.robotId" class="flex1 overflow-text">{{ scope.row.robotId }}</div>
                    <!-- 如果没有机器人ID，显示设置按钮 -->
                    <el-button
                      v-if="!scope.row.robotId"
                      v-hasPermi="['live:corpUser:modifyUserRobot']"
                      size="mini"
                      type="text"
                      icon="el-icon-plus"
                      title="设置机器人"
                      @click="handleSetRobot(scope.row)"
                    >
                      绑定

                    </el-button>
                    <!-- 如果有机器人ID，显示解绑按钮 -->
                    <el-button
                      v-else
                      v-hasPermi="['live:corpUser:modifyUserRobot']"
                      size="mini"
                      type="text"
                      icon="el-icon-close"
                      title="解绑机器人"
                      @click="handleUnbindRobot(scope.row)"
                    >解绑</el-button>
                  </div>
                </template>
              </el-table-column>
            </template>

            <!-- 其他普通列 -->
            <CustomTableColumn v-else :key="i" :data="c" :render-map="renderMap" :sortable="!!c.sortable" />
          </template>
        </template>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="90px">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-promotion"
              @click="handleCreateForwardGroup(scope.row)"
            >新建转发群</el-button>
            <el-button
              v-hasPermi="['live:corpUser:delete']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 授权企微员工对话框 -->
    <el-dialog title="授权企微员工" :visible.sync="open" width="680px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="企业主体" prop="corpId">
          <el-select
            v-model="form.corpId"
            placeholder="请选择企业微信"
            clearable
            filterable
            style="width: 100%"
            @change="handleCorpChange"
          >
            <el-option
              v-for="corp in corpOptions"
              :key="corp.corpId"
              :label="`${corp.corpName} (${corp.corpId})`"
              :value="corp.corpId"
            >
              <span style="float: left">{{ corp.corpName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ corp.corpId }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div v-if="codeImg" class="qrcode">
        <div>
          <el-image :src="codeImg" />
        </div>
        <div class="tips">扫码授权企微员工</div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button :disabled="!codeImg" type="primary" @click="submitForm">我已授权</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 机器人设置/解绑对话框 -->
    <el-dialog :title="robotDialogTitle" :visible.sync="robotDialogVisible" width="500px" append-to-body>
      <el-form ref="robotForm" :model="robotForm" :rules="robotRules" label-width="100px">
        <el-form-item label="员工姓名">
          <el-input v-model="robotForm.userName" disabled />
        </el-form-item>
        <el-form-item label="当前机器人ID">
          <el-input v-model="robotForm.currentRobotId" disabled />
        </el-form-item>
        <el-form-item label="新机器人ID" prop="robotId">
          <el-input v-model="robotForm.robotId" placeholder="请输入新的机器人ID" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRobotForm">确 定</el-button>
        <el-button @click="cancelRobot">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCorpUsers,
  getCorpUserAuthUrl,
  getAuthCorpList,
  delCorpUsers,
  modifyUserRobot,
  unbindUserRobot,
  createForwardGroup
} from '@/api/wx/corpUsers'
import QRCode from 'qrcode'
import { mapGetters } from 'vuex'
import useColumns from '@/hooks/useColumns'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import ImagePreview from '@/components/ImagePreview/index.vue'

export default {
  name: 'CorpUsers',
  components: {
    CustomTableColumn,
    ImagePreview
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企微员工授权表格数据
      corpUsersList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: null,
        mobile: null,
        corpId: null,
        createBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        corpId: [
          { required: true, message: '请选择企业微信', trigger: 'change' }
        ]
      },
      codeImg: '',
      // 企业选项列表
      corpOptions: [],
      // 选中的企业信息
      selectedCorpInfo: null,
      // 机器人设置/解绑弹窗
      robotDialogVisible: false,
      // 机器人对话框标题
      robotDialogTitle: '',
      // 机器人操作类型：'set' 设置，'unbind' 解绑
      robotOperationType: '',
      // 机器人表单参数
      robotForm: {},
      // 机器人表单校验
      robotRules: {
        robotId: [
          { required: true, message: '机器人ID不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'tableHeight', 'device'
    ])
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询企微员工授权列表 */
    getList() {
      this.loading = true
      listCorpUsers(this.queryParams).then(response => {
        this.corpUsersList = response.rows || []
        this.total = response.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        corpId: null
      }
      this.codeImg = ''
      this.selectedCorpInfo = null
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.loadCorpOptions()
      this.open = true
      this.title = '授权企微员工'
    },
    /** 加载企业选项 */
    loadCorpOptions() {
      getAuthCorpList().then(response => {
        this.corpOptions = response.rows || []
      }).catch(err => {
        console.error('获取企业列表失败:', err)
        this.$modal.msgError('获取企业列表失败')
      })
    },
    /** 企业选择变化处理 */
    handleCorpChange(corpId) {
      this.selectedCorpInfo = this.corpOptions.find(corp => corp.corpId === corpId)
      this.generateQRCode()
    },
    /** 生成二维码 */
    generateQRCode() {
      if (!this.form.corpId) {
        this.codeImg = ''
        return
      }

      getCorpUserAuthUrl(this.form.corpId).then(res => {
        if (res.code === 200 && res.data) {
          QRCode.toDataURL(res.data)
            .then(url => {
              this.codeImg = url
            })
            .catch(err => {
              console.error('生成二维码失败:', err)
              this.$modal.msgError('生成二维码失败')
            })
        } else {
          this.$modal.msgError(res.msg || '获取授权链接失败')
        }
      }).catch(err => {
        console.error('获取授权链接失败:', err)
        this.$modal.msgError('获取授权链接失败')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$modal.msgSuccess('授权确认成功')
          this.open = false
          this.getList() // 刷新列表
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids
      const names = row.userName ? [row.userName] : this.ids.map(id => {
        const user = this.corpUsersList.find(item => item.id === id)
        return user ? user.userName : id
      })

      this.$modal.confirm(`是否确认删除企微员工"${names.join('、')}"的授权信息？`).then(function() {
        return delCorpUsers(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => { })
    },
    /** 设置机器人ID */
    handleSetRobot(row) {
      this.robotOperationType = 'set'
      this.robotDialogTitle = '设置机器人ID'
      this.robotForm = {
        corpUserId: row.userId,
        userName: row.userName,
        currentRobotId: row.robotId || '未设置',
        robotId: ''
      }
      this.robotDialogVisible = true
    },
    /** 解绑机器人 */
    handleUnbindRobot(row) {
      this.$modal.confirm(`确认要解绑员工"${row.userName}"的机器人吗？`).then(() => {
        const data = {
          corpUserId: row.userId,
          robotId: row.robotId
        }
        unbindUserRobot(data).then(res => {
          if (res.code === 200) {
            this.$modal.msgSuccess('机器人解绑成功')
            this.getList() // 刷新列表
          } else {
            this.$modal.msgError(res.msg || '解绑失败')
          }
        }).catch(err => {
          console.error('解绑机器人失败:', err)
        })
      }).catch(() => { })
    },
    /** 取消修改机器人 */
    cancelRobot() {
      this.robotDialogVisible = false
      this.resetRobotForm()
    },
    /** 重置机器人表单 */
    resetRobotForm() {
      this.robotForm = {}
      this.resetForm('robotForm')
    },
    /** 提交机器人表单 */
    submitRobotForm() {
      this.$refs['robotForm'].validate(valid => {
        if (valid) {
          const data = {
            corpUserId: this.robotForm.corpUserId,
            robotId: this.robotForm.robotId
          }
          modifyUserRobot(data).then(res => {
            if (res.code === 200) {
              this.$modal.msgSuccess('机器人ID设置成功')
              this.robotDialogVisible = false
              this.getList() // 刷新列表
            } else {
              this.$modal.msgError(res.msg || '设置失败')
            }
          }).catch(err => {
            console.error('设置机器人ID失败:', err)
            this.$modal.msgError('设置失败')
          })
        }
      })
    },
    /** 新建转发群 */
    handleCreateForwardGroup(row) {
      createForwardGroup(row.userId).then(res => {
        this.$modal.msgSuccess(res.msg)
        this.getList() // 刷新列表
      })
    }
  }
}
</script>
<script setup>
import { ref } from 'vue'
import SavedSearches from '@/components/SavedSearches/index.vue'

const tableRef = ref(null)
// 渲染函数映射
const renderMap = {
  gender: (val) => {
    if (val === 1) return '男'
    if (val === 2) return '女'
    return '未知'
  },
  licenseActivate: (val) => {
    return val === 1 ? '已激活' : '未激活'
  }
}

const defaultColumns = [
  { prop: 'avatar', label: '头像', width: 80, align: 'center' },
  { prop: 'userName', label: '企微员工名称', minWidth: 120, fixed: 'left' },
  { prop: 'userMobile', label: '手机号码', width: 120 },
  { prop: 'gender', label: '性别', width: 80, render: true },
  { prop: 'corpId', label: '企微主体ID', width: 180 },
  { prop: 'corpName', label: '企微主体名称', minWidth: 150 },
  { prop: 'userId', label: '企微员工ID', width: 180 },
  { prop: 'robotId', label: '机器人ID', width: 150 },
  { prop: 'licenseActivate', label: '许可激活状态', width: 100, render: true },
  { prop: 'activeTime', label: '许可激活时间', width: 150 },
  { prop: 'expireTime', label: '许可过期时间', width: 150 },
  { prop: 'createUserName', label: '授权人', width: 100 },
  { prop: 'qrCode', label: '个人二维码', width: 100 },
  { prop: 'deptName', label: '部门名称', overflow: true, width: 130 }
]

const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({
  defaultColumns,
  tableRef,
  name: 'corpUsers'
})

</script>

<style lang="scss" scoped>
.tips {
  color: #999;
  font-size: 14px;
  margin-top: 10px;
}

.qrcode {
  display: flex;
  align-items: center;
  flex-direction: column;
  margin: 20px 0;
}

.corp-info {
  margin-bottom: 20px;
}
</style>
