<template>
  <div class="adaption-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" class="search-form" size="small" :inline="true" label-width="68px">
      <el-form-item prop="miniAppId">
        <el-input
          v-model="queryParams.miniAppId"
          placeholder="小程序appid"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="miniAppName">
        <el-input
          v-model="queryParams.miniAppName"
          placeholder="小程序名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="miniAppOriginalId">
        <el-input
          v-model="queryParams.miniAppOriginalId"
          placeholder="原始id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <UserSearchInput :create-by.sync="queryParams.createBy" @query="handleQuery" />
      <BusinessSelector :business.sync="queryParams.business" @query="handleQuery" />
      <DeptTreeSelector :dept-ids.sync="queryParams.deptIds" />
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="dateRange"
          :popper-class="device === 'mobile' ? 'mobile-date-picker' : ''"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :picker-options="device === 'mobile' ? {} : dateRangePickerOptions"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-dateRange.sync="dateRange"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['wx:miniAccountInfo:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['wx:miniAccountInfo:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <div class="table-wrapper">
      <el-table
        v-loading="loading"
        v-bind="tableHeight"
        :data="miniAccountInfoList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="小程序appid" align="center" prop="miniAppId" />
        <el-table-column label="小程序密钥" align="center" prop="miniAppSecret" />
        <el-table-column label="小程序名称" align="center" prop="miniAppName" />
        <el-table-column label="原始id" align="center" prop="miniAppOriginalId" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '可用' : '不可用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="用户信息" align="center" prop="deptName" min-width="150px">
          <template #default="{row}">
            <TableColumnSet
              :set="[{ label: `昵称`, prop: 'nickName' },
                     { label: `负责人`, prop: 'createBy' }]"
              :row="row"
              label-width="50"
            />
          </template>
        </el-table-column>
        <el-table-column label="公司部门信息" align="center" prop="deptName" min-width="150px">
          <template #default="{row}">
            <TableColumnSet
              :set="[{ label: `公司`, prop: 'firstDeptName' },
                     { label: `部门`, prop: 'deptName' }]"
              :row="row"
              label-width="40"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="checkPermi(['promotion:config:duoduo'])" label="商务负责人" align="center" min-width="150px" prop="business">
          <template #default="{row}">
            <TableColumnSet
              :set="[{ label: `昵称`, prop: 'personInCharge' },
                     { label: `商务`, prop: 'business' }]"
              :row="row"
              label-width="40"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="100px" />
        <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['wx:miniAccountInfo:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              v-hasPermi="['wx:miniAccountInfo:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改微信小程序账户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="105px">
        <el-form-item label="小程序appid" prop="miniAppId">
          <el-input v-model="form.miniAppId" placeholder="请输入小程序appid" />
        </el-form-item>
        <el-form-item label="小程序密钥" prop="miniAppSecret">
          <el-input v-model="form.miniAppSecret" placeholder="请输入小程序密钥" />
        </el-form-item>
        <el-form-item label="小程序名称" prop="miniAppName">
          <el-input v-model="form.miniAppName" placeholder="请输入小程序名称" />
        </el-form-item>
        <el-form-item label="原始id" prop="miniAppOriginalId">
          <el-input v-model="form.miniAppOriginalId" placeholder="请输入原始id" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMiniAccountInfo, getMiniAccountInfo, delMiniAccountInfo, addMiniAccountInfo, updateMiniAccountInfo } from '@/api/wx/miniAccountInfo'
import { deptTreeSelect } from '@/api/system/user'
import { mapGetters } from 'vuex'
import DeptTreeSelector from '@/components/DeptTreeSelect/DeptTreeSelector.vue'
import BusinessSelector from '@/components/BusinessSelector/index.vue'
import { dateRangePickerOptions } from '@/config'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import { checkPermi } from '@/utils/permission'
import SavedSearches from '@/components/SavedSearches/index.vue'

export default {
  name: 'MiniAccountInfo',
  components: { SavedSearches, TableColumnSet, BusinessSelector, DeptTreeSelector },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 微信小程序账户表格数据
      miniAccountInfoList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        miniAppId: null,
        miniAppSecret: null,
        miniAppName: null,
        miniAppOriginalId: null,
        firstDeptId: null,
        status: null,
        deleted: null,
        deptIds: []
      },
      dateRange: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        miniAppId: [
          { required: true, message: '小程序appid不能为空', trigger: 'blur' }
        ],
        miniAppOriginalId: [
          { required: true, message: '原始id不能为空', trigger: 'blur' }
        ],
        createTime: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' }
        ]
      },
      deptOptions: []
    }
  },
  computed: {
    dateRangePickerOptions() {
      return dateRangePickerOptions
    },
    ...mapGetters([
      'tableHeight', 'device'
    ])
  },
  created() {
    this.getList()
    this.getDeptTree()
  },
  methods: {
    checkPermi,
    /** 查询微信小程序账户列表 */
    getList() {
      this.loading = true
      listMiniAccountInfo(
        this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.miniAccountInfoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = []
        response.data.forEach(item => {
          this.deptOptions.push(item)
          if (item.children) {
            this.deptOptions.push(...item.children)
          }
        })
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        miniAppId: null,
        miniAppSecret: null,
        miniAppName: null,
        miniAppOriginalId: null,
        firstDeptId: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        deleted: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加微信小程序账户'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getMiniAccountInfo(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改微信小程序账户'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMiniAccountInfo(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addMiniAccountInfo(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除微信小程序账户编号为"' + ids + '"的数据项？').then(function() {
        return delMiniAccountInfo(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('wx/miniAccountInfo/export', {
        ...this.queryParams
      }, `miniAccountInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
