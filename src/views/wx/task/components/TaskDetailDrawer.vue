<template>
  <el-drawer
    title="任务详情"
    :visible.sync="visible"
    direction="rtl"
    size="80%"
    :before-close="handleClose"
    class="task-detail-drawer"
  >
    <div v-loading="loading" class="drawer-content">
      <template v-if="taskData">
        <!-- 发送详情卡片 -->
        <el-card v-if="nodeDetail" class="send-detail-card" shadow="never">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-data-analysis" />
              <span>发送详情</span>
            </div>
            <div class="header-right">
              <!-- 取消节点按钮 -->
              <el-button
                v-if="selectedNodeId && canCancelNode"
                type="danger"
                size="small"
                style="margin-right: 5px;"
                :loading="cancelLoading"
                @click="handleCancelNode"
              >
                取消节点
              </el-button>
              <!-- 任务节点选择 -->
              <el-select
                v-model="selectedNodeId"
                placeholder="请选择任务节点"
                size="small"
                style="width: 250px;"
                :loading="nodeLoading"
                @change="handleNodeChange"
              >
                <el-option v-for="node in taskNodes" :key="node.id" :label="node.taskTime" :value="node.id" />
              </el-select>
            </div>
          </div>

          <!-- 紧凑的发送详情内容 -->
          <div class="send-stats-compact">
            <!-- 状态和核心统计在一行 -->
            <div class="stats-row">
              <!-- 节点状态 -->
              <div class="status-item">
                <span class="item-label">状态</span>
                <el-tag :type="getNodeStatusTagType(nodeDetail.nodeStatus)" size="mini">
                  {{ getNodeStatusText(nodeDetail.nodeStatus) }}
                </el-tag>
              </div>

              <!-- 客户统计 -->
              <div class="stat-item customer-stat">
                <span class="item-label">
                  <i class="el-icon-user" />
                  客户
                </span>
                <div class="stat-numbers">
                  <span class="total-count">{{ nodeDetail.customerCount || 0 }}</span>
                </div>
              </div>

              <!-- 群聊统计 -->
              <div class="stat-item group-stat">
                <span class="item-label">
                  <i class="el-icon-chat-line-square" />
                  群聊
                </span>
                <div class="stat-numbers">
                  <span class="total-count">{{ nodeDetail.crowdCount || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 主要内容区域 - 左右分栏 -->
        <div class="main-content">
          <!-- 左侧：消息预览 -->
          <div class="left-panel">
            <el-card class="preview-card" shadow="never">
              <div slot="header" class="card-header">
                <i class="el-icon-chat-dot-round" />
                <span>消息预览</span>
              </div>
              <div class="message-preview">
                <we-chat-message-editor
                  v-if="messageContents.length > 0"
                  :messages="messageContents"
                  :readonly="true"
                  :show-editor="false"
                />
                <div v-else class="empty-content">
                  <i class="el-icon-chat-dot-round" />
                  <p>暂无消息内容</p>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧：接收者列表 -->
          <div class="right-panel">
            <el-card class="receivers-card" shadow="never">
              <div slot="header" class="card-header">
                <i class="el-icon-user" />
                <span>接收者列表</span>
              </div>
              <div class="receivers-content">

                <!-- 无接收者 -->
                <div v-if="!receiverType" class="empty-content">
                  <i class="el-icon-user" />
                  <p>暂无数据</p>
                </div>
                <!-- Tab切换 -->
                <el-tabs v-show="receiverType" v-model="activeTab" type="card" class="receivers-tabs">
                  <!-- 客户列表Tab -->
                  <el-tab-pane
                    v-if="receiverType === 'customer' || receiverType === 'both'"
                    label="客户列表"
                    name="customer"
                  >
                    <template #label>
                      <span class="tab-label">
                        <i class="el-icon-user" />
                        客户列表
                        <el-badge
                          v-if="customerPagination.total > 0"
                          :value="customerPagination.total"
                          class="tab-badge"
                        />
                      </span>
                    </template>
                    <div class="customer-list">
                      <el-table
                        v-loading="receiversLoading"
                        :data="customerList"
                        stripe
                        size="small"
                        empty-text="暂无客户数据"
                      >
                        <el-table-column label="客户信息" min-width="180">
                          <template #default="scope">
                            <div class="user-info">
                              <div class="user-avatar">
                                <img v-if="scope.row.headImgUrl" :src="scope.row.headImgUrl" class="avatar-img">
                                <div v-else class="avatar-placeholder">{{ scope.row.receiveName ?
                                  scope.row.receiveName.charAt(0) : '客' }}</div>
                              </div>
                              <div class="user-details">
                                <div class="user-name" :title="scope.row.receiveName">{{ scope.row.receiveName || '-' }}
                                </div>
                                <div class="user-phone">{{ scope.row.phoneNumber || '无电话' }}</div>
                              </div>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column prop="wecomUserName" label="分发企微" width="140">
                          <template #default="scope">
                            <div class="wecom-user">
                              <img v-if="scope.row.avatar" :src="scope.row.avatar" class="wecom-avatar">
                              <span :title="scope.row.wecomUserName">{{ scope.row.wecomUserName || '-' }}</span>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column prop="ownedTags" label="标签" min-width="120">
                          <template #default="scope">
                            <customer-tags-display
                              :customer="getCustomerForTagsDisplay(scope.row)"
                              :tags="getTagsForDisplay(scope.row.ownedTags)"
                              :show-edit="false"
                            />
                          </template>
                        </el-table-column>
                        <el-table-column prop="sendStatus" label="执行状态" width="80">
                          <template #default="scope">
                            <el-tag :type="sendStatusMap[scope.row.sendStatus].tag" size="mini">
                              {{ sendStatusMap[scope.row.sendStatus].label }}
                            </el-tag>

                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="80" fixed="right">
                          <template #default="scope">
                            <el-button size="mini" type="text" @click="viewCustomerDetail(scope.row)">
                              详情
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>

                      <!-- 客户列表分页 -->
                      <div v-if="customerPagination.total > 0" class="pagination-container">
                        <el-pagination
                          :current-page="customerPagination.pageNum"
                          :page-sizes="[10, 20, 50, 100]"
                          :page-size="customerPagination.pageSize"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="customerPagination.total"
                          small
                          @size-change="handleCustomerSizeChange"
                          @current-change="handleCustomerPageChange"
                        />
                      </div>
                    </div>
                  </el-tab-pane>

                  <!-- 社群列表Tab -->
                  <el-tab-pane v-if="receiverType === 'group' || receiverType === 'both'" label="社群列表" name="group">
                    <template #label>
                      <span class="tab-label">
                        <i class="el-icon-chat-line-square" />
                        社群列表
                        <el-badge v-if="groupPagination.total > 0" :value="groupPagination.total" class="tab-badge" />
                      </span>
                    </template>
                    <div class="group-list">
                      <el-table v-loading="receiversLoading" :data="groupList" stripe size="small" empty-text="暂无社群数据">
                        <el-table-column label="群信息" min-width="220">
                          <template #default="scope">
                            <div class="group-info">
                              <GroupAvatars :avatars="scope.row.groupAvatars" />
                              <div class="group-details">
                                <div class="group-name" :title="scope.row.groupName">{{ scope.row.groupName || '-' }}
                                </div>
                                <div class="group-meta">
                                  <span class="member-count">{{ scope.row.personCount || 0 }}人</span>
                                  <span class="create-time">{{ formatDate(scope.row.groupCreateTime) }}</span>
                                </div>
                              </div>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column prop="wecomUserName" label="分发企微" width="140">
                          <template #default="scope">
                            <div class="wecom-user">
                              <img v-if="scope.row.avatar" :src="scope.row.avatar" class="wecom-avatar">
                              <span :title="scope.row.wecomUserName">{{ scope.row.wecomUserName || '-' }}</span>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column prop="sendStatus" label="执行状态" width="80">
                          <template #default="scope">
                            <el-tag :type="sendStatusMap[scope.row.sendStatus].tag" size="mini">
                              {{ sendStatusMap[scope.row.sendStatus].label }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="80" fixed="right">
                          <template #default="scope">
                            <el-button size="mini" type="text" @click="viewGroupDetail(scope.row)">
                              详情
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>

                      <!-- 社群列表分页 -->
                      <div v-if="groupPagination.total > 0" class="pagination-container">
                        <el-pagination
                          :current-page="groupPagination.pageNum"
                          :page-sizes="[10, 20, 50, 100]"
                          :page-size="groupPagination.pageSize"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="groupPagination.total"
                          small
                          @size-change="handleGroupSizeChange"
                          @current-change="handleGroupPageChange"
                        />
                      </div>
                    </div>
                  </el-tab-pane>

                  <!-- 消息列表Tab -->
                  <el-tab-pane label="消息列表" name="message">
                    <template #label>
                      <span class="tab-label">
                        <i class="el-icon-message" />
                        消息列表
                        <el-badge v-if="messagePagination.total > 0" :value="messagePagination.total" class="tab-badge" />
                      </span>
                    </template>
                    <div class="message-list">
                      <el-table v-loading="receiversLoading" :data="messageList" stripe size="small" empty-text="暂无消息数据">
                        <el-table-column label="接收客户" min-width="180">
                          <template #default="scope">
                            <div class="user-info">
                              <div class="user-avatar">
                                <div class="avatar-placeholder">{{ scope.row.receiveCustomer ? scope.row.receiveCustomer.charAt(0) : '客' }}</div>
                              </div>
                              <div class="user-details">
                                <div class="user-name" :title="scope.row.receiveCustomer">{{ scope.row.receiveCustomer || '-' }}</div>
                                <div class="user-phone">{{ scope.row.customerId || '无客户ID' }}</div>
                              </div>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column prop="msgType" label="消息类型" width="100">
                          <template #default="scope">
                            <el-tag size="mini" type="info">
                              {{ getMsgTypeText(scope.row.msgType) }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="execStatus" label="执行状态" width="100">
                          <template #default="scope">
                            <el-tag :type="sendStatusMap[scope.row.execStatus].tag" size="mini">
                              {{ sendStatusMap[scope.row.execStatus].label }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" min-width="120">
                          <template #default="scope">
                            <span :title="scope.row.remark">{{ scope.row.remark || '-' }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="createTime" label="创建时间" width="160">
                          <template #default="scope">
                            {{ formatDateTime(scope.row.createTime) }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="80" fixed="right">
                          <template #default="scope">
                            <el-button size="mini" type="text" @click="viewMessageDetail(scope.row)">
                              详情
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>

                      <!-- 消息列表分页 -->
                      <div v-if="messagePagination.total > 0" class="pagination-container">
                        <el-pagination
                          :current-page="messagePagination.pageNum"
                          :page-sizes="[10, 20, 50, 100]"
                          :page-size="messagePagination.pageSize"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="messagePagination.total"
                          small
                          @size-change="handleMessageSizeChange"
                          @current-change="handleMessagePageChange"
                        />
                      </div>
                    </div>
                  </el-tab-pane>
                </el-tabs>

              </div>
            </el-card>
          </div>
        </div>
      </template>
    </div>

    <!-- 客户详情对话框 -->
    <customer-detail-dialog ref="customerDetailDialog" />

    <!-- 社群详情对话框 -->
    <group-detail-dialog ref="groupDetailDialog" />
  </el-drawer>
</template>

<script>
import { getTask } from '@/api/robot/task'
import { getTaskNodes, getTaskNodeDetail, cancelTaskNode } from '@/api/robot/taskNode'
import { getTaskCustomerList, getTaskGroupList, getTaskMessageList } from '@/api/robot/depository'
import WeChatMessageEditor from '@/components/WeChatMessageEditor/index.vue'
import CustomerDetailDialog from './CustomerDetailDialog.vue'
import GroupDetailDialog from './GroupDetailDialog.vue'
import CustomerTagsDisplay from '@/views/wx/customers/components/CustomerTagsDisplay.vue'
import GroupAvatars from '@/components/GroupAvatars/index.vue'
import { sendStatusMap } from '../config/constant.js'

export default {
  name: 'TaskDetailDrawer',
  components: {
    WeChatMessageEditor,
    CustomerDetailDialog,
    GroupDetailDialog,
    CustomerTagsDisplay,
    GroupAvatars
  },
  data() {
    return {
      visible: false,
      loading: false,
      receiversLoading: false,
      nodeLoading: false,
      cancelLoading: false,
      taskData: null,
      taskNodes: [], // 任务节点列表
      selectedNodeId: null, // 选中的节点ID
      nodeDetail: null, // 节点详情
      customerList: [],
      groupList: [],
      messageList: [],
      receiverType: null, // 'customer' | 'group' | 'both' | null
      activeTab: 'customer', // 当前激活的tab
      sendStatusMap,
      // 分页参数
      customerPagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      groupPagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      messagePagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    // 转换消息内容格式供WeChatMessageEditor使用
    messageContents() {
      if (!this.taskData || !this.taskData.contents) return []

      return this.taskData.contents.map(content => ({
        serialNumber: content.serialNumber,
        contentType: content.contentType,
        msgContent: content.msgContent,
        title: content.title,
        linkUrl: content.linkUrl,
        imgUrl: content.imgUrl
      }))
    },

    // 是否可以取消节点
    canCancelNode() {
      return this.nodeDetail && this.nodeDetail.nodeStatus === 0
    }

  },
  methods: {
    // 打开抽屉
    open(taskId) {
      this.visible = true
      this.loadTaskDetail(taskId)
    },

    // 加载任务详情
    async loadTaskDetail(taskId) {
      this.loading = true
      try {
        const response = await getTask(taskId)
        this.taskData = response.data

        // 加载任务节点
        await this.loadTaskNodes(taskId)

        // 分析接收者类型并加载相应数据
        // this.analyzeReceivers()
      } catch (error) {
        console.error('获取任务详情失败：', error)
        this.$message.error('获取任务详情失败')
      } finally {
        this.loading = false
      }
    },

    // 加载任务节点
    async loadTaskNodes(taskId) {
      this.nodeLoading = true
      try {
        const response = await getTaskNodes({ taskId })
        if (response.code === 200) {
          this.taskNodes = response.data || []
          // 默认选择第一个节点
          if (this.taskNodes.length > 0 && !this.selectedNodeId) {
            await this.handleNodeChange(this.taskNodes[0].id)
          }
        } else {
          this.$message.error(response.msg || '获取任务节点失败')
        }
      } catch (error) {
        console.error('获取任务节点失败：', error)
        this.$message.error('获取任务节点失败')
      } finally {
        this.nodeLoading = false
      }
    },

    // 加载节点详情
    async loadNodeDetail(nodeId) {
      if (!nodeId) return

      try {
        const response = await getTaskNodeDetail(nodeId)
        if (response.code === 200) {
          this.nodeDetail = response.data
        } else {
          this.$message.error(response.msg || '获取节点详情失败')
        }
      } catch (error) {
        console.error('获取节点详情失败：', error)
        this.$message.error('获取节点详情失败')
      }
    },

    // 处理节点变化
    async handleNodeChange(nodeId) {
      this.selectedNodeId = nodeId
      await this.loadNodeDetail(nodeId)
      // 重新加载接收者数据
      this.analyzeReceivers()
    },

    // 取消任务节点
    async handleCancelNode() {
      if (!this.selectedNodeId) return

      try {
        await this.$confirm('确定要取消此任务节点吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.cancelLoading = true
        const response = await cancelTaskNode(this.selectedNodeId)

        if (response.code === 200) {
          this.$message.success('任务节点已取消')
          // 重新加载节点详情
          this.loadTaskNodes(this.taskData.id)
          await this.loadNodeDetail(this.selectedNodeId)
        } else {
          this.$message.error(response.msg || '取消任务节点失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消任务节点失败：', error)
          this.$message.error('取消任务节点失败')
        }
      } finally {
        this.cancelLoading = false
      }
    },

    // 分析接收者类型
    analyzeReceivers() {
      this.receiverType = null
      if (!this.taskData) {
        return
      }

      // 当任务状态为已完成时进行查询
      if (this.nodeDetail.nodeStatus !== 1) {
        return
      }

      // 检查任务数据中是否包含客户类型和群聊类型（值为0,1,2,3）
      const hasCustomerType = (this.taskData.customerType !== undefined && this.taskData.customerType !== null) ||
        (this.taskData.receives && this.taskData.receives.some(r => r.customerType !== undefined && r.customerType !== null))
      const hasGroupType = (this.taskData.groupType !== undefined && this.taskData.groupType !== null) ||
        (this.taskData.receives && this.taskData.receives.some(r => r.groupType !== undefined && r.groupType !== null))

      // 根据包含的类型确定接收者类型并加载相应数据
      if (hasCustomerType && hasGroupType) {
        this.receiverType = 'both'
        this.activeTab = 'customer' // 默认显示客户tab
        this.loadCustomerList()
        this.loadGroupList()
      } else if (hasCustomerType) {
        this.receiverType = 'customer'
        this.activeTab = 'customer'
        this.loadCustomerList()
      } else if (hasGroupType) {
        this.receiverType = 'group'
        this.activeTab = 'group'
        this.loadGroupList()
      } else {
        this.activeTab = 'customer'
      }

      // 始终加载消息列表
      this.loadMessageList()
    },

    // 加载客户列表
    async loadCustomerList(pageNum = 1) {
      this.receiversLoading = true
      try {
        const params = {
          taskId: this.taskData.id,
          pageNum: pageNum,
          pageSize: this.customerPagination.pageSize,
          nodeId: this.selectedNodeId
        }

        const response = await getTaskCustomerList(params)

        if (response.code === 200) {
          this.customerList = (response.rows || []).map(customer => ({
            id: customer.id,
            wecomUserId: customer.wecomUserId,
            wecomUserName: customer.wecomUserName,
            receiveName: customer.receiveName,
            phoneNumber: customer.phoneNumber || '',
            avatar: customer.avatar || '',
            headImgUrl: customer.headImgUrl || '',
            ownedTags: customer.ownedTags || [],
            sendStatus: customer.sendStatus
          }))

          // 更新分页信息
          this.customerPagination.pageNum = pageNum
          this.customerPagination.total = response.total || 0
        } else {
          this.customerList = []
          this.$message.error(response.msg || '获取客户列表失败')
        }
      } catch (error) {
        console.error('获取客户列表失败：', error)
        this.$message.error('获取客户列表失败')
        this.customerList = []
      } finally {
        this.receiversLoading = false
      }
    },

    // 加载社群列表
    async loadGroupList(pageNum = 1) {
      this.receiversLoading = true
      try {
        const params = {
          taskId: this.taskData.id,
          pageNum: pageNum,
          pageSize: this.groupPagination.pageSize,
          nodeId: this.selectedNodeId
        }

        const response = await getTaskGroupList(params)

        if (response.code === 200) {
          this.groupList = (response.rows || []).map(group => ({
            id: group.id,
            wecomUserId: group.wecomUserId,
            wecomUserName: group.wecomUserName,
            groupId: group.groupId,
            groupName: group.groupName,
            personCount: group.personCount || 0,
            groupCreateTime: group.groupCreateTime,
            groupAvatars: group.groupAvatars || [],
            sendStatus: group.sendStatus
          }))

          // 更新分页信息
          this.groupPagination.pageNum = pageNum
          this.groupPagination.total = response.total || 0
        } else {
          this.groupList = []
          this.$message.error(response.msg || '获取社群列表失败')
        }
      } catch (error) {
        console.error('获取社群列表失败：', error)
        this.$message.error('获取社群列表失败')
        this.groupList = []
      } finally {
        this.receiversLoading = false
      }
    },

    // 加载消息列表
    async loadMessageList(pageNum = 1) {
      this.receiversLoading = true
      try {
        const params = {
          taskId: this.taskData.id,
          pageNum: pageNum,
          pageSize: this.messagePagination.pageSize,
          nodeId: this.selectedNodeId
        }

        const response = await getTaskMessageList(params)

        if (response.code === 200) {
          this.messageList = response.rows || []

          // 更新分页信息
          this.messagePagination.pageNum = pageNum
          this.messagePagination.total = response.total || 0
        } else {
          this.messageList = []
          this.$message.error(response.msg || '获取消息列表失败')
        }
      } catch (error) {
        console.error('获取消息列表失败：', error)
        this.$message.error('获取消息列表失败')
        this.messageList = []
      } finally {
        this.receiversLoading = false
      }
    },

    // 查看客户详情
    viewCustomerDetail(customer) {
      this.$refs.customerDetailDialog.open(customer)
    },

    // 查看社群详情
    viewGroupDetail(group) {
      this.$refs.groupDetailDialog.open(group)
    },

    // 查看消息详情
    viewMessageDetail(message) {
      // 显示消息详情对话框
      this.$alert(
        `<div style="text-align: left;">
          <p><strong>消息ID:</strong> ${message.id}</p>
          <p><strong>接收客户:</strong> ${message.receiveCustomer || '-'}</p>
          <p><strong>客户ID:</strong> ${message.customerId || '-'}</p>
          <p><strong>消息类型:</strong> ${this.getMsgTypeText(message.msgType)}</p>
          <p><strong>执行状态:</strong> ${this.sendStatusMap[message.execStatus].label}</p>
          <p><strong>备注:</strong> ${message.remark || '-'}</p>
          <p><strong>创建时间:</strong> ${this.formatDateTime(message.createTime)}</p>
          <p><strong>消息内容:</strong></p>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto; white-space: pre-wrap;">${message.msgContent || '-'}</pre>
        </div>`,
        '消息详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        }
      )
    },

    // 关闭抽屉
    handleClose() {
      this.visible = false
      this.taskData = null
      this.taskNodes = []
      this.selectedNodeId = null
      this.nodeDetail = null
      this.customerList = []
      this.groupList = []
      this.messageList = []
      this.receiverType = null
      this.activeTab = 'customer'
      // 重置分页
      this.customerPagination = {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      this.groupPagination = {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      this.messagePagination = {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    },

    // 客户列表分页处理
    handleCustomerPageChange(pageNum) {
      this.loadCustomerList(pageNum)
    },

    handleCustomerSizeChange(pageSize) {
      this.customerPagination.pageSize = pageSize
      this.loadCustomerList(1)
    },

    // 社群列表分页处理
    handleGroupPageChange(pageNum) {
      this.loadGroupList(pageNum)
    },

    handleGroupSizeChange(pageSize) {
      this.groupPagination.pageSize = pageSize
      this.loadGroupList(1)
    },

    // 消息列表分页处理
    handleMessagePageChange(pageNum) {
      this.loadMessageList(pageNum)
    },

    handleMessageSizeChange(pageSize) {
      this.messagePagination.pageSize = pageSize
      this.loadMessageList(1)
    },

    // 获取节点状态标签类型
    getNodeStatusTagType(status) {
      const statusMap = {
        '0': 'info',
        '1': 'success',
        '-1': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 获取节点状态文本
    getNodeStatusText(status) {
      const statusMap = {
        '0': '待执行',
        '1': '已执行',
        '-1': '已取消'
      }
      return statusMap[status] || '未知'
    },

    // 格式化日期（简化版）
    formatDate(dateTime) {
      if (!dateTime) return '-'
      try {
        const date = new Date(dateTime)
        if (isNaN(date.getTime())) return '-'
        const now = new Date()
        const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24))

        if (diffDays === 0) {
          return '今天'
        } else if (diffDays === 1) {
          return '昨天'
        } else if (diffDays < 7) {
          return `${diffDays}天前`
        } else {
          return date.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit'
          }).replace(/\//g, '-')
        }
      } catch (error) {
        return '-'
      }
    },

    // 格式化完整日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      try {
        const date = new Date(dateTime)
        if (isNaN(date.getTime())) return '-'
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }).replace(/\//g, '-')
      } catch (error) {
        return '-'
      }
    },

    // 获取消息类型文本
    getMsgTypeText(msgType) {
      const msgTypeMap = {
        10001: '文本',
        10002: '图片',
        10006: '文件',
        10012: '网页'
      }
      return msgTypeMap[msgType] || `未知(${msgType})`
    },

    // 为CustomerTagsDisplay组件准备客户数据
    getCustomerForTagsDisplay(row) {
      return {
        customerName: row.receiveName || '客户',
        avatar: row.headImgUrl || ''
      }
    },

    // 为CustomerTagsDisplay组件准备标签数据
    getTagsForDisplay(ownedTags) {
      if (!ownedTags || !Array.isArray(ownedTags)) return {}

      // 按分组整理标签
      const tagsByGroup = {}
      ownedTags.forEach(tag => {
        const groupName = tag.groupName || '未分组'
        if (!tagsByGroup[groupName]) {
          tagsByGroup[groupName] = []
        }
        tagsByGroup[groupName].push({
          tagId: tag.tagId,
          tagName: tag.tagName
        })
      })

      return tagsByGroup
    }
  }
}
</script>

<style lang="scss" scoped>
.task-detail-drawer {
  :deep(.el-drawer__header) {
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 0;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }

  .drawer-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 20px;

    .send-detail-card {
      flex-shrink: 0;
      border: 1px solid #e4e7ed;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 500;

        .header-left {
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }

        .header-right {
          display: flex;
          align-items: center;
        }
      }

      .send-stats-compact {
        .stats-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 16px;

          .status-item,
          .stat-item,
          .time-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            min-width: 0;
            flex: 1;

            .item-label {
              font-size: 12px;
              color: #909399;
              font-weight: 500;
              display: flex;
              align-items: center;
              gap: 4px;
              white-space: nowrap;

              i {
                font-size: 14px;
              }
            }

            .time-value {
              font-size: 13px;
              color: #606266;
              font-weight: 500;
            }
          }

          .stat-item {
            .stat-numbers {
              display: flex;
              align-items: baseline;
              gap: 2px;

              .fail-count {
                font-size: 24px;
                font-weight: 700;
                color: #f56c6c;
              }

              .separator {
                font-size: 14px;
                line-height: 18px;
                color: #909399;
                margin: 0 1px;
              }

              .total-count {
                font-size: 24px;;
                font-weight: 600;
                color: #409eff;
              }
            }

            &.customer-stat .item-label i {
              color: #409eff;
            }

            &.group-stat .item-label i {
              color: #67c23a;
            }
          }
        }
      }
    }

    .main-content {
      flex: 1;
      display: flex;
      gap: 20px;
      min-height: 0;

      .left-panel {
        flex: 0 0 375px; // 固定为常用手机宽度
        min-width: 375px;

        .preview-card {
          height: 100%;
          border: 1px solid #e4e7ed;

          :deep(.el-card__body) {
            height: calc(100% - 50px);
            padding: 0;
          }

          .card-header {
            display: flex;
            align-items: center;
            font-weight: 500;

            i {
              margin-right: 8px;
              color: #67c23a;
            }
          }

          .message-preview {
            height: 100%;
            padding: 20px;
            display: flex;
            justify-content: center;

            :deep(.wechat-message-editor) {
              height: 100%;
              width: 100%;

              .preview-container {
                width: 320px; // 手机预览宽度
                max-width: 320px;
                height: 100%;
                margin: 0 auto;

                .phone-frame {
                  height: 100%;
                  max-height: none;
                  width: 100%;

                  .chat-content {
                    height: calc(100% - 90px);
                  }
                }
              }

              .editor-container {
                display: none;
              }
            }
          }
        }
      }

      .right-panel {
        flex: 1;
        min-width: 0;

        .receivers-card {
          height: 100%;
          border: 1px solid #e4e7ed;

          :deep(.el-card__body) {
            height: calc(100% - 50px);
            padding: 0;
          }

          .card-header {
            display: flex;
            align-items: center;
            font-weight: 500;

            i {
              margin-right: 8px;
              color: #e6a23c;
            }
          }

          .receivers-content {
            height: 100%;
            padding: 0;

            .receivers-tabs {
              height: 100%;

              :deep(.el-tabs__header) {
                margin: 0;
                border-bottom: 1px solid #e4e7ed;
              }

              :deep(.el-tabs__nav-wrap) {
                padding: 0 20px;
              }

              :deep(.el-tabs__content) {
                height: calc(100% - 40px);
                padding: 0;
              }

              :deep(.el-tab-pane) {
                height: 100%;
                padding: 20px;
              }

              .tab-label {
                display: flex;
                align-items: center;
                gap: 6px;

                i {
                  font-size: 14px;
                }

                .tab-badge {
                  :deep(.el-badge__content) {
                    font-size: 12px !important;
                    padding: 0 4px;
                    height: 18px;
                    line-height: 16px;
                    border-radius: 8px;
                  }
                }
              }
            }

            .customer-list,
            .group-list,
            .message-list {
              height: 100%;
              overflow: auto;

              .user-info {
                display: flex;
                align-items: center;
                gap: 12px;

                .user-avatar {
                  width: 32px;
                  height: 32px;
                  border-radius: 50%;
                  overflow: hidden;
                  flex-shrink: 0;

                  .avatar-img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }

                  .avatar-placeholder {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #409eff;
                    color: white;
                    font-size: 12px;
                    font-weight: 500;
                  }
                }

                .user-details {
                  flex: 1;
                  min-width: 0;

                  .user-name {
                    font-weight: 500;
                    color: #303133;
                    margin-bottom: 2px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 14px;
                  }

                  .user-phone {
                    font-size: 12px;
                    color: #909399;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                }
              }

              .group-info {
                display: flex;
                align-items: center;
                gap: 12px;

                .group-details {
                  flex: 1;
                  min-width: 0;

                  .group-name {
                    font-weight: 500;
                    color: #303133;
                    margin-bottom: 4px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 14px;
                  }

                  .group-meta {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 12px;
                    color: #909399;

                    .member-count {
                      color: #409eff;
                      font-weight: 500;
                    }

                    .create-time {
                      color: #909399;
                    }
                  }
                }
              }

              .wecom-user {
                display: flex;
                align-items: center;
                gap: 8px;

                .wecom-avatar {
                  width: 20px;
                  height: 20px;
                  border-radius: 50%;
                  object-fit: cover;
                }
              }

              .no-tags {
                font-size: 12px;
                color: #c0c4cc;
                font-style: italic;
              }

              .pagination-container {
                padding: 16px 0;
                display: flex;
                justify-content: center;
                border-top: 1px solid #e4e7ed;
                background: #fafafa;
                margin-top: 16px;
              }
            }
          }
        }
      }
    }

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #909399;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .task-detail-drawer {
    .drawer-content {
      .main-content {
        flex-direction: column;

        .left-panel {
          flex: none;
          min-width: auto;
          width: 100%;
          height: 400px;
        }

        .right-panel {
          flex: none;
          height: 400px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .task-detail-drawer {
    :deep(.el-drawer) {
      width: 100% !important;
    }

    .drawer-content {
      .send-detail-card {
        .card-header {
          flex-direction: column;
          align-items: stretch;
          gap: 12px;

          .header-right {
            justify-content: space-between;

            :deep(.el-select) {
              flex: 1;
              margin-right: 8px;
            }
          }
        }

        .send-stats-compact {
          .stats-row {
            flex-wrap: wrap;
            gap: 12px;

            .status-item,
            .stat-item,
            .time-item {
              min-width: 80px;
              flex: 0 1 auto;
            }
          }
        }
      }

      .main-content {
        .left-panel {
          height: 300px;

          .message-preview {
            :deep(.wechat-message-editor) {
              .preview-container {
                width: 280px;
                max-width: 280px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
