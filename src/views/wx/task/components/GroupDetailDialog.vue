<template>
  <el-dialog
    title="社群详情"
    :visible.sync="visible"
    width="600px"
    :before-close="handleClose"
    class="group-detail-dialog"
    append-to-body
  >
    <div v-loading="loading" class="group-detail">
      <template v-if="groupData">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-chat-line-square" />
            <span>基本信息</span>
          </div>
          <div class="group-info">
            <div class="avatar-section">
              <GroupAvatars
                :avatars="groupData.groupAvatars"
                :max-display="4"
                layout="grid"
              />
            </div>
            <div class="info-section">
              <el-row :gutter="20">
                <el-col :span="24">
                  <div class="info-item">
                    <label>群名称：</label>
                    <span>{{ groupData.groupName || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>群ID：</label>
                    <span>{{ groupData.groupId || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>成员数量：</label>
                    <span>{{ groupData.personCount || 0 }}人</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>创建时间：</label>
                    <span>{{ formatDateTime(groupData.groupCreateTime) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>发送状态：</label>
                    <el-tag :type="sendStatusMap[groupData.sendStatus].tag" size="mini">
                      {{ sendStatusMap[groupData.sendStatus].label }}
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <!-- 企微员工信息 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-s-custom" />
            <span>群主信息</span>
          </div>
          <div class="wecom-info">
            <div class="wecom-user">
              <img v-if="groupData.avatar" :src="groupData.avatar" class="wecom-avatar">
              <div class="wecom-details">
                <div class="wecom-name">{{ groupData.wecomUserName || '-' }}</div>
                <div class="wecom-id">{{ groupData.wecomUserId || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 群成员头像展示 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-user-solid" />
            <span>群成员头像</span>
          </div>
          <div class="members-section">
            <div v-if="groupData.groupAvatars && groupData.groupAvatars.length > 0" class="members-grid">
              <div
                v-for="(avatar, index) in groupData.groupAvatars"
                :key="index"
                class="member-avatar"
              >
                <img :src="avatar" :alt="`成员${index + 1}`">
              </div>
            </div>
            <div v-else class="empty-members">
              <i class="el-icon-user-solid" />
              <span>暂无成员头像</span>
            </div>
          </div>
        </div>
      </template>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { sendStatusMap } from '../config/constant.js'
import GroupAvatars from '@/components/GroupAvatars/index.vue'

export default {
  name: 'GroupDetailDialog',
  components: {
    GroupAvatars
  },
  data() {
    return {
      visible: false,
      loading: false,
      groupData: null,
      sendStatusMap
    }
  },
  methods: {
    // 打开对话框
    open(group) {
      this.visible = true
      this.groupData = group
    },

    // 关闭对话框
    handleClose() {
      this.visible = false
      this.groupData = null
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      try {
        const date = new Date(dateTime)
        if (isNaN(date.getTime())) return '-'
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }).replace(/\//g, '-')
      } catch (error) {
        return '-'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.group-detail-dialog {
  .group-detail {
    .detail-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 500;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;

        i {
          margin-right: 8px;
          color: #409eff;
          font-size: 16px;
        }
      }

      .group-info {
        display: flex;
        gap: 20px;

        .avatar-section {
          flex-shrink: 0;
        }

        .info-section {
          flex: 1;

          .info-item {
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            label {
              display: inline-block;
              font-weight: 500;
              color: #606266;
              margin-right: 8px;
              min-width: 80px;
            }

            span {
              color: #303133;
            }
          }
        }
      }

      .wecom-info {
        .wecom-user {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          background: #f5f7fa;
          border-radius: 8px;

          .wecom-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
          }

          .wecom-details {
            .wecom-name {
              font-weight: 500;
              color: #303133;
              margin-bottom: 4px;
            }

            .wecom-id {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }

      .members-section {
        .members-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(48px, 1fr));
          gap: 8px;
          max-height: 200px;
          overflow-y: auto;

          .member-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            border: 1px solid #e4e7ed;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }

        .empty-members {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px;
          color: #909399;

          i {
            font-size: 32px;
            margin-bottom: 8px;
            opacity: 0.5;
          }

          span {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
