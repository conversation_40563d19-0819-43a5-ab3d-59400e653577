<template>
  <div class="adaption-container">
    <el-form v-show="showSearch" ref="queryForm" class="search-form" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="客户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="gender">
        <el-select v-model="queryParams.gender" placeholder="性别" @change="handleQuery">
          <el-option label="未知" :value="0" />
          <el-option label="男性" :value="1" />
          <el-option label="女性" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item prop="customerMobile">
        <el-input
          v-model="queryParams.customerMobile"
          placeholder="客户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="企微员工ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="addWay">
        <el-select v-model="queryParams.addWay" placeholder="客户来源" clearable @change="handleQuery">
          <el-option
            v-for="dict in dict.type.we_com_add_way"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="企微员工名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="addTime">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          type="datetimerange"
          :picker-options="dateRangePickerOptions"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-date-range.sync="dateRange"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-has-permission="['wx:customerLinkCount:export']"
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportCsv"
        >导出CSV</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-s-data"
          size="mini"
          @click="handleShowStatistics"
        >查看统计</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <div class="table-wrapper">
      <el-table
        v-loading="loading"
        v-bind="tableHeight"
        :data="zlrList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="客户头像" align="center" prop="avatar" width="80">
          <template slot-scope="scope">
            <el-image
              style="width: 50px; height: 50px"
              :src="scope.row.avatar"
            />
          </template>
        </el-table-column>
        <el-table-column label="媒体" align="center" prop="mediaPlatformType">
          <template slot-scope="scope">
            <svg-icon class-name="type-icon" :icon-class="dictMap(dict.type.media_type)[scope.row.mediaPlatformType]" />
            {{ dictMap(dict.type.media_type)[scope.row.mediaPlatformType] }}
          </template>
        </el-table-column>
        <el-table-column label="客户名称" align="center" prop="customerName" min-width="100" />
        <el-table-column label="获客链接id" align="center" prop="linkId" min-width="120" />
        <el-table-column label="客户类型" align="center" prop="customerType" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.customerType === 1" type="success">微信用户</el-tag>
            <el-tag v-else-if="scope.row.customerType === 2" type="primary">企业用户</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="性别" align="center" prop="gender" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.gender === 1" type="primary">男性</el-tag>
            <el-tag v-else-if="scope.row.gender === 2" type="success">女性</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="企微员工" align="center" prop="userName" width="120" />
        <el-table-column label="企微主体" align="center" prop="corpName" width="120" show-overflow-tooltip />
        <el-table-column label="会话状态" align="center" prop="chatStatus" width="140">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.chatStatus === 0" type="info">客户未发消息</el-tag>
            <el-tag v-else-if="scope.row.chatStatus === 1" type="success">客户已发送消息</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="好友状态" align="center" prop="friendStatus" width="110">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.friendStatus === 0" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.friendStatus === 1" type="warning">删除客户</el-tag>
            <el-tag v-else-if="scope.row.friendStatus === 2" type="danger">被客户删除</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="添加时间" align="center" prop="customerCreateTime" width="160" />
        <el-table-column label="添加方式" align="center" prop="addWay" width="100">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.we_com_add_way" :value="scope.row.addWay" />
          </template>
        </el-table-column>
        <el-table-column label="备注手机号" align="center" prop="remarkMobiles" min-width="120" show-overflow-tooltip />
        <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip />
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加统计信息弹窗 -->
    <el-dialog
      title="获客信息统计"
      :visible.sync="statisticsVisible"
      width="600px"
      append-to-body
    >
      <el-table
        v-loading="statisticsLoading"
        :data="statisticsList"
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column
          prop="userName"
          label="企微员工"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div class="user-cell">
              <el-tooltip :content="'ID: ' + scope.row.userId" placement="top">
                <i class="el-icon-info" style="margin-right: 5px; color: #909399;" />
              </el-tooltip>
              <span class="user-name">{{ scope.row.userName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="addCount"
          label="获客数量"
          width="120"
          align="right"
        >
          <template slot-scope="scope">
            <span class="count-value">{{ scope.row.addCount }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { listZlr, getZlr, delZlr, addZlr, updateZlr, exportCsv, getStatisticsList } from '@/api/wx/zlr'
import dayjs from 'dayjs'
import { dateRangePickerOptions } from '@/config'
import { mapGetters } from 'vuex'
import SavedSearches from '@/components/SavedSearches/index.vue'

export default {
  name: 'CustomerLinkCount',
  components: { SavedSearches },
  dicts: ['media_type', 'we_com_add_way'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 通过获客链接添加用户表格数据
      zlrList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        linkId: null,
        customerName: null,
        gender: null,
        userName: null,
        addWay: null,
        customerMobile: null,
        userId: null
      },
      // 日期范围
      dateRange: [],
      dateRangePickerOptions,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: '主键不能为空', trigger: 'blur' }
        ]
      },
      statisticsVisible: false,
      statisticsLoading: false,
      statisticsList: []
    }
  },
  computed: {
    ...mapGetters([
      'tableHeight'
    ])
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询通过获客链接添加用户列表 */
    getList() {
      const linkId = this.$route.params && this.$route.params.linkId
      this.loading = true
      if (!this.dateRange) {
        this.dateRange = [dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss')]
      }
      const query = {
        ...this.queryParams,
        beginTime: this.dateRange[0],
        endTime: this.dateRange[1],
        linkId: linkId || this.queryParams.linkId
      }
      listZlr(query).then(response => {
        this.zlrList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        externalUserId: null,
        linkId: null,
        userId: null,
        customerName: null,
        customerType: null,
        gender: null,
        avatar: null,
        userName: null,
        chatStatus: null,
        customerCreateTime: null,
        addWay: null,
        corpId: null,
        corpName: null,
        friendStatus: null,
        remarkMobiles: null,
        remarkCorpName: null,
        description: null,
        remark: null,
        tags: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [
        dayjs(new Date()).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        dayjs(new Date()).endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ]
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加通过获客链接添加用户'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getZlr(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改通过获客链接添加用户'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateZlr(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addZlr(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除通过获客链接添加用户编号为"' + ids + '"的数据项？').then(function() {
        return delZlr(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出CSV按钮操作 */
    handleExportCsv() {
      this.$modal.confirm('是否确认导出CSV？').then(() => {
        const query = {
          ...this.queryParams,
          linkId: this.$route.params && this.$route.params.linkId,
          beginTime: this.dateRange[0],
          endTime: this.dateRange[1],
          pageNum: null,
          pageSize: null
        }
        exportCsv(query).then(response => {
          this.$modal.msgSuccess(response.msg)
        })
      }).catch(() => {})
    },
    /** 显示统计弹窗 */
    handleShowStatistics() {
      this.statisticsVisible = true
      this.getStatistics()
    },
    /** 获取统计数据 */
    getStatistics() {
      this.statisticsLoading = true
      const query = {
        addWay: this.queryParams.addWay,
        customerMobile: this.queryParams.customerMobile,
        customerName: this.queryParams.customerName,
        gender: this.queryParams.gender,
        userId: this.queryParams.userId,
        linkId: this.$route.params && this.$route.params.linkId,
        beginTime: this.dateRange[0],
        endTime: this.dateRange[1]
      }
      getStatisticsList(query).then(response => {
        if (response.code === 200) {
          this.statisticsList = response.data || []
        }
      }).finally(() => {
        this.statisticsLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user-cell {
  display: flex;
  align-items: center;

  .user-name {
    font-weight: 500;
    color: #303133;
  }
}

.count-value {
  font-weight: bold;
  color: #409EFF;
}
</style>
