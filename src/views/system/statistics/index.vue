<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item label="广告主ID" prop="advertiserId">
        <el-input
          v-model="queryParams.advertiserId"
          placeholder="请输入广告主ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据起始时间，分组条件包含 STAT_GROUP_BY_FIELD_STAT_TIME 时返回，格式为：yyyy-MM-dd HH:mm:ss" prop="statDatetime">
        <el-input
          v-model="queryParams.statDatetime"
          placeholder="请输入数据起始时间，分组条件包含 STAT_GROUP_BY_FIELD_STAT_TIME 时返回，格式为：yyyy-MM-dd HH:mm:ss"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投放广告位，分组条件包含STAT_GROUP_BY_INVENTORY时返回，具体可查看【附录-首选投放位置】" prop="inventory">
        <el-input
          v-model="queryParams.inventory"
          placeholder="请输入投放广告位，分组条件包含STAT_GROUP_BY_INVENTORY时返回，具体可查看【附录-首选投放位置】"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创意类型，分组条件包含STAT_GROUP_BY_CREATIVE_MATERIAL_MODE时返回，允许值：STATIC_ASSEMBLE" prop="creativeMaterialMode">
        <el-input
          v-model="queryParams.creativeMaterialMode"
          placeholder="请输入创意类型，分组条件包含STAT_GROUP_BY_CREATIVE_MATERIAL_MODE时返回，允许值：STATIC_ASSEMBLE"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="出价类型，分组条件包含STAT_GROUP_BY_PRICING时返回，具体可查看【附录-首选投放位置】" prop="pricing">
        <el-input
          v-model="queryParams.pricing"
          placeholder="请输入出价类型，分组条件包含STAT_GROUP_BY_PRICING时返回，具体可查看【附录-首选投放位置】"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="素材类型，分组条件STAT_GROUP_BY_IMAGE_MODE返回，具体可查看【附录-首选投放位置】" prop="imageMode">
        <el-input
          v-model="queryParams.imageMode"
          placeholder="请输入素材类型，分组条件STAT_GROUP_BY_IMAGE_MODE返回，具体可查看【附录-首选投放位置】"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="省份。如果分组条件中包括 STAT_GROUP_BY_PROVINCE_NAME 时返回" prop="provinceName">
        <el-input
          v-model="queryParams.provinceName"
          placeholder="请输入省份。如果分组条件中包括 STAT_GROUP_BY_PROVINCE_NAME 时返回"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="城市。如果分组条件中包括 STAT_GROUP_BY_CITY_NAME 时返回" prop="cityName">
        <el-input
          v-model="queryParams.cityName"
          placeholder="请输入城市。如果分组条件中包括 STAT_GROUP_BY_CITY_NAME 时返回"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别。如果分组条件中包括 STAT_GROUP_BY_GENDER 时返回" prop="gender">
        <el-input
          v-model="queryParams.gender"
          placeholder="请输入性别。如果分组条件中包括 STAT_GROUP_BY_GENDER 时返回"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年龄。如果分组条件中包括 STAT_GROUP_BY_AGE 时返回" prop="age">
        <el-input
          v-model="queryParams.age"
          placeholder="请输入年龄。如果分组条件中包括 STAT_GROUP_BY_AGE 时返回"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="平台。如果分组条件中包括 STAT_GROUP_BY_PLATFORM 时返回" prop="platform">
        <el-input
          v-model="queryParams.platform"
          placeholder="请输入平台。如果分组条件中包括 STAT_GROUP_BY_PLATFORM 时返回"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网络类型。如果分组条件中包括 STAT_GROUP_BY_AC 时返回" prop="ac">
        <el-input
          v-model="queryParams.ac"
          placeholder="请输入网络类型。如果分组条件中包括 STAT_GROUP_BY_AC 时返回"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="展现数据-消耗，表示广告在投放期内的预估花费金额，当天数据可能会有波动，次日稳定" prop="cost">
        <el-input
          v-model="queryParams.cost"
          placeholder="请输入展现数据-消耗，表示广告在投放期内的预估花费金额，当天数据可能会有波动，次日稳定"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="展现数据-展示数，广告展示给用户的次数。计算方式：经平台判定有效且被计费的展示次数" prop="show">
        <el-input
          v-model="queryParams.show"
          placeholder="请输入展现数据-展示数，广告展示给用户的次数。计算方式：经平台判定有效且被计费的展示次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="展现数据-平均千次展现费用，广告平均每一千次展现所付出的费用，计算公式是：消耗/展示数*1000" prop="avgShowCost">
        <el-input
          v-model="queryParams.avgShowCost"
          placeholder="请输入展现数据-平均千次展现费用，广告平均每一千次展现所付出的费用，计算公式是：消耗/展示数*1000"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="展现数据-点击数，当头条用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击" prop="click">
        <el-input
          v-model="queryParams.click"
          placeholder="请输入展现数据-点击数，当头条用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="展现数据-平均点击单价" prop="avgClickCost">
        <el-input
          v-model="queryParams.avgClickCost"
          placeholder="请输入展现数据-平均点击单价"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="展现数据-点击率" prop="ctr">
        <el-input
          v-model="queryParams.ctr"
          placeholder="请输入展现数据-点击率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据-转化数" prop="convert">
        <el-input
          v-model="queryParams.convert"
          placeholder="请输入转化数据-转化数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据-转化成本" prop="convertCost">
        <el-input
          v-model="queryParams.convertCost"
          placeholder="请输入转化数据-转化成本"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据-转化率" prop="convertRate">
        <el-input
          v-model="queryParams.convertRate"
          placeholder="请输入转化数据-转化率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据-深度转化数" prop="deepConvert">
        <el-input
          v-model="queryParams.deepConvert"
          placeholder="请输入转化数据-深度转化数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据-深度转化成本" prop="deepConvertCost">
        <el-input
          v-model="queryParams.deepConvertCost"
          placeholder="请输入转化数据-深度转化成本"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据-深度转化率" prop="deepConvertRate">
        <el-input
          v-model="queryParams.deepConvertRate"
          placeholder="请输入转化数据-深度转化率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据" prop="attributionConvert">
        <el-input
          v-model="queryParams.attributionConvert"
          placeholder="请输入转化数据"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据" prop="attributionConvertCost">
        <el-input
          v-model="queryParams.attributionConvertCost"
          placeholder="请输入转化数据"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据" prop="attributionDeepConvert">
        <el-input
          v-model="queryParams.attributionDeepConvert"
          placeholder="请输入转化数据"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="转化数据" prop="attributionDeepConvertCost">
        <el-input
          v-model="queryParams.attributionDeepConvertCost"
          placeholder="请输入转化数据"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:statistics:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:statistics:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:statistics:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:statistics:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="statisticsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="广告主ID" align="center" prop="advertiserId" />
      <el-table-column label="数据起始时间，分组条件包含 STAT_GROUP_BY_FIELD_STAT_TIME 时返回，格式为：yyyy-MM-dd HH:mm:ss" align="center" prop="statDatetime" />
      <el-table-column label="投放广告位，分组条件包含STAT_GROUP_BY_INVENTORY时返回，具体可查看【附录-首选投放位置】" align="center" prop="inventory" />
      <el-table-column label="创意类型，分组条件包含STAT_GROUP_BY_CREATIVE_MATERIAL_MODE时返回，允许值：STATIC_ASSEMBLE" align="center" prop="creativeMaterialMode" />
      <el-table-column label="推广目的类型，分组条件包含STAT_GROUP_BY_LANDING_TYPE时返回，具体可查看【附录-首选投放位置】" align="center" prop="landingType" />
      <el-table-column label="出价类型，分组条件包含STAT_GROUP_BY_PRICING时返回，具体可查看【附录-首选投放位置】" align="center" prop="pricing" />
      <el-table-column label="素材类型，分组条件STAT_GROUP_BY_IMAGE_MODE返回，具体可查看【附录-首选投放位置】" align="center" prop="imageMode" />
      <el-table-column label="省份。如果分组条件中包括 STAT_GROUP_BY_PROVINCE_NAME 时返回" align="center" prop="provinceName" />
      <el-table-column label="城市。如果分组条件中包括 STAT_GROUP_BY_CITY_NAME 时返回" align="center" prop="cityName" />
      <el-table-column label="性别。如果分组条件中包括 STAT_GROUP_BY_GENDER 时返回" align="center" prop="gender" />
      <el-table-column label="年龄。如果分组条件中包括 STAT_GROUP_BY_AGE 时返回" align="center" prop="age" />
      <el-table-column label="平台。如果分组条件中包括 STAT_GROUP_BY_PLATFORM 时返回" align="center" prop="platform" />
      <el-table-column label="网络类型。如果分组条件中包括 STAT_GROUP_BY_AC 时返回" align="center" prop="ac" />
      <el-table-column label="展现数据-消耗，表示广告在投放期内的预估花费金额，当天数据可能会有波动，次日稳定" align="center" prop="cost" />
      <el-table-column label="展现数据-展示数，广告展示给用户的次数。计算方式：经平台判定有效且被计费的展示次数" align="center" prop="show" />
      <el-table-column label="展现数据-平均千次展现费用，广告平均每一千次展现所付出的费用，计算公式是：消耗/展示数*1000" align="center" prop="avgShowCost" />
      <el-table-column label="展现数据-点击数，当头条用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击" align="center" prop="click" />
      <el-table-column label="展现数据-平均点击单价" align="center" prop="avgClickCost" />
      <el-table-column label="展现数据-点击率" align="center" prop="ctr" />
      <el-table-column label="转化数据-转化数" align="center" prop="convert" />
      <el-table-column label="转化数据-转化成本" align="center" prop="convertCost" />
      <el-table-column label="转化数据-转化率" align="center" prop="convertRate" />
      <el-table-column label="转化数据-深度转化数" align="center" prop="deepConvert" />
      <el-table-column label="转化数据-深度转化成本" align="center" prop="deepConvertCost" />
      <el-table-column label="转化数据-深度转化率" align="center" prop="deepConvertRate" />
      <el-table-column label="转化数据" align="center" prop="attributionConvert" />
      <el-table-column label="转化数据" align="center" prop="attributionConvertCost" />
      <el-table-column label="转化数据" align="center" prop="attributionDeepConvert" />
      <el-table-column label="转化数据" align="center" prop="attributionDeepConvertCost" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:statistics:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['system:statistics:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改广告数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="广告主ID" prop="advertiserId">
          <el-input v-model="form.advertiserId" placeholder="请输入广告主ID" />
        </el-form-item>
        <el-form-item label="数据起始时间，分组条件包含 STAT_GROUP_BY_FIELD_STAT_TIME 时返回，格式为：yyyy-MM-dd HH:mm:ss" prop="statDatetime">
          <el-input v-model="form.statDatetime" placeholder="请输入数据起始时间，分组条件包含 STAT_GROUP_BY_FIELD_STAT_TIME 时返回，格式为：yyyy-MM-dd HH:mm:ss" />
        </el-form-item>
        <el-form-item label="投放广告位，分组条件包含STAT_GROUP_BY_INVENTORY时返回，具体可查看【附录-首选投放位置】" prop="inventory">
          <el-input v-model="form.inventory" placeholder="请输入投放广告位，分组条件包含STAT_GROUP_BY_INVENTORY时返回，具体可查看【附录-首选投放位置】" />
        </el-form-item>
        <el-form-item label="创意类型，分组条件包含STAT_GROUP_BY_CREATIVE_MATERIAL_MODE时返回，允许值：STATIC_ASSEMBLE" prop="creativeMaterialMode">
          <el-input v-model="form.creativeMaterialMode" placeholder="请输入创意类型，分组条件包含STAT_GROUP_BY_CREATIVE_MATERIAL_MODE时返回，允许值：STATIC_ASSEMBLE" />
        </el-form-item>
        <el-form-item label="出价类型，分组条件包含STAT_GROUP_BY_PRICING时返回，具体可查看【附录-首选投放位置】" prop="pricing">
          <el-input v-model="form.pricing" placeholder="请输入出价类型，分组条件包含STAT_GROUP_BY_PRICING时返回，具体可查看【附录-首选投放位置】" />
        </el-form-item>
        <el-form-item label="素材类型，分组条件STAT_GROUP_BY_IMAGE_MODE返回，具体可查看【附录-首选投放位置】" prop="imageMode">
          <el-input v-model="form.imageMode" placeholder="请输入素材类型，分组条件STAT_GROUP_BY_IMAGE_MODE返回，具体可查看【附录-首选投放位置】" />
        </el-form-item>
        <el-form-item label="省份。如果分组条件中包括 STAT_GROUP_BY_PROVINCE_NAME 时返回" prop="provinceName">
          <el-input v-model="form.provinceName" placeholder="请输入省份。如果分组条件中包括 STAT_GROUP_BY_PROVINCE_NAME 时返回" />
        </el-form-item>
        <el-form-item label="城市。如果分组条件中包括 STAT_GROUP_BY_CITY_NAME 时返回" prop="cityName">
          <el-input v-model="form.cityName" placeholder="请输入城市。如果分组条件中包括 STAT_GROUP_BY_CITY_NAME 时返回" />
        </el-form-item>
        <el-form-item label="性别。如果分组条件中包括 STAT_GROUP_BY_GENDER 时返回" prop="gender">
          <el-input v-model="form.gender" placeholder="请输入性别。如果分组条件中包括 STAT_GROUP_BY_GENDER 时返回" />
        </el-form-item>
        <el-form-item label="年龄。如果分组条件中包括 STAT_GROUP_BY_AGE 时返回" prop="age">
          <el-input v-model="form.age" placeholder="请输入年龄。如果分组条件中包括 STAT_GROUP_BY_AGE 时返回" />
        </el-form-item>
        <el-form-item label="平台。如果分组条件中包括 STAT_GROUP_BY_PLATFORM 时返回" prop="platform">
          <el-input v-model="form.platform" placeholder="请输入平台。如果分组条件中包括 STAT_GROUP_BY_PLATFORM 时返回" />
        </el-form-item>
        <el-form-item label="网络类型。如果分组条件中包括 STAT_GROUP_BY_AC 时返回" prop="ac">
          <el-input v-model="form.ac" placeholder="请输入网络类型。如果分组条件中包括 STAT_GROUP_BY_AC 时返回" />
        </el-form-item>
        <el-form-item label="展现数据-消耗，表示广告在投放期内的预估花费金额，当天数据可能会有波动，次日稳定" prop="cost">
          <el-input v-model="form.cost" placeholder="请输入展现数据-消耗，表示广告在投放期内的预估花费金额，当天数据可能会有波动，次日稳定" />
        </el-form-item>
        <el-form-item label="展现数据-展示数，广告展示给用户的次数。计算方式：经平台判定有效且被计费的展示次数" prop="show">
          <el-input v-model="form.show" placeholder="请输入展现数据-展示数，广告展示给用户的次数。计算方式：经平台判定有效且被计费的展示次数" />
        </el-form-item>
        <el-form-item label="展现数据-平均千次展现费用，广告平均每一千次展现所付出的费用，计算公式是：消耗/展示数*1000" prop="avgShowCost">
          <el-input v-model="form.avgShowCost" placeholder="请输入展现数据-平均千次展现费用，广告平均每一千次展现所付出的费用，计算公式是：消耗/展示数*1000" />
        </el-form-item>
        <el-form-item label="展现数据-点击数，当头条用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击" prop="click">
          <el-input v-model="form.click" placeholder="请输入展现数据-点击数，当头条用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击" />
        </el-form-item>
        <el-form-item label="展现数据-平均点击单价" prop="avgClickCost">
          <el-input v-model="form.avgClickCost" placeholder="请输入展现数据-平均点击单价" />
        </el-form-item>
        <el-form-item label="展现数据-点击率" prop="ctr">
          <el-input v-model="form.ctr" placeholder="请输入展现数据-点击率" />
        </el-form-item>
        <el-form-item label="转化数据-转化数" prop="convert">
          <el-input v-model="form.convert" placeholder="请输入转化数据-转化数" />
        </el-form-item>
        <el-form-item label="转化数据-转化成本" prop="convertCost">
          <el-input v-model="form.convertCost" placeholder="请输入转化数据-转化成本" />
        </el-form-item>
        <el-form-item label="转化数据-转化率" prop="convertRate">
          <el-input v-model="form.convertRate" placeholder="请输入转化数据-转化率" />
        </el-form-item>
        <el-form-item label="转化数据-深度转化数" prop="deepConvert">
          <el-input v-model="form.deepConvert" placeholder="请输入转化数据-深度转化数" />
        </el-form-item>
        <el-form-item label="转化数据-深度转化成本" prop="deepConvertCost">
          <el-input v-model="form.deepConvertCost" placeholder="请输入转化数据-深度转化成本" />
        </el-form-item>
        <el-form-item label="转化数据-深度转化率" prop="deepConvertRate">
          <el-input v-model="form.deepConvertRate" placeholder="请输入转化数据-深度转化率" />
        </el-form-item>
        <el-form-item label="转化数据" prop="attributionConvert">
          <el-input v-model="form.attributionConvert" placeholder="请输入转化数据" />
        </el-form-item>
        <el-form-item label="转化数据" prop="attributionConvertCost">
          <el-input v-model="form.attributionConvertCost" placeholder="请输入转化数据" />
        </el-form-item>
        <el-form-item label="转化数据" prop="attributionDeepConvert">
          <el-input v-model="form.attributionDeepConvert" placeholder="请输入转化数据" />
        </el-form-item>
        <el-form-item label="转化数据" prop="attributionDeepConvertCost">
          <el-input v-model="form.attributionDeepConvertCost" placeholder="请输入转化数据" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStatistics, getStatistics, delStatistics, addStatistics, updateStatistics } from '@/api/system/statistics'

export default {
  name: 'Statistics',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 广告数据表格数据
      statisticsList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        advertiserId: null,
        statDatetime: null,
        inventory: null,
        creativeMaterialMode: null,
        landingType: null,
        pricing: null,
        imageMode: null,
        provinceName: null,
        cityName: null,
        gender: null,
        age: null,
        platform: null,
        ac: null,
        cost: null,
        show: null,
        avgShowCost: null,
        click: null,
        avgClickCost: null,
        ctr: null,
        convert: null,
        convertCost: null,
        convertRate: null,
        deepConvert: null,
        deepConvertCost: null,
        deepConvertRate: null,
        attributionConvert: null,
        attributionConvertCost: null,
        attributionDeepConvert: null,
        attributionDeepConvertCost: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        advertiserId: [
          { required: true, message: '广告主ID不能为空', trigger: 'blur' }
        ],
        statDatetime: [
          { required: true, message: '数据起始时间，分组条件包含 STAT_GROUP_BY_FIELD_STAT_TIME 时返回，格式为：yyyy-MM-dd HH:mm:ss不能为空', trigger: 'blur' }
        ],
        inventory: [
          { required: true, message: '投放广告位，分组条件包含STAT_GROUP_BY_INVENTORY时返回，具体可查看【附录-首选投放位置】不能为空', trigger: 'blur' }
        ],
        creativeMaterialMode: [
          { required: true, message: '创意类型，分组条件包含STAT_GROUP_BY_CREATIVE_MATERIAL_MODE时返回，允许值：STATIC_ASSEMBLE不能为空', trigger: 'blur' }
        ],
        landingType: [
          { required: true, message: '推广目的类型，分组条件包含STAT_GROUP_BY_LANDING_TYPE时返回，具体可查看【附录-首选投放位置】不能为空', trigger: 'change' }
        ],
        pricing: [
          { required: true, message: '出价类型，分组条件包含STAT_GROUP_BY_PRICING时返回，具体可查看【附录-首选投放位置】不能为空', trigger: 'blur' }
        ],
        imageMode: [
          { required: true, message: '素材类型，分组条件STAT_GROUP_BY_IMAGE_MODE返回，具体可查看【附录-首选投放位置】不能为空', trigger: 'blur' }
        ],
        cost: [
          { required: true, message: '展现数据-消耗，表示广告在投放期内的预估花费金额，当天数据可能会有波动，次日稳定不能为空', trigger: 'blur' }
        ],
        show: [
          { required: true, message: '展现数据-展示数，广告展示给用户的次数。计算方式：经平台判定有效且被计费的展示次数不能为空', trigger: 'blur' }
        ],
        avgShowCost: [
          { required: true, message: '展现数据-平均千次展现费用，广告平均每一千次展现所付出的费用，计算公式是：消耗/展示数*1000不能为空', trigger: 'blur' }
        ],
        click: [
          { required: true, message: '展现数据-点击数，当头条用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询广告数据列表 */
    getList() {
      this.loading = true
      listStatistics(this.queryParams).then(response => {
        this.statisticsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        advertiserId: null,
        statDatetime: null,
        inventory: null,
        creativeMaterialMode: null,
        landingType: null,
        pricing: null,
        imageMode: null,
        provinceName: null,
        cityName: null,
        gender: null,
        age: null,
        platform: null,
        ac: null,
        cost: null,
        show: null,
        avgShowCost: null,
        click: null,
        avgClickCost: null,
        ctr: null,
        convert: null,
        convertCost: null,
        convertRate: null,
        deepConvert: null,
        deepConvertCost: null,
        deepConvertRate: null,
        attributionConvert: null,
        attributionConvertCost: null,
        attributionDeepConvert: null,
        attributionDeepConvertCost: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.advertiserId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加广告数据'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const advertiserId = row.advertiserId || this.ids
      getStatistics(advertiserId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改广告数据'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.advertiserId != null) {
            updateStatistics(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addStatistics(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const advertiserIds = row.advertiserId || this.ids
      this.$modal.confirm('是否确认删除广告数据编号为"' + advertiserIds + '"的数据项？').then(function() {
        return delStatistics(advertiserIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/statistics/export', {
        ...this.queryParams
      }, `statistics_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
