<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <div class="text-center">
              <userAvatar :user="user" />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="user" />用户名称
                <div class="pull-right">{{ user.userName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone" />手机号码
                <div class="pull-right">{{ user.phonenumber }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email" />用户邮箱
                <div class="pull-right">{{ user.email }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />所属部门
                <div v-if="user.dept" class="pull-right">{{ user.dept.deptName }} / {{ postGroup }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="peoples" />所属角色
                <div class="pull-right">{{ roleGroup }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />创建日期
                <div class="pull-right">{{ user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>基本资料</span>
          </div>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd />
            </el-tab-pane>
            <el-tab-pane label="通知配置" name="noticeGroup">
              <notice-group-config />
            </el-tab-pane>
            <!-- <el-tab-pane label="第三方登录" name="thirdParty">
              <thirdParty :auths="auths" />
            </el-tab-pane> -->
            <!-- <el-tab-pane v-if="checkPermi(['promotion:config:individuation'])" label="个性化配置" name="updateSetting">
              <updateSetting />
            </el-tab-pane>
            <el-tab-pane v-if="checkPermi(['promotion:config:individuation'])" label="回传配置" name="conversion">
              <ConversionSetups type="user" />
            </el-tab-pane> -->
            <el-tab-pane v-if="checkPermi(['promotion:config:deptDomainName'])" label="自定义落地页域名" name="domain">
              <domain-setting />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from './userAvatar.vue'
import userInfo from './userInfo.vue'
import resetPwd from './resetPwd.vue'
import thirdParty from './thirdParty.vue'
import updateSetting from './updateSetting.vue'
import noticeGroupConfig from './noticeGroupConfig.vue'
import { getUserProfile } from '@/api/system/user'
import DomainSetting from '@/views/system/user/profile/domainSetting.vue'
import { checkPermi } from '@/utils/permission'
import ConversionSetups from '@/components/ConversionSetups/index.vue'
import wxCode from './wxCode.vue'
export default {
  name: 'Profile',
  components: { DomainSetting, userAvatar, userInfo, resetPwd, thirdParty, updateSetting, ConversionSetups, wxCode, noticeGroupConfig },
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      auths: [],
      activeTab: 'userinfo'
    }
  },
  created() {
    this.getUser()
  },
  activated() {
    this.getUser()
  },
  methods: {
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data
        this.roleGroup = response.roleGroup
        this.postGroup = response.postGroup
        this.auths = response.auths
      })
    },
    checkPermi
  }
}
</script>
<style scoped lang="scss">
.byFollowed{
 color: rgb(24,144,255);
 cursor: pointer;
}
.goFollow{
  color: #999;
}
</style>
