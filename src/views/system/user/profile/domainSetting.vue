<template>
  <el-form ref="form" :model="form" label-width="150px">
    <el-form-item label="自定义落地页域名" prop="domain">
      <template #label>
        <span>自定义落地页域名</span>
        <el-tooltip class="item" effect="dark" content="域名格式：xxx.com或者xxx.com/" placement="top">
          <i class="el-icon-question" />
        </el-tooltip>
      </template>
      <div style="width: 400px;">
        <InputList
          v-model="domainList"
        />
      </div>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import {
  getDomainName,
  updateDomainName
} from '@/api/system/config'
import InputList from '@/components/InputList/index.vue'

export default {
  components: { InputList },
  data() {
    return {
      form: {},
      domainList: []
    }
  },
  mounted() {
    this.fetchDomainName()
  },
  activated() {
    this.fetchDomainName()
  },
  methods: {
    submit() {
      let pass = true
      this.domainList = Array.from(new Set(this.domainList.filter(item => item)))
      this.domainList.forEach(item => {
        const reg = /^[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/g
        if (!reg.test(item)) {
          pass = false
        }
      })
      if (!pass) {
        this.$message.error('请输入正确的网址，不包含http(s)://协议前缀')
        return
      }
      let domainRegexPass = true
      this.domainList.forEach(item => {
        const domainRegex = /^[a-z0-9]+([-.]{1}[a-z0-9]+)*\.[a-z]{2,6}\/?$/
        if (!domainRegex.test(item)) {
          domainRegexPass = false
        }
      })
      if (!domainRegexPass) {
        this.$message.error('请输入正确格式的域名')
        return
      }

      updateDomainName(Array.from(new Set(this.domainList)).map(domain => {
        if (domain[domain.length - 1] !== '/') {
          domain += '/'
        }
        return domain
      }).join(',')).then(() => {
        this.$message.success('保存成功')
      })
    },

    fetchDomainName() {
      getDomainName().then(response => {
        if (response.data) {
          this.domainList = response.data.split(',')
        } else {
          this.domainList = []
        }
      })
    },
    close() {
      this.$tab.closePage()
    }
  }
}
</script>

<style lang="scss" scoped>

.conversion-item {
  display: flex;
  align-items: center;
  height: 38px;
  gap:10px
}
</style>
