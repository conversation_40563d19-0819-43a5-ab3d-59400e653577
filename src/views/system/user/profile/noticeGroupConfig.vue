<template>
  <el-form ref="form" :model="form" label-width="120px">
    <el-form-item label="通知类型配置">
      <el-checkbox-group v-model="form.businessTypes">
        <el-checkbox
          v-for="dict in dict.type.notice_group_business_type"
          :key="dict.value"
          :label="dict.value"
        >
          {{ dict.label }}
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submitForm">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { addNoticeGroup, updateNoticeGroup, queryNoticeGroupByUserName } from '@/api/system/noticeGroup'

export default {
  name: 'NoticeGroupConfig',
  dicts: ['notice_group_business_type'],
  data() {
    return {
      // 表单参数
      form: {
        id: '',
        businessTypes: [],
        noticeGroupType: 2 // 个人中心类型
      },
      isFirst: false
    }
  },
  created() {
    this.getNoticeGroupConfig()
  },
  methods: {
    // 获取用户通知组配置
    async getNoticeGroupConfig() {
      try {
        const res = await queryNoticeGroupByUserName()
        if (res.data && res.data.businessTypes) {
          this.form.businessTypes = res.data.businessTypes.split(',').filter(Boolean)
          this.form.id = res.data.id
        } else {
          // 如果没有配置，则默认全选
          this.form.businessTypes = this.dict.type.notice_group_business_type.map(item => item.value)
          this.isFirst = true
        }
      } catch (error) {
        console.error('获取用户通知组配置失败', error)
      }
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const data = {
            businessTypes: this.form.businessTypes.join(','),
            noticeGroupType: this.form.noticeGroupType
          }
          if (this.isFirst) {
            addNoticeGroup(data).then(response => {
              this.$modal.msgSuccess('保存成功')
              this.isFirst = false
              this.getNoticeGroupConfig()
            })
          } else {
            data.id = this.form.id
            updateNoticeGroup(data).then(response => {
              this.$modal.msgSuccess('保存成功')
            })
          }
        }
      })
    },
    close() {
      this.$tab.closePage()
    }
  }
}
</script>
