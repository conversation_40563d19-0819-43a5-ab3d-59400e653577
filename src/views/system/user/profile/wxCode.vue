<template>
  <div class="wxQrcode">
    <div v-if="weappQrcodeUrl">
      <el-image
        style="width: 300px; height: 300px"
        :src="weappQrcodeUrl"
        :preview-src-list="[weappQrcodeUrl]"
      />
    </div>
    <div v-else>
      <el-skeleton style="width: 300px; height: 300px" />
    </div>
    <div class="reference_tips">{{ weappQrcodeUrl ? '手机微信扫码关注' : '加载中...' }}</div>
  </div>
</template>

<script>

export default {
  props: {
    weappQrcodeUrl: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.wxQrcode{
  min-height: 200px;
  width: 460px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .reference_tips{
    margin-top: 8px;
    font-size: 14px;
    color: #b4b4b4;
  }
}
</style>
