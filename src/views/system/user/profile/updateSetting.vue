<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="120px">
    <PreWarning
      ref="preWarning"
      v-has-permi="['media:warn:saveOrUpdate']"
      :form="form"
      business-type="2"
    />
    <el-form-item>
      <el-button :loading="loading" type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex'
import { checkPermi } from '@/utils/permission'
import PreWarning from '@/views/promotion/media/components/PreWarning.vue'

export default {
  components: { PreWarning },
  data() {
    return {
      loading: false,
      form: {
      },
      // 表单校验
      rules: {
      }
    }
  },

  computed: {
    ...mapGetters([
      'permissions',
      'roles'
    ])
  },
  mounted() {
  },
  methods: {
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (checkPermi(['media:warn:saveOrUpdate'])) { this.$refs['preWarning'].savePreWarning() }
        }
      })
    },
    close() {
      this.$tab.closePage()
    }

  }
}
</script>

<style lang="scss" scoped>

.conversion-item {
  display: flex;
  align-items: center;
  height: 38px;
  gap:10px;
  .conversion-label {
  width: 110px;
  text-align: right;
  margin-right: 10px;
}
}
</style>
