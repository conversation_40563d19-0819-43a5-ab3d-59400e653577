<script setup>
import { ref, computed } from 'vue'
import { Message, MessageBox } from 'element-ui'
import { remarkEdit } from '@/api/system/dept'
import { checkPermi } from '@/utils/permission'
import { commissionStatus } from '@/config/commission'

const props = defineProps({
  timeList: {
    type: Array,
    default: () => ([])
  },
  display: {
    type: Boolean,
    default: false
  },
  editRemark: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['submit', 'cancel'])
const hasEditRemark = computed(() => checkPermi(['dept:report:editCommissionRemark']) && props.editRemark)

const submit = () => emit('submit')
const cancel = () => emit('cancel')

const timeListValue = ref('1')

const handleEditRemark = (item) => {
  MessageBox.prompt('修改备注', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: item.remark ? item.remark : '请输入备注'
  }).then(res => {
    item.remark = res.value
    remarkEdit({
      id: item.id,
      remark: item.remark
    }).then(res => {
      Message.success('修改备注成功')
      props.editRemark && props.editRemark()
    })
  })
}

</script>

<template>
  <div>
    <div style="margin: 20px">
      <div v-if="!display" class="tip">请确认以下返点设置：</div>
      <el-form label-width="68px">
        <div>
          <el-tabs v-model="timeListValue" type="card">
            <el-tab-pane
              v-for="(item) in timeList"
              :key="item.name"
              :label="item.title"
              :name="item.name"
            >
              <div class="rate-list">
                <el-form-item label="佣金模式">
                  <el-tag type="primary">{{ commissionStatus[item.commissionStatus] }}</el-tag>
                </el-form-item>
                <el-form-item v-if="item.incrementSubsidyRate" label="增量补贴">
                  {{ item.incrementSubsidyRate }}%
                </el-form-item>
                <el-form-item v-if="hasEditRemark || item.remark" label="备注">
                  <el-input v-model="item.remark" type="textarea" disabled :rows="2" placeholder="请输入备注" />
                  <el-button v-if="hasEditRemark" type="text" @click="handleEditRemark(item)">编辑</el-button>
                </el-form-item>
                <div
                  v-for="(comm, i) in item.userCommission"
                  :key="i"
                  class="list-item"
                >
                  <template v-if="comm.stepEnd !== 0">
                    <div class="step-item">{{ comm.stepStart }}</div>
                    ~
                    <div class="step-item">{{ comm.stepEnd }}</div>
                    元时
                  </template>
                  <template v-else>
                    大于
                    <div v-if="item.userCommission.length>=1" class="step-item"> {{ comm.stepStart }}</div>
                    <div v-else class="step-item">{{ comm.stepEnd }}</div>
                    元时
                  </template>
                  , {{ item.commissionStatus === 2 ? '收佣为' : '返点为' }}
                  <div class="step-item"> {{ comm.commissionRate }}%</div>
                </div>
                <div v-if="item.userCommission.length === 0" class="list-item text-center">未配置</div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form>
      <div v-if="!timeList.length">无配置</div>
    </div>
    <div class="dialog-footer-right">
      <el-button type="primary" @click="submit">确 认</el-button>
      <el-button v-if="!display" @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.tip {
  font-weight: bold;
  color: #F56C6C;
  margin-bottom: 20px;
  font-size: 18px;
}
.rate-list{
  border: 1px solid #dfe4ed;
  border-top:none;
  padding: 10px;
.list-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  &:last-of-type {
    margin-bottom: 0;
  }
  .step-item {
    padding:5px 10px;
    text-align: center;
    font-weight: bold;
    color: #1890ff;
  }
}
}
::v-deep(.el-tabs__header) {
  margin: 0;
}
</style>
