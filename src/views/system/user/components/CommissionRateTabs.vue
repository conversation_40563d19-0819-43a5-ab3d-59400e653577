<script setup>
import { ref, nextTick } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  timeList: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update:timeList'])

const defaultRate = () => ({
  stepStart: 0,
  stepEnd: 0,
  commissionRate: 0
})

const timeListValue = ref('1')

function handleTabsEdit(targetName, action) {
  const newTimeList = [...props.timeList]

  if (action === 'add') {
    const newTabName = new Date().getTime() + ''
    const lastItem = newTimeList[newTimeList.length - 1]
    let startTime = new Date()
    if (lastItem) {
      startTime = dayjs(lastItem.stepStartTime).add(1, 'day').toDate()
    }
    startTime = dayjs(startTime).format('YYYY-MM-DD')

    newTimeList.push({
      title: startTime,
      stepStartTime: startTime,
      name: newTabName,
      commissionStatus: 0,
      userCommission: [defaultRate()],
      incrementSubsidyRate: 0,
      remark: ''
    })
    timeListValue.value = newTabName
  }

  if (action === 'remove') {
    if (newTimeList.length === 1) return
    let activeName = timeListValue.value
    if (activeName === targetName) {
      newTimeList.forEach((tab, index) => {
        if (tab.name === targetName) {
          const nextTab = newTimeList[index + 1] || newTimeList[index - 1]
          if (nextTab) {
            activeName = nextTab.name
          }
        }
      })
    }

    timeListValue.value = activeName
    const filteredList = newTimeList.filter(tab => tab.name !== targetName)
    emit('update:timeList', filteredList)
    return
  }

  emit('update:timeList', newTimeList)
}

function handleStepStartChange(item) {
  item.title = item.stepStartTime
  // 排序
  const sortedList = [...props.timeList].sort((a, b) => dayjs(a.stepStartTime).diff(dayjs(b.stepStartTime)))
  emit('update:timeList', sortedList)
}

const addRates = (item) => {
  if (item.userCommission.length === 0) {
    item.userCommission.push(defaultRate())
    return
  }
  const temp = {
    stepStart: item.userCommission[item.userCommission.length - 1].stepStart + 1,
    stepEnd: 0,
    commissionRate: 0
  }
  nextTick(() => {
    item.userCommission.push(temp)
  })
}

const removeRates = (userCommission, i) => {
  userCommission.splice(i, 1)
  onRatesChange(userCommission)
}

const handleStepChange = (userCommission) => {
  onRatesChange(userCommission)
}

const onRatesChange = (userCommission) => {
  const len = userCommission.length
  userCommission.forEach((item, index) => {
    if (index === 0) {
      item.stepStart = 0
    } else {
      if (item.stepStart !== userCommission[index - 1].stepEnd) {
        item.stepStart = userCommission[index - 1].stepEnd
        if (item.stepStart > item.stepEnd && index !== len - 1) {
          item.stepEnd = item.stepStart + 1
        }
      }
    }
    if (index === len - 1) {
      item.stepEnd = 0
    }
  })
}
</script>

<template>
  <el-tabs v-model="timeListValue" type="card" editable @edit="handleTabsEdit">
    <el-tab-pane
      v-for="(item, i) in timeList"
      :key="item.name"
      :label="item.title"
      :name="item.name"
    >
      <el-form class="rate-list" label-width="100px">
        <el-form-item label="佣金模式" prop="commissionStatus" size="mini">
          <el-radio-group v-model="item.commissionStatus" class="line-chart-change">
            <el-radio-button :label="0">消耗返佣</el-radio-button>
            <el-radio-button :label="1">GMV返佣</el-radio-button>
            <el-radio-button :label="2">GMV收佣</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="生效开始时间" size="mini">
          <el-date-picker
            v-model="item.stepStartTime"
            size="mini"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            @change="handleStepStartChange(item)"
          />
        </el-form-item>
        <el-form-item v-if="item.commissionStatus !== 2" label="增量补贴比例" prop="incrementSubsidyRate" size="mini">
          <el-input-number v-model="item.incrementSubsidyRate" clearable size="small" :min="0" :max="100" :precision="2" :controls="false" /> %
        </el-form-item>
        <el-form-item label="备注" prop="remark" size="mini">
          <el-input v-model="item.remark" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
        <div
          v-for="(comm, i) in item.userCommission"
          :key="i"
          class="list-item"
        >
          <template v-if="(i+1) !== item.userCommission.length">
            <div class="step-item">{{ comm.stepStart }}</div>
            ~
            <el-input-number v-model="comm.stepEnd" size="small" class="step-item" :min="comm.stepStart+1" :precision="0" :controls="false" @change="handleStepChange(item.userCommission)" />元
          </template>
          <template v-else>
            大于
            <template v-if="item.userCommission.length>=1"> {{ comm.stepStart }}</template>
            <template v-else>
              <el-input-number v-model="comm.stepEnd" size="small" class="step-item" :min="0" :precision="0" :controls="false" />
            </template>
            元时
          </template>
          , {{ item.commissionStatus === 2 ? '收佣为' : '返点为' }}
          <el-input-number v-model="comm.commissionRate" size="small" class="step-item" :min="0" :max="100" :precision="2" :controls="false" />%
          <el-button size="mini" :disabled="item.userCommission.length === 1" type="text" style="color: #ff4949;margin:0 10px" @click="removeRates(item.userCommission, i)">
            <i class="el-icon-delete" />
          </el-button>
        </div>
        <div v-if="item.userCommission.length === 0" class="list-item text-center">未配置</div>
        <div v-if="item.commissionStatus !== 2 || item.userCommission.length < 1" class="action-btn">
          <el-button class="add-btn" type="primary" plain @click="addRates(item)">+ 新增比例</el-button>
        </div>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
.rate-list{
  border: 1px solid #dfe4ed;
  border-top:none;
  padding: 10px;
  .list-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .step-item {
      flex: 1;
      font-size: 14px;
      padding: 0 10px;
      text-align: center;
    }
  }
}
.add-btn {
  width: 100%;
}
::v-deep(.el-tabs__header) {
  margin: 0;
}
</style>

