<script setup>
import { ref, watch } from 'vue'
import { addUserRate, getUserRateById, updateUserRate } from '@/api/system/user'
import { checkPermi } from '@/utils/permission'
import dayjs from 'dayjs'
import CommissionRateTabs from './CommissionRateTabs.vue'

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const defaultTab = (lastItem) => {
  let startTime = new Date()
  if (lastItem) {
    startTime = dayjs(lastItem.stepStartTime).add(1, 'day').toDate()
  }
  startTime = dayjs(startTime).format('YYYY-MM-DD')

  return {
    title: startTime,
    stepStartTime: startTime,
    name: '1',
    commissionStatus: 0,
    userCommission: [defaultRate()]
  }
}

const defaultRate = () => ({
  stepStart: 0,
  stepEnd: 0,
  commissionRate: 0
})

const timeList = ref([])
const loading = ref(false)
const init = () => {
  if (!checkPermi(['system:rate:list'])) { return }

  if (!props.id) {
    timeList.value = [defaultTab()]
    return
  }

  loading.value = true
  getUserRateById(props.id).then(res => {
    res.data.timeList?.forEach(item => {
      item.title = item.stepStartTime.split(' ')[0]
      const newTabName = tabIndex + ''
      item.name = newTabName
      tabIndex++
    })
    timeList.value = res.data.timeList || [{
      title: dayjs(new Date()).format('YYYY-MM-DD'),
      stepStartTime: dayjs(new Date()).format('YYYY-MM-DD'),
      name: '1',
      userCommission: [] }]
  }).finally(() => {
    loading.value = false
  })
}

watch(() => props.id, () => {
  init()
}, { immediate: true })

let tabIndex = 1

defineExpose({
  saveRate: (deptId, isNew) => {
    if (!checkPermi(['system:rate:add', 'system:rate:edit', 'system:rate:editCommission'])) {
      return Promise.resolve()
    }
    return new Promise(resolve => {
      const postData = {
        deptId,
        timeList: timeList.value.map((item, i) => {
          const temp = {
            stepStartTime: dayjs(item.stepStartTime).format('YYYY-MM-DD HH:mm:ss'),
            userCommission: item.userCommission,
            commissionStatus: item.commissionStatus,
            incrementSubsidyRate: item.incrementSubsidyRate,
            remark: item.remark
          }
          if (i < timeList.value.length - 1) {
            temp.stepEndTime = dayjs(timeList.value[i + 1].stepStartTime).format('YYYY-MM-DD HH:mm:ss')
          }
          return temp
        })
      }
      if (isNew) {
        addUserRate(postData).then(res => {
          resolve(res)
        })
      } else {
        updateUserRate(postData).then(res => {
          resolve(res)
        })
      }
    })
  },
  getData: () => {
    return {
      timeList: timeList.value
    }
  }
})
</script>

<template>
  <div>
    <div style="margin: 0 30px">
      <div style="font-weight: bold;margin-bottom: 10px">返点配置</div>
      <commission-rate-tabs :time-list.sync="timeList" />
      <el-skeleton v-if="loading" style="margin-top: 10px" :rows="5" />
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
