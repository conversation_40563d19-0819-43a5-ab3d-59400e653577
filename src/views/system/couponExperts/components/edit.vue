<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="500px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">

      <el-form-item label="登录账号：" prop="loginAccount">
        <el-input
          v-model.trim="form.loginAccount"
          placeholder="请输入登录账号"
        />
      </el-form-item>
      <el-form-item label="昵称：" prop="nickName">
        <el-input
          v-model.trim="form.nickName"
          placeholder="请输入昵称"
        />
      </el-form-item>
      <el-form-item v-if="!formData||!formData.id" label="登录密码：" prop="loginPwd">
        <el-input
          v-model.trim="form.loginPwd"
          placeholder="请输入登录密码"
        />
      </el-form-item>
      <el-form-item label="授权过期时间：" prop="authExpireTime">
        <el-date-picker
          v-model="form.authExpireTime"
          style="width: 240px"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        v-loading="submitLoding"
        type="primary"
        @click="submitForm"
      >确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addUpdate } from '@/api/system/couponExperts'
export default {
  name: 'CouponExpertsEdit',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    const validateAccount = (rule, value, callback) => {
      var reg = /^[a-zA-Z0-9]+$/
      if (!value) {
        callback(new Error('请输入登录账号'))
      } else if (!reg.test(value)) {
        callback(new Error('只能输入大小写字母和数字'))
      } else {
        callback()
      }
    }
    const validatePwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入登录密码'))
      } else if (/[\u4E00-\u9FA5]/g.test(value)) {
        callback(new Error('不能包含中文!'))
      } else if (value.length < 6 || value.length > 20) {
        callback(new Error('密码长度限制6-20个字符!'))
      } else {
        callback()
      }
    }
    return {
      title: '新增',
      // 表单参数
      form: {
        loginAccount: null,
        nickName: null,
        loginPwd: null,
        authExpireTime: null
      },
      // 表单校验
      rules: {
        loginAccount: [
          { required: true, validator: validateAccount, trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '请输入昵称', trigger: 'blur' }
        ],
        loginPwd: [
          { required: true, validator: validatePwd, trigger: 'blur' }
        ],
        authExpireTime: [
          { required: true, message: '请选择授权过期时间', trigger: 'change' }
        ]
      },
      submitLoding: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  created() {
    if (this.formData && this.formData.id) {
      this.form = { ...this.formData }
      this.form.loginPwd = '******'
      this.title = '修改'
    }
  },
  methods: {
    cancel() {
      this.dialogVisible = false
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const data = {
            ...this.form
          }
          if (this.form.id) {
            data['id'] = this.form.id
          }
          this.submitLoding = true
          addUpdate(data)
            .then((response) => {
              if (this.form.id) {
                this.$modal.msgSuccess('修改成功')
              } else {
                this.$modal.msgSuccess('新增成功')
              }
              this.dialogVisible = false
              this.$emit('Refresh', true)
            })
            .finally(() => {
              this.submitLoding = false
            })
        }
      })
    }
  }
}
</script>
<style lang="scss">

</style>
