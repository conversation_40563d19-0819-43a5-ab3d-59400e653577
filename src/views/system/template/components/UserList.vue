<template>
  <div>
    <div
      v-for="(_, i) in users"
      :key="i"
      class="list-item"
    >
      <el-input
        v-model.trim="users[i]"
        class="form-control flex1"
        placeholder="请输入抖音号"
        clearable
      />
      <el-button size="mini" :disabled="users.length === 0" type="text" style="color: #ff4949;margin:0 10px" @click="removeUser(i)">
        <i class="el-icon-delete" />
      </el-button>
    </div>
    <div class="action-btn">
      <el-button class="flex1 add-btn" type="primary" plain @click="addUser">+ 新增</el-button>
    </div>
  </div>
</template>

<script>

export default {
  name: 'UserInputList',

  model: {
    prop: 'list',
    event: 'change'
  },
  props: {
    list: {
      type: Array,
      default: () => ([])
    }
  },
  data: () => ({
    users: [],
    timer: null,
    isRepetitive: false
  }),
  watch: {
    list: {
      handler(val) {
        this.users = val
      },
      deep: true
    },
    users: {
      handler(val) {
        this.timer && clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          this.$emit('change', val)
        }, 300)
      },
      deep: true
    }
  },
  mounted() {
    this.users = this.list
  },
  methods: {
    addUser() {
      this.users.push('')
    },
    removeUser(i) {
      this.users.splice(i, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.users-main-item {
  margin-right: 12px;
}
.users-item {
  margin-bottom: 10px;
}
.action-btn {
  margin-top: 10px;
  display: flex;
}
.upload-btn {
  margin-left: 10px;
}

.list-item {
  display: flex;
  align-items: center;
  border-radius: 5px;
  margin-bottom: 8px;
  &:last-of-type {
    margin-bottom: 0;
  }
  .form-control {
    margin-right: 10px;
  }
}

.handle {
  float: left;
  font-size: 16px;
  padding: 5px;
  cursor: move;
}
</style>
