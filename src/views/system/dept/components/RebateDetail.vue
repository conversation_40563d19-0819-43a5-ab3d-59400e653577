<script setup>
import { computed, onMounted, ref } from 'vue'
import { commissionStatus } from '@/config/commission'
const props = defineProps({
  timeList: {
    type: Array,
    default: () => []
  }
})
const listData = computed(() => props.timeList)
const emit = defineEmits(['submit'])
function submit() {
  emit('submit')
}
const activeNames = ref([])

onMounted(() => {
  props.timeList.forEach(item => {
    activeNames.value.push(item.name)
  })
})
function handleChange(val) {
  // console.log(val)
}
</script>

<template>
  <div>
    <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item v-for="(item,index) in listData" :key="index" :title="item.title" :name="item.name">
        <el-form label-width="68px">
          <div class="rate-list">
            <el-form-item label="佣金模式">
              <el-tag type="primary">{{ commissionStatus[item.commissionStatus] }}</el-tag>
            </el-form-item>
            <el-form-item v-if="item.incrementSubsidyRate" label="增量补贴">
              {{ item.incrementSubsidyRate }}%
            </el-form-item>
            <el-form-item v-if="item.remark" label="备注">
              <el-input v-model="item.remark" type="textarea" disabled :rows="2" />
            </el-form-item>
            <div
              v-for="(comm, i) in item.userCommission"
              :key="i"
              class="list-item"
            >
              <template v-if="comm.stepEnd !== 0">
                <div class="step-item">{{ comm.stepStart }}</div>
                ~
                <div class="step-item">{{ comm.stepEnd }}</div>
                元时
              </template>
              <template v-else>
                大于
                <div v-if="item.userCommission.length >= 1" class="step-item">
                  {{ comm.stepStart }}
                </div>
                <div v-else class="step-item">{{ comm.stepEnd }}</div>
                元时
              </template>
              , {{ item.commissionStatus === 2 ? '收佣为' : '返点为' }}
              <div class="step-item">{{ comm.commissionRate }}%</div>
            </div>
            <div
              v-if="item.userCommission.length === 0"
              class="list-item text-center"
            >
              未配置
            </div>
          </div>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    <div class="dialog-footer-right">
      <el-button type="primary" @click="submit">确 认</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
.rate-list {
  border: 1px solid #dfe4ed;
  // border-top: none;
  padding: 10px;
  .list-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    &:last-of-type {
      margin-bottom: 0;
    }
    .step-item {
      padding: 5px 10px;
      text-align: center;
      font-weight: bold;
      color: #1890ff;
    }
  }
}
</style>
