<template>
  <el-dialog title="批量返点" :visible.sync="open" width="700px" top="15vh" append-to-body>
    <div class="second-depts-container">
      <div class="second-depts-title">已选{{ selectedDepts.length > 0 ? ` ${selectedDepts.length} 个` : '' }}部门：
        <span v-if="selectedDepts.length === 0">
          全部
        </span>
      </div>
      <div class="second-depts-list">
        <el-tag
          v-for="tag in selectedDepts"
          :key="tag.deptId"
          closable
          type="primary"
          @close="handleRemoveDept(tag)"
        >
          {{ tag.deptName }}
        </el-tag>
      </div>

    </div>
    <commission-rate-tabs :time-list.sync="timeList" />
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { batchRateEdit } from '@/api/system/dept'
import CommissionRateTabs from '@/views/system/user/components/CommissionRateTabs.vue'
import dayjs from 'dayjs'
import { Message } from 'element-ui'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  depts: {
    type: Array,
    default: () => []
  }
})

const open = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

const emit = defineEmits(['update:visible', 'submit'])

const loading = ref(false)
const timeList = ref([])

const selectedDepts = ref([])

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置rateData为默认值
    timeList.value = [{
      title: dayjs(new Date()).format('YYYY-MM-DD'),
      stepStartTime: dayjs(new Date()).format('YYYY-MM-DD'),
      name: '1',
      incrementSubsidyRate: 0,
      userCommission: [{
        stepStart: 0,
        stepEnd: 0,
        commissionRate: 0
      }],
      commissionStatus: 0
    }]
    // 初始化已选部门
    selectedDepts.value = [...props.depts]
  }
})

// 删除部门
const handleRemoveDept = (dept) => {
  selectedDepts.value = selectedDepts.value.filter(item => item.deptId !== dept.deptId)
}

const handleSubmit = () => {
  loading.value = true
  // TODO: 调用批量返点API
  batchRateEdit({
    timeList: timeList.value.map((item, i) => {
      const temp = {
        stepStartTime: dayjs(item.stepStartTime).format('YYYY-MM-DD HH:mm:ss'),
        userCommission: item.userCommission,
        commissionStatus: item.commissionStatus,
        incrementSubsidyRate: item.incrementSubsidyRate,
        remark: item.remark
      }
      if (i < timeList.value.length - 1) {
        temp.stepEndTime = dayjs(timeList.value[i + 1].stepStartTime).format('YYYY-MM-DD HH:mm:ss')
      }
      return temp
    }),
    deptIds: selectedDepts.value.map(item => item.deptId)
  }).then(res => {
    if (res.code === 200 && !res.msg) {
      Message.success('批量修改返点成功')
      open.value = false
    } else {
      Message.error(res.msg || '批量修改返点失败')
    }
  }).finally(() => {
    loading.value = false
  })
}

const handleCancel = () => {
  open.value = false
}
</script>

<style lang="scss" scoped>
.second-depts-container {
  margin-bottom: 10px;

  .second-depts-title {
    margin-bottom: 10px;
    font-weight: bold;
  }

  .second-depts-list {
    display: flex;
    flex-wrap: wrap;
    max-height: 200px;
    overflow-y: auto;
  }

  .el-tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }
}
</style>
