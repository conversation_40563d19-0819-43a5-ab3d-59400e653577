<template>
  <div>
    <el-button type="primary" :disabled="!selectedDepts.length" size="mini" @click="handleBatchEdit">批量修改</el-button>
    <div v-if="deptNamesNotFind.length > 0" class="mb-2">
      未查找到以下部门：
      <el-tag v-for="name in deptNamesNotFind" :key="name" type="danger">{{ name }}</el-tag>
    </div>
    <el-table
      :data="depts"
      height="500"
      style="width: 100%"

      stripe
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column label="部门名称" prop="deptName">
        <!-- <template #default="{ row }">
          <BaseInfoCell :id="row.deptId" class="info-wrap" :name="row.deptName" />
        </template> -->
      </el-table-column>
      <el-table-column label="上级部门" prop="parentName" />
      <el-table-column label="佣金模式" prop="commissionStatus">
        <template #default="{ row }">
          <el-tag type="primary">{{ commissionStatus[row.commissionStatus] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="生效开始时间" prop="stepStartTime" width="100">
        <template slot-scope="{row}">
          {{ row.stepStartTime ? row.stepStartTime.split(' ')[0] : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="增量补贴比例" width="100">
        <template slot-scope="{row}">
          <span :style="{color: row.incrementSubsidyRate>0?'#409EFF':'#333'}">
            {{ row.incrementSubsidyRate !== undefined ? row.incrementSubsidyRate + '%' : '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="佣金比例" prop="userCommissionStr" width="200" show-overflow-tooltip>
        <template slot-scope="{row}">
          {{ row.userCommissionStr }}
          <el-popover
            width="400"
            trigger="hover"
          >
            <div
              v-for="(comm, i) in row.userCommission"
              :key="i"
              class="list-item"
            >
              <template v-if="comm.stepEnd !== 0">
                <div class="step-item">{{ comm.stepStart }}</div>
                ~
                <div class="step-item">{{ comm.stepEnd }}</div>
                元时
              </template>
              <template v-else>
                大于
                <div v-if="row.userCommission.length>=1" class="step-item"> {{ comm.stepStart }}</div>
                <div v-else class="step-item">{{ comm.stepEnd }}</div>
                元时
              </template>
              , {{ comm.commissionStatus === 2 ? '收佣为' : '返点为' }}
              <div class="step-item"> {{ comm.commissionRate }}%</div>
            </div>
            <i slot="reference" class="el-icon-info color-primary" />
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" show-overflow-tooltip />
      <el-table-column label="操作" prop="action">
        <template #default="{ row }">
          <el-button type="primary" size="mini" @click="handleEdit(row)">修改返点</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { commissionStatus } from '@/config/commission'
const props = defineProps({
  depts: {
    type: Array,
    default: () => []
  },
  deptNamesNotFind: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['edit', 'batchEdit'])

const handleEdit = (row) => {
  emit('edit', row)
}

const selectedDepts = ref([])
const handleSelectionChange = (val) => {
  selectedDepts.value = val
}
const handleBatchEdit = () => {
  emit('batchEdit', selectedDepts.value)
}
</script>

<style lang="scss" scoped>
.mb-2 {
  margin-bottom: 10px;
}
  .list-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    &:last-of-type {
      margin-bottom: 0;
    }
    .step-item {
      padding: 5px 10px;
      text-align: center;
      font-weight: bold;
      color: #1890ff;
    }
  }

</style>
