<script setup>
import { computed, onMounted, ref, watch, getCurrentInstance } from 'vue'
import { getTopDepartmentId, deptTransfer } from '@/api/system/dept'
const props = defineProps({
  value: {
    type: Boolean,
    default: false
  },
  // 所有部门
  deptList: {
    type: Array,
    default: () => []
  }
})
const { proxy } = getCurrentInstance()
const dialogVisible = computed({
  get: () => props.value,
  set: () => emit('input', false)
})
const rules = ref({
  transfereeId: [
    { required: true, message: '请选择', trigger: 'change' }
  ],
  transferorId: [
    { required: true, message: '请选择', trigger: 'change' }
  ]
})
const emit = defineEmits(['input', 'Refresh'])
const dataForm = ref({
  transfereeId: '',
  transferorId: ''
})
const ruleForm = ref()
const transOptions = ref([])
const transOptions2 = ref([])
const submitLoading = ref(false)
// 表单提交校验
function handleSubmit() {
  ruleForm.value.validate(valid => {
    if (valid) {
      submitLoading.value = true
      deptTransfer({
        transfereeId: dataForm.value.transfereeId,
        transferorId: dataForm.value.transferorId
      })
        .then((response) => {
          proxy.$modal.msgSuccess('移交成功!')
          dialogVisible.value = false
          emit('Refresh')
        })
        .finally(() => {
          submitLoading.value = false
        })
    }
  })
}
function getTopDepartmentIdHandle() {
  getTopDepartmentId().then(res => {
    transOptions.value = res.data
    transOptions2.value = res.data
  })
}
onMounted(() => {
  getTopDepartmentIdHandle()
})
</script>

<template>
  <div>
    <el-dialog
      title="移交部门"
      :visible.sync="dialogVisible"
      width="500px"
      append-to-body
    >
      <el-form ref="ruleForm" :model="dataForm" :rules="rules" label-width="120px">

        <el-form-item label="需移交部门：" prop="transfereeId">
          <el-select v-model="dataForm.transfereeId" filterable placeholder="请选择">
            <el-option
              v-for="item in transOptions"
              :key="item.deptId"

              :label="item.deptName"
              :value="item.deptId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="移交到新部门：" prop="transferorId">
          <el-select v-model="dataForm.transferorId" filterable placeholder="请选择">
            <el-option
              v-for="item in transOptions2"
              :key="item.deptId"
              :label="item.deptName"
              :value="item.deptId"
            />
          </el-select>
        </el-form-item>
        <div class="dialog-footer">
          <el-button
            :loading="submitLoading"
            type="primary"
            @click="handleSubmit"
          >确认</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </el-form>
    </el-dialog>

  </div>
</template>

<style scoped lang="scss">
.dialog-footer{
  text-align: right;
}
</style>
