<template>
  <!-- 授权用户 -->
  <el-dialog title="选择用户" :visible.sync="visible" width="900px" top="5vh" append-to-body>
    <el-form ref="queryForm" :model="queryParams" size="small" :inline="true">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入手机号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table ref="table" v-loading="loading" :data="userList" height="535px" row-key="userId" @row-click="clickRow" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column label="用户名称" prop="userName" :show-overflow-tooltip="true" />
        <el-table-column label="用户昵称" prop="nickName" :show-overflow-tooltip="true" />
        <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
        <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { saveNoticeBusinessJoin, selectUserNotice, selectUserNoticeCheck } from '@/api/system/noticeGroup'

export default {
  dicts: ['sys_normal_disable'],
  props: {
    // 角色编号
    noticeGroup: {
      type: Object
    }
  },
  emits: ['ok', 'select'],
  data() {
    return {
      type: 'edit',
      loading: false,
      // 遮罩层
      visible: false,
      // 选中数组值
      userIds: [],
      selectedList: [],
      // 总条数
      total: 0,
      // 未授权用户数据
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeGroupId: undefined,
        userName: undefined,
        phoneNumber: undefined
      }
    }
  },
  methods: {
    // 显示弹框
    show(id) {
      this.resetData()
      this.type = 'edit'
      this.visible = true
      this.loading = true
      this.queryParams.noticeGroupId = id
      Promise.all([selectUserNotice(this.queryParams), selectUserNoticeCheck({ noticeGroupId: id })]).then(res => {
        this.userList = res[0].rows
        this.total = res[0].total
        this.selectedList = res[1].data
        this.handleSelectedList()
      }).finally(() => {
        this.loading = false
      })
    },
    showSelect(selectedList = []) {
      this.resetData()
      this.type = 'select'
      this.selectedList = [...selectedList]
      this.visible = true
      this.loading = true
      selectUserNotice(this.queryParams).then(res => {
        this.userList = res.rows
        this.total = res.total
        this.handleSelectedList()
      }).finally(() => {
        this.loading = false
      })
    },
    resetData() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        noticeGroupId: 0,
        userName: undefined,
        phoneNumber: undefined
      }
      this.selectedList = []
      this.userIds = []
      this.total = 0
      this.$nextTick(() => {
        this.$refs.table.clearSelection()
      })
    },
    handleSelectedList() {
      if (!this.selectedList.length) return
      this.$nextTick(() => {
        this.userList.forEach(item => {
          const index = this.selectedList.findIndex(s => s === item.userId)
          if (~index) {
            this.$refs.table.toggleRowSelection(item)
            this.selectedList.splice(index, 1)
          }
        })
      })
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userIds = selection.map(item => item.userId)
    },
    // 查询表数据
    getList() {
      this.loading = true
      selectUserNotice(this.queryParams).then(res => {
        this.userList = res.rows
        this.handleSelectedList()
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 选择授权用户操作 */
    handleSelectUser() {
      // if (!this.userIds.length) {
      //   this.$modal.msgError('请选择要分配的用户')
      //   return
      // }
      this.userIds.push(...this.selectedList)
      if (this.type === 'select') {
        this.$emit('select', this.userIds)
        this.visible = false
        return
      }
      saveNoticeBusinessJoin({ noticeGroupId: this.queryParams.noticeGroupId, businessIds: this.userIds, businessType: 1 }).then(res => {
        this.$modal.msgSuccess(res.msg)
        if (res.code === 200) {
          this.visible = false
          this.$emit('ok')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-top:10px ;
  padding-bottom:10px ;
}
</style>
