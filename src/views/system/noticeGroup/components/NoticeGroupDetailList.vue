<script setup>
import { ref, computed } from 'vue'
import { Message } from 'element-ui'
import { getDicts } from '@/api/system/dict/data'
import { validEmail } from '@/utils/validate'
import { testNoticeMessage } from '@/api/system/noticeGroup'

const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['update:value'])
const noticeTypes = ref([])
getDicts('notice_group_type').then(response => {
  noticeTypes.value = response.data.map(item => ({
    label: item.dictLabel,
    value: +item.dictValue
  }))
  addType.value = noticeTypes.value[0].value
})

const noticeMap = computed(() => {
  return props.list.reduce((acc, cur, i) => {
    if (!acc[cur.noticeType]) { acc[cur.noticeType] = [] }
    cur.i = i
    acc[cur.noticeType].push(cur)
    return acc
  }, {})
})

const phoneRules = [
  { trigger: 'blur', validator: (rule, value, callback) => {
    if (value) {
      if (value.toString().length !== 11) {
        callback(new Error('手机号非11位'))
      } else {
        callback()
      }
    } else {
      callback(new Error('手机号不能为空'))
    }
  } }
]
const requiredRules = [
  { trigger: 'blur', required: true, message: '该值不能为空' }
]
const emailRules = [
  { trigger: 'blur', validator: (rule, value, callback) => {
    if (value) {
      if (validEmail(value)) {
        callback()
      } else {
        callback(new Error('邮箱格式不正确'))
      }
    } else {
      callback(new Error('邮箱不能为空'))
    }
  } }
]

const keyMap = {
  1: 'phoneNumber',
  2: ['token', 'appSecret'],
  3: 'mailAdd',
  4: ['webhook', 'sign'],
  6: 'token'
}
const addType = ref('')
const addNotice = () => {
  const type = +addType.value
  if (type === 1) {
    if (noticeMap.value[1] && noticeMap.value[1].length >= 5) {
      Message.warning('短信通知最多设置5个')
      return
    }
  }
  const notice = {
    noticeType: type
  }
  if (Array.isArray(keyMap[type])) {
    keyMap[type].forEach(key => {
      notice[key] = ''
    })
  } else {
    notice[keyMap[type]] = ''
  }
  emit('update:list', [...props.list, notice])
}

const testNotice = (notice, type) => {
  if (type === 6 && !notice.token) {
    Message.warning('请输入企业微信群机器人Key')
    return
  }
  if (type === 2 || type === 4) {
    if (!notice.token || !notice.appSecret) {
      Message.warning('请填写完整信息')
      return
    }
  }

  testNoticeMessage({
    noticeType: type,
    token: notice.token,
    appSecret: notice.appSecret
  }).then(response => {
    if (response.code === 200) { Message.success('发送成功，请到目标应用中查看消息') }
  })
}

const removeNotice = (notice) => {
  const list = [...props.list]
  const index = list.findIndex(item => item === notice)
  list.splice(index, 1)
  emit('update:list', list)
}

const authList = [2, 4, 6]
function handelDocument(type) {
  switch (type) {
    case 2:
      window.open('https://ziuqxc1rltx.feishu.cn/docx/YyYmdilb6odXkExZoxtc2gF9nmh')
      break
    case 4:
      window.open('https://ziuqxc1rltx.feishu.cn/docx/DNDndv1bxoOpGPxQTJBcXfHnnsb')
      break
    case 6:
      window.open('https://open.work.weixin.qq.com/help2/pc/14931?is_tencent=0&version=4.0.12.6015&platform=win')
      break
    default:

      break
  }
}
const onInput = (val, index, event) => {
  if (val === 2) {
    // 钉钉群通知
    if (event.match(/\?access_token=/)) {
      const arr = event.split('access_token=')
      if (arr.length > 1) {
        noticeMap.value[val][index].token = arr[arr.length - 1]
      }
    }
  } else if (val === 4) {
    // 飞书群通知
    if (event.match(/hook\//)) {
      const arr = event.split('hook/')
      if (arr.length > 1) {
        noticeMap.value[val][index].token = arr[arr.length - 1]
      }
    }
  } else if (val === 6) {
    // 企业微信群通知
    if (event.match(/\?key=/)) {
      const arr = event.split('?key=')
      if (arr.length > 1) {
        noticeMap.value[val][index].token = arr[arr.length - 1]
      }
    }
  }
}
</script>

<template>
  <div class="search-form-thin">
    <el-form-item label="通知组">
      <el-select v-model="addType" size="small">
        <el-option
          v-for="item in noticeTypes"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button type="primary" style="margin-left: 10px" size="small" @click="addNotice">
        <i class="el-icon-plus" />
        新增
      </el-button>
    </el-form-item>
    <template v-for="type in noticeTypes">
      <div v-if="noticeMap[type.value] && noticeMap[type.value].length" :key="type.value">
        <el-divider content-position="left">
          <i v-if="type.value===1" class="el-icon-mobile" />
          <svg-icon v-else-if="type.value===2" icon-class="dingding" />
          <i v-else-if="type.value===3" class="el-icon-message" />
          <svg-icon v-else-if="type.value===4" icon-class="feishu" />
          <svg-icon v-else-if="type.value===6" icon-class="wework" />
          {{ type.label }}
          <el-tooltip class="item" effect="dark" content="跳转授权说明文档地址" placement="top">
            <i v-if="authList.includes(type.value)" class="el-icon-question ml10 documentTips" @click="handelDocument(type.value)" />
          </el-tooltip>

        </el-divider>
        <template v-for="(notice,i) in noticeMap[type.value]">
          <!--     noticeGroupDetailList此字段在父组件中的el-form生效     -->
          <div :key="i+type.value" class="notice-item">
            <el-form-item v-if="type.value === 1" class="flex1" :rules="phoneRules" :prop="'noticeGroupDetailList.' + notice.i + '.phoneNumber'">
              <el-input v-model.number="notice.phoneNumber" placeholder="请输入手机号码" />
            </el-form-item>
            <template v-else-if="type.value === 2">
              <el-form-item class="flex1" :rules="requiredRules" :prop="'noticeGroupDetailList.' + notice.i + '.token'">
                <el-input v-model="notice.token" class="flex1" placeholder="请输入钉钉的token" @input="onInput(type.value,i, $event)" />
              </el-form-item>
              <el-form-item class="flex1" :rules="requiredRules" :prop="'noticeGroupDetailList.' + notice.i + '.appSecret'">
                <el-input v-model="notice.appSecret" style="width: 100%" placeholder="请输入钉钉密钥" />
              </el-form-item>
            </template>
            <el-form-item v-else-if="type.value === 3" class="flex1" :rules="emailRules" :prop="'noticeGroupDetailList.' + notice.i + '.mailAdd'">
              <el-input v-model="notice.mailAdd" placeholder="请输入邮箱" />
            </el-form-item>
            <template v-else-if="type.value === 4">
              <el-form-item class="flex1" :rules="requiredRules" :prop="'noticeGroupDetailList.' + notice.i + '.token'">
                <el-input v-model="notice.token" class="flex1" placeholder="请输入飞书的webhook token" @input="onInput(type.value, i,$event)" />
              </el-form-item>
              <el-form-item class="flex1" :rules="requiredRules" :prop="'noticeGroupDetailList.' + notice.i + '.appSecret'">
                <el-input v-model="notice.appSecret" class="flex1" placeholder="请输入飞书的签名" />
              </el-form-item>
            </template>
            <template v-else-if="type.value === 6">
              <el-form-item class="flex1" :rules="requiredRules" :prop="'noticeGroupDetailList.' + notice.i + '.token'">
                <el-input v-model="notice.token" placeholder="请输入企业微信群机器人Key" @input="onInput(type.value,i, $event)" />
              </el-form-item>
            </template>
            <el-button v-if="[2,4,6].includes(type.value)" size="mini" type="text" class="notice-icon" @click="testNotice(notice,type.value)">
              <el-tooltip class="item" effect="dark" content="发送测试消息" placement="top">
                <i class="el-icon-s-promotion color-primary ml10" />
              </el-tooltip>
            </el-button>
            <el-button size="mini" type="text" class="notice-icon text-danger" @click="removeNotice(notice)">
              <i class="el-icon-delete" />
            </el-button>
          </div>
        </template>
      </div>
    </template>

  </div>
</template>

<style scoped lang="scss">

.notice-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap:10px
}
.notice-icon {
  font-size: 18px;
}
::v-deep .notice-item .el-form-item {
  margin-bottom: 0;
}

</style>
