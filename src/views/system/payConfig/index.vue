<template>
  <div class="adaption-container">
    <el-form v-show="showSearch" ref="queryForm" class="search-form" :model="queryParams" size="small" :inline="true" label-width="100px">
      <el-form-item prop="configName">
        <el-input
          v-model.trim="queryParams.configName"
          placeholder="配置名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="firstDeptId">
        <DeptTreeSelect v-model="queryParams.firstDeptId" :options="deptOptions" :select-level="1" />
      </el-form-item>
      <el-form-item prop="maAppId">
        <el-input
          v-model.trim="queryParams.maAppId"
          placeholder="小程序AppID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="mpAppId">
        <el-input
          v-model.trim="queryParams.mpAppId"
          placeholder="公众号AppID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="wechatPayMchId">
        <el-input
          v-model.trim="queryParams.wechatPayMchId"
          placeholder="商户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="list"
        row-key="id"
        border
        stripe
        @header-dragend="handleHeaderDragend"
      >
        <template v-for="c in columns">
          <el-table-column v-if="c.prop === 'action'" :key="c.prop" label="操作" width="180" sortable>
            <template #default="scope">
              <el-button type="text" size="mini" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button type="text" size="mini" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <CustomTableColumn v-else :key="c.prop" :data="c" :sortable="c.sortable" />
        </template>
      </el-table>
    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改支付配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" top="5vh" append-to-body>
      <el-form v-if="open" ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="是否默认配置" prop="defaultConfiguration">
          <el-switch v-model="form.defaultConfiguration" />
        </el-form-item>
        <el-form-item label="公司" prop="firstDeptId">
          <DeptTreeSelect
            v-model="form.firstDeptId"
            :is-search="false"
            :multiple="false"
            placeholder="请选择公司"
            :options="deptOptions"
            :select-level="1"
            @select="handleDeptSelect"
          />
        </el-form-item>
        <el-form-item label="生效部门" prop="effectiveDept">
          <DeptTreeSelect v-model="form.effectiveDept" :is-search="false" :disabled="!form.firstDeptId" :options="selectLeaves" />
        </el-form-item>
        <el-form-item label="微信小程序AppID" prop="maAppId">
          <el-input v-model="form.maAppId" placeholder="请输入微信小程序AppID" />
        </el-form-item>
        <el-form-item label="微信小程序Secret" prop="maAppSecret">
          <el-input v-model="form.maAppSecret" placeholder="请输入微信小程序AppSecret" />
        </el-form-item>
        <el-form-item label="微信公众号AppID" prop="mpAppId">
          <el-input v-model="form.mpAppId" placeholder="请输入微信公众号AppID" />
        </el-form-item>
        <el-form-item label="微信公众号Secret" prop="mpAppSecret">
          <el-input v-model="form.mpAppSecret" placeholder="请输入微信公众号AppSecret" />
        </el-form-item>
        <el-form-item label="微信支付商户ID" prop="wechatPayMchId">
          <el-input v-model="form.wechatPayMchId" placeholder="请输入微信支付商户ID" />
        </el-form-item>
        <el-form-item label="微信支付v3密钥" prop="wechatPayApiV3Key">
          <el-input v-model="form.wechatPayApiV3Key" placeholder="请输入微信支付v3密钥" />
        </el-form-item>
        <el-form-item label="证书序列号" prop="wechatPayCertSerialNo">
          <el-input v-model="form.wechatPayCertSerialNo" placeholder="请输入微信支付证书序列号" />
        </el-form-item>
        <el-form-item label="微信支付公钥ID" prop="publicKeyId">
          <el-input v-model="form.publicKeyId" placeholder="请输入微信支付公钥ID" />
        </el-form-item>
        <el-form-item label="微信支付公钥内容" prop="publicKeyContent">
          <el-input v-model="form.publicKeyContent" type="textarea" rows="5" placeholder="请输入微信支付公钥内容" />
        </el-form-item>
        <el-form-item label="微信支付私钥key内容" prop="wechatPayPrivateKeyContent">
          <el-input v-model="form.wechatPayPrivateKeyContent" type="textarea" rows="5" placeholder="请输入微信支付私钥key内容" />
        </el-form-item>
        <el-form-item label="私钥证书内容" prop="wechatPayPrivateCertContent">
          <el-input v-model="form.wechatPayPrivateCertContent" type="textarea" rows="5" placeholder="请输入微信支付私钥证书内容" />
        </el-form-item>
        <el-form-item label="微信服务号域名" prop="domainName">
          <el-input v-model="form.domainName" placeholder="请输入微信服务号域名" />
        </el-form-item>
        <el-form-item label="是否开启分账" prop="shareConfig.open">
          <el-switch v-model="form.shareConfig.open" />
        </el-form-item>
        <div v-if="form.shareConfig.open">
          <el-form-item label="分账配置">
            <div v-for="(item, index) in form.shareConfig.items" :key="index" class="share-config-item">
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item :prop="`shareConfig.items.${index}.merchantId`" :rules="shareConfigRules.merchantId">
                    <el-input v-model="item.merchantId" placeholder="商户ID" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :prop="`shareConfig.items.${index}.merchantName`" :rules="shareConfigRules.merchantName">
                    <el-input v-model="item.merchantName" placeholder="商户名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :prop="`shareConfig.items.${index}.shareRatio`" :rules="shareConfigRules.shareRatio">
                    <el-input-number
                      v-model="item.shareRatio"
                      :min="0"
                      :max="30"
                      :precision="2"
                      placeholder="分账比例"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="form.shareConfig.items.length <= 1"
                    @click="removeShareConfigItem(index)"
                  >删除</el-button>
                </el-col>
              </el-row>
            </div>
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="addShareConfigItem">添加分账配置</el-button>
            <div class="share-config-tip">
              <el-alert
                title="注意：所有分账比例加起来不得超过30%"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPayConfig, addOrUpdatePayConfig, deletePayConfig } from '@/api/promotion/payConfig'
import { mapGetters } from 'vuex'

export default {
  name: 'PayConfig',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 支付配置列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: '',
        firstDeptId: null,
        maAppId: '',
        mpAppId: '',
        wechatPayMchId: ''
      },
      // 表单参数
      form: {
        shareConfig: {}
      },
      selectDept: [],
      // 表单校验
      rules: {
        configName: [
          { required: true, message: '配置名称不能为空', trigger: 'blur' }
        ],
        firstDeptId: [
          { required: true, message: '公司不能为空', trigger: 'change' }
        ]
      },
      // 分账配置验证规则
      shareConfigRules: {
        merchantId: [
          { required: true, message: '商户ID不能为空', trigger: 'blur' }
        ],
        merchantName: [
          { required: true, message: '商户名称不能为空', trigger: 'blur' }
        ],
        shareRatio: [
          { required: true, message: '分账比例不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error('分账比例必须大于0'))
              } else if (value > 30) {
                callback(new Error('单个分账比例不能超过30%'))
              } else {
                // 检查总比例是否超过30
                const totalRatio = this.form.shareConfig.items.reduce((sum, item) => sum + (item.shareRatio || 0), 0)
                if (totalRatio > 30) {
                  callback(new Error('所有分账比例总和不能超过30%'))
                }
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'device',
      'tableHeight'
    ]),
    selectLeaves() {
      return this.selectDept?.leaves ?? []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询支付配置列表 */
    getList() {
      this.loading = true
      listPayConfig(this.queryParams).then(response => {
        this.list = response.rows || []
        this.total = response.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        configName: '',
        defaultConfiguration: false,
        firstDeptId: null,
        effectiveDept: '',
        maAppId: '',
        maAppSecret: '',
        mpAppId: '',
        mpAppSecret: '',
        wechatPayMchId: '',
        wechatPayApiV3Key: '',
        wechatPayCertSerialNo: '',
        publicKeyId: '',
        publicKeyContent: '',
        wechatPayPrivateKeyContent: '',
        wechatPayPrivateCertContent: '',
        shareConfig: {
          open: false,
          items: [
            {
              merchantId: '',
              merchantName: '',
              shareRatio: 0
            }
          ]
        }
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        configName: '',
        firstDeptId: null,
        maAppId: '',
        mpAppId: '',
        wechatPayMchId: ''
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加企业支付配置'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const form = JSON.parse(JSON.stringify(row))
      if (!form.shareConfig) {
        form.shareConfig = {
          open: false,
          items: []
        }
      }
      this.selectDept = this.findSelectDept(form.firstDeptId, this._setupProxy.deptOptions)
      this.$set(this, 'form', form)
      this.open = true
      this.title = '修改企业支付配置'
    },
    findSelectDept(id, options) {
      for (const element of options) {
        if (element.id === id) {
          return element
        } else if (element.children) {
          const result = this.findSelectDept(id, element.children)
          if (result) {
            return result
          }
        }
      }
      return null
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.loading = true
          addOrUpdatePayConfig(this.form).then(response => {
            this.$modal.msgSuccess('操作成功')
            this.open = false
            this.getList()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('确认删除该支付配置吗?', '警告', {
        type: 'warning'
      }).then(() => {
        this.loading = true
        return deletePayConfig(row.id)
      }).then(() => {
        this.loading = false
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
        this.loading = false
      })
    },
    /** 表头拖拽事件 */
    handleHeaderDragend(newWidth, oldWidth, column) {
      this.columnsInstance.handleHeaderDragend(newWidth, oldWidth, column)
    },
    addShareConfigItem() {
      this.form.shareConfig.items.push({
        merchantId: '',
        merchantName: '',
        shareRatio: 0
      })
    },
    removeShareConfigItem(index) {
      this.form.shareConfig.items.splice(index, 1)
    },
    handleDeptSelect(val) {
      this.selectDept = val
      this.form.effectiveDept = ''
    }
  }
}
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import DeptTreeSelect from '@/components/DeptTreeSelect/DeptTreeSelect.vue'
import useDeptOptions from '@/components/DeptTreeSelect/useDeptOptions'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import useColumns from '@/hooks/useColumns'

const defaultColumns = [
  { label: '配置名称', prop: 'configName', minWidth: '150', sortable: false },
  { label: '是否默认', prop: 'defaultConfiguration', width: '100', sortable: false,
    format: (value) => value ? '是' : '否' },
  { label: '公司', prop: 'firstDeptName', minWidth: '120', sortable: false },
  { label: '小程序AppID', prop: 'maAppId', minWidth: '200', sortable: false },
  { label: '公众号AppID', prop: 'mpAppId', minWidth: '200', sortable: false },
  { label: '商户ID', prop: 'wechatPayMchId', minWidth: '150', sortable: false },
  { label: '操作', prop: 'action', width: '180', sortable: false }
]

const tableRef = ref(null)
const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({ defaultColumns, tableRef })

const deptOptions = ref([])
useDeptOptions(options => {
  options.value.forEach(item => {
    const temp = {
      id: item.id,
      label: item.label,
      leaves: item.children ?? []
    }
    if (item.children) {
      temp.children = item.children.map(c => ({
        id: c.id,
        label: c.label,
        leaves: c.children ?? []
      })
      )
    }
    deptOptions.value.push(temp)
  })
})
</script>

<style lang="scss" scoped>
.table-wrapper {
  margin-top: 20px;
}

.share-config-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}

.share-config-tip {
  margin-top: 10px;
}
</style>
