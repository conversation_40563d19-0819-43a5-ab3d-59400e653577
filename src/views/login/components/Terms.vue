<script setup>
import { computed } from 'vue'
const props = defineProps({
  value: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['input', 'agree'])
const open = computed({
  get: () => props.value,
  set: () => emit('input', false)
})

const agreeTerms = (agree) => {
  open.value = false
  emit('agree', agree)
}
</script>

<template>
  <el-dialog :visible.sync="open" width="50%" top="5vh">
    <h2 slot="title" style="margin: 0">软件免责声明</h2>
    <div class="terms-content">
      <h3 class="terms-item-title" style="margin-top: 0">1.权利归属声明</h3>
      <p class="terms-item-content">
        1.1.除非本平台另行声明，我们在本平台上所生成、制作、推出的所有产品、技术、软件、程序、数据及相关内容信息（包括文字、图片、音视频、网页版面、图表、数据等权利和权益内容）的所有权利（包括知识产权、商业秘密及其他相关权利）均归本平台及/或其关联公司所有。未经本平台及/或其关联公司许可，任何人擅自使用上述内容，均可能会侵犯本平台及/或其关联公司的权利，我们将会追究侵权者的法律责任。
      </p>
      <p class="terms-item-content">
        1.2.本平台上的商标性文字及/或图形标识，服务运营商字号名称等均为本平台及/或其关联公司在中国和其他国家的商标等权利，如有宣传、展示等任何使用需要，您必须取得本平台及/或其关联公司事先书面授权。
      </p>
      <h3 class="terms-item-title">2.内容限制</h3>
      <p class="terms-item-content">
        2.1.为维护本平台的正常运行秩序及效率，未经本平台及/或其关联公司许可，任何人不得自行、授权或协助第三方对本平台及其系统进行地址扫描、网络端口扫描、操作系统探测等扫描及/或探测，或以包括通过机器人、蜘蛛等程序或设备监视、复制、传播、展示、镜像、上载、下载等方式擅自获取、使用本平台内的任何内容。
      </p>
      <p class="terms-item-content">
        2.2.任何人亦不得通过本平台的账号体系的借用、仅凭用户授权等形式，在未经本平台同意的情况下，通过账号体系获取账号体系内的任何资料、信息，特别是涉及到商业秘密、个人信息在内的敏感类信息。
      </p>
      <h3 class="terms-item-title">3.网络生态建设</h3>
      <p class="terms-item-content">
        3.1.为了营造良好网络生态，保障公民、法人和其他组织的合法权益，维护国家安全和公共利益，本平台已充分告知用户在使用本平台时，引用、发布的任何信息，自行销售或为他人销售的任何商品，均需遵守媒体平台、电商平台的相应规定及国家有关法律法规、行政规定。
      </p>
      <p class="terms-item-content">
        3.2.用户对上述告知已充分知悉，并作出如下承诺：在使用本平台时将遵守包括但不限于以下国家法律法规等：《广告法》《互联网广告管理办法》《商标法》《专利法》《著作权法》《电子商务法》《个人信息保护法》《刑法》及媒体平台、电商平台现行的及后续不断修订更新的平台规则和管理办法。
      </p>
      <p class="terms-item-content">
        3.3.如用户违法上述规定，由此产生的全部民事责任、刑事责任均由用户自行承担，如因此导致平台权利受损或遭受处罚的，平台有权向用户追偿。
      </p>
      <h3 class="terms-item-title">4.责任限制</h3>
      <p class="terms-item-content">
        4.1.鉴于本平台为用户提供信息发布、存储等技术服务，除非本平台单独提示或声明，本平台上的店铺、商品、内容信息等均由用户自行提供并上传，用户对其信息承担相应法律责任。本平台转载的内容系出于传递更多信息之目的，并不意味我们赞同其观点或已经证实其内容的真实性。
      </p>
      <p class="terms-item-content">
        4.2.本公司无法全面监控用户运营行为，您从本公司平台购买账户时，同意自行判断并承担所有风险，而不依赖于我司。在任何情况下，我司有权依法停止账户并采取相应行动，包括但不限于对于相关平台进行停用、删除服务的全部或部分、保存有关记录并向有关机关报告。由此对您及第三人可能造成的损失，我司不承担任何直接、间接或者连带的责任。
      </p>
      <p class="terms-item-content">
        4.3.因和我司站点（授权账户或绑定域名）链接的其它网站所造成的个人资料泄露及由此而导致的任何法律争议和后果，我司不承担任何直接、间接或者连带的责任。
      </p>
      <p class="terms-item-content">
        4.4.本平台依照法律规定履行基础保障义务，但对于下述原因导致的合同履行障碍、履行瑕疵、履行延后或履行内容变更等情形，本平台并不承担相应的责任：
      </p>
      <p class="terms-item-content">
        （1）因自然灾害、罢工、暴乱、战争等不可抗力因素；
      </p>
      <p class="terms-item-content">
        （2）因电力供应故障、通讯网络故障等公共服务因素；
      </p>
      <p class="terms-item-content">
        （3）在本平台已尽善意管理的情况下，因常规或紧急的设备与系统维护、设备与系统故障、网络信息与数据安全等因素。
      </p>
      <p class="terms-item-content">
        4.5.任何通过使用本平台而链接、跳转到的第三方网页、程序均系他人制作或提供，您可能从该第三方网页上获得资讯及享用服务，除法律法规另有规定的，本平台对其合法性概不负责，亦不承担法律责任。
      </p>
      <h3 class="terms-item-title">5.知识产权保护</h3>
      <p class="terms-item-content">
        我们尊重知识产权，反对并打击侵犯知识产权的行为。知识产权权利人若认为本平台上的内容（包括但不限于我们的用户发布的各类信息）侵犯其合法权益的，可以通过我们设置的投诉通道进行投诉，我们将在收到知识产权权利人整改通知后依据相应的法律法规以及平台规则及时处理。
      </p>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="agreeTerms(false)">不同意</el-button>
      <el-button type="primary" @click="agreeTerms(true)">同 意</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
