<template>
  <div class="time-background">
    <!-- 模拟登录表单区域 -->
    <div class="login-form-overlay">
      <div class="login-container">
        <div class="brand-section">
          <!-- 动态背景渐变 -->
          <div class="brand-section-background-gradient" :style="gradientStyle" />

          <!-- 粒子效果容器 -->
          <div ref="brandParticlesContainer" class="brand-section-particles-container">
            <canvas ref="brandParticlesCanvas" class="brand-section-particles-canvas" />
          </div>

          <!-- 几何装饰元素 -->
          <div class="brand-section-geometric-decorations">
            <div class="brand-section-geometric-shape brand-shape-1" />
            <div class="brand-section-geometric-shape brand-shape-2" />
            <div class="brand-section-geometric-shape brand-shape-3" />
          </div>

          <div class="brand-section-content-wrapper">
            <div class="logo">
              <img src="@/assets/logo/logo.png" alt="花粉" class="logo-image">
              <h1>花粉</h1>
            </div>
            <div class="brand-content">
              <p class="subtitle">Smart WeChat Marketing</p>
              <h2 class="main-title">聚客户，享直播，智管理</h2>
              <ul class="features">
                <li>智能化企业微信加粉，精准触达目标客户群体</li>
                <li>私域直播营销，打造专属品牌传播矩阵</li>
                <li>企业微信消息自动发送，提升客户服务效率</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="form-container">
            <div class="welcome-header">
              <h3>Welcome to <span class="brand-highlight">{{ name }}</span></h3>
            </div>

            <el-form
              ref="loginForm"
              :model="loginForm"
              :rules="loginRules"
              class="login-form"
              size="small"
            >
              <el-form-item prop="username">
                <div class="form-group-embedded">
                  <span class="embedded-label">账号名</span>
                  <el-input
                    v-model="loginForm.username"
                    type="text"
                    auto-complete="off"
                    placeholder="请输入账号名"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="password">
                <div class="form-group-embedded">
                  <span class="embedded-label">密码</span>
                  <el-input
                    v-model="loginForm.password"
                    type="password"
                    auto-complete="off"
                    placeholder="请输入登录密码"
                    @keyup.enter.native="handleLogin"
                  />
                </div>
              </el-form-item>

              <el-form-item v-if="captchaEnabled" prop="code">
                <div class="form-group-embedded">
                  <span class="embedded-label">验证码</span>
                  <div class="captcha-container">
                    <el-input
                      v-model="loginForm.code"
                      auto-complete="off"
                      placeholder="请输入验证码"
                      @keyup.enter.native="handleLogin"
                    />
                    <div class="login-code">
                      <img :src="codeUrl" class="login-code-img" @click="getCode">
                    </div>
                  </div>
                </div>
              </el-form-item>

              <div class="form-options">
                <span class="checkbox-label">
                  <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
                </span>
                <span class="checkbox-label">
                  <el-checkbox v-model="loginForm.termsOfService">阅读并同意</el-checkbox>
                  <el-button
                    type="text"
                    @click="termsVisible = true"
                  >《用户协议与隐私政策》</el-button>
                </span>
              </div>

              <!--  第三方应用登录 -->
              <!-- <div class="oauth-login" style="margin: 20px 0;">
                <div class="oauth-login-item" @click="doSocialLogin('wechat_open')">
                  <svg-icon icon-class="weixin" class="mr10" style="height: 1.2em" />
                  <span style="color: #50B674;font-size: 14px">微信登录</span>
                </div>
              </div> -->

              <el-button
                :loading="loading"
                size="medium"
                type="primary"
                class="login-btn"
                @click.native.prevent="handleLogin"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>

              <div v-if="register" class="register-link">
                <router-link
                  class="link-type"
                  :to="'/register'"
                >立即注册</router-link>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <!--  底部  -->
    <div v-if="copyright" class="login-footer">
      <span>
        {{ copyright }}
        <a href="https://beian.miit.gov.cn/" target="_blank">({{ icp }})</a>
      </span>
    </div>
    <el-dialog :visible.sync="termsVisible" width="50%" top="5vh">
      <h2 slot="title" style="margin: 0">软件免责声明</h2>
      <div class="terms-content">
        <h3 class="terms-item-title" style="margin-top: 0">1.权利归属声明</h3>
        <p class="terms-item-content">
          1.1.除非本平台另行声明，我们在本平台上所生成、制作、推出的所有产品、技术、软件、程序、数据及相关内容信息（包括文字、图片、音视频、网页版面、图表、数据等权利和权益内容）的所有权利（包括知识产权、商业秘密及其他相关权利）均归本平台及/或其关联公司所有。未经本平台及/或其关联公司许可，任何人擅自使用上述内容，均可能会侵犯本平台及/或其关联公司的权利，我们将会追究侵权者的法律责任。
        </p>
        <p class="terms-item-content">
          1.2.本平台上的商标性文字及/或图形标识，服务运营商字号名称等均为本平台及/或其关联公司在中国和其他国家的商标等权利，如有宣传、展示等任何使用需要，您必须取得本平台及/或其关联公司事先书面授权。
        </p>
        <h3 class="terms-item-title">2.内容限制</h3>
        <p class="terms-item-content">
          2.1.为维护本平台的正常运行秩序及效率，未经本平台及/或其关联公司许可，任何人不得自行、授权或协助第三方对本平台及其系统进行地址扫描、网络端口扫描、操作系统探测等扫描及/或探测，或以包括通过机器人、蜘蛛等程序或设备监视、复制、传播、展示、镜像、上载、下载等方式擅自获取、使用本平台内的任何内容。
        </p>
        <p class="terms-item-content">
          2.2.任何人亦不得通过本平台的账号体系的借用、仅凭用户授权等形式，在未经本平台同意的情况下，通过账号体系获取账号体系内的任何资料、信息，特别是涉及到商业秘密、个人信息在内的敏感类信息。
        </p>
        <h3 class="terms-item-title">3.网络生态建设</h3>
        <p class="terms-item-content">
          3.1.为了营造良好网络生态，保障公民、法人和其他组织的合法权益，维护国家安全和公共利益，本平台已充分告知用户在使用本平台时，引用、发布的任何信息，自行销售或为他人销售的任何商品，均需遵守媒体平台、电商平台的相应规定及国家有关法律法规、行政规定。
        </p>
        <p class="terms-item-content">
          3.2.用户对上述告知已充分知悉，并作出如下承诺：在使用本平台时将遵守包括但不限于以下国家法律法规等：《广告法》《互联网广告管理办法》《商标法》《专利法》《著作权法》《电子商务法》《个人信息保护法》《刑法》及媒体平台、电商平台现行的及后续不断修订更新的平台规则和管理办法。
        </p>
        <p class="terms-item-content">
          3.3.如用户违法上述规定，由此产生的全部民事责任、刑事责任均由用户自行承担，如因此导致平台权利受损或遭受处罚的，平台有权向用户追偿。
        </p>
        <h3 class="terms-item-title">4.责任限制</h3>
        <p class="terms-item-content">
          4.1.鉴于本平台为用户提供信息发布、存储等技术服务，除非本平台单独提示或声明，本平台上的店铺、商品、内容信息等均由用户自行提供并上传，用户对其信息承担相应法律责任。本平台转载的内容系出于传递更多信息之目的，并不意味我们赞同其观点或已经证实其内容的真实性。
        </p>
        <p class="terms-item-content">
          4.2.本公司无法全面监控用户运营行为，您从本公司平台购买账户时，同意自行判断并承担所有风险，而不依赖于我司。在任何情况下，我司有权依法停止账户并采取相应行动，包括但不限于对于相关平台进行停用、删除服务的全部或部分、保存有关记录并向有关机关报告。由此对您及第三人可能造成的损失，我司不承担任何直接、间接或者连带的责任。
        </p>
        <p class="terms-item-content">
          4.3.因和我司站点（授权账户或绑定域名）链接的其它网站所造成的个人资料泄露及由此而导致的任何法律争议和后果，我司不承担任何直接、间接或者连带的责任。
        </p>
        <p class="terms-item-content">
          4.4.本平台依照法律规定履行基础保障义务，但对于下述原因导致的合同履行障碍、履行瑕疵、履行延后或履行内容变更等情形，本平台并不承担相应的责任：
        </p>
        <p class="terms-item-content">
          （1）因自然灾害、罢工、暴乱、战争等不可抗力因素；
        </p>
        <p class="terms-item-content">
          （2）因电力供应故障、通讯网络故障等公共服务因素；
        </p>
        <p class="terms-item-content">
          （3）在本平台已尽善意管理的情况下，因常规或紧急的设备与系统维护、设备与系统故障、网络信息与数据安全等因素。
        </p>
        <p class="terms-item-content">
          4.5.任何通过使用本平台而链接、跳转到的第三方网页、程序均系他人制作或提供，您可能从该第三方网页上获得资讯及享用服务，除法律法规另有规定的，本平台对其合法性概不负责，亦不承担法律责任。
        </p>
        <h3 class="terms-item-title">5.知识产权保护</h3>
        <p class="terms-item-content">
          我们尊重知识产权，反对并打击侵犯知识产权的行为。知识产权权利人若认为本平台上的内容（包括但不限于我们的用户发布的各类信息）侵犯其合法权益的，可以通过我们设置的投诉通道进行投诉，我们将在收到知识产权权利人整改通知后依据相应的法律法规以及平台规则及时处理。
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="agreeTerms(false)">不同意</el-button>
        <el-button type="primary" @click="agreeTerms(true)">同 意</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCodeImg } from '@/api/login'
import { authBinding } from '@/api/system/auth'
import Cookies from 'js-cookie'
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: 'LoginPage',
  data() {
    return {
      codeUrl: '',
      loginForm: {
        username: '',
        password: '',
        rememberMe: false,
        termsOfService: false,
        code: '',
        uuid: ''
      },
      loginRules: {
        username: [
          { required: true, trigger: 'blur', message: '请输入您的账号' }
        ],
        password: [
          { required: true, trigger: 'blur', message: '请输入您的密码' }
        ],
        code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined,
      termsVisible: false,
      isLogin: false,
      // 动画相关
      brandAnimationId: null
    }
  },
  computed: {
    // 动态背景渐变样式（简化为两色渐变）
    gradientStyle() {
      const hour = new Date().getHours()

      // 黎明 (5-8点) - 温柔的粉紫渐变
      if (hour >= 5 && hour < 8) {
        return {
          background: 'linear-gradient(135deg, #f093fb 0%, #667eea 100%)'
        }
      }

      // 上午 (8-11点) - 清新的蓝色渐变
      if (hour >= 8 && hour < 11) {
        return {
          background: 'linear-gradient(135deg, #4facfe 0%, #667eea 100%)'
        }
      }

      // 中午 (11-14点) - 柔和的蓝绿渐变
      if (hour >= 11 && hour < 14) {
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #43a047 100%)'
        }
      }

      // 下午 (14-17点) - 温暖的橙紫渐变
      if (hour >= 14 && hour < 17) {
        return {
          background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
        }
      }

      // 黄昏 (17-19点) - 深邃的紫橙渐变
      if (hour >= 17 && hour < 19) {
        return {
          background: 'linear-gradient(135deg, #8e24aa 0%, #d84315 100%)'
        }
      }

      // 傍晚 (19-22点) - 经典的紫色渐变
      if (hour >= 19 && hour < 22) {
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
      }

      // 夜晚 (22-5点) - 深邃的夜空渐变
      return {
        background: 'linear-gradient(135deg, #2c3e50 0%, #667eea 100%)'
      }
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.getCode()
    this.getCookie()
  },
  mounted() {
    this.initBrandParticles()
  },
  beforeDestroy() {
    if (this.brandAnimationId) {
      cancelAnimationFrame(this.brandAnimationId)
    }
  },
  methods: {
    // 初始化品牌区域粒子效果
    initBrandParticles() {
      if (!this.$refs.brandParticlesCanvas || !this.$refs.brandParticlesContainer) return

      const canvas = this.$refs.brandParticlesCanvas
      const container = this.$refs.brandParticlesContainer
      const ctx = canvas.getContext('2d')

      // 设置画布尺寸
      const resizeCanvas = () => {
        const rect = container.getBoundingClientRect()
        const dpr = window.devicePixelRatio || 1

        // 设置画布的实际像素尺寸
        canvas.width = rect.width * dpr
        canvas.height = rect.height * dpr

        // 设置画布的显示尺寸
        canvas.style.width = rect.width + 'px'
        canvas.style.height = rect.height + 'px'

        // 缩放绘图上下文以匹配设备像素比
        ctx.scale(dpr, dpr)
      }

      resizeCanvas()

      // 监听窗口大小变化
      const handleResize = () => {
        resizeCanvas()
        createParticles() // 重新创建粒子以适应新尺寸
      }

      window.addEventListener('resize', handleResize)

      // 粒子数组
      const particles = []
      const particleCount = 50

      // 创建粒子的函数
      const createParticles = () => {
        particles.length = 0 // 清空现有粒子
        const rect = container.getBoundingClientRect()

        for (let i = 0; i < particleCount; i++) {
          particles.push({
            x: Math.random() * rect.width,
            y: Math.random() * rect.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 2 + 1,
            opacity: Math.random() * 0.5 + 0.2
          })
        }
      }

      // 初始创建粒子
      createParticles()

      // 动画循环
      const animate = () => {
        const rect = container.getBoundingClientRect()
        ctx.clearRect(0, 0, rect.width, rect.height)

        particles.forEach(particle => {
          // 更新位置
          particle.x += particle.vx
          particle.y += particle.vy

          // 边界检测
          if (particle.x < 0 || particle.x > rect.width) particle.vx *= -1
          if (particle.y < 0 || particle.y > rect.height) particle.vy *= -1

          // 绘制粒子
          ctx.beginPath()
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
          ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`
          ctx.fill()
        })

        this.brandAnimationId = requestAnimationFrame(animate)
      }

      animate()
    },
    getCode() {
      getCodeImg().then((res) => {
        this.captchaEnabled =
          res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = 'data:image/gif;base64,' + res.img
          this.loginForm.uuid = res.uuid
        }
      })
    },
    getCookie() {
      const username = Cookies.get('username')
      const password = Cookies.get('password')
      const rememberMe = Cookies.get('rememberMe')
      const termsOfService = Cookies.get('termsOfService')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        termsOfService:
          termsOfService === undefined ? false : Boolean(termsOfService)
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          if (this.loginForm.termsOfService === false) {
            setTimeout(() => {
              this.$message({
                message: '请先阅读并同意平台使用协议',
                type: 'warning'
              })
            })
            this.termsVisible = true
            this.isLogin = true
            return
          }
          this.loading = true
          if (this.loginForm.rememberMe) {
            Cookies.set('username', this.loginForm.username, { expires: 30 })
            Cookies.set('password', encrypt(this.loginForm.password), {
              expires: 30
            })
            Cookies.set('rememberMe', this.loginForm.rememberMe, {
              expires: 30
            })
          } else {
            Cookies.remove('username')
            Cookies.remove('password')
            Cookies.remove('rememberMe')
          }
          Cookies.set('termsOfService', this.loginForm.termsOfService, {
            expires: 30
          })
          this.$store
            .dispatch('Login', this.loginForm)
            .then(() => {
              this.$router.push({ path: this.redirect || '/' }).catch(() => {})
            })
            .catch(() => {
              this.loading = false
              if (this.captchaEnabled) {
                this.getCode()
              }
            })
        }
      })
    },
    doSocialLogin(source) {
      authBinding(source).then((res) => {
        top.location.href = res.msg
      })
    },
    agreeTerms(agree) {
      this.termsVisible = false
      this.loginForm.termsOfService = agree
      if (this.isLogin && agree) {
        this.handleLogin()
      }
      this.isLogin = false
    }
  }
}
</script>

<script setup>
import useDifferentEndpoint from '@/hooks/useDifferentEndpoint'

const { name, copyright, icp } = useDifferentEndpoint()
</script>

<style lang="scss" scoped>
// ================================
// SCSS 变量定义
// ================================
$primary-color: #667eea;
$secondary-color: #764ba2;
$accent-color: #fa709a;
$text-light: rgba(255, 255, 255, 0.9);
$text-dark: #333;
$border-light: rgba(255, 255, 255, 0.2);
$glass-bg: rgba(255, 255, 255, 0.95);
$glass-bg-light: rgba(255, 255, 255, 0.1);

// 间距变量
$spacing-xs: 8px;
$spacing-sm: 12px;
$spacing-md: 20px;
$spacing-lg: 40px;
$spacing-xl: 60px;

// 圆角变量
$border-radius-sm: 8px;
$border-radius-md: 12px;
$border-radius-lg: 20px;
$border-radius-xl: 24px;

// 阴影变量
$shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
$shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);

// ================================
// SCSS 混合器
// ================================
@mixin glass-effect($opacity: 0.95, $blur: 20px) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur($blur);
}

@mixin absolute-full {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin text-gradient($color1: $primary-color, $color2: $secondary-color) {
  background: linear-gradient(135deg, $color1 0%, $color2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// ================================
// 基础布局样式
// ================================
.time-background {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.login-form-overlay {
  @include absolute-full;
  z-index: 100;
}

.login-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// ================================
// 品牌区域样式
// ================================
.brand-section {
  flex: 1;
  position: relative;
  color: white;
  overflow: hidden;

  // 背景渐变层
  &-background-gradient {
    @include absolute-full;
    z-index: 1;
  }

  // 粒子效果层
  &-particles-container {
    @include absolute-full;
    z-index: 2;
    pointer-events: none;
  }

  &-particles-canvas {
    display: block;
    width: 100%;
    height: 100%;
    opacity: 0.6;
  }

  // 几何装饰层
  &-geometric-decorations {
    @include absolute-full;
    pointer-events: none;
    overflow: hidden;
    z-index: 2;
  }

  &-geometric-shape {
    position: absolute;
    background: $glass-bg-light;
    border-radius: $border-radius-lg;
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;

    &.brand-shape-1 {
      width: 150px;
      height: 150px;
      top: 15%;
      right: -30px;
      transform: rotate(45deg);
      animation-delay: 0s;
    }

    &.brand-shape-2 {
      width: 100px;
      height: 100px;
      top: 60%;
      right: -20px;
      transform: rotate(30deg);
      animation-delay: 2s;
    }

    &.brand-shape-3 {
      width: 80px;
      height: 80px;
      bottom: 20%;
      right: -15px;
      transform: rotate(60deg);
      animation-delay: 4s;
    }
  }

  // 内容容器
  &-content-wrapper {
    position: relative;
    z-index: 10;
    padding: $spacing-xl 50px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

// ================================
// 品牌内容样式
// ================================
.logo {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;

  &-image {
    width: 64px;
    height: auto;
    filter: drop-shadow($shadow-sm);
  }

  h1 {
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
    color: white;
    text-shadow: $shadow-sm;
  }
}

.brand-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin: 0 0 $spacing-md 0;
    font-weight: 300;
  }

  .main-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin: 0 0 $spacing-lg 0;
    line-height: 1.2;
  }
}

.features {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    position: relative;
    padding-left: $spacing-md;
    margin-bottom: 16px;
    opacity: 0.9;
    line-height: 1.5;

    &::before {
      content: '■';
      position: absolute;
      left: 0;
      color: $text-light;
    }
  }
}

// ================================
// 表单区域样式
// ================================
.form-section {
  flex: 1.2;
  @include glass-effect;
  @include flex-center;
  padding: $spacing-xl 50px;
  border-left: 1px solid $border-light;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.welcome-header {
  h3 {
    font-size: 1.8rem;
    color: $text-dark;
    margin: 0 0 $spacing-lg 0;
    text-align: center;
    font-weight: 500;
  }
}

.brand-highlight {
  @include text-gradient;
}

// ================================
// 登录表单样式
// ================================
.login-form {
  display: flex;
  flex-direction: column;

  :deep(.el-form-item) {
    margin-bottom: $spacing-md;
  }

  // 嵌入式表单组样式
  .form-group-embedded {
    position: relative;
    margin-top: 8px; // 为标签留出空间

    .embedded-label {
      position: absolute;
      top: -10px;
      left: 12px;
      background: white;
      padding: 2px 8px;
      font-size: 14px;
      line-height: 1;
      color: #666;
      font-weight: 500;
      z-index: 10;
      transition: all 0.3s ease;
    }
  }

  :deep(.el-input) {
    .el-input__inner {
      height: 48px;
      font-size: 1rem;
      padding: 16px 16px;
      border: 1px solid #ddd;
      border-radius: $border-radius-sm;
      transition: all 0.3s ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
      }
    }
  }

  // 当输入框获得焦点或有内容时，标签样式变化
  .form-group-embedded:focus-within .embedded-label {
    color: $primary-color;
    transform: translateY(-2px);
  }
}

.captcha-container {
  display: flex;
  gap: 10px;
  align-items: center;

  .el-input {
    flex: 1;
  }
}

.login-code {
  width: 120px;
  height: 48px;
  border-radius: $border-radius-sm;
  border: 1px solid #ddd;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    height: 48px;
    cursor: pointer;
    vertical-align: middle;
  }
}

.form-options {
  display: flex;
  // flex-direction: column;
  justify-content: space-between;
  align-items: center;
  gap: $spacing-xs;
  font-size: 0.9rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  color: #666;
  cursor: pointer;
}

.oauth-login {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: $spacing-sm;
  border: 1px solid #e5e5ea;
  border-radius: $border-radius-sm;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
}

.oauth-login-item {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

:deep(.login-btn) {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border: none;
  border-radius: $border-radius-sm;
  font-size: 1rem;
  font-weight: 500;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &:focus {
    transform: translateY(-1px);
  }
}

.register-link {
  text-align: center;
  margin-top: $spacing-sm;

  .link-type {
    color: $primary-color;
    text-decoration: none;
  }
}

.terms-link {
  text-align: center;
  margin-top: $spacing-sm;
}

// ================================
// 底部样式
// ================================
.login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  text-align: center;
  color: #999;
  font-family: Arial, sans-serif;
  font-size: 12px;
  letter-spacing: 1px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

// ================================
// 动画效果
// ================================
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(-20px) rotate(var(--rotation, 0deg));
  }
}

// ================================
// 响应式设计
// ================================
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .brand-section {
    min-height: 40vh;

    &-content-wrapper {
      padding: $spacing-lg 30px;
    }
  }

  .form-section {
    padding: $spacing-lg 30px;
    min-height: 60vh;
  }
}
</style>

<style>
/* 全局样式，用于覆盖Element UI的样式 */
.terms-content {
  height: calc(100vh - 300px);
  overflow-y: auto;
  padding: 0 20px;
  color: #333;
}

.terms-item-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.terms-item-content {
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 10px;
  text-indent: 2em;
}

/* 登录页面特定的Element UI样式覆盖 */
.time-background .el-input__inner {
  background-color: #fff;
  border: 1px solid #ddd;
  color: #333;
}

.time-background .el-input__inner:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 嵌入式标签的额外样式 */
.time-background .form-group-embedded .embedded-label {
  background: white;
}

.time-background .form-group-embedded:focus-within .embedded-label {
  color: #667eea;
  font-weight: 600;
  border-color: #667eea;
  background: #f8f9ff;
}

/* 当输入框有内容时的标签样式 */
.time-background .form-group-embedded .el-input__inner:not(:placeholder-shown) + .embedded-label {
  color: #667eea;
  border-color: #667eea;
}

.time-background .el-checkbox__label {
  color: #666;
  font-size: 14px;
}

.time-background .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.time-background .el-button--primary:hover,
.time-background .el-button--primary:focus {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateY(-1px);
}

.time-background .el-button--text {
  color: #667eea;
}

.time-background .el-button--text:hover,
.time-background .el-button--text:focus {
  color: #764ba2;
}
</style>
