<template>
  <el-dialog :title="title" :visible.sync="visibleOpen" width="700px" append-to-body>
    <div class="component">
      <el-form ref="formRef" v-loading="formLoading" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="公司名称：" prop="companyId">
          <el-select v-model="form.companyId" filterable placeholder="请选择" @change="getUserList(form.companyId)">
            <el-option
              v-for="item in companyOptions"
              :key="item.deptId"
              :label="item.deptName"
              :value="item.deptId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门用户：" prop="userName" filterable>
          <el-select v-model="form.userName" :placeholder="form.companyId?'请选择':'请先选择公司名称'" :no-data-text="form.companyId?'请选择':'请先选择公司名称'">
            <el-option
              v-for="item in userOptions"
              :key="item.userId"
              :label="item.userName"
              :value="item.userName"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="域名URL："
          :rules="[
            { required: true}]"
        >
          <template #label>
            <span>域名URL：</span>
            <el-tooltip class="item" effect="dark" content="域名格式：xxx.com或者xxx.com/" placement="top">
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
          <el-form-item
            v-for="(item, index) in form.domainUrlList"
            :key="index"
            class="domainUrl"
            :rules="rules.domainUrl"
            :prop="'domainUrlList.' + index + '.domainUrl'"
          >
            <div class="domainUrl_item flex">
              <div>
                <el-input
                  v-model.trim="item.domainUrl"
                  style="width: 350px;"
                  placeholder="请输入"
                >
                  <template slot="prepend">http(s)://</template>
                </el-input>
              </div>
              <el-button v-if="!form.id" icon="el-icon-plus" class="addBtn" @click="addDomain(index)">新增</el-button>
              <el-button v-if="index>0&&!form.id" type="danger" icon="el-icon-delete" class="addBtn" @click="deleteDomain(index)">删除</el-button>
            </div>
          </el-form-item>
        </el-form-item>

        <el-form-item label="域名备注：" prop="remark">
          <el-input
            v-model.trim="form.remark"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button
          v-loading="submitLoding"
          type="primary"
          @click="submitForm"
        >确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { addDomain, updataDomain } from '@/api/monitor/domainName'
import { listDept } from '@/api/system/dept'
import { listUser } from '@/api/system/user'
import dayjs from 'dayjs'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    dataRow: {
      type: Object,
      default: () => null
    }
  },
  data() {
    var validateUrl = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入域名'))
      } else {
        const domainRegex = /^[a-z0-9]+([-.]{1}[a-z0-9]+)*\.[a-z]{2,6}\/?$/
        if (!domainRegex.test(value)) {
          callback('请输入正确格式的域名')
        }
        callback()
      }
    }
    return {
      formLoading: false,
      // 表单参数
      form: {
        id: null,
        // 公司名称
        companyId: null,
        // 域名备注
        remark: null,
        // 域名url列表
        domainUrlList: [
          { domainUrl: '' }
        ],
        userName: null
      },
      // 部门用户
      userOptions: [],
      // 表单校验
      rules: {
        companyId: [
          { required: true, message: '请选择公司名称', trigger: 'change' }
        ],
        domainUrl: [
          { required: true, validator: validateUrl, trigger: 'blur' }
        ],
        userName: [
          { required: true, message: '请选择部门用户', trigger: 'change' }
        ]
      },
      submitLoding: false,
      // 公司名称可选项
      companyOptions: [],
      title: ''
    }
  },
  computed: {
    visibleOpen: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', false)
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.reset()
          if (this.dataRow && this.dataRow.id) {
            this.title = '修改域名'
            const { id, companyId, remark, domainUrl, createBy } = this.dataRow
            this.form.id = id
            this.form.companyId = companyId
            this.form.remark = remark
            this.getUserList(companyId)
            this.form.userName = createBy
            if (domainUrl) {
              this.form.domainUrlList = [{ domainUrl: domainUrl }]
            }
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getListDept()
  },
  methods: {
    getUserList(id) {
      this.form.userName = null
      listUser({ deptId: id }).then(res => {
        this.userOptions = res.rows
      })
    },
    addDomain(index) {
      this.form.domainUrlList.splice(index + 1, 0, { domainUrl: '' })
    },
    deleteDomain(index) {
      this.form.domainUrlList.splice(index, 1)
    },
    // 重置
    reset() {
      this.title = '新增域名'
      if (this.$refs.formRef) {
        this.$refs['formRef'].resetFields()
      }
      this.submitLoding = false
      this.form.id = null
      this.form.companyId = null
      this.form.remark = null
      this.form.domainUrlList = [{ domainUrl: '' }]
      this.form.userName = null
      this.userOptions = []
    },
    getListDept() {
      listDept().then((response) => {
        // 使用 filter 方法筛选出 ancestors 值只存在一个逗号分隔项的数据
        const singleAncestorData = response.data.filter((item) => {
          const ancestors = item.ancestors
          // 使用正则表达式检查 ancestors 值是否只包含一个逗号
          return ancestors.split(',').length === 1 || ancestors.split(',').length === 2
        })
        this.companyOptions = singleAncestorData
      }).finally(() => {
        this.formLoading = false
      })
    },

    getDateByNumber(num) {
      return dayjs().add(num, 'day').format('YYYY-MM-DD HH:mm:ss')
    },
    // 函数判断输入日期距离当前时间多少天
    daysFromNow(inputDate) {
      const now = dayjs() // 当前时间
      const inputDayjs = dayjs(inputDate) // 输入日期转换为dayjs对象
      return inputDayjs.diff(now, 'day') // 计算差异，结果以天数为单位
    },

    cancel() {
      this.$emit('close')
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          const data = {
            companyId: this.form.companyId,
            companyName: this.companyOptions.filter(
              (item) => item.deptId === this.form.companyId
            )[0].deptName,
            remark: this.form.remark,
            createBy: this.form.userName
          }
          const arr = this.form.domainUrlList.map(item => {
            return item.domainUrl[item.domainUrl.length - 1] === '/' ? item.domainUrl : item.domainUrl + '/'
          })
          if (this.form.id) {
            this.submitLoding = true

            data.domainUrl = arr.join(',')
            data.id = this.form.id

            updataDomain(data)
              .then((response) => {
                this.submitLoding = false
                this.$modal.msgSuccess('修改成功')
                this.$emit('close', true)
              })
              .catch(() => {
                this.submitLoding = false
              })
          } else {
            this.submitLoding = true

            data.domainName = arr.join(',')
            addDomain(data)
              .then((response) => {
                this.submitLoding = false
                this.$modal.msgSuccess('新增成功')
                this.$emit('close', true)
              })
              .catch(() => {
                this.submitLoding = false
              })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.component {
  .dialog-footer{
    text-align: right;
  }
  ::v-deep .domainUrl{
    padding-bottom: 20px;
    .domainUrl_item{
      align-items: center;

    }
    .addBtn{
      margin-left: 10px;
      height: 36px;
      padding: 0 20px !important;
    }
    .el-form-item__error{
      left: 94px !important;
    }
  }
}

</style>
