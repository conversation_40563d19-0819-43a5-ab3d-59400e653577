<script setup>
import WxOptions from '@/views/form/list/components/WxOptions.vue'
import WxWorkOptions from '@/views/form/list/components/WxWorkOptions.vue'
import SchemeLinkSelector from '@/views/form/list/components/SchemeLinkSelector.vue'
import { computed, ref } from 'vue'
const props = defineProps({
  value: {
    type: Object,
    default: () => {}
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const jumpOption = ref([
  {
    name: 'END结束',
    value: -1
  },
  {
    name: '按顺序执行',
    value: 0
  }

])
const buildTypeOption = ref([
  {
    value: 1,
    name: '纯文本'
  },
  {
    value: 2,
    name: '选项'
  },
  {
    value: 3,
    name: '按钮'
  }
])
const buildTypeActive = ref(2)
// 当前问题的选中下标
const activeIndex = ref(0)
const dataForm = computed({
  get: () => props.value,
  set: () => emit('input', false)
})
const rules = ref({})
const emit = defineEmits(['add', 'questionList', 'formValue'])
// 一级问题的添加
function handleQuestion(index) {
  activeIndex.value = index
  handleAdd()
}
// 一级问题的删除
function deleteQuestion(index) {
  dataForm.value.list.splice(index, 1)
  initialize()
}
const dialogVisible = ref(false)
function handleAdd() {
  buildTypeActive.value = 2
  dialogVisible.value = true
}
function handleBuildType() {
  if (buildTypeActive.value === 1) {
    dataForm.value.list.splice(activeIndex.value + 1, 0, {
      questionName: '',
      value: '',
      type: 1,
      ruleRestriction: 0, jumpOption: []
    })
  } else if (buildTypeActive.value === 3) {
    dataForm.value.list.splice(activeIndex.value + 1, 0, {
      type: 3,
      ruleRestriction: 0, jumpOption: [],
      innerItem: {
        label: '点击添加微信',
        fontSize: 16,
        w: 375,
        h: 36,
        radius: 0,
        color: '#333333',
        background: '#ffffff',
        wxType: 2,
        hasCallbackDuration: true,
        callbackDuration: 60,
        value: [{ }]
      }
    })
  } else {
    dataForm.value.list.splice(activeIndex.value + 1, 0, {
      questionName: '',
      value: '',
      type: 2,
      answers: [{ answersValue: '', ruleRestriction: 0, jumpOption: [] }]
    })
  }
  initialize()
  dialogVisible.value = false
}
// 初始化可选选项
function initialize() {
  const arr = JSON.parse(JSON.stringify(dataForm.value.list))
  dataForm.value.list.forEach((item, index) => {
    if (item.answers && index < arr.length) {
      item.answers.forEach(answersItem => {
        const newArr = []
        arr.slice(index + 1).forEach((answersItem2, indexNumber) => {
          newArr.push({ name: '序列' + Number(indexNumber + 1 + index + 1), value: Number(indexNumber + 1 + index + 1) })
        })
        answersItem.jumpOption = jumpOption.value.concat(newArr)
      })
    } else {
      const newArr = []
      arr.slice(index + 1).forEach((answersItem2, indexNumber) => {
        newArr.push({ name: '序列' + Number(indexNumber + 1 + index + 1), value: Number(indexNumber + 1 + index + 1) })
      })
      item.jumpOption = jumpOption.value.concat(newArr)
    }
  })
}
// 子级选项的添加
function handleChildrenQuestion(index, index3) {
  dataForm.value.list[index].answers.splice(index3 + 1, 0, {
    answersValue: '',
    ruleRestriction: 0
  })
  initialize()
}
// 子级选项的删除
function deleteChildrenQuestion(index, index3) {
  dataForm.value.list[index].answers.splice(index3, 1)
  initialize()
}
const ruleForm = ref()
// 表单提交校验
function handleSubmit() {
  return new Promise((resolve, reject) => {
    ruleForm.value.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

defineExpose({
  handleSubmit
})
</script>

<template>
  <div>
    <el-button
      v-if="!dataForm ||!dataForm.list||!dataForm.list.length"
      type="primary"
      icon="el-icon-plus"
      size="small"
      class="mb20"
      @click="handleAdd()"
    >新增</el-button>
    <el-form
      v-if="dataForm && dataForm.list"
      ref="ruleForm"
      :model="dataForm"
      :rules="rules"
      label-width="90px"
    >
      <div
        v-for="(itemlist, index) in dataForm.list"
        :key="index"
        class="formItem"
      >

        <div>
          <div class="sequence">序列{{ index+1 }}：</div>
          <template v-if="itemlist.type === 1">
            <div class="flex">
              <el-form-item
                :label="'内容' + ':'"
                :prop="'list.' + index + '.questionName'"
                :rules="[
                  { required: true, message: '请输入', trigger: 'blur' },
                ]"
              >
                <div class="questionType">
                  <el-input
                    v-model="itemlist.questionName"
                    style="width: 100%"
                    placeholder="请输入"
                  />
                </div>
                <div style="margin-top: 12px;">
                  <span>执行逻辑：</span>
                  <el-select v-model="itemlist.ruleRestriction" style="width: 140px" placeholder="请选择">
                    <el-option
                      v-for="list2 in itemlist.jumpOption"
                      :key="list2.value"
                      :label="list2.name"
                      :value="list2.value"
                    />
                  </el-select>
                </div>
              </el-form-item>
              <div class="ml20" style="width: 100px">
                <div style="display: flex; align-items: center; height: 36px">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="handleQuestion(index)"
                  />
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="small"
                    @click="deleteQuestion(index)"
                  />
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="itemlist.type === 3">
            <div class="flex">
              <div>
                <el-form-item label="文字">
                  <el-input v-model="itemlist.innerItem.label" clearable />
                </el-form-item>
                <el-form-item label="文字大小">
                  <div class="flex">
                    <el-slider v-model="itemlist.innerItem.fontSize" class="flex1 mr10" :min="12" :max="64" :step="1" />
                    <el-input-number v-model="itemlist.innerItem.fontSize" :controls="false" :min="12" :max="64" style="width:66px" />
                  </div>
                </el-form-item>
                <el-form-item label="宽">
                  <el-input-number v-model="itemlist.innerItem.w" :controls="false" />
                </el-form-item>
                <el-form-item label="高">
                  <el-input-number v-model="itemlist.innerItem.h" :controls="false" />
                </el-form-item>
                <el-form-item label="倒角">
                  <el-input-number v-model="itemlist.innerItem.radius" :controls="false" />
                </el-form-item>
                <el-form-item label="文字颜色">
                  <el-color-picker v-model="itemlist.innerItem.color" />
                </el-form-item>
                <el-form-item label="背景色">
                  <el-color-picker v-model="itemlist.innerItem.background" />
                </el-form-item>
                <el-form-item label="微信类型">
                  <el-radio-group v-model="itemlist.innerItem.wxType" @change="() => {itemlist.innerItem.value = [{}]}">
                    <!-- <el-radio-button :label="1">个人微信</el-radio-button> -->
                    <el-radio-button :label="2">企业微信</el-radio-button>
                    <el-radio-button :label="3">微信短链</el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <template v-if="itemlist.innerItem.wxType === 1">
                  <el-form-item label="回传时间">
                    <el-switch
                      v-model="itemlist.innerItem.hasCallbackDuration"
                      class="mr5"
                      active-color="#13ce66"
                    />
                    <template v-if="itemlist.innerItem.hasCallbackDuration"><el-input-number v-model="itemlist.innerItem.callbackDuration" :controls="false" :min="10" /> 秒</template>
                  </el-form-item>
                  <span class="ml20">微信账号</span>
                  <WxOptions :options.sync="itemlist.innerItem.value" :is-show-file="false" />
                </template>
                <template v-else-if="itemlist.innerItem.wxType === 2">
                  获客链接
                  <WxWorkOptions :options.sync="itemlist.innerItem.value" />
                </template>
                <template v-else-if="itemlist.innerItem.wxType === 3">

                  <el-form-item label="微信短链">
                    <SchemeLinkSelector :link.sync="itemlist.innerItem.schemeLink" />
                    <div class="flex gap-10 mt10">
                      <el-input v-model="itemlist.innerItem.wx" type="text" size="mini" class="flex1" placeholder="微信账号" clearable />
                      <el-input v-model="itemlist.innerItem.wxId" type="text" size="mini" class="flex1" placeholder="微信ID" clearable />
                    </div>
                  </el-form-item>
                </template>
                <div style="margin-top: 12px;">
                  <span>执行逻辑：</span>
                  <el-select v-model="itemlist.ruleRestriction" style="width: 140px" placeholder="请选择">
                    <el-option
                      v-for="list2 in itemlist.jumpOption"
                      :key="list2.value"
                      :label="list2.name"
                      :value="list2.value"
                    />
                  </el-select>
                </div>
              </div>

              <div class="ml20" style="width: 100px">
                <div style="display: flex; align-items: center; height: 36px">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="handleQuestion(index)"
                  />
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="small"
                    @click="deleteQuestion(index)"
                  />
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="flex">
              <el-form-item
                :label="'问题' + ':'"
                :prop="'list.' + index + '.questionName'"
                :rules="[
                  { required: true, message: '请输入', trigger: 'blur' },
                ]"
              >
                <el-input
                  v-model="itemlist.questionName"
                  style="width: 100%"
                  placeholder="请输入"
                />
              </el-form-item>
              <div class="ml20" style="width: 100px">
                <div style="display: flex; align-items: center; height: 36px">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="handleQuestion(index)"
                  />
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="small"
                    @click="deleteQuestion(index)"
                  />
                </div>
              </div>
            </div>

            <div
              v-for="(item3, index3) in itemlist.answers"
              :key="'answers' + index3"
              class="children_form"
            >
              <el-form-item
                label="选项："
                :prop="'list.' + index + '.answers.' + index3 + '.answersValue'"
                :rules="[
                  { required: true, message: '请输入', trigger: 'blur' },
                ]"
              >
                <div class="flex">
                  <el-input
                    v-model="item3.answersValue"
                    style="width: 38%"
                    placeholder="请输入"
                  />
                  <el-select v-model="item3.ruleRestriction" style="width: 140px" placeholder="请选择">
                    <el-option

                      v-for="list2 in item3.jumpOption"
                      :key="list2.value"
                      :label="list2.name"
                      :value="list2.value"
                    />
                  </el-select>
                  <div class="ml20" style="width: 100px">
                    <el-button
                      type="primary"
                      icon="el-icon-plus"
                      circle
                      @click="handleChildrenQuestion(index, index3)"
                    />
                    <el-button
                      v-if="index3 > 0"
                      type="danger"
                      icon="el-icon-delete"
                      circle
                      @click="deleteChildrenQuestion(index, index3)"
                    />
                  </div>
                </div>

              </el-form-item>

            </div>

          </template>

        </div>
      </div>
    </el-form>
    <el-dialog
      title="选择创建类型"
      :visible.sync="dialogVisible"
      top="25vh"
      width="500px"
      append-to-body
    >
      <div class="buildType">
        <div
          v-for="(item, index) in buildTypeOption"
          :key="index"
          class="type_style"
          :class="item.value === buildTypeActive ? 'buildType_active' : ''"
          @click="buildTypeActive = item.value"
        >
          {{ item.name }}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleBuildType">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.components-title {
  font-size: 14px;
  color: #333;
  margin: 8px 0;
}
.sequence{
  font-weight: bold;
  margin-left: 12px;
  margin-bottom: 12px;
}
.components-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 5px;
  .component-item {
    padding: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    user-select: none;
    transition: all 0.3s;
    &:hover {
      box-shadow: 0 0 10px 0 #d7d7d7;
    }
    &:active {
      box-shadow: none;
      background: #f5f7fa;
    }
  }
  .component-icon {
    margin-bottom: 5px;
    font-size: 20px;
    color: #409eff;
  }
}

.disabled .component-item {
  cursor: not-allowed;
  opacity: 0.5;
  &:hover {
    box-shadow: none;
  }
  &:active {
    box-shadow: none;
    background: white;
  }
}
::v-deep .el-dialog__body {
  padding-top: 0 !important;
}
.formItem {
  display: flex;
  background: #fafafa;
  padding-top: 24px;
  margin-bottom: 12px;
}
.children_form {
  padding-left: 30px;
  display: flex;
}
.buildType {
  display: flex;
  margin-top: 10px;
  justify-content: center;
  .type_style {
    width: 100px;
    height: 42px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    cursor: pointer;
    &:hover {
      background: rgb(191, 216, 243);
      color: #fff;
    }
  }
  .buildType_active {
    background: rgb(102, 177, 255);
    color: #fff;
  }
}
.questionType {
  display: flex;
  justify-content: space-between;
}
.addRestriction{
  background-color: #234154;
  border: none;
}

</style>
