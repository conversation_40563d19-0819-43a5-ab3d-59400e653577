// https://fanspvt.com/monitor/mor/click_tencent?click_id=click_id11111111&click_time=click_time1&callback=callback111&account_id=********&adgroup_id=adgroup_id1&request_id=request_id123456789123&goodsId=goodsId716427444044
export const trackFunc = `
    function trackEvent(eventName, properties = {}) {
        const formOrderInfo = getFormOrderInfo();
        // 构造埋点数据
        if(formOrderInfo.formId) {
            const eventData = {
                account_id: ********,
                click_id: getParam('click_id') || getParam('ckId'),
                goodsId: formOrderInfo.formId,
                callback: 'callback',
                adgroup_id: formOrderInfo.orderNo || 'adgroup_id',
                request_id: encodeURIComponent(JSON.stringify({eventName, ...properties})),
            };
            axios({
                method: 'get',
                url: 'https://fanspvt.com/monitor/mor/click_tencent',
                params: eventData
            })
        }
    }`
