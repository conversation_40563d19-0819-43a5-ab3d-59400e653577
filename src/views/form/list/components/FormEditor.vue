<script setup>
import { computed, onMounted, reactive, ref, getCurrentInstance } from 'vue'
import VueDragResizeRotate from '@gausszhou/vue-drag-resize-rotate'
import {
  baseComponents,
  flowComponents,
  layoutComponents,
  presetComponents,
  submitComponents
} from '@/views/form/list/components/config'
import Draggable from 'vuedraggable'
import ComponentList from '@/views/form/list/components/ComponentList.vue'
import FileRes from '@/views/promotion/fileres/index.vue'
import VantDragItem from '@/views/form/list/components/Vant/DragItem.vue'
import VantCustomForm from '@/views/form/list/components/Vant/CustomForm.vue'
import { saveForms } from '@/api/form/form'
import { compileHtml } from '@/views/form/list/components/compileHtml'
import LinkSelect from '@/views/wx/customerLink/components/LinkSelect.vue'
import { Message } from 'element-ui'
import useConfigs from '@/hooks/useConfigs'
import questionsAnswersComponents from './questionsAnswersComponents/index.vue'
import questionForm from './questionForm.vue'
import ProductCountdown from './Js/countdown/index.vue'
import EvaluateModule from './Js/evaluate/index.vue'
import { checkRole } from '@/utils/permission'
import 'quill/dist/quill.core.css'
const { proxy } = getCurrentInstance()
useConfigs(['form_monitor_link'])
const props = defineProps({
  formType: {
    default: 1,
    type: Number
  },
  pageBid: {
    default: null,
    type: String
  },
  form: {
    type: Object
  }
})
const emit = defineEmits(['success'])

const isEdit = computed(() => {
  return props.form?.id
})

onMounted(() => {
  if (props.form?.config) {
    const config = JSON.parse(props.form.config)
    pageForm.value = {
      name: props.form.name,
      remark: props.form.remark,
      downloadLimit: props.form.maxDownloadCount === -1,
      flow: config.flow ?? [],
      layout: config.layout ?? [],
      backgroundColor: config.backgroundColor ?? '#fcfcfc',
      script: config.script ?? '',
      isTrack: config.isTrack ?? false,
      isMiddlePage: config.isMiddlePage ?? false,
      middleConfig: config.middleConfig ?? {},
      itemList: props.form.itemsList.map(item => {
        const temp = JSON.parse(item.config)
        if (temp.deliveryResource) {
          temp.deliveryResource = props.form.fileResourceInfo
        }
        if (temp.suspend === true && temp.bottom !== undefined) {
          const windowHeight = scrollbarRef.value.$el.offsetHeight - 2
          temp.y = windowHeight - temp.h / 2 - Math.round(windowHeight * temp.bottom / 100) - (temp.size === 'large' ? 48 : 42) / 2
        }

        return temp
      })
    }
  }
})

const scrollbarRef = ref()
const pageForm = ref({
  name: '',
  remark: '',
  script: '',
  backgroundColor: '#fcfcfc',
  pageHeight: 0,
  windowHeight: 0,
  flow: [],
  layout: [],
  itemList: [],
  isTrack: false,
  isMiddlePage: false,
  middleConfig: {
    standbyDuration: 3,
    showStandby: false,
    linkUrl: '',
    linkName: ''
  }
})

const submitDisabled = computed(() => pageForm.value.itemList.some(item => item.key === 'submit'))
const questionsAnswersDisabled = computed(() => pageForm.value.flow.some(item => item.type === 'question'))
// 设置组件属性
const selectItem = ref()
const activeTab = ref('component')
const setSelectItem = (item) => {
  selectItem.value = item
  activeTab.value = 'component'
}
const removeSelectItem = () => {
  removeItem(selectItem.value)
}

const removeItem = (_item) => {
  const formIndex = pageForm.value.itemList.findIndex(item => item.cKey === _item.cKey)
  const flowIndex = pageForm.value.flow.findIndex(item => item.cKey === _item.cKey)
  const layoutIndex = pageForm.value.layout.findIndex(item => item.cKey === _item.cKey)
  if (formIndex !== -1) pageForm.value.itemList.splice(formIndex, 1)
  if (flowIndex !== -1) pageForm.value.flow.splice(flowIndex, 1)
  if (layoutIndex !== -1) pageForm.value.layout.splice(layoutIndex, 1)
  selectItem.value = null
}
const addFlowComponent = (item) => {
  const com = {
    cKey: new Date().getTime() + '',
    ...item,
    comments: []
  }
  pageForm.value.flow.push(com)
  setSelectItem(com)
}
const addLayoutComponent = (item) => {
  if (item.type === 'navigation') {
    if (pageForm.value.layout.some(item => item.type === 'navigation')) {
      Message.warning('导航栏只能添加一个')
      return
    }
  }
  if (item.type === 'service') {
    if (pageForm.value.layout.some(item => item.type === 'service')) {
      Message.warning('客服只能添加一个')
      return
    }
  }
  const com = {
    cKey: new Date().getTime() + '',
    ...item
  }
  pageForm.value.layout.push(com)
  setSelectItem(com)
}
const addComponent = (item) => {
  if (item.type === 'multiple') {
    item.components.forEach((c, i) => {
      c.x = item.x || 0
      c.y = item.y || 0
      c.cKey = new Date().getTime() + i

      if (i > 0) {
        c.x = item.components[i - 1].x + item.components[i - 1].w
      }
      pageForm.value.itemList.push(c)
    })
    setSelectItem(pageForm.value.itemList[pageForm.value.itemList.length - 1])
    return
  }
  const com = {
    cKey: new Date().getTime() + '',
    ...item
  }
  pageForm.value.itemList.push(com)
  setSelectItem(com)
}

// 处理表单事件
const showServicePopup = ref(false)
const serviceInfo = ref({
  weixin: '',
  phone: ''
})
const handleFormEvent = (event, item) => {
  if (event === 'previewService') {
    showServicePopup.value = true
    serviceInfo.value.weixin = item.weixin
    serviceInfo.value.phone = item.phone
  } else if (event === 'previewAction') {
    showFormPopup.value = true
    innerItem.value = item
  } else if (event === 'previewFallAdd') {
    showFallToAddPopup.value = true
    serviceInfo.value.weixin = item.weixin
    serviceInfo.value.phone = item.phone
  }
}

// 处理拖拽组件
const handleDrop = (e) => {
  // 1.获取到拖拽元素数据
  if (!e.dataTransfer.getData('drag-cmp')) return
  const cmpDrag = JSON.parse(e.dataTransfer.getData('drag-cmp'))
  if (!cmpDrag) return

  cmpDrag.y = e.layerY
  cmpDrag.x = e.layerX - cmpDrag.w / 2
  addComponent(cmpDrag)
}
const allowDrop = (e) => {
  e.preventDefault()
}

const isRefresh = ref(true)
const refreshDrag = () => {
  isRefresh.value = false
  setTimeout(() => {
    isRefresh.value = true
  })
}

const onResizeStop = (item, x, y, w, h) => {
  Object.assign(item, {
    x: Math.round(x),
    y: Math.round(y),
    w: Math.round(w),
    h: Math.round(h)
  })
}
const onDragStop = (item, x, y) => {
  Object.assign(item, {
    x: Math.round(x),
    y: Math.round(y)
  })
}

// 处理辅助线
const RefLineState = reactive({
  vLine: [],
  hLine: []
})
const getRefLineParams = (params) => {
  const { vLine, hLine } = params
  RefLineState.vLine = vLine
  RefLineState.hLine = hLine
}

// 选择文件资源
const fileVisible = ref(false)
const fileComp = ref(null)
const showFileRes = (c) => {
  fileVisible.value = true
  fileComp.value = c
}

const handleFileSelect = (url) => {
  fileComp.value.value = url
  fileVisible.value = false
}

const formPageRef = ref()
const flowRef = ref()
const navigationRef = ref()
const submitLoading = ref(false)
const vantCustomFormRef = ref()

const submit = async(type) => {
  if (vantCustomFormRef.value) {
    // 问答组件表单校验
    vantCustomFormRef.value.questionsAnswerscheck().then(result => {
      return result
    }).catch(() => {

    })
  }
  // emit('next', pageForm.value)
  pageForm.value.pageHeight = flowRef.value.$el.offsetHeight
  pageForm.value.windowHeight = scrollbarRef.value.$el.offsetHeight - 2
  // console.log(compileHtml(pageForm.value))
  // return
  // if (pageForm.value.flow.filter(i => i).length === 0) {
  //   Message.error('请选择背景图片')
  //   return
  // }
  const configFlow = JSON.parse(JSON.stringify(pageForm.value.flow))
  let isValid = false

  configFlow.forEach(item => {
    if (item.type && (item.type === 'question')) {
      item.list.forEach(subItem => {
        if (subItem.type !== 3) {
          subItem.value = ''
          if (!subItem.questionName) {
            isValid = true
          }
          if (item.answers && item.answers.length) {
            item.answers.forEach(answersItem => {
              if (!answersItem.answersValue) {
                isValid = true
              }
            })
          }
        }
      })
    }
  })
  if (isValid) {
    proxy.$modal.msgError('请检测问答组件的内容、问题、可选答案是否有未填写项')
    return isValid
  }

  const config = {
    flow: configFlow,
    script: pageForm.value.script,
    backgroundColor: pageForm.value.backgroundColor,
    layout: pageForm.value.layout,
    form: pageForm.value.itemList,
    isTrack: pageForm.value.isTrack,
    isMiddlePage: pageForm.value.isMiddlePage,
    middleConfig: pageForm.value.middleConfig
  }

  const formItemHasWx = pageForm.value.itemList.find(item => item.hasWx)
  if (formItemHasWx) {
    config.linkConfig = {
      ...formItemHasWx.wxConfig,
      wxType: formItemHasWx.wxType
    }
  }

  const postData = {
    name: pageForm.value.name,
    remark: pageForm.value.remark,
    formType: props.formType,
    enableAbPage: props.form?.enableAbPage,
    config: JSON.stringify(config)
  }

  const subBtn = pageForm.value.itemList.find(item => item.key === 'submit')
  if (subBtn) {
    if (subBtn.hasPay) {
      if (!subBtn.payPrice) {
        Message.error('请在按钮自定义属性中填写支付金额')
        return
      }
      postData.payRequired = 1
      postData.payPrice = subBtn.payPrice * 100
    } else {
      postData.payRequired = 0
    }
    if (subBtn.hasDownload) {
      if (!subBtn.deliveryResource) {
        Message.error('请在按钮自定义属性中选择下载资源')
        return
      }
      postData.deliveryResourceId = subBtn.deliveryResource.id
      subBtn.deliveryResource = subBtn.deliveryResource.id
      postData.maxDownloadCount = subBtn.downloadLimit ? -1 : subBtn.maxDownloadCount

      if (!postData.maxDownloadCount) {
        Message.error('请在按钮自定义属性中填写下载次数')
        return
      }
    }
  }
  // pageForm.value.layout.forEach((cp, index) => {
  //   if (cp?.linkUrls.length) {
  //
  //   }
  // })
  // for (let i = 0; i < pageForm.value.layout.length; i++) {
  //   if (pageForm.value.layout[i]) {
  //
  //   }
  //   pageForm.value.layout.forEach((cp, index) => {
  //     if (cp?.linkUrls.length) {
  //
  //     }
  //   })
  // }
  if (type !== 'copy' && props.form) postData.id = props.form.id
  if (props.pageBid) postData.pageBid = props.pageBid
  if (!postData.payRequired) postData.payRequired = 0
  postData.htmlContent = compileHtml(pageForm.value, {
    bottomHeight: navigationRef?.value?.[0]?.offsetHeight,
    formType: props.formType
  })

  postData.itemsList = pageForm.value.itemList.map((item, i) => {
    return {
      key: 'other',
      ...item,
      required: item.required ? 1 : 0,
      config: JSON.stringify(item)
    }
  })

  saveForms(postData)
    .then(data => {
      if (data.code === 200) {
        Message.success('操作成功')
        emit('success')
      }
    })
}

// 在 van-popup 服务弹窗后面添加表单弹窗
const showFormPopup = ref(false)
const showFallToAddPopup = ref(false)
const innerItem = ref(null)
const onFormSubmit = (values) => {
  showFormPopup.value = false
}

const linkVisible = ref(false)
const selectLink = (item) => {
  pageForm.value.middleConfig.linkUrl = item.linkUrl
  pageForm.value.middleConfig.linkName = item.linkName
  linkVisible.value = false
}

const filterLowComponents = computed(() => {
  return flowComponents.filter(item => {
    if (item.role) {
      return checkRole(item.role)
    }
    return true
  })
})

</script>

<template>
  <div>
    <div class="form-body">
      <div class="form-components">
        <div class="components-title">标题</div>
        <el-input v-model="pageForm.name" size="mini" class="mb5 mt5" />
        <div class="components-wrapper">
          <div class="components-title">流式组件</div>
          <ComponentList :list="filterLowComponents" :draggable="false" @add="addFlowComponent" />
          <!--        <div class="components-title">表单块</div>-->
          <div class="components-title">浮动组件</div>
          <ComponentList :list="layoutComponents" @add="addLayoutComponent" />
          <div class="components-title">表单组件</div>
          <ComponentList :list="baseComponents" @add="addComponent" />
          <ComponentList title="预设组件" :list="presetComponents" @add="addComponent" />
          <questionsAnswersComponents
            title="问答组件"
            :disabled="questionsAnswersDisabled"
            @questionList="addFlowComponent"
          />
          <ComponentList title="提交组件" :list="submitComponents" :disabled="submitDisabled" @add="addComponent" />

        </div>
        <div class="form-next">
          <el-button :loading="submitLoading" type="primary" class="flex1" @click="submit">完成</el-button>
          <el-button v-if="form" :loading="submitLoading" @click="submit('copy')">复制</el-button>
        </div>
      </div>
      <div id="form-page" class="form-page">
        <template v-for="layout in pageForm.layout">
          <div
            v-if="layout.type === 'navigation'"
            ref="navigationRef"
            :key="layout.cKey"
            style="position: absolute;bottom: 1px;right: 1px;left: 1px;z-index: 998"
            :class="selectItem?.cKey === layout.cKey ? 'active-item' : ''"
            @click="setSelectItem(layout)"
          >
            <div
              class="action-wrap"
              :style="{ textAlign: 'center', minHeight: layout.height + 'px', fontSize: layout.fontSize + 'px', color: layout.color, background: layout.background, backgroundSize: 'cover' }"
            >
              <el-button
                class="delete-button"
                type="danger"
                circle
                icon="el-icon-delete"
                plain
                size="mini"
                @click="removeItem(layout)"
              />
              <img :src="layout.backgroundImage" :style="{ width: '100%', verticalAlign: 'middle' }" alt="">
              <div class="absolute-center" style="z-index: 1">{{ layout.label }}</div>
            </div>
          </div>

          <div
            v-else-if="layout.type === 'affix'"
            :key="layout.cKey"
            :class="[layout.flash ? `animation-${layout.flash}` : '', { 'active-item': selectItem?.cKey === layout.cKey }]"
            :style="{ position: 'absolute', bottom: layout.bottom + 'px', right: layout.right + 'px', zIndex: 999 }"
            @click="setSelectItem(layout)"
          >
            <div class="action-wrap" :style="{ width: layout.width + 'px', height: layout.height + 'px' }">
              <el-button
                class="delete-button"
                type="danger"
                circle
                icon="el-icon-delete"
                plain
                size="mini"
                @click="removeItem(layout)"
              />
              <img v-if="layout.backgroundImage" :src="layout.backgroundImage" style="width:100%;height: 100%;" alt="">
              <div v-else>请选择固钉图片</div>
            </div>
          </div>
          <div
            v-else-if="layout.type === 'service'"
            :key="layout.cKey"
            :class="{ 'active-item': selectItem?.cKey === layout.cKey }"
            :style="{
              position: 'absolute',
              bottom: layout.bottom + 'px',
              zIndex: 1999
            }"
            @click="setSelectItem(layout)"
          >
            <div
              class="support-float-btn"
              :style="{
                right: layout.right + 'px',
                background: layout.background,
                color: layout.color
              }"
            >{{ layout.label }}</div>
          </div>
        </template>
        <template v-for="item in pageForm.itemList">
          <vue-drag-resize-rotate
            v-if="item.suspend"
            :key="`suspendKey`"
            :w="item.w"
            :h="item.h"
            :parent="true"
            :x="item.x"
            :y="item.y"
            :z="item.z"
            :min-width="100"
            :min-height="30"
            :snap="true"
            :snap-tolerance="10"
            @activated="setSelectItem(item)"
            @resizestop="(x, y, width, height) => onResizeStop(item, x, y, width, height)"
            @dragstop="(x, y) => onDragStop(item, x, y)"
          >
            <VantDragItem :item="item" :active="selectItem?.cKey === item.cKey" />
          </vue-drag-resize-rotate>
        </template>
        <van-popup v-model="showServicePopup" position="bottom" round get-container="#form-page">
          <!-- 头部区域 -->
          <div class="modal-header">
            <van-icon name="service" color="#409EFF" />
            <h3 class="header-title">需要帮助？</h3>
            <van-icon name="close" class="close-icon" />
          </div>

          <!-- 正文内容区域 -->
          <div class="modal-body">
            <!-- 场景提示 -->
            <div class="scene-tip">
              <van-icon name="info" size="16px" />
              <span>请选择以下方式联系我们的客服人员</span>
            </div>

            <!-- 客服信息卡片 -->
            <div class="contact-card">
              <!-- 微信模块 -->
              <div v-if="serviceInfo.weixin" class="contact-item wechat">
                <div class="item-header">
                  <van-icon name="wechat" size="20px" color="#67C23A" />
                  <span class="item-title">微信客服</span>
                </div>
                <div class="item-content">
                  <div class="copy-wrapper">
                    <span class="contact-info">{{ serviceInfo.weixin }}</span>
                    <van-button size="small" class="copy-btn">
                      <van-icon name="copy" size="14px" />复制
                    </van-button>
                  </div>
                  <!-- PC端显示二维码 -->
                  <!-- <img
              v-if="!isMobile"
              v-lazy="wechatQR"
              class="qr-code"
              alt="微信二维码"
            /> -->
                </div>
              </div>

              <!-- 电话模块 -->
              <div v-if="serviceInfo.phone" class="contact-item phone">
                <div class="item-header">
                  <van-icon name="phone" size="20px" color="#409EFF" />
                  <span class="item-title">客服电话</span>
                </div>
                <div class="item-content">
                  <a :href="`tel:${serviceInfo.phone}`" class="phone-link">
                    {{ serviceInfo.phone }}
                    <van-icon name="phone-circle" size="16px" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </van-popup>
        <van-popup v-model="showFallToAddPopup" round>
          <div class="fd-modal-header">
            <h3 class="fd-header-title">未成功添加微信？</h3>
            <van-icon name="close" class="fd-close-icon" @click="showFallToAddPopup = false" />
          </div>

          <div class="fd-modal-body">
            <div style="text-align: center;font-size: 14px;margin-bottom: 5px;">请注意查看跳转提示并允许打开微信</div>
            <van-button
              type="danger"
              style="width: 100%;font-size: 20px;font-weight: bold;"
              size="large"
              round
            >点击添加微信</van-button>
            <div v-if="serviceInfo.weixin || serviceInfo.phone" class="fd-scene-tip">
              <van-icon class="fd-scene-tip-icon" name="info" />
              <span>或联系客服人员</span>
            </div>

            <div v-if="serviceInfo.weixin" class="fd-contact-card">
              <div class="fd-contact-item">
                <div class="fd-item-header">
                  <van-icon name="wechat" size="16px" color="#67C23A" />
                  <span class="fd-item-title">微信</span>
                </div>
                <div class="fd-item-content">
                  <div class="fd-copy-wrapper">
                    <span class="fd-contact-info">{{ serviceInfo.weixin }}</span>
                    <div class="fd-copy-btn">
                      <van-icon name="copy" size="14px" />复制
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="serviceInfo.phone" class="fd-contact-item">
                <div class="fd-item-header">
                  <van-icon name="phone" size="16px" color="#409EFF" />
                  <span class="fd-item-title">电话</span>
                </div>
                <div class="fd-item-content">
                  <a :href="`tel:${serviceInfo.phone}`" class="fd-phone-link">
                    {{ serviceInfo.phone }}
                    <van-icon class="fd-phone-link-icon" name="phone-circle" size="16px" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </van-popup>
        <van-popup
          v-model="showFormPopup"
          v-bind="innerItem?.afterPay?.data?.position === 'default' ? {} : { position: innerItem?.afterPay?.data?.position }"
          round
          get-container="#form-page"
        >
          <!-- 头部区域 -->
          <div class="modal-header">
            <h3 class="header-title">{{ innerItem?.afterPay?.data?.title || '请填写信息' }}</h3>
            <van-icon name="close" class="close-icon" @click="showFormPopup = false" />
          </div>

          <!-- 表单内容区域 -->
          <div class="modal-body" style="min-width: 300px">
            <van-form @submit="onFormSubmit">
              <template v-for="field in innerItem?.afterPay?.data?.customFields">
                <van-field
                  :key="field.id"
                  v-model="field.value"
                  :name="field.label"
                  :label="field.label"
                  :placeholder="field.placeholder"
                  :rules="[
                    { required: field.required, message: `请输入${field.label}` },
                    { pattern: new RegExp(field.regexp), message: field.errorMsg }
                  ]"
                />
              </template>

              <div style="margin: 16px">
                <van-button
                  :color="innerItem?.afterPay?.data?.buttonColor"
                  round
                  block
                  type="primary"
                  native-type="submit"
                >
                  {{ innerItem?.afterPay?.data?.buttonText || '确认提交' }}
                </van-button>
              </div>
            </van-form>
          </div>
        </van-popup>
        <el-scrollbar ref="scrollbarRef" class="scroll-page" :style="{ backgroundColor: pageForm.backgroundColor }">
          <div
            id="formPageRef"
            ref="formPageRef"
            v-resize="refreshDrag"
            class="form-page-container"
            @drop="handleDrop"
            @dragover="allowDrop"
          >
            <template v-for="item in pageForm.itemList">
              <template v-if="isRefresh">
                <vue-drag-resize-rotate
                  v-if="!item.suspend"
                  :key="item.cKey"
                  :w="item.w"
                  :h="item.h"
                  :parent="true"
                  :x="item.x"
                  :y="item.y"
                  :z="item.z"
                  :min-width="100"
                  :min-height="30"
                  :snap="true"
                  :snap-tolerance="10"
                  @activated="setSelectItem(item)"
                  @refLineParams="getRefLineParams"
                  @resizestop="(x, y, width, height) => onResizeStop(item, x, y, width, height)"
                  @dragstop="(x, y) => onDragStop(item, x, y)"
                >
                  <!--                <ElementDragItem :item="item" :active="selectItem?.cKey === item.cKey" />-->
                  <VantDragItem :item="item" :active="selectItem?.cKey === item.cKey" />
                </vue-drag-resize-rotate>

                <span
                  v-for="(item, index) in RefLineState.vLine"
                  v-show="item.display"
                  :key="'v_' + index"
                  class="ref-line v-line"
                  :style="{
                    left: item.position,
                    top: item.origin,
                    height: item.lineLength
                  }"
                />
                <span
                  v-for="(item, index) in RefLineState.hLine"
                  :key="'h_' + index"
                  class="ref-line h-line"
                  :style="{
                    top: item.position,
                    left: item.origin,
                    width: item.lineLength
                  }"
                />
              </template>

            </template>

            <Draggable ref="flowRef" tag="ul" :list="pageForm.flow" class="list-group" handle=".handle-button">
              <div
                v-for="c in pageForm.flow"
                :key="c.cKey"
                class="action-wrap"
                :class="selectItem?.cKey === c.cKey ? 'active-item' : ''"
                @click="setSelectItem(c)"
              >
                <el-button class="handle-button" circle icon="el-icon-sort" plain size="mini" />
                <el-button
                  class="delete-button"
                  type="danger"
                  circle
                  icon="el-icon-delete"
                  plain
                  size="mini"
                  @click="removeItem(c)"
                />
                <template v-if="c.type === 'img'">
                  <img v-if="c.value" class="bg-img" :src="c.value">
                  <div v-else class="text-center p10">请选择图片</div>
                </template>
                <template v-if="c.type === 'code'">
                  <div v-if="c.value" class="bili-video" v-html="c.value" />
                  <div v-else class="text-center p10">请输入代码</div>
                </template>
                <template v-if="c.type === 'richText'">
                  <div v-if="c.value" class="ql-editor" v-html="c.value" />
                  <div v-else class="text-center p10">请输入富文本</div>
                </template>
                <template v-if="c.type === 'iframe'">
                  <iframe
                    v-if="c.value"
                    ref="browserRef"
                    class="webview publicWebview"
                    frameborder="no"
                    :src="c.value"
                    :style="{ height: c.autoHeight ? '890px' : c.height + 'px', width: '100%', pointerEvents: 'none' }"
                  />
                  <div v-else class="text-center p10">请输入网址</div>
                </template>
                <template v-if="c.type === 'blank'">
                  <div :style="{ height: c.height + 'px' }" />
                </template>
                <template v-if="c.type === 'wx' && c.value">
                  <div
                    v-if="c.wxType === 1"
                    :class="selectItem?.cKey === c.cKey ? 'active-item' : ''"
                    :style="{ position: 'relative', textAlign: c.textAlign, fontWeight: c.bold ? 'bold' : '', fontSize: c.fontSize + 'px', minHeight: '40px' }"
                  >
                    <!--                  <img v-show="c.backgroundImg" :src="c.backgroundImg" alt="" style="width: 100%">-->
                    <!--                  <div v-if="c.value[0]" style="position: absolute;top:0;left: 0;right: 0;bottom: 0">-->
                    <div v-if="c.value[0]">
                      <img
                        v-show="c.value[0].qrcode"
                        :src="c.value[0].qrcode"
                        :style="{ maxWidth: '80%', height: c.h + 'px', verticalAlign: 'middle' }"
                        alt=""
                      >
                      <div>{{ c.value[0].wx }}</div>
                      <div v-if="!c.value[0].qrcode && !c.value[0].wx" class="text-center p10">
                        请填写微信信息
                      </div>
                    </div>
                    <div v-else class="text-center p10">
                      请填写微信信息
                    </div>
                  </div>
                  <div
                    v-if="c.wxType === 2"
                    :class="selectItem?.cKey === c.cKey ? 'active-item' : ''"
                    :style="{ textAlign: c.textAlign, fontWeight: c.bold ? 'bold' : '', fontSize: c.fontSize + 'px' }"
                  >
                    <template v-if="c.value.length">
                      <a v-if="c.imgUrl">
                        <img :src="c.imgUrl" :style="{ width: '100%', verticalAlign: 'middle' }" alt="">
                        {{ c.label }}
                      </a>
                      <div v-else class="text-center p10">
                        请填写图片链接
                      </div>
                    </template>
                    <div v-else class="text-center p10">
                      请填写企业微信信息。
                    </div>
                  </div>
                  <div
                    v-if="c.wxType === 3"
                    :class="selectItem?.cKey === c.cKey ? 'active-item' : ''"
                    :style="{ textAlign: c.textAlign, fontWeight: c.bold ? 'bold' : '', fontSize: c.fontSize + 'px' }"
                  >
                    <template v-if="c.schemeLink">
                      <a v-if="c.imgUrl">
                        <img :src="c.imgUrl" :style="{ width: '100%', verticalAlign: 'middle' }" alt="">
                        {{ c.label }}
                      </a>
                      <div v-else class="text-center p10">
                        请填写图片链接
                      </div>
                    </template>
                    <div v-else class="text-center p10">
                      请填写微信短链。
                    </div>
                  </div>
                </template>
                <template v-if="c.type === 'question'">
                  <questionForm v-model="c.list" />
                </template>
                <template v-if="c.type === 'countdown'">
                  <product-countdown
                    :background-image-url="c.backgroundImageUrl"
                    :sale-price="c.salePrice"
                    :sale-price-color="c.salePriceColor"
                    :original-price="c.originalPrice"
                    :original-price-color="c.originalPriceColor"
                    :countdown-text-color="c.countdownTextColor"
                    :target-timestamp="c.targetTimestamp"
                  />
                </template>
                <template v-if="c.type === 'evaluate'">
                  <evaluate-module :title="c.title" :comments="c.comments" :show-num="c.showNum" />
                </template>
              </div>
            </Draggable>
          </div>
          <!--          <div v-if="addDisabled" class="select-bg">请选择背景图片</div>-->
        </el-scrollbar>
      </div>
      <el-tabs v-model="activeTab" class="flex1" type="border-card">
        <el-tab-pane name="component" label="组件属性">
          <VantCustomForm
            v-if="selectItem"
            ref="vantCustomFormRef"
            :select-item.sync="selectItem"
            :form-type="formType"
            :is-edit="isEdit"
            @triggerEvent="handleFormEvent"
            @removeSelectItem="removeSelectItem"
          />
        </el-tab-pane>
        <el-tab-pane name="form" label="表单属性">
          <el-form :model="pageForm" label-width="80px" size="mini">
            <el-form-item label="备注">
              <el-input v-model="pageForm.remark" size="mini" />
            </el-form-item>
            <el-form-item label="嵌入代码">
              <el-input v-model="pageForm.script" size="mini" />
            </el-form-item>
            <el-form-item label="背景颜色">
              <el-color-picker v-model="pageForm.backgroundColor" size="mini" />
            </el-form-item>
            <el-form-item label="开启埋点">
              <el-switch v-model="pageForm.isTrack" size="mini" />
            </el-form-item>
            <el-divider content-position="left">
              <div class="flex align-center">
                中间页
                <el-switch
                  v-model="pageForm.isMiddlePage"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  style="margin-left: 10px;"
                  size="mini"
                />
                <el-tooltip content="将表单页作为中间页使用，用户到达该页面将直接跳转至获客链接" placement="top" effect="light">
                  <i class="el-icon-question color-primary" style="margin-left: 5px;" />
                </el-tooltip>
              </div>
            </el-divider>
            <div
              v-if="pageForm.isMiddlePage"
              style="margin-left: 20px;padding-left: 10px; border-left: 1px solid #e5e5e5;"
            >
              <el-form-item label="获客链接">
                <div class="flex gap-10">
                  <el-input
                    v-model="pageForm.middleConfig.linkUrl"
                    type="text"
                    size="mini"
                    placeholder="获客链接"
                    clearable
                  />
                  <el-button class="flex1 add-btn" type="primary" size="mini" @click="linkVisible = true">选择</el-button>
                </div>
              </el-form-item>
              <!-- <el-form-item label="备用页">
                <template #label>
                  <div class="flex align-center">
                    备用页
                    <el-tooltip content="当用户未及时添跳转时，会显示当前创建的页面" placement="top">
                      <i class="el-icon-question color-primary" style="margin-left: 5px;" />
                    </el-tooltip>
                  </div>
                </template>
                <div class="flex align-center" style="height: 28px;">
                  <el-switch
                    v-model="pageForm.middleConfig.showStandby"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    size="mini"
                  />
                  <template v-if="pageForm.middleConfig.showStandby">
                    <el-input-number v-model="pageForm.middleConfig.standbyDuration" style="width: 100px; margin:0 5px;" placeholder="3" :controls="false" :min="1" /> 秒后显示备用页
                  </template>
                </div>
                <div class="mt5" style="font-size: 12px;color: #999;line-height: 1.5;">{{ pageForm.middleConfig.showStandby ? `当用户停留在页面未跳转超过 ${pageForm.middleConfig.standbyDuration || 3} 秒时，会展示备用页面。跳转成功后将回到来源的页面` : '定时跳转回到来源的页面' }}</div>
              </el-form-item> -->
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog title="选择文件" :visible.sync="fileVisible" width="90%" top="2vh" append-to-body>
      <FileRes :img-file-size="5" selector @select="handleFileSelect" />
    </el-dialog>

    <el-dialog :visible.sync="linkVisible" append-to-body width="70%">
      <link-select v-if="linkVisible" @select="selectLink" />
    </el-dialog>
  </div>
</template>

<style lang="scss">
.bili-video {
  iframe {
    width: 95%;
    margin-left: 2.5%;
    height: 300px;
  }
}
</style>

<style scoped lang="scss">
.components-title {
  font-size: 16px;
  color: #333;
  margin: 8px 0;
  font-weight: bold;
}

//.active-item {
//  content: "";
//  position: absolute;
//  top: 0;
//  left: 0;
//  right: 0;
//  bottom: 0;
//  border: 2px solid #84c1ff;
//}
.flow-item {
  position: relative;
  z-index: -1;

  .flow-item-buttons {
    display: none;
    position: absolute;
    bottom: 5px;
    right: 5px;
    z-index: 1;
  }

  &:hover .flow-item-buttons {
    display: block;
  }

  &:hover::after {
    content: "";
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    border: 2px dashed #84c1ff;
  }
}

.action-wrap {
  position: relative;

  .handle-button {
    display: none;
    position: absolute;
    bottom: 5px;
    left: 5px;
    z-index: 1;
  }

  .delete-button {
    display: none;
    position: absolute;
    bottom: 5px;
    right: 5px;
    z-index: 1;
  }

  &:hover .delete-button {
    display: block;
  }

  &:hover .handle-button {
    display: block;
  }

  &:hover::after {
    content: "";
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    border: 2px dashed #84c1ff;
  }
}

.active-item::after {
  content: "";
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  border: 2px solid #84c1ff;
}

.form-body {
  display: flex;
  padding: 0 100px;
  height: calc(100vh - 150px);
  gap: 30px;
  min-width: 1400px;

  .form-components,
  .form-page,
  .form-info {
    height: 100%;
  }

  .form-components {
    flex: 1;
  }

  .form-page {
    position: relative;
    width: 375px;

  }

  .components-wrapper {
    max-height: calc(100vh - 280px);
    overflow: auto;
  }

  .form-next {
    display: flex;
    margin-top: 20px;
  }
}

.form-info {
  flex: 1;

  .operation-title {
    margin-bottom: 14px;
  }

  .operation-form {
    padding: 10px 10px;
    border: 1px solid #DCDFE6;
  }

  .operation-no-select {
    height: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #999;
    border: 1px solid #DCDFE6;
    user-select: none;
  }

  .title-icon {
    margin-right: 10px;
  }
}

.scroll-page {
  height: 100%;
  border: 1px solid #eaebef;
  background-color: #fcfcfc;
  overflow-x: hidden;

  .bg-img {
    width: 100%;
    display: block;
    border: 0;
    object-fit: cover;
    -webkit-user-drag: none;
    -moz-user-drag: none;
    -ms-user-drag: none;
    user-drag: none;
  }
}

.form-page-container {
  position: relative;
  overflow-y: hidden;
  overflow-x: hidden;
  height: 100%;
  min-height: calc(100vh - 152px);
  z-index: 0;
}

.select-bg {
  position: absolute;
  top: 300px;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  z-index: 9;
  color: #999;
}

.item-wrapper {
  width: 100%;
  height: 100%;
}

.pd-drag-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  .pd-form-component {
    flex: 1;
  }

  .pd-form-label {
    padding-right: 15px;
  }

  .is-required {
    position: relative;

    &:after {
      content: '*';
      font-size: 20px;
      color: red;
      position: absolute;
    }
  }
}

.animation-bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-20px);
  }

  60% {
    transform: translateY(-10px);
  }
}

.animation-breath {
  animation: breath 2s ease-in-out infinite;
}

@keyframes breath {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.ref-line {
  position: absolute;
  background-color: rgb(219, 89, 110);
  z-index: 99;
}

.v-line {
  width: 1px;
}

.h-line {
  height: 1px;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

:deep(.el-collapse-item__content) {
  padding: 10px 0 0;
}

:deep(.van-popup) {
  position: absolute;
}

:deep(.van-overlay) {
  position: absolute;
}

:deep(.el-dialog__body) {
  padding-top: 0;
}

.support-float-btn {
  font-size: 18px;
  writing-mode: vertical-rl;
  text-orientation: upright;
  padding: 10px 6px;
  background: #07c160;
  color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s;
}

.support-content {
  padding: 20px;
  text-align: center;
  min-height: 300px;
}

.service-modal {
  /* Vant弹窗默认样式覆盖 */
  --van-popup-background: #fff;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
  position: relative;

  .header-title {
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: normal;
  }

  .close-icon {
    position: absolute;
    right: 16px;
    font-size: 20px;
    color: #999;
    cursor: pointer;

    &:hover {
      color: #666;
    }
  }
}

.modal-body {
  padding: 16px;
  max-height: 70vh;
  overflow-y: auto;

  :deep(.van-cell) {
    padding: 12px 0;
  }

  :deep(.van-field__label) {
    width: 80px;
    color: #333;
  }
}

.contact-card {
  .contact-item {
    margin-bottom: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .item-header {
      padding: 12px;
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;

      .item-title {
        margin-left: 8px;
        font-weight: 600;
        color: #333;
      }
    }

    .item-content {
      padding: 12px;

      .copy-wrapper {
        display: flex;
        align-items: center;

        .contact-info {
          flex: 1;
          color: #666;
          font-family: monospace;
        }

        .copy-btn {
          margin-left: 8px;

          :deep(.van-button__text) {
            display: flex;
            align-items: center;
          }
        }
      }

      .qr-code {
        width: 120px;
        height: 120px;
        border: 1px solid #eee;
        display: block;
        margin: 8px auto;
      }

      .phone-link {
        color: #409EFF;
        text-decoration: none;
        font-size: 16px;
        display: flex;
        align-items: center;

        .van-icon {
          margin-left: 6px;
        }
      }

      .work-time {
        margin-top: 8px;
        font-size: 12px;
        color: #999;
      }
    }
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .contact-item {
      margin-bottom: 16px;

      .item-content {
        padding: 12px;
      }

      .qr-code {
        width: 100px;
        height: 100px;
      }
    }
  }
}

.fd-modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.fd-close-icon {
  position: absolute;
  right: 16px;
  font-size: 20px;
  color: #999;
  cursor: pointer;
}

.fd-header-title {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: normal;
}

.fd-modal-body {
  padding: 16px;
  width: 320px;
  max-height: 70vh;
  overflow-y: auto;
}

.fd-scene-tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 10px 0;
}

.fd-contact-card {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.fd-contact-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.fd-item-header {
  padding: 8px;
  display: flex;
  font-size: 14px;
  align-items: center;
}

.fd-item-content {
  padding: 8px;
}

.fd-copy-wrapper {
  display: flex;
  align-items: center;
}

.fd-contact-info {
  flex: 1;
  color: #666;
  font-family: monospace;
}

.fd-copy-btn {
  margin-left: 4px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 12px;
}

.fd-phone-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.fd-phone-link-icon {
  margin-left: 6px;
}

.animation-shake {
  animation: shake 1s infinite;
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

.animation-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.7);
  }

  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}
</style>
