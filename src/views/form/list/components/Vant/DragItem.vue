<script setup>
import FormLabel from '@/views/form/list/components/FormLabel.vue'
import { reactive, ref } from 'vue'

defineProps({
  item: {
    type: Object
  },
  active: {
    type: Boolean,
    default: false
  }
})

const selectValue = ref()
const pickerState = reactive({
  visible: false,
  value: ''

})

function onConfirm(value) {
  pickerState.value = value
  pickerState.visible = false
}
</script>

<template>
  <div class="item-wrapper" :class="active ? 'active-item':''">
    <div class="pd-drag-wrapper">
      <template v-if="item.type === 'text' || item.modelButton === 'text'">
        <div class="pd-form-component js-modal-trigger" :style="{textAlign:item.textAlign,color: item.color, fontWeight: item.bold ? 'bold': '',fontSize: item.fontSize + 'px'}">{{ item.label }}</div>
      </template>
      <template v-else-if="item.type === 'button' || item.modelButton === 'button'">
        <van-button
          class="pd-form-component aaaa"
          :class="item.size + ' '+ (item.flash? `animation-${item.flash}`: '')"
          :color="item.background"
          :size="item.size"
          :round="item.round"
          :style="{color: item.color, fontWeight: item.bold ? 'bold': '',fontSize: item.fontSize + 'px'}"
        >{{ item.label }}</van-button>
      </template>
      <template v-else-if="item.type === 'input'">
        <!--        <FormLabel :item="item" />-->
        <van-cell-group :style="{width: item.w+'px'}" inset>
          <van-field :label="item.label" :style="{display: item.labelPosition === 'top' ? 'block' : ''}" class=" pd-form-component" :label-width="item.labelPosition === 'left' ? `${item.labelWidth}em` : '100%'" :required="item.required" :size="item.size" type="text" :placeholder="item.placeholder" />
        </van-cell-group>
      </template>
      <template v-else-if="item.type === 'number'">
        <van-cell-group :style="{width: item.w+'px'}" inset>
          <van-field :label="item.label" :style="{display: item.labelPosition === 'top' ? 'block' : ''}" class=" pd-form-component" :label-width="item.labelPosition === 'left' ? `${item.labelWidth}em` : '100%'" type="number" :required="item.required" :size="item.size" :placeholder="item.placeholder" />
        </van-cell-group>
      </template>
      <template v-else-if="item.type === 'radio'">
        <van-cell-group :style="{width: item.w+'px'}" inset>
          <van-field name="radio" :style="{display: item.labelPosition === 'top' ? 'block' : ''}" :label="item.label" :label-width="item.labelPosition === 'left' ? `${item.labelWidth}em` : '100%'" :required="item.required">
            <template #input>
              <van-radio-group direction="horizontal">
                <van-radio v-for="o in item.options" :key="o.value ?? o.label" :name="o.value">{{ o.label }}</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
      </template>
      <template v-else-if="item.type === 'checkbox'">
        <van-cell-group :style="{width: item.w+'px'}" inset>
          <van-field name="checkbox" :style="{display: item.labelPosition === 'top' ? 'block' : ''}" :label="item.label" :label-width="item.labelPosition === 'left' ? `${item.labelWidth}em` : '100%'" :required="item.required">
            <template #input>
              <van-checkbox-group direction="horizontal">
                <van-checkbox v-for="o in item.options" :key="o.label?? o.label" shape="square"> {{ o.label }}</van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
        </van-cell-group>
      </template>
      <template v-else-if="item.type === 'select'">
        <van-cell-group :style="{width: item.w+'px'}" inset>
          <van-field
            :style="{display: item.labelPosition === 'top' ? 'block' : ''}"
            readonly
            clickable
            name="picker"
            :label-width="item.labelPosition === 'left' ? `${item.labelWidth}em` : '100%'"
            :value="pickerState.value"
            :label="item.label"
            :placeholder="item.placeholder"
            :required="item.required"
          />
        </van-cell-group>
        <!-- @click="pickerState.visible = true"-->
        <!-- <van-popup v-model="pickerState.visible" position="bottom">
          <van-picker
            show-toolbar
            :columns="item.options.map(o => o.label)"
            @confirm="onConfirm"
            @cancel="pickerState.visible = false"
          />
        </van-popup>
              <el-select v-model="selectValue" class=" pd-form-component">
                 <el-option v-for="o in item.options" :key="o.value" :value="o.value" :label="o.label" />
               </el-select> -->
      </template>
      <template v-else-if="item.type === 'captcha'">
        <van-field
          center
          clearable
          :label="item.label"
          :label-width="item.labelWidth+'em'"
          :required="item.required"
          :placeholder="'请输入'+item.label"
        >
          <template #button>
            <van-button
              size="small"
              type="primary"
              :color="item.background"
              :style="{color: item.color}"
            >发送验证码</van-button>
          </template>
        </van-field>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}
.active-item::after {
  content: "";
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  border: 2px solid #84c1ff;
}
.pd-drag-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row ;
  align-items: center;
  .pd-form-component {
    flex: 1;
  }
  .pd-form-label {
    padding-right: 15px;
  }
  .is-required {
    position: relative;
    &:after {
      content: '*';
      font-size: 20px;
      color: red;
      position: absolute;
    }
  }
}

.animation-bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

.animation-breath {
  animation: breath 1.5s ease-in-out infinite;
}

@keyframes breath {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.animation-shake {
  animation: shake 2s infinite;
}

@keyframes shake {
  0%, 5%, 95%, 100% { transform: translateX(0); }
  15%, 35%, 55%, 75% { transform: translateX(-5px); }
  25%, 45%, 65%, 85% { transform: translateX(5px); }
}

.animation-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

</style>
