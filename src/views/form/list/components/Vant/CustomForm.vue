<script setup>
import { computed, ref, onMounted } from 'vue'
import { getUsablePayConfigList } from '@/api/form/form'
import { TypeNameMap } from '@/views/form/list/components/config'
import OptionsInputList from '@/views/form/list/components/OptionsInputList.vue'
import FileResSelector from '@/components/FileResSelector/index.vue'
import WxOptions from '@/views/form/list/components/WxOptions.vue'
import WxWorkOptions from '@/views/form/list/components/WxWorkOptions.vue'
import SchemeLinkSelector from '@/views/form/list/components/SchemeLinkSelector.vue'
import LinkSelect from '@/views/wx/customerLink/components/LinkSelect.vue'
import LinkGroupSelect from '@/views/wx/customerLinkGroup/index.vue'
import questionsAnswersComponents from '@/views/form/list/components/questionsAnswersComponents/questionsAnswers.vue'
import CountdownForm from '@/views/form/list/components/Js/countdown/form.vue'
import EvaluateForm from '@/views/form/list/components/Js/evaluate/form.vue'
const props = defineProps({
  selectItem: {
    type: Object,
    default: () => ({})
  },
  formType: {
    type: Number,
    default: 1
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['removeSelectItem', 'triggerEvent'])

const innerItem = computed({
  get: () => {
    return props.selectItem
  },
  set: (val) => {
    emit('update:selectItem', val)
  }
})

const isMiniProgram = computed(() => {
  return props.formType === 2
})
const activeCollapse = ref(['1', '2', '3', '4', '5'])
const showAllCollapse = () => {
  activeCollapse.value = ['1', '2', '3', '4', '5']
}

const removeSelectItem = () => {
  emit('removeSelectItem')
}

const notNil = (val) => {
  return !(val === null || val === undefined)
}

const linkVisible = ref(false)
const selectLink = (item) => {
  if (!innerItem.value.wxConfig) {
    innerItem.value.wxConfig = {}
  }
  innerItem.value.wxConfig.linkId = item.linkId
  innerItem.value.wxConfig.linkUrl = item.linkUrl
  innerItem.value.wxConfig.linkName = item.linkName
  linkVisible.value = false
}

const linkGroupVisible = ref(false)
const selectLinkGroup = (item) => {
  if (!innerItem.value.wxConfig) {
    innerItem.value.wxConfig = {}
  }
  innerItem.value.wxConfig.linkGroupId = item.id
  innerItem.value.wxConfig.linkGroupName = item.groupName
  linkGroupVisible.value = false
}

const removeLinkGroup = () => {
  innerItem.value.wxConfig = {
    ...innerItem.value.wxConfig,
    linkGroupId: undefined,
    linkGroupName: undefined
  }
}

const questionsAnswersRef = ref()
function questionsAnswerscheck() {
  return new Promise((resolve, reject) => {
    if (questionsAnswersRef.value) {
      questionsAnswersRef.value.handleSubmit().then(result => {
        if (result) {
          resolve(true)
        } else {
          resolve(false)
        }
      })
    } else {
      resolve(false)
    }
  })
}

const previewService = (item) => {
  emit('triggerEvent', 'previewService', item)
}
const previewFallAdd = (item) => {
  emit('triggerEvent', 'previewFallAdd', item)
}
const previewAction = (item) => {
  emit('triggerEvent', 'previewAction', item)
}

const addCustomField = () => {
  if (!innerItem.value.afterPay.data.customFields) {
    innerItem.value.afterPay.data.customFields = []
  }
  if (!innerItem.value.afterPay.data.title) {
    innerItem.value.afterPay.data.title = '请填写信息'
  }
  if (!innerItem.value.afterPay.data.buttonText) {
    innerItem.value.afterPay.data.buttonText = '确认提交'
  }
  innerItem.value.afterPay.data.customFields.push({
    id: new Date().getTime(),
    type: 'text',
    label: '',
    key: '',
    placeholder: '',
    required: false,
    validateType: 'none',
    regexp: '',
    errorMsg: ''
  })
}

const removeCustomField = (index) => {
  innerItem.value.afterPay.data.customFields.splice(index, 1)
}

const handleFieldTypeChange = (type, field) => {
  switch (type) {
    case 'name':
      field.label = '姓名'
      field.key = 'name'
      field.placeholder = '请输入姓名'
      field.validateType = 'none'
      field.required = true
      field.regexp = ''
      field.errorMsg = ''
      break
    case 'phone':
      field.label = '手机号'
      field.key = 'mobile'
      field.placeholder = '请输入手机号'
      field.validateType = 'phone'
      field.regexp = '^1[3-9]\\d{9}$'
      field.errorMsg = '请输入正确的手机号'
      field.required = true
      break
    case 'email':
      field.label = '邮箱'
      field.key = 'email'
      field.placeholder = '请输入邮箱地址'
      field.validateType = 'email'
      field.regexp = '^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$'
      field.errorMsg = '请输入正确的邮箱地址'
      field.required = true
      break
    default:
      // 如果是其他类型，清空相关字段
      field.label = ''
      field.placeholder = ''
      field.validateType = 'none'
      field.regexp = ''
      field.errorMsg = ''
  }
}

const handleValidateTypeChange = (type, field) => {
  switch (type) {
    case 'phone':
      field.regexp = '^1[3-9]\\d{9}$'
      field.errorMsg = '请输入正确的手机号'
      break
    case 'email':
      field.regexp = '^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$'
      field.errorMsg = '请输入正确的邮箱地址'
      break
    case 'none':
    case 'custom':
      field.regexp = ''
      field.errorMsg = ''
      break
  }
}

const moveField = (index, direction) => {
  const fields = innerItem.value.afterPay.data.customFields
  if (direction === 'up' && index > 0) {
    [fields[index - 1], fields[index]] = [fields[index], fields[index - 1]]
  } else if (direction === 'down' && index < fields.length - 1) {
    [fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]
  }
  innerItem.value.afterPay.data.customFields = [...fields]
}

const getFieldTypeTag = (type) => {
  const typeMap = {
    text: '',
    phone: 'success',
    email: 'warning',
    number: 'info'
  }
  return typeMap[type] || ''
}

const getFieldTypeLabel = (type) => {
  const typeMap = {
    text: '文本',
    phone: '手机号',
    email: '邮箱',
    number: '数字',
    name: '姓名'
  }
  return typeMap[type] || '未知类型'
}

defineExpose({
  showAllCollapse, questionsAnswerscheck
})

</script>

<template>
  <div class="form-info">
    <div class="operation-title">
      <div class="">
        {{ TypeNameMap[innerItem.type] }}
      </div>
      <div class="text-danger pointer" @click="removeSelectItem">
        <i class="el-icon-delete" /> 删 除
      </div>
    </div>
    <el-form :model="innerItem" label-width="80px" size="mini" class="operation-form" @submit.native.prevent>
      <el-form-item v-if="innerItem.type === 'richText'" label="富文本" label-width="30px">
        <editor v-model="innerItem.value" :min-height="192" />
      </el-form-item>
      <el-form-item v-if="innerItem.type === 'code'" label="代码">
        <el-input v-model="innerItem.value" clearable />
      </el-form-item>
      <el-form-item v-if="innerItem.type === 'iframe'" label="地址">
        <el-input v-model="innerItem.value" clearable />
        <el-alert
          class="mt10"
          title="部分网页会阻止添加到内嵌网页中，导致页面无法显示"
          type="warning"
          show-icon
          :closable="false"
        />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.url)" label="跳转地址">
        <el-input v-model="innerItem.url" clearable />
      </el-form-item>
      <el-form-item v-if="innerItem.type === 'img'" label="图片">
        <div class="flex">
          <el-input v-model="innerItem.value" size="mini" type="text" class="form-control flex1 mr10" autocomplete="off" placeholder="请输入图片地址" clearable />
          <FileResSelector v-model="innerItem.value" width="80" height="28" />
        </div>
      </el-form-item>
      <el-form-item v-if="innerItem.type === 'img'" label="继承提交按钮操作" label-width="80px">
        <el-switch v-model="innerItem.inheritSubmit" />
        <div class=" color-warning ">
          <i class="el-icon-warning-outline" style="margin-right: 5px;" />
          开启后，点击图片会执行提交按钮操作
        </div>
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.required)" label="是否必填">
        <el-checkbox v-model="innerItem.required" />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.labelPosition)" label="标签位置">
        <el-radio-group v-model="innerItem.labelPosition">
          <el-radio-button label="left">左</el-radio-button>
          <el-radio-button label="top">上</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.regexp)" label="正则表达式" label-width="90px">
        <el-input v-model="innerItem.regexp" clearable />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.errorMsg)" label="错误提示">
        <el-input v-model="innerItem.errorMsg" clearable />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.captcha)" label="短信验证">
        <el-switch v-model="innerItem.captcha" />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.options)" label="选项">
        <OptionsInputList :options.sync="innerItem.options" has-value />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.modelText)" label="弹框文本">
        <el-input
          v-model="innerItem.modelText"
          clearable
          type="textarea"
          :rows="2"
        />
      </el-form-item>
      <!--      <el-form-item v-if="notNil(innerItem.navigationType)" label="尺寸">-->
      <!--        <el-radio-group v-model="innerItem.navigationType">-->
      <!--          <el-radio-button label="text">文本</el-radio-button>-->
      <!--          <el-radio-button label="img">图片</el-radio-button>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item v-if="innerItem.label !== undefined" label="文字">
        <el-input v-model="innerItem.label" clearable />
      </el-form-item>
      <el-form-item v-if="innerItem.fontSize !== undefined" label="文字大小">
        <div class="flex">
          <el-slider v-model="innerItem.fontSize" class="flex1 mr10" :min="12" :max="64" :step="1" />
          <el-input-number v-model="innerItem.fontSize" :controls="false" :min="12" :max="64" style="width:66px" />
        </div>
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.width)" label="宽度">
        <div class="flex">
          <el-slider v-model="innerItem.width" class="flex1 mr10" :min="0" :max="200" :step="1" />
          <el-input-number v-model="innerItem.width" :controls="false" :min="0" :max="200" style="width:66px" />
        </div>
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.height)" label="高度">
        <div v-if="!innerItem.noMax" class="flex">
          <el-slider v-model="innerItem.height" class="flex1 mr10" :min="0" :max="200" :step="1" />
          <el-input-number v-model="innerItem.height" :controls="false" :min="0" :max="200" style="width:66px" />
        </div>
        <el-input-number v-else-if="innerItem.type !== 'iframe' || !innerItem.autoHeight" v-model="innerItem.height" :controls="false" :min="0" />
        <el-switch
          v-if="innerItem.type === 'iframe'"
          v-model="innerItem.autoHeight"
          class="ml10"
          active-color="#13ce66"
          active-text="屏幕高度"
        />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.right) && innerItem.type !== 'button'" label="右边距">
        <div class="flex">
          <el-slider v-model="innerItem.right" class="flex1 mr10" :min="0" :max="300" :step="1" />
          <el-input-number v-model="innerItem.right" :controls="false" :min="0" :max="300" style="width:66px" />
        </div>
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.bottom) && innerItem.type !== 'button'" label="下边距">
        <div class="flex">
          <el-slider v-model="innerItem.bottom" class="flex1 mr10" :min="0" :max="500" :step="1" />
          <el-input-number v-model="innerItem.bottom" :controls="false" :min="0" :max="500" style="width:66px" />
        </div>
      </el-form-item>
      <el-form-item v-if="innerItem.labelWidth !== undefined" label="标签宽度">
        <el-slider v-model="innerItem.labelWidth" :min="3" :max="8" :step="1" :disabled="innerItem.labelPosition === 'top'" />
      </el-form-item>
      <el-form-item v-if="innerItem.bold!==undefined" label="加粗">
        <el-checkbox v-model="innerItem.bold" />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.w) || notNil(innerItem.h)" label="规格">
        宽 <el-input-number v-if="notNil(innerItem.w)" v-model="innerItem.w" :controls="false" />
        高 <el-input-number v-if="notNil(innerItem.h)" v-model="innerItem.h" :controls="false" />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.size)" label="尺寸">
        <el-radio-group v-model="innerItem.size">
          <el-radio-button label="medium">默认</el-radio-button>
          <el-radio-button label="large">大</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.round)" label="圆角">
        <el-switch v-model="innerItem.round" active-color="#13ce66" inactive-color="#ff4949" />
      </el-form-item>
      <el-form-item v-if="innerItem.type==='textarea'" label="行数">
        <el-slider v-model="innerItem.rows" :min="2" :max="10" :step="1" />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.color)" label="文字颜色">
        <el-color-picker v-model="innerItem.color" />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.background)" label="背景色">
        <el-color-picker v-model="innerItem.background" />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.backgroundImage)" label="背景图片">
        <div class="flex">
          <el-input v-model="innerItem.backgroundImage" class="flex1 mr10" />
          <FileResSelector v-model="innerItem.backgroundImage" width="80" height="30" />
        </div>
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.flash)" label="动画">
        <el-radio-group v-model="innerItem.flash">
          <el-radio-button label="">无</el-radio-button>
          <el-radio-button label="bounce">跳动</el-radio-button>
          <el-radio-button label="breath">呼吸</el-radio-button>
          <el-radio-button label="shake">抖动</el-radio-button>
          <el-radio-button label="pulse">脉冲</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <template v-if="notNil(innerItem.placeholder)">
        <el-form-item label="占位文本">
          <el-input v-model="innerItem.placeholder" />
        </el-form-item>
      </template>
      <el-form-item v-if="innerItem.z !== undefined" label="显示层级">
        <el-input-number v-model="innerItem.z" :min="1" />
      </el-form-item>
      <el-form-item v-if="notNil(innerItem.suspend)" label="固定位置">
        <div class="flex align-center">
          <el-switch v-model="innerItem.suspend" active-color="#13ce66" inactive-color="#ff4949" />
          <el-tooltip content="固定位置后，表单项会固定在屏幕相对位置，不会随页面滚动而移动。" placement="top">
            <i class="el-icon-warning-outline" style="font-size: 18px;margin-left: 5px;" />
          </el-tooltip>
        </div>
      </el-form-item>
      <template v-if="innerItem.isAction">
        <el-form-item label="允许重复提交" label-width="100px">
          <el-switch
            v-model="innerItem.resubmit"
            class="ml5"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </el-form-item>
        <el-form-item label="客服微信">
          <el-input v-model="innerItem.weixin" />
        </el-form-item>
        <el-form-item label="功能">
          <el-checkbox v-if="notNil(innerItem.hasPay)" v-model="innerItem.hasPay">支付</el-checkbox>
          <el-checkbox v-if="notNil(innerItem.hasDownload)&& !isMiniProgram" v-model="innerItem.hasDownload">下载</el-checkbox>
          <el-checkbox v-if="notNil(innerItem.hasWx) && !isMiniProgram" v-model="innerItem.hasWx">加粉</el-checkbox>
        </el-form-item>
        <el-form-item v-if="innerItem.hasPay" label="支付金额">
          <el-input-number v-model="innerItem.payPrice" :controls="false" class="mr5" /> 元
        </el-form-item>
        <el-form-item v-if="innerItem.hasWx && !isMiniProgram" label="自动添加">
          <el-switch v-model="innerItem.autoAdd" />
        </el-form-item>
        <el-form-item v-if="innerItem.hasWx" label="添加提示">
          <div class="flex align-center">
            <el-switch
              v-model="innerItem.addPrompt"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
            <el-tooltip content="开启后，当用户支付成功后，添加微信时，会先弹窗提示，3 秒后再自动跳转" placement="top">
              <i class="el-icon-warning-outline" style="font-size: 18px;margin-left: 5px;" />
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item v-if="innerItem.hasPay && notNil(innerItem.preventRepeatPay)" label="阻止重复支付" label-width="100px">
          <div class="flex align-center">
            <el-switch
              v-model="innerItem.preventRepeatPay"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
            <el-tooltip content="当用户已经支付成功后，再次支付时会提示已支付，并跳转至结果页。若需大幅调整页面，建议复制到新表单后调整。" placement="top">
              <i class="el-icon-warning-outline" style="font-size: 18px;margin-left: 5px;" />
            </el-tooltip>
          </div>
        </el-form-item>
        <template v-if="innerItem.hasWx">
          <el-form-item v-if="!isMiniProgram" label="微信类型">
            <el-radio-group v-model="innerItem.wxType" @change="() => {innerItem.wxConfig = {}}">
              <!-- <el-radio-button :label="1">个人微信</el-radio-button> -->
              <el-radio-button :label="2">企业微信</el-radio-button>
              <el-radio-button :label="3">微信短链</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item v-if="innerItem.qrcode!==undefined" label="二维码">
            <div class="flex">
              <el-input v-model="innerItem.qrcode" class="flex1 mr10" />
              <FileResSelector v-model="innerItem.qrcode" width="80" height="30" />
            </div>
          </el-form-item> -->
          <template v-if="innerItem.wxType === 1">
            <el-form-item label="微信账号">
              <div class="flex gap-10">
                <el-input v-model="innerItem.wxConfig.wx" type="text" size="mini" class="flex1" placeholder="微信账号" clearable />
                <el-input v-model="innerItem.wxConfig.wxId" type="text" size="mini" class="flex1" placeholder="微信ID" clearable />
              </div>
            </el-form-item>
          </template>
          <template v-else-if="innerItem.wxType === 2">
            <el-form-item label="获客链接">
              <div class="flex gap-10">
                <el-input v-model="innerItem.wxConfig.linkUrl" type="text" size="mini" placeholder="获客链接" clearable />
                <el-button class="flex1 add-btn" type="primary" size="mini" @click="linkVisible = true">选择</el-button>
              </div>
            </el-form-item>
            <el-form-item label="获客链接组" label-width="90px">
              <div class="flex gap-10">
                <div v-if="innerItem.wxConfig.linkGroupName">{{ innerItem.wxConfig.linkGroupName }}</div>
                <el-button class="add-btn" type="primary" icon="el-icon-edit" size="mini" @click="linkGroupVisible = true">选择</el-button>
                <el-button v-if="innerItem.wxConfig.linkGroupName" class="add-btn" type="danger" icon="el-icon-delete" size="mini" @click="removeLinkGroup">移除</el-button>
              </div>
            </el-form-item>
          </template>
          <template v-else-if="innerItem.wxType === 3">
            <el-form-item label="微信短链">
              <SchemeLinkSelector :link.sync="innerItem.wxConfig.schemeLink" />
            </el-form-item>
          </template>
        </template>
        <template v-if="isMiniProgram">
          <el-form-item v-if="isMiniProgram" label="副标题" label-width="90px">
            <div class="flex align-center">
              <el-input v-model="innerItem.wxConfig.subGuideTitle" class="flex1 mr10" type="text" size="mini" placeholder="副标题，可选" clearable />
              <el-color-picker v-model="innerItem.wxConfig.subGuideTitleColor" />
            </div>
          </el-form-item>
          <el-form-item v-if="isMiniProgram" label="小程序提示" label-width="90px">
            <div class="flex align-center">
              <el-input v-model="innerItem.wxConfig.guideText" class="flex1 mr10" type="text" size="mini" placeholder="成功支付提示，未填写则默认：🎁 微信扫一扫 或 长按识别 添加微信" clearable />
              <el-tooltip content="文字颜色" placement="bottom">
                <el-color-picker v-model="innerItem.wxConfig.guideTextColor" class="mr10" />
              </el-tooltip>
              <el-tooltip content="背景颜色" placement="bottom">
                <el-color-picker v-model="innerItem.wxConfig.guideTextBgColor" />
              </el-tooltip>
            </div>
          </el-form-item>
        </template>
        <el-form-item v-if="notNil(innerItem.omitResult) && !isMiniProgram" label-width="90px" label="省略结果页">
          <el-switch
            v-model="innerItem.omitResult"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </el-form-item>
        <template v-if="innerItem.omitResult && innerItem.hasWx">
          <div class="flex align-center">
            <el-divider content-position="left">
              未添加微信提示
              <el-switch
                v-model="innerItem.popupFallAdd"
                active-color="#13ce66"
                inactive-color="#ff4949"
                style="margin-left: 10px;"
              />
            </el-divider>
            <el-button v-if="innerItem.popupFallAdd" type="primary" size="mini" style="height: 28px;margin-left: 10px;" @click="previewFallAdd(innerItem)">预览</el-button>
          </div>
          <div v-if="innerItem.popupFallAdd" style="margin-left: 20px;padding-left: 10px; border-left: 1px solid #e5e5e5;">
            <el-form-item label="停留时间" label-width="90px">
              <el-input-number v-model="innerItem.popupFallAddDuration" style="width: 100px; margin-right: 5px;" placeholder="3" :controls="false" :min="1" /> 秒
              <el-tooltip :content="`当用户停留在页面未跳转超过 ${innerItem.popupFallAddDuration || 3} 秒时，会提示用户添加微信。`" placement="top">
                <i class="el-icon-warning-outline" style="font-size: 18px;margin-left: 5px;" />
              </el-tooltip>
            </el-form-item>
            <el-form-item label="再进入提示" label-width="90px">
              <div class="flex align-center" style="height: 28px;">
                <el-switch
                  v-model="innerItem.popupOnAgain"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  style="margin-right: 10px;"
                />
                <el-tooltip :content="`开启后，当支付成功且未成功添加微信的用户再次进入页面时，会提示用户添加微信。`" placement="top">
                  <i class="el-icon-warning-outline" style="font-size: 18px;margin-left: 5px;" />
                </el-tooltip>
              </div>
            </el-form-item>
            <el-divider content-position="left">客服信息（可选）</el-divider>
            <el-form-item label="微信号">
              <el-input v-model="innerItem.weixin" />
            </el-form-item>
            <el-form-item label="联系电话">
              <el-input v-model="innerItem.phone" />
            </el-form-item>
          </div>
        </template>

        <el-form-item v-if="innerItem.hasDownload" label="资源附件">
          <FileResSelector v-model="innerItem.deliveryResource" type="file" height="30" default-selected="H5表单附件" upload-all />
        </el-form-item>
        <el-form-item v-if="innerItem.hasDownload" label="下载次数">
          <el-input-number v-if="!innerItem.downloadLimit" v-model="innerItem.maxDownloadCount" :controls="false" :min="1" />
          <el-switch
            v-model="innerItem.downloadLimit"
            class="ml5"
            active-color="#13ce66"
          />
          <span class="ml5" :style="{color: innerItem.downloadLimit? '#13ce66':'#dadada'}">不限制</span>
        </el-form-item>
      </template>
      <template v-if="notNil(innerItem.linkUrls)">
        获客链接
        <WxWorkOptions :options.sync="innerItem.linkUrls" />
      </template>
      <template v-if="innerItem.type === 'wx'">
        <!--        <el-form-item label="背景图片">-->
        <!--          <div class="flex">-->
        <!--            <el-input v-model="innerItem.backgroundImg" size="mini" type="text" class="form-control flex1 mr10" autocomplete="off" placeholder="请输入图片地址" clearable />-->
        <!--            <FileResSelector v-model="innerItem.backgroundImg" width="80" height="28" />-->
        <!--          </div>-->
        <!--        </el-form-item>-->
        <el-form-item label="微信类型">
          <el-radio-group v-model="innerItem.wxType" @change="() => {innerItem.value = [{}]}">
            <!-- <el-radio-button :label="1">个人微信</el-radio-button> -->
            <el-radio-button :label="2">企业微信</el-radio-button>
            <el-radio-button :label="3">微信短链</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <template v-if="innerItem.wxType === 1">
          <el-form-item label="回传时间">
            <el-switch
              v-model="innerItem.hasCallbackDuration"
              class="mr5"
              active-color="#13ce66"
            />
            <template v-if="innerItem.hasCallbackDuration"><el-input-number v-model="innerItem.callbackDuration" :controls="false" :min="10" /> 秒</template>
          </el-form-item>
          微信账号
          <WxOptions :options.sync="innerItem.value" />
        </template>
        <template v-else-if="innerItem.wxType === 2">
          <el-form-item v-if="innerItem.imgUrl!==undefined" label="图片链接">
            <div class="flex">
              <el-input v-model="innerItem.imgUrl" class="flex1 mr10" />
              <FileResSelector v-model="innerItem.imgUrl" width="80" height="30" />
            </div>
          </el-form-item>
          获客链接
          <WxWorkOptions :options.sync="innerItem.value" />
        </template>
        <template v-else-if="innerItem.wxType === 3">
          <el-form-item label="图片链接">
            <div class="flex">
              <el-input v-model="innerItem.imgUrl" class="flex1 mr10" />
              <FileResSelector v-model="innerItem.imgUrl" width="80" height="30" />
            </div>
          </el-form-item>
          <el-form-item label="微信短链">
            <!--            <el-input v-model="innerItem.schemeLink" class="flex1 mr10" clearable />-->
            <SchemeLinkSelector :link.sync="innerItem.schemeLink" />
            <div class="flex gap-10 mt10">
              <el-input v-model="innerItem.wx" type="text" size="mini" class="flex1" placeholder="微信账号" clearable />
              <el-input v-model="innerItem.wxId" type="text" size="mini" class="flex1" placeholder="微信ID" clearable />
            </div>
          </el-form-item>
        </template>
      </template>
      <template v-if="innerItem.type === 'service'">
        <el-divider content-position="left">客服信息</el-divider>
        <el-form-item label="微信号">
          <el-input v-model="innerItem.weixin" />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="innerItem.phone" />
        </el-form-item>
        <el-form-item label="仅支付后展示">
          <el-switch v-model="innerItem.shouldPay" />
        </el-form-item>
        <el-form-item label="预览">
          <el-button type="primary" @click="previewService(innerItem)">预览</el-button>
        </el-form-item>
      </template>
      <template v-if="innerItem.afterPay">
        <div class="flex align-center">
          <el-divider content-position="left">提交填表
            <el-switch
              v-model="innerItem.afterPay.doAfterPay"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </el-divider>
          <el-button v-if="innerItem.afterPay.doAfterPay" style="height: 28px;margin-left: 10px;" size="mini" type="primary" @click="previewAction(innerItem)">预览</el-button>
        </div>
        <div v-if="innerItem.afterPay.doAfterPay" style="margin-left: 20px;padding-left: 10px; border-left: 1px solid #e5e5e5;">
          <el-form-item label="操作类型">
            <el-radio-group v-model="innerItem.afterPay.type">
              <el-radio-button label="popup">弹窗</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="弹窗时机">
            <el-radio-group v-model="innerItem.afterPay.popupTime">
              <el-radio-button label="submit">提交时立即填表</el-radio-button>
              <el-radio-button v-if="!isMiniProgram" label="paid">支付成功后</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <!-- 修改自定义表单配置部分的模板 -->
          <el-divider content-position="left">自定义表单配置</el-divider>
          <el-form-item label="弹窗标题">
            <el-input v-model="innerItem.afterPay.data.title" placeholder="请输入弹窗标题" />
          </el-form-item>

          <el-form-item label="弹窗按钮">
            <div class="flex align-center">
              <el-input v-model="innerItem.afterPay.data.buttonText" class="mr10" placeholder="请输入按钮文字" />
              <el-color-picker v-model="innerItem.afterPay.data.buttonColor" />
            </div>
          </el-form-item>
          <el-form-item label="弹窗位置">
            <el-radio-group v-model="innerItem.afterPay.data.position">
              <el-radio-button label="default">默认</el-radio-button>
              <el-radio-button label="top">顶部</el-radio-button>
              <el-radio-button label="bottom">底部</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <div class="custom-form-container">
            <div class="custom-form-header">
              <span class="custom-form-title">表单项配置</span>
              <el-button type="primary" size="mini" plain @click="addCustomField">
                <i class="el-icon-plus" /> 添加表单项
              </el-button>
            </div>

            <div v-if="!innerItem.afterPay.data.customFields?.length" class="empty-tip">
              <i class="el-icon-document" />
              <p>暂无表单项，点击上方按钮添加</p>
            </div>

            <el-card
              v-for="(field, index) in innerItem.afterPay.data.customFields"
              :key="field.id"
              class="custom-field-card mb10"
              shadow="hover"
            >
              <template #header>
                <div class="flex justify-between align-center">
                  <div class="flex align-center">
                    <el-tag size="small" :type="getFieldTypeTag(field.type)">{{ getFieldTypeLabel(field.type) }}</el-tag>
                    <span class="ml10">{{ field.label || '未命名字段' }}</span>
                  </div>
                </div>
              </template>

              <div class="field-operations">
                <el-tooltip content="上移" placement="top" :disabled="index === 0">
                  <el-button
                    type="text"
                    :disabled="index === 0"
                    @click="moveField(index, 'up')"
                  >
                    <i class="el-icon-top" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="下移" placement="top" :disabled="index === innerItem.afterPay.data.customFields.length - 1">
                  <el-button
                    type="text"
                    :disabled="index === innerItem.afterPay.data.customFields.length - 1"
                    @click="moveField(index, 'down')"
                  >
                    <i class="el-icon-bottom" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button type="text" class="color-danger" @click="removeCustomField(index)">
                    <i class="el-icon-delete" />
                  </el-button>
                </el-tooltip>
              </div>

              <el-form label-width="80px" size="mini">
                <div class="flex gap-10">
                  <el-form-item label="字段类型" class="flex1">
                    <el-select
                      v-model="field.type"
                      placeholder="请选择字段类型"
                      style="width: 100%"
                      @change="(type) => handleFieldTypeChange(type, field)"
                    >
                      <el-option label="文本" value="text" />
                      <el-option label="姓名" value="name" />
                      <el-option label="手机号" value="phone" />
                      <el-option label="邮箱" value="email" />
                      <el-option label="数字" value="number" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="必填" label-width="50px" style="margin-bottom: 0">
                    <el-switch v-model="field.required" />
                  </el-form-item>
                </div>

                <div class="flex gap-10">
                  <el-form-item label="标签名称" class="flex1">
                    <el-input v-model="field.label" placeholder="请输入标签名称" />
                  </el-form-item>
                  <el-form-item label="验证" label-width="50px" class="flex1">
                    <el-select
                      v-model="field.validateType"
                      placeholder="验证规则"
                      style="width: 100%"
                      @change="handleValidateTypeChange($event, field)"
                    >
                      <el-option label="无" value="none" />
                      <el-option label="手机号" value="phone" />
                      <el-option label="邮箱" value="email" />
                      <el-option label="自定义" value="custom" />
                    </el-select>
                  </el-form-item>
                </div>

                <el-form-item label="占位文本">
                  <el-input v-model="field.placeholder" placeholder="请输入占位文本" />
                </el-form-item>

                <template v-if="field.validateType === 'custom'">
                  <el-form-item label="正则">
                    <el-input v-model="field.regexp" placeholder="请输入正则表达式" />
                  </el-form-item>
                </template>

                <template v-if="field.validateType !== 'none'">
                  <el-form-item label="错误提示">
                    <el-input v-model="field.errorMsg" placeholder="请输入验证失败提示语" />
                  </el-form-item>
                </template>
              </el-form>
            </el-card>
          </div>
        </div>
      </template>
      <template v-if="innerItem.type=='question'">
        <questionsAnswersComponents ref="questionsAnswersRef" v-model="innerItem" />
      </template>
      <template v-if="innerItem.type === 'countdown'">
        <countdown-form v-model="innerItem" />
      </template>
      <template v-if="innerItem.type === 'evaluate'">
        <evaluate-form v-model="innerItem" />
      </template>
    </el-form>

    <el-dialog :visible.sync="linkVisible" title="选择获客链接" top="5vh" append-to-body width="70%">
      <link-select v-if="linkVisible" :form-type="formType" @select="selectLink" />
    </el-dialog>
    <el-dialog :visible.sync="linkGroupVisible" title="选择获客链接组" top="5vh" append-to-body width="70%">
      <LinkGroupSelect v-if="linkGroupVisible" :form-type="formType" is-selector @select="selectLinkGroup" />
    </el-dialog>
  </div>

</template>

<style lang="scss" scoped>
.form-info {
  height: 100%;
  flex:1;
  .operation-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .operation-form {
    height: calc(100vh - 250px);
    overflow: auto;
  }
  .operation-no-select {
    height: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #999;
    user-select: none;
  }
  .title-icon {
    margin-right: 10px;
  }
}

.custom-field-item {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.text-center {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.mb10 {
  margin-bottom: 10px;
}

.custom-form-container {
  margin-top: 20px;
}

.custom-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.custom-form-title {
  font-weight: bold;
}

.empty-tip {
  text-align: center;
  color: #909399;
  padding: 20px;
  border: 1px dashed #EBEEF5;
  border-radius: 4px;
  i {
    font-size: 24px;
    margin-bottom: 10px;
  }
}

.custom-field-card {
  margin-bottom: 10px;
  position: relative;

  :deep(.el-card__header) {
    padding: 8px 15px;
  }

  :deep(.el-card__body) {
    padding: 10px 15px;
  }

  :deep(.el-form-item) {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.field-operations {
  position: absolute;
  right: 5px;
  top: 5px;
  z-index: 1;
  background-color: #fff;
  padding: 0 5px;
  border-radius: 4px;

  .el-button {
    padding: 3px 5px;

    &[disabled] {
      color: #c0c4cc;
    }
  }
}

.flex {
  display: flex;

  &.gap-10 {
    gap: 10px;
  }
}

.flex1 {
  flex: 1;
}

.form-item-card {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 15px;
  padding-bottom:0;
  margin-top: 10px;
}
</style>
