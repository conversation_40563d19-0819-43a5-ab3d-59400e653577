import Vue from 'vue'

import Cookies from 'js-cookie'

import UmyUi from 'umy-ui'
import 'umy-ui/lib/theme-chalk/index.css'// 引入样式
import Element from 'element-ui'
import './assets/styles/element-variables.scss'
// 全局样式
import './styles/index.scss'
import {
  Column,
  Colgroup,
  Table
} from 'vxe-table'
import 'vxe-table/lib/style.css'

// vant
import 'vant/lib/index.css'
import useVant from './utils/vant'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App.vue'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import versionPolling from './plugins/version-polling' // plugins
import { download,downloadJSON } from '@/utils/request'
import './utils/electron'

import './assets/icons' // icon
import 'virtual:svg-icons-register' // vite svg icons
import './permission' // permission control
import { getDicts } from '@/api/system/dict/data'
import { getConfigKey } from '@/api/system/config'
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from '@/utils/ruoyi'
import { dictMap } from '@/utils/dict'

// 分页组件
import Pagination from '@/components/Pagination/index.vue'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar/index.vue'
// 富文本组件
import Editor from '@/components/Editor/index.vue'
// 文件上传组件
import FileUpload from '@/components/FileUpload/index.vue'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload/index.vue'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview/index.vue'
// 字典标签组件
import DictTag from '@/components/DictTag/index.vue'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData/index.js'
// 负责人查询
import UserSearchInput from '@/components/UserSearchInput.vue'

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.downloadJSON = downloadJSON
Vue.prototype.handleTree = handleTree
Vue.prototype.dictMap = dictMap
Vue.prototype.$bus = new Vue()
// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
Vue.component('UserSearchInput', UserSearchInput)

useVant(Vue)
Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
Vue.use(versionPolling)
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.use(Column)
Vue.use(Colgroup)
Vue.use(Table)
Vue.use(UmyUi)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
