import { onUnmounted } from 'vue'

export default function({ onVisible, onHidden }) {
  function visibilitychange() {
    if (document.visibilityState === 'visible') {
      onVisible && onVisible()
    } else {
      onHidden && onHidden()
    }
  }
  document.addEventListener('visibilitychange', visibilitychange)

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', visibilitychange)
  })
}
