import dayjs from 'dayjs'

export const MediaPlatformType = {
  DOUYIN: '1',
  KUAISHOU: '2',
  WEIBO: '3',
  TENCENT: '4'
}

export const LandingImageType = {
  SLIDE: '1',
  NORMAL: '2',
  FIXED: '3',
  BOTTOM: '4'
}

function TimeAdd(picker, type, unit) {
  let date = new Date()
  if (picker.value) {
    date = picker.value[0]
  }
  picker.$emit('pick', [
    dayjs(date)[type](1, unit).startOf(unit).toDate(),
    dayjs(date)[type](1, unit).endOf(unit).toDate()
  ])
}

export const dateRangePickerOptions = {
  shortcuts: [{
    text: '今日',
    onClick(picker) {
      picker.$emit('pick', [
        dayjs(new Date()).startOf('day').toDate(),
        dayjs(new Date()).endOf('day').toDate()
      ])
    }
  }, {
    text: '昨天',
    onClick(picker) {
      picker.$emit('pick', [
        dayjs(new Date()).subtract(1, 'days').startOf('day').toDate(),
        dayjs(new Date()).subtract(1, 'days').endOf('day').toDate()
      ])
    }
  }, {
    text: '前一天',
    onClick(picker) {
      TimeAdd(picker, 'subtract', 'days')
    }
  }, {
    text: '后一天',
    onClick(picker) {
      TimeAdd(picker, 'add', 'days')
    }
  },
  {
    text: '本周',
    onClick(picker) {
      picker.$emit('pick', [
        dayjs(new Date()).startOf('week').add(1, 'day').toDate(),
        dayjs(new Date()).endOf('day').toDate()
      ])
    }
  }, {
    text: '上一周',
    onClick(picker) {
      TimeAdd(picker, 'subtract', 'week')
    }
  }, {
    text: '本月',
    onClick(picker) {
      picker.$emit('pick', [
        dayjs(new Date()).startOf('month').toDate(),
        dayjs(new Date()).endOf('day').toDate()
      ])
    }
  }, {
    text: '上一月',
    onClick(picker) {
      TimeAdd(picker, 'subtract', 'month')
    }
  }, {
    text: '近七天',
    onClick(picker) {
      const end = dayjs(new Date()).endOf('day').toDate()
      const start = dayjs(new Date()).subtract(6, 'days').startOf('day').toDate()
      picker.$emit('pick', [start, end])
    }
  }, {
    text: '近十五天',
    onClick(picker) {
      const end = dayjs(new Date()).endOf('day').toDate()
      const start = dayjs(new Date()).subtract(14, 'days').startOf('day').toDate()
      picker.$emit('pick', [start, end])
    }
  }]
}
