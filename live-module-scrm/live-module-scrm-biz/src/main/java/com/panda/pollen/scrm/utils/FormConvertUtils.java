package com.panda.pollen.scrm.utils;

import com.alibaba.fastjson2.JSON;
import com.panda.pollen.common.constant.CacheConstants;
import com.panda.pollen.common.core.redis.RedisCache;
import com.panda.pollen.common.utils.DateUtils;
import com.panda.pollen.scrm.enums.ConvertType;
import com.panda.pollen.scrm.domain.FormsResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 表单回传utils
 * <AUTHOR>
 * @Date : 2025年02月19日 11:58
 */
@Component
@Slf4j
public class FormConvertUtils {

    @Autowired
    private RedisCache redisCache;

    /**
     * 判断表单提交结果是否已经回传
     * @param convertType   转化类型
     * @param formsResults  表单提交结果
     * @return
     */
    public boolean isConverted(ConvertType convertType, FormsResults formsResults) {
        // 同一个aid + convertType + clickId只能回传一次
        String cacheKey = CacheConstants.DO_CONVERT + formsResults.getAdvertiserId() + ":" + convertType.toString() + ":" + formsResults.getClickId();
        if (redisCache.hasKey(cacheKey)) {
            log.error("当天已回传，禁止重复回传，回传表单数据：{}", JSON.toJSONString(formsResults));
            recordConvertMessage(convertType, formsResults);
            return true;
        }
        return false;
    }

    /**
     * 记录回传错误信息
     * @param convertType
     * @param formsResults
     */
    private void recordConvertMessage(ConvertType convertType, FormsResults formsResults) {
        // 如果convertType=表单提交，那么记录表单提交回传结果
        if (convertType == ConvertType.FORM_SUBMIT) {
            formsResults.setSubConvertMessage("当天已回传，禁止回传");
        }
        // convertType=表单支付，那么记录表单支付回传结果
        else if (convertType == ConvertType.FORM_PAY) {
            formsResults.setPayConvertMessage("当天已回传，禁止回传");
        }
        // convertType=表单加微，那么记录表单加微回传结果
        else if (convertType == ConvertType.FORM_WECHAT) {
            formsResults.setAddConvertMessage("当天已回传，禁止回传");
        }
    }


    /**
     * 设置表单回传标记以及结果
     * @param convertType
     * @param formsResults
     * @param convertFlag
     * @param convertMessage
     * @param timestamp
     */
    public void setConvertFlagAndMessage(ConvertType convertType, FormsResults formsResults, Integer convertFlag, String convertMessage, Long timestamp) {
        // 如果convertType=表单提交，那么设置表单提交回传结果
        if (convertType == ConvertType.FORM_SUBMIT) {
            formsResults.setSubConvertFlag(convertFlag);
            formsResults.setSubConvertMessage(convertMessage);
            formsResults.setSubConvertTimestamp(timestamp);
        }
        // 如果convertType=表单支付，那么设置表单支付回传结果
        if (convertType == ConvertType.FORM_PAY) {
            formsResults.setPayConvertFlag(convertFlag);
            formsResults.setPayConvertMessage(convertMessage);
            formsResults.setPayConvertTimestamp(timestamp);
        }
        // 如果convertType=表单加微，那么设置表单加微回传结果
        if (convertType == ConvertType.FORM_WECHAT) {
            formsResults.setAddConvertFlag(convertFlag);
            formsResults.setAddConvertMessage(convertMessage);
            formsResults.setAddConvertTimestamp(timestamp);
        }
        // 如果convertType=表单退款，那么设置表单退款回传结果
        if (convertType == ConvertType.FORM_REFUND) {
            formsResults.setRefundConvertFlag(convertFlag);
            formsResults.setRefundConvertMessage(convertMessage);
            formsResults.setRefundConvertTimestamp(timestamp);
        }

    }



    /**
     * 设置表单提交结果已经回传
     * @param
     *
     * @param formsResults
     */
    public void setConvertedCache(ConvertType convertType, FormsResults formsResults) {
        // 同一个aid + convertType + clickId只能回传一次
        String cacheKey = CacheConstants.DO_CONVERT + formsResults.getAdvertiserId() + ":" + convertType.toString() + ":" + formsResults.getClickId();
        long timeOut = DateUtils.getRemainSecondsOneDay(new Date());
        redisCache.setCacheObject(cacheKey, formsResults, (int) timeOut, TimeUnit.SECONDS);
    }


}
