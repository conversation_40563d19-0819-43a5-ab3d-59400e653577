package com.panda.pollen.scrm.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.common.constant.Constants;
import com.panda.pollen.common.core.redis.RedisCache;
import com.panda.pollen.common.core.service.ISysConfigService;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.scrm.enums.ConvertType;
import com.panda.pollen.scrm.domain.FormsResults;
import com.panda.pollen.scrm.domain.FormsSpecialResults;
import com.panda.pollen.scrm.mapper.FormsSpecialResultsMapper;
import com.panda.pollen.scrm.service.IFormsSpecialResultsService;
import com.panda.pollen.scrm.utils.FormsResultsPlanIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import static com.panda.pollen.common.constant.Constants.FORM_SPECIAL_ACCOUNT_CONVERT_SETTINGS;

/**
 * <AUTHOR>
 * @Date : 2025年06月05日 15:40
 */
@Service
@Slf4j
public class FormsSpecialResultsServiceImpl extends ServiceImpl<FormsSpecialResultsMapper, FormsSpecialResults> implements IFormsSpecialResultsService {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public void recordSpecialConvert(FormsResults formsResults) {
        try {
            boolean specialAccount = isSpecialAccount(formsResults);
            if (specialAccount) {
                FormsSpecialResults formsSpecialResults = new FormsSpecialResults();
                formsSpecialResults.setAId(formsResults.getAdvertiserId());
                formsSpecialResults.setPlanId(FormsResultsPlanIdUtils.getPlanId(formsResults));
                formsSpecialResults.setClickId(formsResults.getClickId());
                formsSpecialResults.setCreateTime(new Date());
                save(formsSpecialResults);
            }
        } catch (Exception e) {
            log.error("表单特殊账户回传标记记录出错", e);
        }
    }

    /**
     * 判断是否是特殊账号
     * @param formsResults
     * @return
     */
    @Override
    public boolean isSpecialAccount(FormsResults formsResults) {
        String aId = formsResults.getAdvertiserId();
        String planId = FormsResultsPlanIdUtils.getPlanId(formsResults);
        String clickId = formsResults.getClickId();
        if (StrUtil.startWith(aId, "__") || StrUtil.startWith(planId, "__") || StrUtil.startWith(clickId, "__")) {
            return false;
        }
        String configValue = sysConfigService.selectConfigByKey(FORM_SPECIAL_ACCOUNT_CONVERT_SETTINGS);
        if (StrUtil.isBlank(configValue)) {
            return false;
        }
        JSONObject configObject = JSONObject.parseObject(configValue);
        List<String> advertiserIdList = configObject.getList("advertiserIds", String.class);
        return advertiserIdList.contains(aId);
    }

    /**
     * 是否在比例内
     * @return
     */
    private boolean isRangeRatio() {
        String configValue = sysConfigService.selectConfigByKey(FORM_SPECIAL_ACCOUNT_CONVERT_SETTINGS);
        if (StrUtil.isBlank(configValue)) {
            return false;
        }
        JSONObject configObject = JSONObject.parseObject(configValue);
        int ratio = configObject.getIntValue("ratio", 20);
        int random = RandomUtil.randomInt(1, 101);
        return random <= ratio;
    }

    /**
     * 特殊账号回传替换
     * @param formsResults
     */
    @Override
    public void selectOneSetClickIdThenDelete(FormsResults formsResults) {
        try {
            boolean specialAccount = isSpecialAccount(formsResults);
            if (!specialAccount) {
                return;
            }
            if (!isRangeRatio()) {
                return;
            }
            String aId = formsResults.getAdvertiserId();
            String planId = FormsResultsPlanIdUtils.getPlanId(formsResults);
            String clickId = formsResults.getClickId();
            // 首先取24小时以内的最老的数据
            FormsSpecialResults formsSpecialResults = baseMapper.selectUnConvertOne(aId, planId, clickId, true);
            // 如果取不到，就取24小时以前最新的
            if (formsSpecialResults == null) {
                formsSpecialResults = baseMapper.selectUnConvertOne(aId, planId, clickId, false);
            }
            if (formsSpecialResults != null) {
                log.info("特殊账号回传替换，原来clickId={}, 替换后clickId={}", formsResults.getClickId(), formsSpecialResults.getClickId());
                baseMapper.deleteById(formsSpecialResults.getId());
                // 然后将clickId修改掉
                formsResults.setClickId(formsSpecialResults.getClickId());
                // 通过redis记录一下
                redisCache.incrementCacheMapValue(Constants.FORM_SPECIAL_ACCOUNT_CONVERT_COUNT + aId, LocalDate.now().toString());
            }
        } catch (Exception e) {
            log.error("特殊账号回传替换替换ClickID时出错：", e);
        }
    }

    /**
     * 如果是支付回传，直接走特殊账号回传标记记录里面删除掉
     * @param convertType
     * @param formsResults
     */
    @Override
    public void deletePayedResult(ConvertType convertType, FormsResults formsResults) {
        try {
            if (convertType == ConvertType.FORM_PAY && isSpecialAccount(formsResults)) {
                String planId = FormsResultsPlanIdUtils.getPlanId(formsResults);
                baseMapper.deletePayedResult(formsResults.getAdvertiserId(), planId, formsResults.getClickId());
            }
        } catch (Exception e) {
            log.error("走特殊账号回传标记记录里面删除已支付的记录出错：", e);
        }
    }
}
