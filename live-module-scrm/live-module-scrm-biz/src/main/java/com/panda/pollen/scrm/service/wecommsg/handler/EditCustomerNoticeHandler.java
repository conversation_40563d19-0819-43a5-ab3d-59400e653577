package com.panda.pollen.scrm.service.wecommsg.handler;

import com.panda.pollen.scrm.domain.AuthCorp;
import com.panda.pollen.scrm.domain.AuthCorpUserCustomer;
import com.panda.pollen.scrm.enums.WeComNoticeType;
import com.panda.pollen.scrm.model.WeComeNotifyMessage;
import com.panda.pollen.scrm.service.IAuthCorpService;
import com.panda.pollen.scrm.service.IAuthCorpUserCustomerService;
import com.panda.pollen.scrm.service.wecommsg.IWeComNoticeHandler;
import com.panda.pollen.scrm.vo.AuthCorpUserCustomerSyncVO;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 企业客户-编辑事件消息处理
 * <AUTHOR>
 * @Date : 2025年07月01日 17:13
 */
@Component
public class EditCustomerNoticeHandler implements IWeComNoticeHandler {

    @Autowired
    private IAuthCorpUserCustomerService authCorpUserCustomerService;

    @Autowired
    private IAuthCorpService authCorpService;

    @Override
    public WeComNoticeType getWeComNoticeType() {
        return WeComNoticeType.EDIT_CUSTOMER;
    }

    @Override
    public void handleWeComNotice(WeComeNotifyMessage wxMessage) throws WxErrorException {
        String corpId = wxMessage.getToUserName();
        String userId = wxMessage.getUserId();
        String externalUserId = wxMessage.getExternalUserId();
        AuthCorp authCorp = authCorpService.selectAuthCorpByCorpId(corpId, false);
        // 根据userId和externalUserId去数据库查询
        List<AuthCorpUserCustomer> authCorpUserCustomers = authCorpUserCustomerService.selectByUserIdAndExternalUserId(userId, externalUserId);
        for (AuthCorpUserCustomer customer : authCorpUserCustomers) {
            AuthCorpUserCustomerSyncVO customerSyncVO = new AuthCorpUserCustomerSyncVO();
            customerSyncVO.setId(customer.getId());
            customerSyncVO.setUserId(userId);
            customerSyncVO.setExternalUserId(externalUserId);
            customerSyncVO.setCorpId(authCorp.getCorpId());
            customerSyncVO.setPermanentCode(authCorp.getPermanentCode());
            authCorpUserCustomerService.syncUserCustomerDetail(customerSyncVO);
        }
    }
}
