package com.panda.pollen.scrm.enums;

/**
 * <AUTHOR> zlr
 */
public enum LandingPageStatus {
    TAOBAO_PUBLISH(1, "待发布"),
    TAOBAO_AUDIT(2, "淘宝审核中"),
    TAOBAO_AUDIT_REJECT(3, "淘宝被驳回"),
    TAOBAO_AUDIT_PASS(4, "已发布"),
    TAOBAO_OFFLINE(5, "已下线"),
    TAOBAO_EXPIRE(6, "已过期"),
    XM_AUDIT(7, "熊猫审核中"),
    XM_AUDIT_REJECT(8, "熊猫被驳回"),
    XM_AUDIT_PASS(9, "熊猫已通过");

    private int code;

    private String desc;

    LandingPageStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
