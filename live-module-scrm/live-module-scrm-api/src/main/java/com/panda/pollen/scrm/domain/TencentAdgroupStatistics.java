package com.panda.pollen.scrm.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.panda.pollen.common.base.BaseEntityV2;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 腾讯广告报表实体对象
 * <AUTHOR>
 * @date: 2025年07月22日 17:41
 */
@Data
@TableName(value = "ads_tencent_adgroup_statistics")
public class TencentAdgroupStatistics extends BaseEntityV2 {

    @Serial
    private static final long serialVersionUID = -8656437699550292296L;

    /**
     * 报表日期（yyyy-MM-dd）
     */
    private LocalDate statDate;

    /**
     * 媒体类型
     */
    private Integer mediaPlatformType;

    /**
     * 媒体账户ID
     */
    private String advertiserId;

    /**
     * 媒体账户名称
     */
    private String advertiserName;

    /**
     * 广告组ID
     */
    private String adgroupId;

    /**
     * 广告组名称
     */
    private String adgroupName;

    /**
     * 曝光次数。广告被展示给用户的次数
     */
    private BigDecimal viewCount;

    /**
     * 曝光人数。产生广告曝光的独立用户数
     */
    private BigDecimal viewUserCount;

    /**
     * 人均曝光次数。人均产生的广告曝光次数，计算公式是：曝光次数 / 曝光人数
     */
    private BigDecimal avgViewPerUser;

    /**
     * 点击次数。广告被用户点击的次数
     */
    private BigDecimal validClickCount;

    /**
     * 点击人数。点击广告的独立用户数
     */
    private BigDecimal clickUserCount;

    /**
     * 点击均价。广告主为每次点击付出的费用成本，计算公式是：花费 / 点击次数
     */
    private BigDecimal cpc;

    /**
     * 点击率。广告被点击的比率，计算公式是：点击次数 / 曝光次数 * 100%
     */
    private BigDecimal ctr;

    /**
     * 花费。广告主为广告投放总共付出的费用成本，实际花费请以财务记录为准
     */
    private BigDecimal cost;

    /**
     * 一键起量消耗。广告在一键起量过程中产生的消耗，若多次使用一键起量，则为多次起量的总消耗
     */
    private BigDecimal acquisitionCost;

    /**
     * 千次展现均价。广告平均每一千次展现所付出的费用，计算公式是：花费 / 曝光次数 * 1000
     */
    private BigDecimal thousandDisplayPrice;

    /**
     * 目标转化量。优化目标的转化量
     */
    private BigDecimal conversionsCount;

    /**
     * 目标转化率。优化目标的点击转化率，计算公式是：目标转化量 / 点击次数 * 100%
     */
    private BigDecimal conversionsRate;

    /**
     * 目标转化成本。广告主为每个目标转化量付出的费用成本，计算公式是：花费 / 目标转化量
     */
    private BigDecimal conversionsCost;

    /**
     * 深度目标转化量。深度优化目标的转化量（如果没有使用深度转化优化功能，这个指标可能为空）
     */
    private BigDecimal deepConversionsCount;

    /**
     * 深度目标转化率。深度优化目标的点击转化率，计算公式是：深度目标转化量 / 点击次数 * 100%
     */
    private BigDecimal deepConversionsRate;

    /**
     * 深度转化成本。广告主为每个深度目标转化量付出的费用成本，计算公式是：花费 / 深度目标转化量
     */
    private BigDecimal deepConversionsCost;

    /**
     * 视频有效播放次数。在广告效曝光的前提下产生的视频有效播放数量，视频封面图曝光等场景不计算在内
     */
    private BigDecimal videoOuterPlayCount;

    /**
     * 视频有效播放人数。在广告效曝光的前提下产生的视频有效播放的用户数，视频封面图曝光等场景不计算在内
     */
    private BigDecimal videoOuterPlayUserCount;

    /**
     * 人均播放次数。人均有效播放朋友圈外层视频的次数，计算公式：视频有效播放次数 / 朋友圈外层视频播放人数
     */
    private BigDecimal avgUserPlayCount;

    /**
     * 平均有效播放时长。平均有效播放时长，计算公式：视频有效播放总时长 / 视频有效播放次数
     */
    private BigDecimal videoOuterPlayTimeCount;

    /**
     * 平均有效播放进度。平均有效播放进度，计算公式：视频有效播放总时长 / 视频播放总时长
     */
    private BigDecimal videoOuterPlayTimeAvgRate;

    /**
     * 有效播放率。曝光有效播放率，计算公式：视频有效播放次数 / 曝光次数 * 100%
     */
    private BigDecimal videoOuterPlayRate;

    /**
     * 有效播放成本。广告主为每次视频有效播放付出的费用成本，计算公式：花费 / 视频有效播放量
     */
    private BigDecimal videoOuterPlayCost;

    /**
     * 10%进度播放次数。播放进度大于等于10%的有效播放量
     */
    private BigDecimal videoOuterPlay10Count;

    /**
     * 25%进度播放次数。播放进度大于等于25%的有效播放量
     */
    private BigDecimal videoOuterPlay25Count;

    /**
     * 50%进度播放次数。播放进度大于等于50%的有效播放量
     */
    private BigDecimal videoOuterPlay50Count;

    /**
     * 75%进度播放次数。播放进度大于等于75%的有效播放量
     */
    private BigDecimal videoOuterPlay75Count;

    /**
     * 90%进度播放次数。播放进度大于等于90%的有效播放量
     */
    private BigDecimal videoOuterPlay90Count;

    /**
     * 95%进度播放次数。播放进度大于等于95%的有效播放量
     */
    private BigDecimal videoOuterPlay95Count;

    /**
     * 100%进度播放次数。播放进度100%的有效播放量
     */
    private BigDecimal videoOuterPlay100Count;

    /**
     * 3s播放完成次数。有效播放视频，播放进度大于等于3s的有效播放量
     */
    private BigDecimal videoOuterPlay3sCount;

    /**
     * 5s播放完成次数。有效播放视频，播放进度大于等于5s的有效播放量
     */
    private BigDecimal videoOuterPlay5sCount;

    /**
     * 7s播放完成次数。有效播放视频，播放进度大于等于7s的有效播放量
     */
    private BigDecimal videoOuterPlay7sCount;

    /**
     * 评论次数。广告被受众评论的数量
     */
    private BigDecimal commentCount;

    /**
     * 评论成本。广告主为每次评论行为付出的费用成本，计算公式是：花费 / 评论次数
     */
    private BigDecimal commentCost;

    /**
     * 点赞次数。表示广告被受众点赞的数量（微信视频号版位下点赞量=喜欢+推荐+私密赞）仅包含广告外层的点赞
     */
    private BigDecimal praiseCount;

    /**
     * 点赞成本。广告主为每次点赞行为付出的费用成本，计算公式是：花费 / 点赞次数
     */
    private BigDecimal praiseCost;

    /**
     * 分享次数。广告被受众转发分享的数量
     */
    private BigDecimal forwardCount;

    /**
     * 分享成本。广告主为每次转发分享行为付出的费用成本，计算公式是：花费 / 分享次数
     */
    private BigDecimal forwardCost;

    /**
     * 不感兴趣点击次数。用户点击不感兴趣广告次数，目前仅朋友圈流量可统计
     */
    private BigDecimal noInterestCount;

    /**
     * 表单展示数量
     */
    private BigDecimal formShowCount;

    /**
     * 表单提交数量
     */
    private BigDecimal formSubCount;

    /**
     * 表单提交回传数量
     */
    private BigDecimal formSubConvertCount;

    /**
     * 表单提交扣回传数量
     */
    private BigDecimal formSubDeductionCount;

    /**
     * 表单支付数量（含退款）
     */
    private BigDecimal formPayCount;

    /**
     * 表单支付数量（不含退款）
     */
    private BigDecimal formPayRealCount;

    /**
     * 表单支付回传数量
     */
    private BigDecimal formPayConvertCount;

    /**
     * 表单支付扣回传数量
     */
    private BigDecimal formPayDeductionCount;

    /**
     * 表单支付金额（含退款）
     */
    private BigDecimal formPayAmount;

    /**
     * 表单支付金额（不含退款）
     */
    private BigDecimal formPayRealAmount;

    /**
     * 表单退款数量
     */
    private BigDecimal formRefundCount;

    /**
     * 表单退款金额
     */
    private BigDecimal formRefundAmount;

    /**
     * 表单退款回传数量
     */
    private BigDecimal formRefundConvertCount;

    /**
     * 表单退款扣回传数量
     */
    private BigDecimal formRefundDeductionCount;

    /**
     * 表单加微数量
     */
    private BigDecimal formAddCount;

    /**
     * 表单加微回传数量
     */
    private BigDecimal formAddConvertCount;

    /**
     * 表单加微扣回传数量
     */
    private BigDecimal formAddDeductionCount;

    /**
     * 删除状态（1-删除 0-未删除 ）
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

}
