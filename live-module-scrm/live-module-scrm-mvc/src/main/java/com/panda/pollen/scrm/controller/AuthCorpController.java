package com.panda.pollen.scrm.controller;

import com.panda.pollen.common.annotation.Log;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxPageResult;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import com.panda.pollen.common.enums.BusinessType;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.scrm.dto.AuthCorpMenuNameDTO;
import com.panda.pollen.scrm.dto.SyncCorpGroupDTO;
import com.panda.pollen.scrm.query.AuthCorpListQuery;
import com.panda.pollen.scrm.service.IAuthCorpService;
import com.panda.pollen.scrm.vo.AuthCorpVO;
import com.panda.pollen.wecom.config.WeComProperties;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxTpCustomizedAuthUrl;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 企微主体管理
 * <AUTHOR>
 * @date 2024-04-29
 */
@RestController
@RequestMapping("/wx/authCorp")
@Slf4j
public class AuthCorpController extends BaseController {

    @Autowired
    private IAuthCorpService authCorpService;

    @Autowired
    private WxCpTpService wxCpTpService;

    @Autowired
    private WeComProperties weComProperties;

    /**
     * 获取企业微信授权URL（依赖权限：wx:authCorp:add）
     * @return
     */
    @GetMapping("/getCorpAuthUrl")
    @PreAuthorize("@ss.hasPermi('wx:authCorp:add')")
    public AjaxResultV2<String> getCorpAuthUrl() throws WxErrorException {
        List<String> templateIdList = new ArrayList<>();
        templateIdList.add(weComProperties.getSuiteId());
        // 接口文档地址：https://developer.work.weixin.qq.com/document/path/98744
        WxTpCustomizedAuthUrl wxTpCustomizedAuthUrl = wxCpTpService.getCustomizedAuthUrl(SecurityUtils.getUsername(), templateIdList);
        return AjaxResultV2.success("success", wxTpCustomizedAuthUrl.getQrCodeURL());
    }


    /**
     * 分页查询主体列表（依赖权限：wx:authCorp:list）
     * @param listQuery
     * @return
     */
    @GetMapping("/list")
    public AjaxPageResult<AuthCorpVO> list(AuthCorpListQuery listQuery) {
        startPage();
        List<AuthCorpVO> dataList = authCorpService.queryAuthCorpList(listQuery);
        return AjaxPageResult.build(dataList);
    }


    /**
     * 删除企业授权信息（依赖权限：wx:authCorp:delete）
     * @param ids
     * @return
     */
    @PreAuthorize("@ss.hasPermi('wx:authCorp:delete')")
    @Log(title = "企业授权信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResultV2<Void> remove(@PathVariable Long[] ids) {
        authCorpService.deleteById(Arrays.asList(ids));
        return AjaxResultV2.success();
    }

    /**
     * 同步获客助手剩余量
     * @return
     */
    @GetMapping("/syncAcquisitionQuota")
    public AjaxResultV2<Void> syncAcquisitionQuota(String corpId) {
        authCorpService.syncAcquisitionQuota(corpId);
        return AjaxResultV2.success();
    }

    /**
     * 链接消息应用配置（依赖权限：wx:authCorp:menu）
     * @param menuNameDTO
     * @return
     */
    @PostMapping("/setMenuName")
    @PreAuthorize("@ss.hasPermi('wx:authCorp:menu')")
    public AjaxResultV2<Void> setMenuName(@RequestBody @Validated AuthCorpMenuNameDTO menuNameDTO) {
        authCorpService.setMenuName(menuNameDTO);
        return AjaxResultV2.success();
    }

    /**
     * 同步企微主体可见范围成员（异步任务，依赖权限：wx:authCorp:sync）
     *
     * @param syncCorpGroupDTO
     */
    @PreAuthorize("@ss.hasPermi('wx:authCorp:sync')")
    @GetMapping("/syncCorpAllowUsers")
    public AjaxResultV2<Void> syncCorpAllowUsers(SyncCorpGroupDTO syncCorpGroupDTO) {
        authCorpService.recordSyncCorpAllowUsers(syncCorpGroupDTO);
        return AjaxResultV2.success("同步企微主体可见范围成员任务创建成功，您可前往右上角【任务中心】查看任务进度~");
    }
}
