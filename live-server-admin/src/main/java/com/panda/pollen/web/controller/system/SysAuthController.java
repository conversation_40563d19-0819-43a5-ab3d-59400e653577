package com.panda.pollen.web.controller.system;

import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.panda.pollen.common.constant.Constants;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.common.core.domain.entity.SysAuthUser;
import com.panda.pollen.common.core.domain.entity.SysUser;
import com.panda.pollen.common.core.domain.model.LoginUser;
import com.panda.pollen.common.core.service.ISysUserService;
import com.panda.pollen.common.enums.UserStatus;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.AuthUtils;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.common.utils.StringUtils;
import com.panda.pollen.framework.web.service.SysPermissionService;
import com.panda.pollen.framework.web.service.TokenService;
import com.panda.pollen.modules.domain.ThridAuthConfig;
import com.panda.pollen.modules.domain.ThridAuthorizes;
import com.panda.pollen.system.mapper.SysUserMapper;
import com.panda.pollen.web.core.cache.AuthStateRedisCache;

import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;

/**
 * 第三方认证授权处理
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/system/auth")
public class SysAuthController extends BaseController {
	
	@Autowired
	private AuthStateRedisCache authStateCache;

	@Autowired
	private ISysUserService userService;

	@Autowired
	private SysPermissionService permissionService;

	@Autowired
	private TokenService tokenService;

	@Autowired
	private SysUserMapper userMapper;

	/**
	 * 认证授权
	 * 
	 * @param source
	 * @throws IOException
	 */
	@GetMapping("/binding/{source}")
	@ResponseBody
	public AjaxResult authBinding(@PathVariable("source") String source, HttpServletRequest request)
			throws IOException {
		LoginUser tokenUser = tokenService.getLoginUser(request);
		if (StringUtils.isNotNull(tokenUser) && userMapper.checkAuthUser(tokenUser.getUserId(), source) > 0) {
			return error(source + "平台账号已经绑定");
		}
		Map<String, ThridAuthorizes> auths = ThridAuthConfig.get(source);
		if (MapUtils.isEmpty(auths)) {
			return error(source + "平台账号暂不支持");
		}
		ThridAuthorizes authorizes = auths.get(getDomain(request));
		AuthRequest authRequest = AuthUtils.getAuthRequest(source, authorizes.getClientId(),
				authorizes.getClientSecret(), authorizes.getRedirectUri(), authStateCache);
		String authorizeUrl = authRequest.authorize(AuthStateUtils.createState());
		return success(authorizeUrl);
	}
	
	private String getDomain(HttpServletRequest request) {
		String serverName = request.getServerName();
		log.info("请求域名为：{} ---------->>> ", serverName);
		return serverName;
	}

	@SuppressWarnings("unchecked")
	@GetMapping("/social-login/{source}")
	public AjaxResult socialLogin(@PathVariable("source") String source, AuthCallback callback,
			HttpServletRequest request) {
		Map<String, ThridAuthorizes> auths = ThridAuthConfig.get(source);
		if (MapUtils.isEmpty(auths)) {
			return AjaxResult.error(10002, "第三方平台系统不支持或未提供来源");
		}
		ThridAuthorizes authorizes = auths.get(getDomain(request));
		AuthRequest authRequest = AuthUtils.getAuthRequest(source, authorizes.getClientId(),
				authorizes.getClientSecret(), authorizes.getRedirectUri(), authStateCache);
		AuthResponse<AuthUser> response = authRequest.login(callback);
		if (response.ok()) {
			LoginUser tokenUser = tokenService.getLoginUser(request);
			if (StringUtils.isNotNull(tokenUser)) {
				SysUser user = userMapper.selectAuthUserByUuid(source + response.getData().getUuid());
				if (StringUtils.isNotNull(user)) {
					String token = tokenService.createToken(SecurityUtils.getLoginUser());
					return success().put(Constants.TOKEN, token);
				}
				// 若已经登录则直接绑定系统账号
				SysAuthUser authUser = new SysAuthUser();
				authUser.setAvatar(response.getData().getAvatar());
				authUser.setUuid(source + response.getData().getUuid());
				authUser.setUserId(SecurityUtils.getUserId());
				authUser.setUserName(response.getData().getUsername());
				authUser.setNickName(response.getData().getNickname());
				authUser.setEmail(response.getData().getEmail());
				authUser.setSource(source);
				userMapper.insertAuthUser(authUser);
				String token = tokenService.createToken(SecurityUtils.getLoginUser());
				return success().put(Constants.TOKEN, token);
			}
			String thridToken = source + response.getData().getUuid();
			SysUser authUser = userMapper.selectAuthUserByUuid(thridToken);
			if (StringUtils.isNotNull(authUser)) {
				SysUser user = userService.selectUserByUserName(authUser.getUserName());
				if (StringUtils.isNull(user)) {
					throw new ServiceException("登录用户：" + user.getUserName() + " 不存在");
				} else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
					throw new ServiceException("对不起，您的账号：" + user.getUserName() + " 已被删除");
				} else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
					throw new ServiceException("对不起，您的账号：" + user.getUserName() + " 已停用");
				}
				LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(), user,
						permissionService.getMenuPermission(user), thridToken);
				String token = tokenService.createToken(loginUser);
				return success().put(Constants.TOKEN, token);
			} else {
				return AjaxResult.error(10002, "对不起，您没有绑定注册用户，请先注册后在个人中心绑定第三方授权信息！");
			}
		}
		return AjaxResult.error(10002, "对不起，授权信息验证不通过，请联系管理员");
	}

	/**
	 * 取消授权
	 */
	@DeleteMapping(value = "/unlock/{authId}")
	public AjaxResult unlockAuth(@PathVariable Long authId) {
		return toAjax(userMapper.deleteAuthUser(authId));
	}
}
