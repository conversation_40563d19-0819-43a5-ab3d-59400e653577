package com.panda.pollen.modules.ads.manager.impl;

import cn.hutool.core.util.NumberUtil;
import com.panda.pollen.ocean.model.advertiser.AdvertiserInfo;
import com.panda.pollen.ocean.service.OceanTokenService;
import com.panda.pollen.tencent.model.TencentAdvertiser;
import com.panda.pollen.tencent.service.V3.TencentAdvertiserV3Service;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.common.enums.ads.TxOneIndustryEnum;
import com.panda.pollen.common.enums.ads.TxTwoIndustryEnum;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.modules.ads.domain.MediaAccountInfo;
import com.panda.pollen.modules.ads.manager.MediaAccountIndustrySyncManager;
import com.panda.pollen.modules.ads.service.IMediaAccountInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class MediaAccountIndustrySyncManagerImpl implements MediaAccountIndustrySyncManager {

    @Autowired
    private OceanTokenService oceanTokenService;

    @Autowired
    private IMediaAccountInfoService mediaAccountInfoService;

    @Autowired
    private TencentAdvertiserV3Service tencentAdvertiserService;

    @Autowired
    private IMediaAccountInfoService iMediaAccountInfoService;

    @Override
    public void doSyncOcean(List<MediaAccountInfo> mediaAccountInfoList) {
        for (MediaAccountInfo mediaAccountInfo : mediaAccountInfoList) {
            doSyncOcean(mediaAccountInfo);
        }
    }

    public void doSyncTx(List<MediaAccountInfo> mediaAccountInfoList) {
        for (MediaAccountInfo mediaAccountInfo : mediaAccountInfoList) {
            String accessToken = mediaAccountInfo.getAccessToken();
            String advertiserId = mediaAccountInfo.getAdvertiserId();
            List<TencentAdvertiser> advertiserInfoList = tencentAdvertiserService.getAdvertiserList(accessToken, advertiserId);
            if (ListUtils.isNotEmpty(advertiserInfoList)) {
                TencentAdvertiser advertiserInfo = advertiserInfoList.get(0);
                if (advertiserInfo.getSystemIndustryId() != null) {
                    TxTwoIndustryEnum txTwoIndustryEnum = TxTwoIndustryEnum.get(advertiserInfo.getSystemIndustryId().toString());
                    if (txTwoIndustryEnum != null) {
                        TxOneIndustryEnum txOneIndustryEnum = TxOneIndustryEnum.getByCode(txTwoIndustryEnum.getCollection());
                        if (txOneIndustryEnum != null) {
                            mediaAccountInfo.setFirstIndustryName(txOneIndustryEnum.getName());
                        }
                        mediaAccountInfo.setSecondIndustryName(txTwoIndustryEnum.getDesc());
                    }
                    mediaAccountInfo.setCompanyName(advertiserInfo.getCompanyName());
                    mediaAccountInfoService.updateMediaAccountIndustry(mediaAccountInfo);
                }
            }
        }
    }

    @Override
    public void doSyncIndustryName(List<MediaAccountInfo> mediaAccountInfoList, Integer mediaType) {
        if (mediaType == MediaTypeEnum.OCEANDP.getCode() || mediaType == MediaTypeEnum.OCEAN.getCode()) {
            doSyncOcean(mediaAccountInfoList);
        } else if (mediaType == MediaTypeEnum.TENCENT.getCode()) {
            doSyncTx(mediaAccountInfoList);
        }
    }

    @Override
    public void doSyncOcean(MediaAccountInfo mediaAccountInfo) {
        String accessToken = mediaAccountInfo.getAccessToken();
        String advertiserId = mediaAccountInfo.getAdvertiserId();
        List<AdvertiserInfo> advertiserInfoList = oceanTokenService.getAdvertiserInfoList(accessToken, Collections.singletonList(NumberUtil.parseLong(advertiserId)));
        if (ListUtils.isNotEmpty(advertiserInfoList)) {
            AdvertiserInfo advertiserInfo = advertiserInfoList.get(0);
            if (StringUtils.isNotEmpty(advertiserInfo.getFirstIndustryName())
                    || StringUtils.isNotEmpty(advertiserInfo.getSecondIndustryName())) {
                mediaAccountInfo.setFirstIndustryName(advertiserInfo.getFirstIndustryName());
                mediaAccountInfo.setSecondIndustryName(advertiserInfo.getSecondIndustryName());
                mediaAccountInfo.setCompanyName(advertiserInfo.getCompanyName());
                mediaAccountInfoService.updateMediaAccountIndustry(mediaAccountInfo);
            }
        }
    }


    @Override
    public void doSyncTx(MediaAccountInfo mediaAccountInfo) {
        String accessToken = mediaAccountInfo.getAccessToken();
        String advertiserId = mediaAccountInfo.getAdvertiserId();
        List<TencentAdvertiser> advertiserInfoList = tencentAdvertiserService.getAdvertiserList(accessToken, advertiserId);
        if (ListUtils.isNotEmpty(advertiserInfoList)) {
            TencentAdvertiser advertiserInfo = advertiserInfoList.get(0);
            if (advertiserInfo.getSystemIndustryId() != null) {
                TxTwoIndustryEnum txTwoIndustryEnum = TxTwoIndustryEnum.get(advertiserInfo.getSystemIndustryId().toString());
                if (txTwoIndustryEnum != null) {
                    TxOneIndustryEnum txOneIndustryEnum = TxOneIndustryEnum.getByCode(txTwoIndustryEnum.getCollection());
                    if (txOneIndustryEnum != null) {
                        mediaAccountInfo.setFirstIndustryName(txOneIndustryEnum.getName());
                    }
                    mediaAccountInfo.setSecondIndustryName(txTwoIndustryEnum.getDesc());
                }
                mediaAccountInfo.setCompanyName(advertiserInfo.getCompanyName());
                mediaAccountInfoService.updateMediaAccountIndustry(mediaAccountInfo);
            }
        }
    }
}
