package com.panda.pollen.modules.ads.controller;

import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.modules.ads.dto.CustomColumnDTO;
import com.panda.pollen.modules.ads.service.ICustomColumnService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 自定义列Controller
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
@RestController
@RequestMapping("/custom/column")
public class CustomColumnController extends BaseController {

    @Autowired
    private ICustomColumnService customColumnService;


    /**
     * 获取自定义列详细信息
     */
    @GetMapping( value = {"/get/{page}","/get"})
    public AjaxResult getInfo(@PathVariable(required = false) String page) {
        return success(customColumnService.getInfo(page));
    }

    /**
     * 新增或者修改自定义列
     */
    @PostMapping
    public AjaxResult add(@RequestBody CustomColumnDTO columnDTO) {
        customColumnService.addOrModify(columnDTO);
        return success();
    }

    /**
     * 删除自定义列
     */
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable Long id) {
        customColumnService.removeById(id);
        return success();
    }

}
