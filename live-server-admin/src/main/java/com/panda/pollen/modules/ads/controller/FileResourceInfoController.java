package com.panda.pollen.modules.ads.controller;

import com.panda.pollen.common.annotation.Log;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.common.core.page.TableDataInfo;
import com.panda.pollen.common.enums.BusinessType;
import com.panda.pollen.common.utils.poi.ExcelUtil;
import com.panda.pollen.modules.ads.domain.FileResourceInfo;
import com.panda.pollen.modules.ads.vo.FileResourceInfoVO;
import com.panda.pollen.modules.ads.service.IFileResourceInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 资源文件Controller
 * 
 * <AUTHOR>
 * @date 2023-05-06
 */
@RestController
@RequestMapping("/promotion/fileres")
public class FileResourceInfoController extends BaseController {

    @Autowired
    private IFileResourceInfoService fileResourceInfoService;

    /**
     * 查询资源文件列表
     */
    @PreAuthorize("@ss.hasPermi('promotion:fileres:list')")
    @GetMapping("/list")
    public TableDataInfo list(FileResourceInfo fileResourceInfo) {
        startPage();
        fileResourceInfo.setCreateBy(getUsername());
        List<FileResourceInfoVO> list = fileResourceInfoService.queryFileResourceInfoList(fileResourceInfo);
        return getDataTable(list);
    }

    /**
     * 导出资源文件列表
     */
//    @PreAuthorize("@ss.hasPermi('promotion:fileres:export')")
    @Log(title = "导出资源文件列表", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
    public void export(HttpServletResponse response, FileResourceInfo fileResourceInfo) {
        List<FileResourceInfoVO> list = fileResourceInfoService.queryFileResourceInfoList(fileResourceInfo);
        ExcelUtil<FileResourceInfoVO> util = new ExcelUtil<FileResourceInfoVO>(FileResourceInfoVO.class);
        util.exportExcel(response, list, "资源文件数据");
    }

    /**
     * 获取资源文件详细信息
     */
    @PreAuthorize("@ss.hasPermi('promotion:fileres:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(fileResourceInfoService.getById(id));
    }

    /**
     * 新增资源文件
     */
//    @PreAuthorize("@ss.hasPermi('promotion:fileres:add')")
    @Log(title = "新增资源文件", businessType = BusinessType.INSERT)
//    @PostMapping
    public AjaxResult add(@RequestBody FileResourceInfo fileResourceInfo) {
        return toAjax(fileResourceInfoService.save(fileResourceInfo));
    }

    /**
     * 修改资源文件
     */
    @PreAuthorize("@ss.hasPermi('promotion:fileres:edit')")
    @Log(title = "修改资源文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FileResourceInfo fileResourceInfo) {
        FileResourceInfo resourceInfo=new FileResourceInfo();
        resourceInfo.setId(fileResourceInfo.getId());
        resourceInfo.setFileName(fileResourceInfo.getFileName());
        resourceInfo.setUpdateBy(getLoginUser().getUsername());
        resourceInfo.setUpdateTime(new Date());
        return toAjax(fileResourceInfoService.updateById(fileResourceInfo));
    }

    /**
     * 删除资源文件
     */
    @PreAuthorize("@ss.hasPermi('promotion:fileres:remove')")
    @Log(title = "删除资源文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(fileResourceInfoService.removeByIds(Arrays.asList(ids)));
    }
    
}
