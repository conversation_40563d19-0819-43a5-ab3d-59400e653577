package com.panda.pollen.pay.dto;

import com.panda.pollen.scrm.enums.PayBusinessType;
import com.panda.pollen.scrm.enums.PayType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date : 2025年03月03日 10:23
 */
@Data
public class RefundInfoDTO {

    /**
     * 支付业务类型
     */
    private PayBusinessType payBusinessType;

    /**
     * 支付类型-必填
     */
    private PayType payType;

    /**
     * 支付流水号
     */
    private String payNo;

    /**
     * 商户订单编号
     */
    private String outTradeNo;

    /**
     * 回调通知地址-必填
     */
    private String notifyUrl;

    /**
     * 退款金额（单位：分）
     */
    private Long refundAmount;


}
