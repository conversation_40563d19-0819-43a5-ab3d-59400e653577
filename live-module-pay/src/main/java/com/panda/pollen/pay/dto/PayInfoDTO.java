package com.panda.pollen.pay.dto;

import cn.hutool.core.util.StrUtil;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.scrm.enums.PayBusinessType;
import com.panda.pollen.scrm.enums.PayType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date : 2024年02月29日 10:06
 */
@Data
public class PayInfoDTO {

    /**
     * 支付业务类型
     */
    private PayBusinessType payBusinessType;

    /**
     * 支付类型-必填
     */
    private PayType payType;

    /**
     * 商品信息-必填
     */
    private String goodsName;

    /**
     * 商户订单编号-必填
     */
    private String outTradeNo;

    /**
     * 回调通知地址-必填
     */
    private String notifyUrl;

    /**
     * 支付完成跳转地址
     */
    private String returnUrl;

    /**
     * 支付金额（单位：分）-必填
     */
    private Long payAmount;

    /**
     * 微信服务号用户openId
     */
    private String mpOpenId;

    /**
     * 微信小程序用户openId
     */
    private String maOpenId;

    /**
     * 校验参数格式
     */
    public void validateParams() {
        if (payBusinessType == null) {
            throw new ServiceException("请指定支付业务类型");
        }
        if (payType == null) {
            throw new ServiceException("请指定支付类型");
        }
        if (StrUtil.isBlank(goodsName)) {
            throw new ServiceException("商品描述不能为空");
        }
        if (StrUtil.isBlank(outTradeNo)) {
            throw new ServiceException("商户订单号不能为空");
        }
        if (StrUtil.isBlank(notifyUrl)) {
            throw new ServiceException("支付回调通知地址不能为空");
        }
        if (payAmount == null || payAmount <= 0) {
            throw new ServiceException("支付的金额不能小于1分钱");
        }
    }

}
