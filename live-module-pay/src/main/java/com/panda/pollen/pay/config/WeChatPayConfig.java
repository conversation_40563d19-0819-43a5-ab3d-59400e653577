package com.panda.pollen.pay.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceHttpClientImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedissonConfigImpl;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.panda.pollen.common.constant.PayConstants;
import com.panda.pollen.common.core.service.ISysConfigService;
import com.panda.pollen.scrm.enums.PayType;
import lombok.Data;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceHttpClientImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 微信支付所需配置
 * <AUTHOR>
 * @Date : 2024年02月28日 16:50
 */
@Component
@Data
public class WeChatPayConfig {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 获取默认的微信支付WxPayConfig配置对象
     * @return
     */
    public WxPayConfig getDefaultWxPayConfig(PayType payType) {
        WxPayConfig wxPayConfig = new WxPayConfig();
        // 微信支付对应的AppId
        if (payType == PayType.WECHAT_MA) {
            wxPayConfig.setAppId(sysConfigService.selectConfigByKey(PayConstants.WECHAT_MA_APP_ID_KEY));
        } else {
            wxPayConfig.setAppId(sysConfigService.selectConfigByKey(PayConstants.WECHAT_MP_APP_ID_KEY));
        }
        // 微信支付mchId
        wxPayConfig.setMchId(sysConfigService.selectConfigByKey(PayConstants.WECHAT_MCH_ID_KEY));
        // 微信支付 apiv3Key
        wxPayConfig.setApiV3Key(sysConfigService.selectConfigByKey(PayConstants.WECHAT_API_V3_KEY));
        // 微信支付证书序列号
        wxPayConfig.setCertSerialNo(sysConfigService.selectConfigByKey(PayConstants.WECHAT_CERT_SERIAL_NO_KEY));
        // 微信支付 apiv3 商户apiclient_key.pem内容
        wxPayConfig.setPrivateKeyContent(sysConfigService.selectConfigByKey(PayConstants.WECHAT_PRIVATE_KEY_CONTENT_KEY).getBytes());
        // 微信支付 apiv3 商户apiclient_cert.pem内容
        wxPayConfig.setPrivateCertContent(sysConfigService.selectConfigByKey(PayConstants.WECHAT_PRIVATE_CERT_CONTENT_KEY).getBytes());
        return wxPayConfig;
    }


    /**
     * 获取默认的WxMaService对象
     * @return
     */
    public WxMaService getDefaultWxMaService() {
        // access token用redis缓存
        String appId = sysConfigService.selectConfigByKey(PayConstants.WECHAT_MA_APP_ID_KEY);
        String appSecret = sysConfigService.selectConfigByKey(PayConstants.WECHAT_MA_APP_SECRET_KEY);
        WxMaRedissonConfigImpl maRedissonConfig = new WxMaRedissonConfigImpl(redissonClient, "ma_config_key_prefix:" + appId);
        maRedissonConfig.setAppid(appId);
        maRedissonConfig.setSecret(appSecret);
        WxMaServiceHttpClientImpl wxMaServiceHttpClient = new WxMaServiceHttpClientImpl();
        wxMaServiceHttpClient.setWxMaConfig(maRedissonConfig);
        return wxMaServiceHttpClient;
    }

    /**
     * 获取默认的WxMpService对象
     * @return
     */
    public WxMpService getDefaultWxMpService() {
        // access token用redis缓存
        String appId = sysConfigService.selectConfigByKey(PayConstants.WECHAT_MP_APP_ID_KEY);
        String appSecret = sysConfigService.selectConfigByKey(PayConstants.WECHAT_MP_APP_SECRET_KEY);
        WxMpRedissonConfigImpl wxMpRedisConfig = new WxMpRedissonConfigImpl(redissonClient, "mp_config_key_prefix:" + appId);
        wxMpRedisConfig.setAppId(appId);
        wxMpRedisConfig.setSecret(appSecret);
        WxMpServiceHttpClientImpl wxMpServiceHttpClient = new WxMpServiceHttpClientImpl();
        wxMpServiceHttpClient.setWxMpConfigStorage(wxMpRedisConfig);
        return wxMpServiceHttpClient;
    }


    /**
     * 获取默认的WxPayService对象
     * @param payType
     * @return
     */
    public WxPayService getDefaultWxPayService(PayType payType) {
        WxPayConfig payConfig = getDefaultWxPayConfig(payType);
        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }


}
