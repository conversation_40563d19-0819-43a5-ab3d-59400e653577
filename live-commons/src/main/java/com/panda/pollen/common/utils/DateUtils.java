package com.panda.pollen.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String YYYYMMDDHH = "yyyyMMddHH";

    public static String YYYYMMDD = "yyyyMMdd";
    public static String YYYY_MM_DD_HH = "yyyy-MM-dd HH";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String HHMMSS = "HHmmss";

    public static String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

    private static String[] parsePatterns = {"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM);


    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String getTimeOnly() {
        return dateTimeNow(HHMMSS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     */
    public static int differentDaysByMillisecond(Date date1, Date date2) {
        return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    /**
     * 计算时间差
     *
     * @param endDate   最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }


    /**
     * Title：getBetweenDates <br>
     * Description：获取两个日期之间的所有日期集合 <br>
     * author：罗江林 <br>
     * date：2023年4月8日 下午4:31:24 <br>
     *
     * @param start
     * @param end
     * @return <br>
     */
    public static List<Date> getBetweenDates(Date start, Date end) {
        List<Date> dates = new ArrayList<Date>();
        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(start);
        tempStart.add(Calendar.DAY_OF_YEAR, 1);

        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(end);
        dates.add(start);
        while (tempStart.before(tempEnd)) {
            dates.add(tempStart.getTime());
            tempStart.add(Calendar.DAY_OF_YEAR, 1);
        }
        return dates;
    }

    /**
     * Title：splitDate <br>
     * Description：获取两个日期之间的所有日期字符串集合 <br>
     * author：罗江林 <br>
     * date：2023年4月8日 下午4:31:42 <br>
     *
     * @param startDate
     * @param endDate
     * @return <br>
     */
    public static List<String> splitDate(Date startDate, Date endDate) {
        List<String> dates = new ArrayList<>();
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            while (calendar.getTime().before(endDate) || calendar.getTime().equals(endDate)) {
                dates.add(DateUtil.format(calendar.getTime(), DatePattern.NORM_DATE_PATTERN));
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            return dates;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dates;
    }

//    /**
//     * 校验字符串是否符合时间格式
//     *
//     * @param dateStr
//     * @param format
//     * @return
//     */
//    public static boolean checkDateFormat(String dateStr, String format) {
//        try {
//            if (!dateStr.startsWith(DATE_YEAR_PREFIX)) {
//                return false;
//            }
//            SimpleDateFormat sdf = new SimpleDateFormat(format);
//            sdf.parse(dateStr);
//            return true;
//        } catch (ParseException pe) {
//            return false;
//        }
//    }

    public static final Pattern DATA_PATTERN = Pattern.compile("^20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])(20|21|22|23|[0-1]\\d)$");

    /**
     * 检查日期格式  年月日时
     *
     * @param dateStr str日期
     * @return boolean
     */
    public static boolean checkDateFormat(String dateStr) {
        dateStr = StringUtils.substring(dateStr, 0, 10);
        //可以超10位，不能少于10位
        if (StringUtils.length(dateStr) != 10) {
            return false;
        }
        // 正则判断是否日期字符串
        return DATA_PATTERN.matcher(dateStr).matches();
    }

    /**
     * 获取传入时间的0点的秒时间戳
     *
     * @return
     */
    public static Integer getZeroTimeSeconds(Long timeStamp) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timeStamp);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Long todayZeroTime = cal.getTimeInMillis() / 1000;
        return todayZeroTime.intValue();
    }

    /**
     * 获取传入时间的23点59分59的秒数
     *
     * @return
     */
    public static Integer getEndTimeSeconds(Long timeStamp) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timeStamp);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        Long todayZeroTime = cal.getTimeInMillis() / 1000;
        return todayZeroTime.intValue();
    }

    /**
     * 获取传入时间的0点的毫秒时间戳
     *
     * @return
     */
    public static Long getZeroTimeMilliSeconds(Long timeStamp) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timeStamp);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获取传入时间的23点59分59的毫秒数
     *
     * @return
     */
    public static Long getEndTimeMilliSeconds(Long timeStamp) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timeStamp);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTimeInMillis();
    }

    /**
     * 获取当天时间的23点59分59的秒数
     *
     * @return
     */
    public static Integer getTodayEndSeconds() {
        int i = (int) (DateUtils.getZeroTimeSeconds(DateUtils.addDays(DateUtils.getNowDate(), 1).getTime()) - DateUtils.getNowDate().getTime() / 1000) - 1;
        return i;
    }

    /**
     * 获取传入时间的下一小时的秒数差
     *
     * @param date
     * @return
     */
    public static int getNextHourStartDiffSeconds(Date date) {
        return (int) ((DateUtil.endOfHour(date).getTime() / 1000 + 1) - (date.getTime() / 1000));
    }

    public static Date getStartTimeOfToday() {
        Calendar calendar = Calendar.getInstance();
        // 设置Calendar的时间为当前时间
        calendar.setTime(getNowDate());
        // 时分秒毫秒都设为0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 时间字符串转为秒时间戳
     *
     * @param date
     * @return
     */
    public static Long getSecondTimeStamp(String date) {
        return new BigDecimal(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, date).getTime()).divide(new BigDecimal(1000)).longValue();
    }

    /**
     * 获取当前时间的秒级时间戳
     * @return
     */
    public static Long getSecondTimeStamp() {
        return Instant.now().getEpochSecond();
    }

    /**
     * 将秒级时间戳格式化字符串
     *
     * @param secondTimestamp
     * @param format
     * @return
     */
    public static String formatWithSecondTimeStamp(Long secondTimestamp, String format) {
        return formatWithMillisecondTimeStamp(secondTimestamp * 1000, format);
    }

    /**
     * 将毫秒级时间戳格式化为字符串
     *
     * @param milliSecondTimestamp
     * @param format
     * @return
     */
    public static String formatWithMillisecondTimeStamp(Long milliSecondTimestamp, String format) {
        // 将时间戳转换为Instant对象
        Instant instant = Instant.ofEpochMilli(milliSecondTimestamp);
        // 将Instant对象转换为指定时区的LocalDateTime
        LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
        // 定义日期时间格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        // 格式化为字符串
        return localDateTime.format(formatter);
    }

    public static long getRemainSecondsOneDay(Date currentDate) {
        //使用plusDays加传入的时间加1天，将时分秒设置成0
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                        ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        //使用ChronoUnit.SECONDS.between方法，传入两个LocalDateTime对象即可得到相差的秒数
        return ChronoUnit.SECONDS.between(currentDateTime, midnight) - 1;
    }

    /**
     * @param dateString 年月 YYYY-MM
     * @Description 获取当月的起始时间戳和结束时间戳
     * <AUTHOR>
     * @param:
     * @Date : 2024/8/16 15:46
     * @return: java.lang.Long[]
     */
    public static Long[] getMonthTimestamps(String dateString) {
        // 将字符串解析为YearMonth对象
        YearMonth yearMonth = YearMonth.parse(dateString, DATE_FORMATTER);
        // 直接获取该月的第一天
        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        // 直接获取该月的最后一天
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

        LocalDateTime startOfMonth = firstDayOfMonth.atStartOfDay();
        LocalDateTime endOfMonth = lastDayOfMonth.atTime(23, 59, 59);

        long startOfMonthTimestamp = startOfMonth.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;
        long endOfMonthTimestamp = endOfMonth.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;

        return new Long[]{startOfMonthTimestamp, endOfMonthTimestamp};
    }


    /**
     * @param params
     * @Description 获取过去七天的开始时间戳和当天的结束时间戳
     * <AUTHOR>
     * @param:
     * @Date : 2024/8/16 15:45
     * @return: void
     */
    public static void getPastSevenDaysAndToday(Map<String, Object> params) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 计算6天前的日期
        LocalDateTime sevenDaysAgo = now.minusDays(6).withHour(0).withMinute(0).withSecond(0).withNano(0);
        // 设置当天的结束时间为23:59:59
        LocalDateTime endOfDay = now.withHour(23).withMinute(59).withSecond(59).withNano(999_999_999);
        // 转换为Unix时间戳（秒）
        long startTimestamp = sevenDaysAgo.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;
        long endTimestamp = endOfDay.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;
        params.put("orderPayTimeStart", startTimestamp);
        params.put("orderPayTimeEnd", endTimestamp);
    }

    /**
     * @param date1
     * @param date2
     * @Description 判断两个日期是否为同一天
     * <AUTHOR>
     * @param:
     * @Date : 2024/8/22 14:36
     * @return: boolean true跨天false不跨天
     */
    public static boolean isDifferentDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        // 获取两个日期的年、‌月、‌日字段
        int year1 = cal1.get(Calendar.YEAR);
        int month1 = cal1.get(Calendar.MONTH);
        int day1 = cal1.get(Calendar.DAY_OF_MONTH);
        int year2 = cal2.get(Calendar.YEAR);
        int month2 = cal2.get(Calendar.MONTH);
        int day2 = cal2.get(Calendar.DAY_OF_MONTH);
        // 比较年、‌月、‌日字段是否全部相同
        return !(year1 == year2 && month1 == month2 && day1 == day2);
    }

    /**
     * 判断两个日期之间的时间差是否小于或等于指定天数
     *
     * @param date1
     * @param date2
     * @param days
     * @return
     */
    public static boolean areDatesWithinSpecifyDays(Date date1, Date date2, Integer days) {
        Instant instant1 = date1.toInstant();
        Instant instant2 = date2.toInstant();

        Duration duration = Duration.between(instant1, instant2);
        long daysBetween = Math.abs(duration.toDays());

        return daysBetween <= days;
    }

    /**
     * 将日期字符串转换为长整型
     *
     * @param dateString 日期字符串，格式为yyyy-MM-dd
     * @return 长整型数值
     * @throws IllegalArgumentException 如果输入的日期字符串不符合预期格式，则抛出异常
     */
    public static Integer convertToInteger(String dateString) {
        if (dateString == null || dateString.length() != 10 || !dateString.matches("\\d{4}-\\d{2}-\\d{2}")) {
            throw new IllegalArgumentException("Invalid date format. Expected format is 'yyyy-MM-dd'.");
        }
        // 替换掉日期字符串中的破折号
        String cleanedDate = dateString.replace("-", "");
        // 将字符串转换为长整型
        return Integer.valueOf(cleanedDate);
    }

    /**
     * 将HH:mm:ss或mm:ss格式的时间转为秒
     * @param timeStr
     * @return
     */
    public static long convertTimeToSeconds(String timeStr) {
        // 分割输入的时间字符串为小时、分钟和秒
        String[] parts = timeStr.split(":");
        // 根据分割后的部分数量确定是"HH:mm"还是"mm:ss"格式
        if (parts.length == 2) { // "mm:ss"格式
            int minutes = Integer.parseInt(parts[0]);
            int seconds = Integer.parseInt(parts[1]);
            return minutes * 60L + seconds;
        } else if (parts.length == 3) { // "HH:mm:ss"格式
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            int seconds = Integer.parseInt(parts[2]);
            return hours * 3600L + minutes * 60L + seconds;
        } else {
            throw new IllegalArgumentException("Invalid time format: " + timeStr);
        }
    }

    /**
     * 比较日期是否是今天
     * @param dateStr   日期（yyyy-MM-dd）
     * @return
     */
    public static boolean isSameToday(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate inputDate = LocalDate.parse(dateStr, formatter);
        LocalDate today = LocalDate.now();
        return inputDate.isEqual(today);
    }

}
