package com.panda.pollen.common.utils.toMap;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.panda.pollen.common.utils.collect.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * ClassName:com.panda.pollen.common.utils
 * Description:映射对象转map对象工具
 *
 * <AUTHOR> <br>
 * date 2024年 08月23日 11:10 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class InsinuateToMapUtils {

    /**
     * @param dto 要转换的数据传输对象，可以为null，如果为null则返回空Map。
     * @param <T> DTO对象的类型。
     * @return 转换后的Map对象，键为字段名，值为字段值。
     * @throws IllegalAccessException 如果访问字段时发生错误。
     * <AUTHOR>
     * @Date : 2024/4/7 11:01
     * 将DTO对象转换为Map形式。
     */
    public static <T> Map<String, Object> copyToMap(T dto) throws IllegalAccessException {
        Map<String, Object> map = MapUtils.newHashMap();
        if (ObjectUtils.isEmpty(dto)) {
            return map;
        }
        // 获取类
        Class<?> clazz = dto.getClass();
        // 获取类中的所有字段 进行遍历
        for (Field field : clazz.getDeclaredFields()) {
            // 设置访问权限，以便能访问私有字段
            field.setAccessible(true);
            //获取
            String fieldName = field.getName();
            Object fieldValue = field.get(dto);
            // 过滤掉值为空的属性
            if (ObjectUtils.isNotEmpty(fieldValue)) {
                map.put(fieldName, fieldValue);
            }
        }
        return map;
    }


    /**
     * 将对象的属性转换为Map，其中键为下划线形式 value为属性值
     *
     * @param dto 需要转换的对象
     * @return 包含对象属性的Map，键为下划线形式
     */
    public static <T> Map<String, Object> objectToUnderscoreMap(T dto) {
        Map<String, Object> map = new HashMap<>();
        if (dto == null) {
            throw new IllegalArgumentException("对象不能为空。");
        }
        try {
            // 获取类
            Class<?> clazz = dto.getClass();
            // 获取类中的所有方法
            Method[] methods = clazz.getMethods();
            // 遍历所有方法，寻找以"get"开头的方法（即getter方法）
            for (Method method : methods) {
                if (method.getName().startsWith("get") && !method.getName().equals("getClass")) {
                    // 提取属性名
                    String fieldName = method.getName().substring(3);
                    // 将属性名转换为下划线形式
                    String underscoreFieldName = camelCaseToUnderscore(fieldName);
                    // 通过反射调用getter方法获取属性值
                    Object value = method.invoke(dto);
                    // 将属性名和值存入Map
                    map.put(underscoreFieldName, value);
                }
            }
        } catch (Exception e) {
            log.error("转换对象属性为Map时发生错误: {}", e.getMessage());
        }
        return map;
    }

    /**
     * 将驼峰式命名的字符串转换为下划线形式。
     *
     * @param input 驼峰式命名的字符串
     * @return 下划线形式的字符串，首字母为小写
     */
    private static String camelCaseToUnderscore(String input) {
        // 创建一个StringBuilder，用于存储转换后的结果
        StringBuilder result = new StringBuilder();
        // 遍历输入字符串的每个字符
        for (int i = 0; i < input.length(); i++) {
            // 获取当前字符
            char ch = input.charAt(i);
            // 如果当前字符是大写，并且它不是字符串的第一个字符，则插入下划线
            if (Character.isUpperCase(ch) && i > 0) {
                result.append('_');
                // 将大写字母转换为小写
                result.append(Character.toLowerCase(ch));
            } else {
                // 对于第一个字符，确保它是小写
                if (i == 0) {
                    result.append(Character.toLowerCase(ch));
                } else {
                    // 其他字符直接添加
                    result.append(ch);
                }
            }
        }
        return result.toString();
    }

    // 公共方法，用于将传入的 JSON 对象转换为驼峰命名法
    public static JSONObject convertSnakeToCamel(JSONObject obj) {
        return convertKeysToCamelCase(obj); // 调用私有方法进行实际转换
    }

    // 私有递归方法，用于转换 JSON 对象中的键为驼峰命名法
    private static JSONObject convertKeysToCamelCase(JSONObject object) {
        JSONObject newObject = new JSONObject(); // 创建一个新的 JSON 对象来存储转换后的结果

        // 遍历原始 JSON 对象的所有键
        for (String key : object.keySet()) {
            Object value = object.get(key); // 获取键对应的值

            // 将当前键转换为驼峰命名法
            String camelCaseKey = toCamelCase(key);

            // 如果值是一个 JSON 对象，则递归调用此方法以转换其内部键
            if (value instanceof JSONObject) {
                newObject.put(camelCaseKey, convertKeysToCamelCase((JSONObject) value));
            }
            // 如果值是一个 JSON 数组，则遍历数组并将每个元素转换（如果它们是 JSON 对象的话）
            else if (value instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) value;
                JSONArray newArray = new JSONArray(); // 创建一个新的 JSON 数组

                // 遍历 JSON 数组中的每个元素
                for (Object item : jsonArray) {
                    // 如果元素是 JSON 对象，则转换其内部键
                    if (item instanceof JSONObject) {
                        newArray.add(convertKeysToCamelCase((JSONObject) item));
                    }
                    // 否则直接添加元素
                    else {
                        newArray.add(item);
                    }
                }
                // 将处理过的数组添加到新对象中
                newObject.put(camelCaseKey, newArray);
            }
            // 如果值不是 JSON 对象或数组，则直接添加到新对象中
            else {
                newObject.put(camelCaseKey, value);
            }
        }

        return newObject; // 返回转换后的新 JSON 对象
    }

    // 辅助方法，用于将下划线命名法字符串转换为驼峰命名法
    private static String toCamelCase(String input) {
        String[] parts = input.split("_"); // 使用下划线分割字符串

        StringBuilder camelCaseString = new StringBuilder(parts[0]); // 创建一个字符串构建器，并加入第一个部分

        // 遍历分割后的每一部分
        for (int i = 1; i < parts.length; i++) {
            // 将每一部分的首字母转为大写并拼接到字符串构建器中
            camelCaseString.append(Character.toUpperCase(parts[i].charAt(0)));
            camelCaseString.append(parts[i].substring(1)); // 添加剩余部分
        }

        return camelCaseString.toString(); // 返回转换后的字符串
    }
}
