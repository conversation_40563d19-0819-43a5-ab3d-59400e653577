package com.panda.pollen.common.enums.ads;

import cn.hutool.core.util.StrUtil;
import com.panda.pollen.common.exception.ServiceException;

import java.util.Arrays;

public enum MediaTypeEnum {

    /**
     * 未知媒体
     */
    UNKNOWN(-1, "未知媒体"),

    /**
     * 巨量DP
     */
    OCEANDP(0, "巨量DP"),

    /**
     * 巨量
     * 40100-请求超过服务整体频控，可以在开发者频控范围内重新尝试请求
     * 40110-请求超过开发者频控范围，请登陆开发者后台-接口频控处查看当前接口对应分配qps，在要求范围内请求接口
     */
    OCEAN(1, "巨量", 40110),

    /**
     * 快手
     * 400001-触发系统限流，全部开发者限流
     */
    KUAISHOU(2, "快手", 400001),

    /**
     * 微博
     */
    WEIBO(3, "微博"),

    /**
     * 腾讯
     * 11008-该应用的请求次数已达限制，请稍后重试
     * 11017-应用请求已达分钟频次上限，请登陆开发者后台查看当前接口对应分配的频次，并在要求范围内请求接口。
     * 11019-您的请求过于频繁或主体/开发者的频控已达到上限
     * 60005-请求频率超出限制
     * 72005-超过系统频率限制，请稍后再试
     */
    TENCENT(4, "腾讯", 11008, 11017, 11019, 60005, 72005),

    /**
     * B站
     * 21003-请求被限流，请稍后再试
     */
    BILIBIL(5, "B站", 21003),

    /**
     * 支付宝
     */
    ALIPAY(6, "支付宝"),

    /**
     * UC
     */
    UC(7, "UC"),

    /**
     * 百度
     */
    BAIDU(8, "百度"),

    /**
     * 奇虎360
     */
    QIHU(9, "奇虎"),

    /**
     * 小红书
     */
    XHS(10, "小红书"),

    /**
     * 墨迹天气
     */
    YOYI(11, "悠易"),

    /**
     * SOUL
     */
    SOUL(12, "SOUL"),

    /**
     * 知乎
     * 3001-请求受限 (并发操作 、接口限流等)
     */
    ZH(13, "ZH", 3001),

    /**
     * 优酷
     */
    YOUKU(14, "YOUKU"),

    /**
     * 拼多多商品限制
     */
    PDD(999, "拼多多商品限制"),
    ;

    private int code;

    private String desc;

    /**
     * 请求超出频控限制错误code
     */
    private int[] requestLimitCode;

    MediaTypeEnum(int code, String desc, int... requestLimitCode) {
        this.code = code;
        this.desc = desc;
        this.requestLimitCode = requestLimitCode;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public int[] getRequestLimitCode() {
        return requestLimitCode;
    }

    public static MediaTypeEnum get(int code) {
        for (MediaTypeEnum mt : MediaTypeEnum.values()) {
            if (mt.getCode() == code) {
                return mt;
            }
        }
        return null;
    }

    public static MediaTypeEnum get(String name) {
        for (MediaTypeEnum mt : MediaTypeEnum.values()) {
            if (StrUtil.equals(mt.getDesc(), name)) {
                return mt;
            }
        }
        return null;
    }

    public static String getDesc(int code) {
        return get(code).getDesc();
    }

    public static boolean isWeibo(int code) {
        MediaTypeEnum media = get(code);
        return media == MediaTypeEnum.WEIBO;
    }

    public static boolean isOcean(int code) {
        MediaTypeEnum media = get(code);
        return media == MediaTypeEnum.OCEAN;
    }

    public static boolean isTENCENT(int code) {
        MediaTypeEnum media = get(code);
        return media == MediaTypeEnum.TENCENT;
    }

    /**
     * 根据枚举Code值返回枚举类型
     *
     * @param code
     * @return
     */
    public static MediaTypeEnum getByValue(int code) {
        return Arrays.stream(values())
                .filter(mediaTypeEnum -> mediaTypeEnum.getCode() == code)
                .findFirst()
                .orElseThrow(() -> new ServiceException("未知的媒体类型"));
    }

    /**
     * 判断是否是请求超出频控限制错误code
     *
     * @param errorCode
     * @return boolean
     */
    public boolean isLimitCode(Number errorCode) {
        if (errorCode == null) {
            return false;
        }
        for (int code : this.getRequestLimitCode()) {
            if (errorCode.intValue() == code) {
                return true;
            }
        }
        return false;
    }

}
