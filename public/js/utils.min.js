class PaymentService {
  constructor() {
    if (!PaymentService.instance) {
      this.isWechat = this.detectWechat()
      this.isIOS = this.detectIOS()
      this.statusCheckTimer = null
      PaymentService.instance = this
    }
    return PaymentService.instance
  }
  detectWechat() {
    return navigator.userAgent.toLowerCase().includes("micromessenger")
  }
  detectIOS() {
    return /iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase())
  }
  async getWxConfig(callback) {
    try {
      let url = window.location.href
      if (this.isIOS) {
        url = window.location.href.split("#")[0]
      }

      const response = await fetch(
        "https://fanspvt.com/monitor/mp/pay/createJsapiSignature",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ url, formId: getParam("formId") }),
        }
      )

      const data = await response.json()
      if (data.code === 200) {
        const { appId, timestamp, nonceStr, signature } = data.data
        window.wx.config({
          debug: false,
          appId,
          timestamp,
          nonceStr,
          signature,
          jsApiList: ["chooseWXPay"],
        })
        if (callback) {
          callback()
        }
      }
    } catch (error) {
      console.error("[WX Config Error]", error)
      throw error
    }
  }
  wechatJSAPIPay(params) {
    return new Promise((resolve, reject) => {
      this.getWxConfig(() => {
        this.startPaymentStatusCheck({
          orderNo: params.orderNo,
          onSuccess: () => {
            resolve()
          }
        })
        window.wx.chooseWXPay({
          appId: params.appId,
          timestamp: params.timeStamp,
          nonceStr: params.nonceStr,
          package: params.packageValue,
          signType: params.signType,
          paySign: params.paySign,
          complete: (res) => {
            this.startPaymentStatusCheck({
              orderNo: params.orderNo,
              onSuccess: () => {
                resolve()
              },
            })
          },
        })
      })
    })
  }

  handleRedirectPayment({
    payUrl,
    returnUrl,
    needsDownload = false,
    orderNo,
    onSuccess,
  }) {
    if (needsDownload) {
      returnUrl += "&download=1"
    }
    localStorage.setItem(getParam("formId").slice(0, 19) + "_h5_pay", "1")
    window.location.href =
      payUrl + "&redirect_url=" + encodeURIComponent(returnUrl)
    this.startPaymentStatusCheck({
      orderNo,
      onSuccess: () => {
        onSuccess?.()
      },
    })
  }

  async pay(options) {
    const {
      orderNo,
      payType,
      mpOpenId,
      returnUrl,
      onLoading,
      onSuccess,
      onError,
      onComplete,
    } = options
    try {
      onLoading?.(true)
      const postData = { orderNo, payType }

      if (mpOpenId) {
        postData.mpOpenId = mpOpenId
        postData.payType = "3"
      }

      const response = await fetch(
        "https://fanspvt.com/monitor/forms-results-public/pay",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(postData),
        }
      )

      const data = await response.json()
      const res = data.data

      if (!res || !res.payContent) {
        throw new Error("Invalid payment response")
      }

      if (postData.payType === "3") {
        await this.wechatJSAPIPay({
          orderNo,
          ...res.payContent,
        })
        onSuccess?.()
      } else {
        this.handleRedirectPayment({
          orderNo,
          payUrl: res.payContent,
          returnUrl,
          onSuccess,
          needsDownload: res.nextAction?.actionCode === 2,
        })
      }
    } catch (error) {
      console.error("[Payment Error]", error)
      alert(error)
      onError?.(error)
      throw error
    } finally {
      onLoading?.(false)
      onComplete?.()
    }
  }

  async checkPaymentStatus(orderNo) {
    try {
      const response = await fetch(
        "https://fanspvt.com/monitor/forms-results-public/isPay/" + orderNo
      )
      const data = await response.json()
      return data.data
    } catch (error) {
      console.error("[Payment Status Check Error]", error)
      throw error
    }
  }

  async startPaymentStatusCheck(options) {
    const {
      orderNo,
      maxRetries = 30,
      interval = 2000,
      onSuccess,
      onError,
      onComplete,
      onLoading,
    } = options

    if (this.statusCheckTimer) {
      clearTimeout(this.statusCheckTimer)
      this.statusCheckTimer = null
    }

    let retries = 0
    const check = async () => {
      try {
        onLoading?.(true)
        const isPaid = await this.checkPaymentStatus(orderNo)
        if (isPaid) {
          this.statusCheckTimer = null
          onSuccess?.()
          return true
        }

        if (retries < maxRetries) {
          retries++
          this.statusCheckTimer = setTimeout(check, interval)
          return false
        }
      } catch (error) {
        onError?.(error)
        if (retries < maxRetries) {
          retries++
          this.statusCheckTimer = setTimeout(check, interval)
          return false
        }
      } finally {
        onLoading?.(false)
        if (retries >= maxRetries) {
          this.statusCheckTimer = null
          onComplete?.()
        }
      }
    }

    return check()
  }

  initPayment(options) {
    const {
      orderNo,
      isFirst,
      onLoading,
      onSuccess,
      onError = (error) => {
        if (error === "支付取消") {
          alert("支付取消")
        } else {
          alert("支付失败")
        }
      }
    } = options

    if (!orderNo) return

    const defaultSuccess = () => {
      window.location.href =
        "https://fanspvt.com/forms/result.html" + location.search
    }

    const handleSuccess = () => {
      if (onSuccess) {
        onSuccess()
      } else {
        defaultSuccess()
      }
    }

    if (isFirst) {
      this.pay({
        ...options,
        onSuccess: handleSuccess
      })
    } else {
      this.startPaymentStatusCheck({
        orderNo,
        onSuccess: handleSuccess,
        onError,
        onLoading
      })
    }
  }
}

const paymentService = new PaymentService()
window.wxPaymentService = paymentService
function setDataWithExpiry(e, t, a) { t = { value: t, expiry: (new Date).getTime() + 24 * a * 60 * 60 * 1e3 }; localStorage.setItem(e, JSON.stringify(t)) } function getDataWithExpiry(e) { var t = localStorage.getItem(e); return t ? (t = JSON.parse(t), (new Date).getTime() > t.expiry ? (localStorage.removeItem(e), null) : t.value) : null }
function getParam(n) { let val = new URLSearchParams(window.location.search).get(n); return val ? decodeURIComponent(val) : val; }
