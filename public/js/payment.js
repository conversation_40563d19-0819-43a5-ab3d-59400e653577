class PaymentService {
  constructor() {
    if (!PaymentService.instance) {
      this.isWechat = this.detectWechat()
      this.isIOS = this.detectIOS()
      PaymentService.instance = this
    }
    return PaymentService.instance
  }
  detectWechat() { return navigator.userAgent.toLowerCase().includes('micromessenger') }
  detectIOS() { return /iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()) }
  async getWxConfig(callback) {
    try {
      let url = window.location.href
      if (this.isIOS) {
        url = window.location.href.split('#')[0]
      }

      const response = await fetch('https://pangdasc.com/prod-api/mp/open/createJsapiSignature', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ url })
      })

      const data = await response.json()
      if (data.code === 200) {
        const { appId, timestamp, nonceStr, signature } = data.data
        window.wx.config({
          debug: false,
          appId,
          timestamp,
          nonceStr,
          signature,
          jsApiList: ['chooseWXPay']
        })
        if (callback) {
          callback()
        }
      }
    } catch (error) {
      console.error('[WX Config Error]', error)
      throw error
    }
  }

  /**
   * Handle WeChat JSAPI Payment
   * @param {Object} params - Payment parameters from backend
   * @returns {Promise} Payment result
   */
  wechatJSAPIPay(params) {
    return new Promise((resolve, reject) => {
      this.getWxConfig(() => {
        window.wx.chooseWXPay({
          appId: params.appId,
          timestamp: params.timeStamp,
          nonceStr: params.nonceStr,
          package: params.packageValue,
          signType: params.signType,
          paySign: params.paySign,
          // success: (res) => {
          //   res.errMsg === 'chooseWXPay:ok' ? resolve(res) : reject('支付取消')
          // },
          complete: (res) => {
            if (res.errMsg === 'chooseWXPay:ok') {
              resolve(res)
            } else {
              reject('支付取消')
            }
          },
        })
      })
    })
  }

  /**
   * Handle redirect payment
   * @param {string} payUrl - Payment URL from backend
   * @param {string} returnUrl - Return URL after payment
   * @param {boolean} needsDownload - If download is needed after payment
   */
  handleRedirectPayment(payUrl, returnUrl, needsDownload = false) {
    if (needsDownload) {
      returnUrl += '&download=1'
    }
    window.location.href = payUrl + '&redirect_url=' + encodeURIComponent(returnUrl)
  }

  /**
   * Main payment method
   * @param {Object} options - Payment options
   * @param {string} options.orderNo - Order number
   * @param {string} options.payType - Payment type
   * @param {string} [options.mpOpenId] - WeChat OpenID for JSAPI payment
   * @param {string} [options.returnUrl] - Return URL for redirect payment
   * @param {Function} [options.onLoading] - Loading state callback
   * @param {Function} [options.onSuccess] - Success callback
   * @param {Function} [options.onError] - Error callback
   * @param {Function} [options.onComplete] - Complete callback
   * @returns {Promise} Payment result
   */
  async pay(options) {
    const {
      orderNo,
      payType,
      mpOpenId,
      returnUrl,
      onLoading,
      onSuccess,
      onError,
      onComplete
    } = options
    try {
      onLoading?.(true)
      const postData = { orderNo, payType }

      if (mpOpenId) {
        postData.mpOpenId = mpOpenId
        postData.payType = '3' // Force WeChat JSAPI payment
      }

      const response = await fetch('https://fanspvt.com/monitor/forms-results-public/pay', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(postData)
      })

      const data = await response.json()
      const res = data.data

      if (!res || !res.payContent) {
        throw new Error('Invalid payment response')
      }

      if (postData.payType === '3') {
        // WeChat JSAPI payment
        const result = await this.wechatJSAPIPay(res.payContent)
        // Start checking payment status
        onSuccess?.()
        return result
      } else {
        // Redirect payment
        this.handleRedirectPayment(
          res.payContent,
          returnUrl,
          res.nextAction?.actionCode === 2
        )
      }
    } catch (error) {
      console.error('[Payment Error]', error)
      alert(error)
      onError?.(error)
      throw error
    } finally {
      onLoading?.(false)
      onComplete?.()
    }
  }

  /**
   * Check payment status
   * @param {string} orderNo - Order number
   * @returns {Promise<boolean>} Payment status
   */
  async checkPaymentStatus(orderNo) {
    try {
      const response = await fetch('https://fanspvt.com/monitor/forms-results-public/isPay/' + orderNo)
      const data = await response.json()
      return data.data
    } catch (error) {
      console.error('[Payment Status Check Error]', error)
      throw error
    }
  }

  /**
   * Start checking payment status with retry
   * @param {Object} options - Check options
   * @param {string} options.orderNo - Order number
   * @param {number} [options.maxRetries=30] - Maximum number of retries
   * @param {number} [options.interval=2000] - Interval between checks in milliseconds
   * @param {Function} [options.onSuccess] - Success callback
   * @param {Function} [options.onError] - Error callback
   * @param {Function} [options.onComplete] - Complete callback
   * @param {Function} [options.onLoading] - Loading state callback
   */
  async startPaymentStatusCheck(options) {
    const {
      orderNo,
      maxRetries = 30,
      interval = 2000,
      onSuccess,
      onError,
      onComplete,
      onLoading
    } = options

    let retries = 0
    const check = async () => {
      try {
        onLoading?.(true)
        const isPaid = await this.checkPaymentStatus(orderNo)
        if (isPaid) {
          onSuccess?.()
          return true
        }

        if (retries < maxRetries) {
          retries++
          setTimeout(check, interval)
          return false
        }
      } catch (error) {
        onError?.(error)
        if (retries < maxRetries) {
          retries++
          setTimeout(check, interval)
          return false
        }
      } finally {
        onLoading?.(false)
        if (retries >= maxRetries) {
          onComplete?.()
        }
      }
    }

    return check()
  }

  /**
   * Initialize payment flow
   * @param {Object} options - Payment options
   * @param {string} options.orderNo - Order number
   * @param {string} options.payType - Payment type
   * @param {string} [options.mpOpenId] - WeChat OpenID for JSAPI payment
   * @param {string} [options.returnUrl] - Return URL for redirect payment
   * @param {boolean} [options.isFirst] - Whether this is the first payment attempt
   * @param {Function} [options.onLoading] - Loading state callback
   * @param {Function} [options.onSuccess] - Success callback
   * @param {Function} [options.onError] - Error callback
   */
  initPayment(options) {
    const {
      orderNo,
      isFirst,
      onLoading,
      onSuccess,
      onError = (error) => {
        if (error === '支付取消') {
          alert('支付取消')
        } else {
          alert('支付失败')
        }
      }
    } = options

    if (!orderNo) return

    // 默认的成功处理
    const defaultSuccess = () => {
      window.location.href = 'https://fanspvt.com/forms/result.html' + location.search
    }

    const handleSuccess = () => {
      if (onSuccess) {
        onSuccess()
      } else {
        defaultSuccess()
      }
    }

    if (isFirst) {
      // Start payment flow
      this.pay({
        ...options,
        onSuccess: handleSuccess
      })
    } else {
      // Start checking payment status
      this.startPaymentStatusCheck({
        orderNo,
        onSuccess: handleSuccess,
        onError,
        onLoading
      })
    }
  }
}

// Create singleton instance
const paymentService = new PaymentService()
window.wxPaymentService = paymentService // Make it available globally 