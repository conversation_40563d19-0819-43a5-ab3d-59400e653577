<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权结果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 400px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            animation: scaleIn 0.8s ease-out 0.2s both;
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.5);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .success-icon {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .error-icon {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            animation: fadeIn 1s ease-out 0.4s both;
        }

        .success-title {
            color: #4CAF50;
        }

        .error-title {
            color: #f44336;
        }

        .message {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 32px;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        .error-message {
            color: #f44336;
            background: #ffebee;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #f44336;
            text-align: left;
            word-break: break-word;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 32px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: fadeIn 1s ease-out 0.8s both;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }

            .title {
                font-size: 20px;
            }

            .message {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div id="icon" class="icon success-icon">
            ✓
        </div>
        <h1 id="title" class="title success-title">授权成功</h1>
        <div id="message" class="message">
            您已成功完成授权，请点击[我已授权]按钮，或刷新页面。
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                failMsg: params.get('failMsg')
            };
        }

        // 初始化页面
        function initPage() {
            const params = getUrlParams();
            const icon = document.getElementById('icon');
            const title = document.getElementById('title');
            const message = document.getElementById('message');

            if (params.failMsg) {
                // 显示失败信息
                icon.textContent = '✗';
                icon.className = 'icon error-icon';
                title.textContent = '授权失败';
                title.className = 'title error-title';

                // 解码失败消息
                const failMessage = decodeURIComponent(params.failMsg);
                message.innerHTML = `<div class="error-message">${failMessage}</div>`;

                // 更新页面标题
                document.title = '授权失败';
            }
        }

        // 关闭窗口
        function closeWindow() {
            // 尝试关闭当前窗口
            if (window.opener) {
                window.close();
            } else {
                // 如果无法关闭，返回上一页或跳转到首页
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    window.location.href = '/';
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);

        // 支持ESC键关闭
        document.addEventListener('keydown', function (event) {
            if (event.key === 'Escape') {
                closeWindow();
            }
        });
    </script>
</body>

</html>