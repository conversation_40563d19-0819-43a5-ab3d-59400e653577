<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信 JSSDK 登录测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .status.info {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .status.success {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .status.error {
            background-color: #ffebee;
            color: #c62828;
        }

        .status.warning {
            background-color: #fff3e0;
            color: #f57c00;
        }

        button {
            background-color: #07c160;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }

        button:hover {
            background-color: #06ad56;
        }

        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .user-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }

        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
            vertical-align: middle;
        }

        .log {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #07c160;
            background-color: #f8f9fa;
        }

        .step h3 {
            margin-top: 0;
            color: #07c160;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>微信 JSSDK 登录测试页面</h1>

        <div class="step">
            <h3>步骤 1: 环境检测</h3>
            <div id="envStatus" class="status info">检测中...</div>
            <button id="detectBtn" onclick="detectEnvironment()">重新检测</button>
        </div>

        <div class="step">
            <h3>步骤 2: 微信授权登录</h3>
            <div id="authStatus" class="status info">等待授权...</div>
            <button id="loginBtn" onclick="startWechatLogin()" disabled>开始微信登录</button>
            <button id="clearBtn" onclick="clearAuthData()">清除授权数据</button>
        </div>

        <div class="step">
            <h3>步骤 3: 获取用户信息</h3>
            <div id="userStatus" class="status info">等待获取用户信息...</div>
            <div id="tokenInfo" class="user-info" style="display: none;"></div>
            <div id="userInfo" class="user-info" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>调试日志</h3>
            <div id="debugLog" class="log"></div>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        // 配置信息
        const CONFIG = {
            appId: 'wxef0ed36332a50cf1',
            apiBaseUrl: 'https://fanspvt.com/customer-apis',
            redirectUri: window.location.origin + window.location.pathname
        };

        // 全局变量
        let wxCode = null;
        let wxParams = null;
        let accessToken = null;
        let openId = null;
        let userInfo = null;

        // 工具函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debugLog');
            const typePrefix = type.toUpperCase();
            logElement.textContent += `[${timestamp}] ${typePrefix}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${typePrefix}]`, message);
        }

        function clearLog() {
            document.getElementById('debugLog').textContent = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        function isWeixinBrowser() {
            const ua = navigator.userAgent.toLowerCase();
            return ua.includes('micromessenger');
        }

        // 环境检测
        function detectEnvironment() {
            log('开始环境检测...');

            const isWeixin = isWeixinBrowser();
            const currentUrl = window.location.href;
            const hasCode = getUrlParam('code');
            const hasState = getUrlParam('state');

            log(`当前 URL: ${currentUrl}`);
            log(`是否在微信浏览器: ${isWeixin}`);
            log(`URL 中是否有 code: ${hasCode ? 'Yes' : 'No'}`);
            log(`URL 中是否有 state: ${hasState ? 'Yes' : 'No'}`);

            if (!isWeixin) {
                updateStatus('envStatus', '⚠️ 当前不在微信浏览器环境中，请在微信中打开此页面', 'warning');
                document.getElementById('loginBtn').disabled = true;
                return;
            }

            updateStatus('envStatus', '✅ 微信浏览器环境检测通过', 'success');
            document.getElementById('loginBtn').disabled = false;

            // 检查是否已经有授权码
            if (hasCode) {
                wxCode = hasCode;
                log(`检测到授权码: ${wxCode}`);
                updateStatus('authStatus', `✅ 已获取到授权码: ${wxCode}`, 'success');
                getUserInfo(wxCode);
            } else {
                updateStatus('authStatus', '等待用户授权...', 'info');
            }
        }

        // 开始微信登录
        function startWechatLogin() {
            log('开始微信授权登录流程...');

            const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?` +
                `appid=${CONFIG.appId}&` +
                `redirect_uri=${encodeURIComponent(CONFIG.redirectUri)}&` +
                `response_type=code&` +
                `scope=snsapi_userinfo&` +
                `state=needCode#wechat_redirect`;

            log(`跳转到授权页面: ${authUrl}`);
            updateStatus('authStatus', '正在跳转到微信授权页面...', 'info');

            window.location.href = authUrl;
        }

        // 通过 code 获取用户信息（包含授权信息和用户详细信息）
        function getUserInfo(code) {
            log(`开始通过 code 获取用户信息: ${code}`);
            updateStatus('userStatus', '正在获取用户信息...', 'info');

            const apiUrl = `${CONFIG.apiBaseUrl}/mp/open/getMpUserInfo?code=${code}`;
            log(`请求 API: ${apiUrl}`);

            const xhr = new XMLHttpRequest();
            xhr.open('GET', apiUrl);

            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            log(`getMpUserInfo API 响应: ${JSON.stringify(response, null, 2)}`);

                            if (response.data) {
                                const { authInfo, userInfo: userInfoData } = response.data;

                                // 保存授权信息
                                if (authInfo) {
                                    accessToken = authInfo.access_token;
                                    openId = authInfo.openid;
                                    displayTokenInfo(authInfo);
                                }

                                // 保存并显示用户信息
                                if (userInfoData) {
                                    userInfo = userInfoData;
                                    displayUserInfo(userInfo);
                                    updateStatus('userStatus', '✅ 用户信息获取成功', 'success');
                                } else {
                                    throw new Error('响应中没有用户信息数据');
                                }
                            } else {
                                throw new Error('响应中没有数据');
                            }
                        } catch (error) {
                            log(`解析响应失败: ${error.message}`, 'error');
                            updateStatus('userStatus', `❌ 解析用户信息失败: ${error.message}`, 'error');
                        }
                    } else {
                        log(`API 请求失败: HTTP ${xhr.status}`, 'error');
                        updateStatus('userStatus', `❌ 获取用户信息失败: HTTP ${xhr.status}`, 'error');
                    }
                }
            };

            xhr.onerror = function (error) {
                log(`网络请求错误: ${error}`, 'error');
                updateStatus('userStatus', '❌ 网络请求失败', 'error');
            };

            xhr.send();
        }

        // 显示授权信息
        function displayTokenInfo(authInfo) {
            const tokenInfoElement = document.getElementById('tokenInfo');

            let html = '<h4>授权信息:</h4>';
            html += '<div>';
            if (authInfo.access_token) {
                const shortToken = authInfo.access_token.substring(0, 20) + '...';
                html += `<p><strong>Access Token:</strong> ${shortToken}</p>`;
            }
            if (authInfo.openid) html += `<p><strong>OpenID:</strong> ${authInfo.openid}</p>`;
            if (authInfo.unionid) html += `<p><strong>UnionID:</strong> ${authInfo.unionid}</p>`;
            if (authInfo.expires_in) html += `<p><strong>过期时间:</strong> ${authInfo.expires_in} 秒</p>`;
            if (authInfo.refresh_token) {
                const shortRefreshToken = authInfo.refresh_token.substring(0, 20) + '...';
                html += `<p><strong>Refresh Token:</strong> ${shortRefreshToken}</p>`;
            }
            if (authInfo.scope) html += `<p><strong>授权范围:</strong> ${authInfo.scope}</p>`;
            if (authInfo.is_snapshotuser) html += `<p><strong>快照页模式:</strong> ${authInfo.is_snapshotuser === 1 ? '是' : '否'}</p>`;
            html += '</div>';

            tokenInfoElement.innerHTML = html;
            tokenInfoElement.style.display = 'block';

            log('授权信息显示完成');
        }

        // 显示用户信息
        function displayUserInfo(info) {
            const userInfoElement = document.getElementById('userInfo');

            let html = '<h4>用户详细信息:</h4>';

            if (info.headimgurl) {
                html += `<img src="${info.headimgurl}" alt="头像" onerror="this.style.display='none'">`;
            }

            html += '<div>';
            if (info.nickname) html += `<p><strong>昵称:</strong> ${info.nickname}</p>`;
            if (info.openid) html += `<p><strong>OpenID:</strong> ${info.openid}</p>`;
            if (info.unionid) html += `<p><strong>UnionID:</strong> ${info.unionid}</p>`;
            if (info.sex !== undefined) {
                const sexText = info.sex === 1 ? '男' : info.sex === 2 ? '女' : '未知';
                html += `<p><strong>性别:</strong> ${sexText}</p>`;
            }
            if (info.country) html += `<p><strong>国家:</strong> ${info.country}</p>`;
            if (info.province) html += `<p><strong>省份:</strong> ${info.province}</p>`;
            if (info.city) html += `<p><strong>城市:</strong> ${info.city}</p>`;
            if (info.privilege && info.privilege.length > 0) {
                html += `<p><strong>特权信息:</strong> ${info.privilege.join(', ')}</p>`;
            }
            html += '</div>';

            userInfoElement.innerHTML = html;
            userInfoElement.style.display = 'block';

            log('用户详细信息显示完成');
        }

        // 清除授权数据
        function clearAuthData() {
            log('清除授权数据...');

            // 清除 URL 参数
            const url = new URL(window.location);
            url.searchParams.delete('code');
            url.searchParams.delete('state');
            window.history.replaceState({}, document.title, url.toString());

            // 重置变量
            wxCode = null;
            wxParams = null;
            accessToken = null;
            openId = null;
            userInfo = null;

            // 重置 UI
            updateStatus('authStatus', '等待用户授权...', 'info');
            updateStatus('userStatus', '等待获取用户信息...', 'info');
            document.getElementById('tokenInfo').style.display = 'none';
            document.getElementById('userInfo').style.display = 'none';

            log('授权数据已清除');
        }

        // 页面加载完成后自动检测环境
        window.addEventListener('load', function () {
            log('页面加载完成，开始初始化...');
            detectEnvironment();
        });
    </script>
</body>

</html>