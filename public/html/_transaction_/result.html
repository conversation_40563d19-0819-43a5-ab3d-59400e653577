<html lang="en" class="trancy-zh-CN" style="font-size: 100px">
  <head>
    <meta charset="UTF-8" />
    <meta name="force-rendering" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta http-equiv="pragram" content="no-cache" />
    <meta name="referrer" content="unsafe-url" />
    <meta
      http-equiv="cache-control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no,viewport-fit=cover"
    />
    <script src="https://oss.fanspvt.com/assets/jquery.min.js"></script>
    <script src="https://oss.fanspvt.com/assets/clipboard.min.js"></script>
    <style>
      body,
      dd,
      div,
      dl,
      footer,
      form,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      header,
      input,
      label,
      li,
      menu,
      nav,
      ol,
      p,
      section,
      select,
      td,
      textarea,
      th,
      time,
      ul {
        margin: 0;
        padding: 0;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
      }

      html,
      body {
        max-width: 750px;
        margin: auto;
        font-size: 0.24rem;
      }

      img {
        width: 100%;
        display: block;
      }

      .main {
        width: 100%;
        min-height: 100vh;
      }

      .scale-btn {
        animation: scaleMotion 2s linear infinite;
      }

      @keyframes scaleMotion {
        0% {
          transform: scale(1);
        }

        50% {
          transform: scale(0.9);
        }

        100% {
          transform: scale(1);
        }
      }

      .position-relative {
        position: relative;
      }
    </style>
    <title>支付结果</title>
    <script>
      function _resize() {
        var html = document.getElementsByTagName("html")[0];
        var hW = html.offsetWidth > 750 ? 750 : html.offsetWidth;
        var fS = (100 / 750) * hW;
        html.style.fontSize = fS + "px";
      }
      _resize();
      window.onresize = function () {
        _resize();
      };
    </script>
    <style>
      .main {
        padding-top: 1.5rem;
      }

      .main .btn {
        width: 6.7rem;
        height: 0.98rem;
        line-height: 0.98rem;
        text-align: center;
        border-radius: 0.49rem;
        border: 1px solid #eb5648;
        color: #eb5648;
        font-size: 0.36rem;
        margin: 30px auto;
      }

      .main .continue {
        background: #eb5648;
        color: #fff;
      }

      .flash {
        animation-name: flash;
        animation-duration: 2s;
        animation-fill-mode: both;
        animation-iteration-count: infinite;
      }

      @keyframes flash {
        0%,
        50%,
        to {
          transform: scale(1.05);
        }

        25%,
        75% {
          transform: scale(0.95);
        }
      }

      .loading {
        position: fixed;
        top: 50%;
        display: none;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.6);
        width: 1.4rem;
        height: 1.4rem;
        border-radius: 0.14rem;
        z-index: 99999;
      }

      .loading .img {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
      }

      .title {
        text-align: center;
        font-size: 0.4rem;
        padding-top: 0.4rem;
      }

      .title-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .icon {
        width: 1rem;
        height: 1rem;
        margin-right: 0.4rem;
      }

      .success-result {
        text-align: center;
        font-size: 0.75rem;
        color: #eb5648;
      }

      #sub-tooltip {
        margin-top: 0.4rem;
        font-size: 0.4rem;
      }

      .tips {
        text-align: center;
      }

      .pc_total_container {
        width: 100vw;
        background: white;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .desc-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
      }

      .qrcode_logo {
        width: 54px;
        height: 54px;
      }

      .qrcode_pc_logo_desc {
        font-size: 16px;
        color: #10141a;
        font-weight: 500;
      }

      .qrcode_pc_container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 310px;
        margin: 0 auto;
        padding: 40px 0;
        font-size: 16px;
        font-weight: 500;
        border-radius: 12px;
        background: rgba(5, 14, 26, 0.03);
        box-sizing: border-box;
        color: black;
      }

      .qrcode_gray_text {
        color: #10141a;
      }

      #androidOpenBrowser,
      #downloadBtn,
      #copyBtn,
      #wx,
      #contactBtn,
      #add_wechat,
      #qrcode_wrap,
      #copy_to_add,
      #click_to_add {
        display: none;
      }
    </style>
  </head>

  <body>
    <div class="main">
      <!--    <div class="title">支付结果</div>-->
      <div class="title-wrap">
        <svg
          t="1710232453827"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="6601"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          width="200"
          height="200"
        >
          <path
            d="M32.64 625.152a19.2 19.2 0 0 0 0-38.4 19.2 19.2 0 0 0 0 38.4z"
            fill="#FFFFFF"
            p-id="6602"
          ></path>
          <path
            d="M291.84 460.8l164.352 230.4a40.832 40.832 0 0 0 54.272 10.24 38.4 38.4 0 0 0 10.24-9.472l424.832-414.72a36.48 36.48 0 0 0-6.784-51.2l-2.048-1.408a40.832 40.832 0 0 0-54.656 7.936l-391.296 384 7.424-1.024-140.8-196.096a40.832 40.832 0 0 0-54.272-10.24 36.48 36.48 0 0 0-12.8 50.048z"
            fill="#EB5648"
            p-id="6603"
          ></path>
          <path
            d="M512 1017.344c-11.136 0-22.144 0-33.28-1.024A505.472 505.472 0 1 1 807.04 102.4a31.744 31.744 0 0 1 7.168 44.16 31.744 31.744 0 0 1-44.16 7.552 441.216 441.216 0 1 0 174.08 266.624 31.744 31.744 0 0 1 62.08-12.8A505.856 505.856 0 0 1 512 1017.344z"
            fill="#EB5648"
            p-id="6604"
          ></path>
        </svg>
        <div class="success-result">支付成功</div>
        <div id="sub-tooltip"></div>
      </div>

      <div id="add_wechat" class="pc_total_container">
        <div class="desc-wrap">
          <img
            class="qrcode_logo"
            src="https://wwcdn.weixin.qq.com/node/wework/images/qrcode_logo.7d1af620e9.png"
            alt=""
          />
          <div class="qrcode_pc_logo_desc">
            <span class="qrcode_gray_text" style="padding-right: 3px">“</span>
            请添加我的微信与我联系吧
            <span class="qrcode_gray_text" style="padding-left: 3px">”</span>
          </div>
        </div>
        <div id="add_cp" class="qrcode_pc_container">
          <div id="qrcode_wrap">
            <img
              style="width: 200px; height: 200px"
              id="js_qrcode_img"
              src="https://open.weixin.qq.com/zh_CN/htmledition/res/assets/res-design-download/icon64_appwx_logo.png"
            />
          </div>
          <div id="copy_to_add" style="margin-top: 20px">
            <p style="text-align: center">
              复制微信号：<span id="wx_account"></span>
            </p>
            <p style="text-align: center">长按复制微信号，去微信搜索添加</p>
          </div>
          <div id="click_to_add" style="margin-top: 20px">
            <svg
              id="wx-svg"
              t="*************"
              style="width: 150px; height: 150px"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="4130"
              width="200"
              height="200"
            >
              <path
                d="M683.058 364.695c11 0 22 1.016 32.943 1.976C686.564 230.064 538.896 128 370.681 128c-188.104 0.66-342.237 127.793-342.237 289.226 0 93.068 51.379 169.827 136.725 229.256L130.72 748.43l119.796-59.368c42.918 8.395 77.37 16.79 119.742 16.79 11 0 21.46-0.48 31.914-1.442a259.168 259.168 0 0 1-10.455-71.358c0.485-148.002 128.744-268.297 291.403-268.297l-0.06-0.06z m-184.113-91.992c25.99 0 42.913 16.79 42.913 42.575 0 25.188-16.923 42.579-42.913 42.579-25.45 0-51.38-16.85-51.38-42.58 0-25.784 25.93-42.574 51.38-42.574z m-239.544 85.154c-25.384 0-51.374-16.85-51.374-42.58 0-25.784 25.99-42.574 51.374-42.574 25.45 0 42.918 16.79 42.918 42.575 0 25.188-16.924 42.579-42.918 42.579z m736.155 271.655c0-135.647-136.725-246.527-290.983-246.527-162.655 0-290.918 110.88-290.918 246.527 0 136.128 128.263 246.587 290.918 246.587 33.972 0 68.423-8.395 102.818-16.85l93.809 50.973-25.93-84.677c68.907-51.93 120.286-119.815 120.286-196.033z m-385.275-42.58c-16.923 0-34.452-16.79-34.452-34.179 0-16.79 17.529-34.18 34.452-34.18 25.99 0 42.918 16.85 42.918 34.18 0 17.39-16.928 34.18-42.918 34.18z m188.165 0c-16.984 0-33.972-16.79-33.972-34.179 0-16.79 16.927-34.18 33.972-34.18 25.93 0 42.913 16.85 42.913 34.18 0 17.39-16.983 34.18-42.913 34.18z"
                fill="#09BB07"
                p-id="4131"
              ></path>
            </svg>
            <p
              id="add_wx_tip"
              style="text-align: center; font-size: 20px; font-weight: 500"
            >
              点击添加微信
            </p>
            <p
              id="auto_add_tip"
              style="
                text-align: center;
                font-size: 16px;
                color: #999;
                display: none;
              "
            >
              3 秒后自动添加
            </p>
          </div>
        </div>
      </div>
      <div class="btn-group">
        <div class="continue btn" id="androidOpenBrowser">打开浏览器下载</div>
        <div class="continue btn" id="downloadBtn">下载资源</div>
        <div class="yet btn" id="copyBtn">复制下载链接</div>
      </div>
      <div class="yet btn" id="contactBtn">联系客服</div>
      <div class="tips" id="wx"></div>
      <div class="loading">
        <div class="img" style="background: none">
          <img src="https://oss.pangdasc.com/assets/loading.png" alt="" />
        </div>
      </div>
    </div>
    <script type="text/javascript">
      var pageType = getParam("addWechat") ? "wx" : "download";
      var url = "https://118ttc.com/monitor/forms-results-public";
      var orderNo = getParam("orderNo");
      var customer_channel = getParam("customer_channel");
      var hasDownload = getParam("download");
      var isBrowser = getParam("browser");
      var weixin = getParam("_wx");
      var autoAdd = getParam("autoAdd");
      var isAndroid = /android/i.test(navigator.userAgent);
      var loading = false;
      var downloadUrl = "";

      if (autoAdd) {
        $("#auto_add_tip").show();
      }

      $(function () {
        if (hasDownload) {
          if (isAndroid && !isBrowser) {
            $("#androidOpenBrowser").show();
            $("#androidOpenBrowser").addClass("flash");
            $("#androidOpenBrowser").on("click", function () {
              window.location.href = `intent://${
                location.host + location.pathname + location.search
              }&browser=1#Intent;scheme=http;package=com.android.browser;end`;
            });
          }
          if (isBrowser) {
            $("#downloadBtn").show();
            $("#downloadBtn").addClass("flash");
            $("#downloadBtn").on("click", function () {
              if (downloadUrl) downloadFile(downloadUrl);
            });
          }

          $("#copyBtn").show();
        }

        if (weixin) {
          $("#wx").text("微信号：" + weixin);
          document
            .getElementById("contactBtn")
            .setAttribute("data-clipboard-text", weixin);
          var wxc = new ClipboardJS("#contactBtn");
          wxc.on("success", function (e) {
            alert("已拷贝微信号，可到微信添加客服好友。", 2000);
            // window.location.href='weixin://';
            $("#wx").show();
            e.clearSelection();
          });
        } else {
          $("#contactBtn").on("click", function () {
            if (!weixin) {
              alert(`请等待客服联系您`);
            }
          });
        }

        if (orderNo && !loading) {
          loading = true;
          $.ajax({
            url: url + "/isPay/" + orderNo,
            type: "get",
            async: true,
            contentType: "application/json",
            success: function (data) {
              if (data.data) {
                if (pageType === "download") {
                  $.ajax({
                    url: url + "/download/" + orderNo,
                    type: "get",
                    async: true,
                    success: function (data) {
                      if (data.data) {
                        $("#sub-tooltip").text("选择适合您的资源下载方式：");
                        downloadUrl = data.data;
                        document
                          .getElementById("copyBtn")
                          .setAttribute("data-clipboard-text", data.data);
                        var copy = new ClipboardJS("#copyBtn");
                        copy.on("success", function (e) {
                          alert("已拷贝下载地址，请复制到手机浏览器下载");
                          e.clearSelection();
                        });
                      } else {
                        alert("获取下载地址失败:" + data.msg);
                      }
                      loading = false;
                    },
                    error: function () {
                      loading = false;
                    },
                  });
                } else if (pageType === "wx") {
                  $.ajax({
                    url: url + "/config/" + orderNo,
                    type: "get",
                    async: true,
                    success: function (data) {
                      if (data.data) {
                        var wxConfig = JSON.parse(data.data).form.find(
                          (item) => item.hasWx
                        );
                        initWx(wxConfig);
                      } else {
                        alert("获取信息失败:" + data.msg);
                      }
                      loading = false;
                    },
                    error: function () {
                      loading = false;
                    },
                  });
                }
              } else {
                loading = false;
                alert("支付未完成");
              }
            },
            error: function () {
              loading = false;
            },
          });
        }
      });
      function initWx(config) {
        var wxType = config.wxType;
        pageType = "wx";
        $("#add_wechat").show();
        var qrcode = config.qrcode;
        if (qrcode) {
          $("#js_qrcode_img").attr("src", qrcode);
          $("#qrcode_wrap").show();
          $("#wx-svg").hide();
        }
        switch (wxType) {
          case 1:
            $("#copy_to_add").show();
            $("#wx_account").text(config.wxConfig.wx);
            break;
          case 2:
            $("#click_to_add").show();
            if (!config.wxConfig.linkUrl) {
              if (qrcode) $("#add_wx_tip").text("长按二维码添加");
              else $("#add_wx_tip").hide();
              return;
            }
            $("#add_cp").click(function () {
              openWx(config.wxConfig.linkUrl);
            });
            if (autoAdd) {
              setTimeout(function () {
                openWx(config.wxConfig.linkUrl);
              }, 3000);
            }
            break;
          case 3:
            $("#click_to_add").show();
            if (!config.wxConfig.schemeLink) {
              if (qrcode) $("#add_wx_tip").text("长按二维码添加");
              else $("#add_wx_tip").hide();
              return;
            }
            $("#add_cp").click(function () {
              openWx(config.wxConfig.schemeLink);
            });
            if (autoAdd) {
              setTimeout(function () {
                openWx(config.wxConfig.schemeLink);
              }, 3000);
            }
            break;
        }
      }
      function getParam(n) {
        return new URLSearchParams(window.location.search).get(n);
      }
      function openWx(url) {
        // 判断是否在微信环境
        if (/(MicroMessenger|WeiChat|Weixin)/i.test(navigator.userAgent)) {
          window.open(url + "?customer_channel=" + customer_channel);
        } else {
          const iframe = document.createElement("iframe");
          iframe.src = url + "?customer_channel=" + customer_channel;
          document.body.appendChild(iframe);
        }
      }
      function downloadFile(url) {
        var link = document.createElement("a");
        link.href = url;

        var paths = location.pathname.split("/");
        link.setAttribute("download", paths[paths.length - 1]);

        if (typeof link.download === "string") {
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } else {
          window.open(url);
        }
      }
    </script>
  </body>
</html>
