<html lang="en" class="trancy-zh-CN" style="font-size: 100px">

<head>
  <meta charset="UTF-8" />
  <meta name="force-rendering" content="webkit" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
  <meta http-equiv="pragram" content="no-cache" />
  <meta name="referrer" content="unsafe-url" />
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no,viewport-fit=cover" />
  <script src="https://oss.fanspvt.com/assets/jquery.min.js"></script>
  <script src="https://oss.fanspvt.com/assets/jwx.js"></script>
  <style>
    body,
    dd,
    div,
    dl,
    footer,
    form,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    header,
    input,
    label,
    li,
    menu,
    nav,
    ol,
    p,
    section,
    select,
    td,
    textarea,
    th,
    time,
    ul {
      margin: 0;
      padding: 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
    }

    html,
    body {
      max-width: 750px;
      margin: auto;
      font-size: 0.24rem;
    }

    img {
      width: 100%;
      display: block;
    }

    .main {
      width: 100%;
      min-height: 100vh;
    }
  </style>
  <title>支付</title>
  <script>
    function _resize() {
      var html = document.getElementsByTagName("html")[0];
      var hW = html.offsetWidth > 600 ? 600 : html.offsetWidth;
      var fS = (100 / 750) * hW;
      html.style.fontSize = fS + "px";
    }
    _resize();
    window.onresize = function () {
      _resize();
    };
  </script>
  <style>
    .main .btn-group .btn {
      width: 6.7rem;
      height: 0.98rem;
      line-height: 0.98rem;
      text-align: center;
      border-radius: 0.49rem;
      border: 1px solid #28c445;
      color: #28c445;
      font-size: 0.36rem;
      margin: 0 auto;
    }

    .main .btn:first-child {
      margin: 30px auto;
      background: #28c445;
      color: #fff;
    }

    .loading {
      position: fixed;
      top: 50%;
      display: none;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.6);
      width: 1.4rem;
      height: 1.4rem;
      border-radius: 0.14rem;
      z-index: 99999;
    }

    .loading .img {
      position: absolute;
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
    }
  </style>
</head>

<body>
  <div class="main">
    <div class="pay-bannder">
      <img src="https://oss.pangdasc.com/assets/pay_banner.png" alt="" />
    </div>
    <div class="btn-group">
      <div class="continue btn" id="continueBtn">继续支付</div>
      <div class="yet btn" id="finishBtn">已完成支付</div>
    </div>
    <div class="loading">
      <div class="img" style="background: none">
        <img src="https://oss.pangdasc.com/assets/loading.png" alt="" />
      </div>
    </div>
  </div>
  <script type="text/javascript">
    function getParam(n) {
      return new URLSearchParams(window.location.search).get(n);
    }

    var formId = getParam("formId");
    var orderNo = getParam("orderNo");
    var payType = getParam("payType");
    var isFirst = getParam("first");
    var openid = getParam("openid");
    var returnUrl = decodeURIComponent(getParam("returnUrl"));
    var times = 2;

    var loadTimes = 30;
    var url = "https://blue-monitor.118ttc.com/monitor/forms-results-public";

    $(function () {
      $("#finishBtn").on("click", function () {
        check();
      });

      // 继续付款
      $("#continueBtn").on("click", function () {
        pay();
      });

      $(".loading").show();

      if (isFirst) {
        pay();
      } else {
        loadCheck();
      }
    });

    function check() {
      if (orderNo)
        $.ajax({
          url: url + "/isPay/" + orderNo,
          type: "get",
          async: true,
          contentType: "application/json",
          beforeSend: function () {
            $(".loading").show();
          },
          success: function (data) {
            if (data.data) {
              window.location.href =
                "https://fanspvt.com/forms/result.html" +
                location.search;
            } else if (times <= 0) {
              alert("支付未完成");
            } else {
              times--;
              setTimeout(check, 1500);
            }
          },
          complete: function () {
            $(".loading").hide();
          },
          error: function () {
            setTimeout(check, 2000);
          },
        });
    }

    var isLoad = false;
    function loadCheck() {
      if (isLoad) return;
      isLoad = true;
      loadTimes--;
      if (orderNo)
        $.ajax({
          url: url + "/isPay/" + orderNo,
          type: "get",
          async: true,
          contentType: "application/json",
          success: function (data) {
            if (data.data) {
              window.location.href =
                "https://fanspvt.com/forms/result.html" +
                location.search;
            } else if (loadTimes >= 0) {
              setTimeout(loadCheck, 2000);
            }
          },
          error: function () {
            setTimeout(loadCheck, 2500);
          },
        });
    }

    function pay() {
      if (orderNo) {
        const postData = { orderNo, payType };
        if (openid) {
          postData.mpOpenId = openid;
          postData.payType = "3";
        }
        $.ajax({
          url: url + "/pay",
          type: "post",
          data: JSON.stringify(postData),
          contentType: "application/json",
          beforeSend: function () {
            $(".loading").show();
          },
          success: function (data) {
            let res = data.data;
            if (res && res.payContent && postData.payType === "3") {
              const payContent = res.payContent;
              // 初始化支付控制器
              const payment = new WechatH5Payment();
              payment
                .pay(payContent)
                .then((res) => {
                  loadCheck();
                })
                .catch((err) => {
                  alert("支付失败");
                });
            } else if (res && res.payContent) {
              var url =
                "https://h5.pangdacd.com/pay.html" +
                location.search.replace("first", "second");
              if (res.nextAction.actionCode === 2) url += "&download=1";
              window.location.href =
                res.payContent + "&redirect_url=" + encodeURIComponent(url);
            }
          },
          complete: function () {
            $(".loading").hide();
          },
        });
      }
    }
    class WechatH5Payment {
      constructor() { }
      async pay(paymentParams) {
        try {
          // 环境检测
          this.detectEnvironment();
          return await this.wechatJSAPIPay(paymentParams);
        } catch (error) {
          this.handleError(error);
        }
      }

      // 环境检测
      detectEnvironment() {
        const ua = navigator.userAgent.toLowerCase();
        this.isWechat = ua.includes("micromessenger");
        this.isIOS = /iphone|ipad|ipod/.test(ua);
      }
      // 微信JSAPI支付
      wechatJSAPIPay(params) {
        return new Promise((resolve, reject) => {
          this.getWxConfig(() => {
            window.wx.chooseWXPay({
              appId: params.appId,
              timestamp: params.timeStamp,
              nonceStr: params.nonceStr,
              package: params.packageValue,
              signType: params.signType,
              paySign: params.paySign,
              success: (res) => {
                res.errMsg === "chooseWXPay:ok"
                  ? resolve(params)
                  : reject("支付取消");
              },
              fail: (err) => reject(err),
            });
          });
        });
      }
      getWxConfig(cb) {
        let os = "";
        let _url = window.location.href;
        if (this.isIOS) {
          // ios
          _url = window.location.href.split("#")[0];
        }

        fetch(
          "https://blue-monitor.118ttc.com/monitor/mp/pay/createJsapiSignature",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ url: _url, formId }),
          }
        )
          .then((response) => response.json())
          .then((data) => {
            if (data.code === 200) {
              const { appId, timestamp, nonceStr, signature } = data.data;
              window.wx.config({
                debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId: appId, // 必填，公众号的唯一标识
                timestamp: timestamp, // 必填，生成签名的时间戳
                nonceStr: nonceStr, // 必填，生成签名的随机串
                signature: signature, // 必填，签名，见附录1
                jsApiList: ["chooseWXPay"], // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
              });
              if (cb) {
                cb();
              }
            }
          });
      }

      // 错误处理
      handleError(error) {
        console.error("[Payment Error]", error);
      }
    }
  </script>
</body>

</html>
