<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="renderer" content="webkit" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <meta name="referrer" content="no-referrer" />
  <link rel="icon" />
  <title>花粉</title>
  <!--[if lt IE 11
      ]><script>
        window.location.href = "/html/ie.html";
      </script><!
    [endif]-->
  <style>
    /* iOS 风格的 CSS 变量 */
    :root {
      --ios-blue: #007aff;
      --ios-blue-light: #5ac8fa;
      --ios-blue-dark: #0051d5;
      --ios-white: #ffffff;
      --ios-light-gray: #f2f2f7;
      --ios-light-gray-2: #e5e5ea;
      --ios-gray: #8e8e93;
      --ios-gray-light: #aeaeb2;
      --ios-text-primary: #000000;
      --ios-text-secondary: #6d6d70;
      --ios-border-radius: 12px;
      --ios-border-radius-large: 16px;
      --ios-shadow-light: rgba(0, 0, 0, 0.08);
      --ios-shadow-medium: rgba(0, 0, 0, 0.12);
      --ios-font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
    }

    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
      font-family: var(--ios-font-family);
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      /* iOS 风格的渐变背景 */
      background: linear-gradient(135deg,
          var(--ios-light-gray) 0%,
          var(--ios-white) 50%,
          var(--ios-light-gray-2) 100%);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }

    /* iOS 风格的加载器容器 */
    .loader-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 24px;
    }

    /* iOS 风格的加载器 - 使用活动指示器样式 */
    #loader {
      width: 60px;
      height: 60px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: var(--ios-border-radius);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px var(--ios-shadow-medium);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 1001;
    }

    /* iOS 系统活动指示器 */
    .ios-activity-indicator {
      width: 28px;
      height: 28px;
      position: relative;
    }

    .ios-activity-indicator::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 2px solid transparent;
      border-top: 2px solid var(--ios-blue);
      border-radius: 50%;
      animation: ios-spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    }

    .ios-activity-indicator::after {
      content: "";
      position: absolute;
      top: 3px;
      left: 3px;
      width: calc(100% - 6px);
      height: calc(100% - 6px);
      border: 1px solid transparent;
      border-top: 1px solid var(--ios-blue-light);
      border-radius: 50%;
      animation: ios-spin 0.8s cubic-bezier(0.4, 0, 0.2, 1) infinite reverse;
    }

    @keyframes ios-spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* iOS 风格的进度条 */
    .ios-progress {
      width: 200px;
      height: 4px;
      background: var(--ios-light-gray-2);
      border-radius: 2px;
      overflow: hidden;
      position: relative;
    }

    .ios-progress::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
          transparent,
          var(--ios-blue),
          transparent);
      animation: ios-progress 2s ease-in-out infinite;
    }

    @keyframes ios-progress {
      0% {
        left: -100%;
      }

      50% {
        left: 100%;
      }

      100% {
        left: 100%;
      }
    }

    /* 移除原来的分屏效果，使用更优雅的淡入效果 */
    #loader-wrapper .loader-section {
      display: none;
    }

    .loaded #loader-wrapper {
      opacity: 0;
      visibility: hidden;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .loaded #loader {
      transform: scale(0.8);
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: var(--ios-text-primary);
    }

    /* iOS 风格的加载文字 */
    #loader-wrapper .load_title {
      font-family: var(--ios-font-family);
      color: var(--ios-text-primary);
      font-size: 17px;
      font-weight: 500;
      text-align: center;
      z-index: 9999999999999;
      opacity: 0.8;
      line-height: 1.4;
      letter-spacing: 0.5px;
      animation: ios-fade 2s ease-in-out infinite alternate;
    }

    @keyframes ios-fade {
      0% {
        opacity: 0.6;
      }

      100% {
        opacity: 1;
      }
    }

    #loader-wrapper .load_title span {
      font-weight: 400;
      font-style: normal;
      font-size: 15px;
      color: var(--ios-text-secondary);
      opacity: 0.7;
      display: block;
      margin-top: 4px;
    }

    /* 添加一些装饰性的浮动元素 */
    .floating-elements {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 1000;
    }

    .floating-elements::before,
    .floating-elements::after {
      content: "";
      position: absolute;
      width: 120px;
      height: 120px;
      background: radial-gradient(circle,
          rgba(0, 122, 255, 0.1) 0%,
          transparent 70%);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    .floating-elements::before {
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-elements::after {
      bottom: 20%;
      right: 10%;
      animation-delay: 3s;
    }

    @keyframes float {

      0%,
      100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.5;
      }

      50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
      }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      #loader {
        width: 50px;
        height: 50px;
      }

      .ios-activity-indicator {
        width: 24px;
        height: 24px;
      }

      .ios-progress {
        width: 160px;
      }

      #loader-wrapper .load_title {
        font-size: 16px;
        padding: 0 20px;
      }

        #loader-wrapper .load_title span {
          font-size: 14px;
        }
      }
    </style>
    <script src="/aliyun-upload-sdk/lib/es6-promise.min.js"></script>
    <script src="/aliyun-upload-sdk/lib/aliyun-oss-sdk-6.17.1.min.js"></script>
    <script src="/aliyun-upload-sdk/aliyun-upload-sdk-1.5.7.min.js"></script>
  </head>
  <body>
    <div id="app">
      <div id="loader-wrapper">
        <div class="floating-elements"></div>
        <div class="loader-container">
          <div id="loader">
            <div class="ios-activity-indicator"></div>
          </div>
          <div class="ios-progress"></div>
          <div class="load_title">
            正在加载系统资源，请耐心等待
            <span>Loading system resources...</span>
          </div>
        </div>
        <div class="loader-section section-left"></div>
        <div class="loader-section section-right"></div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
