package com.panda.pollen.sender.core.alisms;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.panda.pollen.sender.core.model.SendResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 阿里云短信发送
 * 官方文档：https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-sendsms?spm=a2c4g.11186623.help-menu-44282.d_4_2_4_2_0.2c762d779guqWW
 * <AUTHOR>
 * @Date : 2024年12月23日 18:06
 */
@Component
@Slf4j
public class AliSmsSender {

    private Config config;


    /**
     * 发送短信
     * @param templateCode      模板编号
     * @param phoneNumber       电话号码
     * @param templateParams    模板参数
     * @return
     */
    public SendResult send(String templateCode, String phoneNumber, Map<String, Object> templateParams) {
        Config sendConfig = builderConfig();
        // 配置 Endpoint
        sendConfig.endpoint = "dysmsapi.aliyuncs.com";
        AliSmsConfig aliSmsConfig = SpringUtil.getBean(AliSmsConfig.class);
        // 构造请求对象
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phoneNumber)
                .setSignName(aliSmsConfig.getSignName())
                .setTemplateCode(templateCode)
                .setTemplateParam(JSON.toJSONString(templateParams));
        SendResult sendResult = new SendResult();
        try {
            Client client = new Client(sendConfig);
            // 发送 API 请求
            SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
            log.info("短信发送结果为：{}", JSON.toJSONString(sendSmsResponse));
            sendResult.setResponse(sendSmsResponse.getBody());
            if (sendSmsResponse.getStatusCode() == 200 && sendSmsResponse.getBody().getCode().equals("OK")) {
                sendResult.setSuccess(true);
            } else {
                sendResult.setSuccess(false);
            }
        } catch (Exception e) {
            sendResult.setSuccess(false);
            sendResult.setResponse("发送短信发生异常：" + ExceptionUtil.getMessage(e));
        }
        return sendResult;
    }

    private Config builderConfig() {
        if (config == null) {
            AliSmsConfig aliSmsConfig = SpringUtil.getBean(AliSmsConfig.class);
            config = new Config()
                    // 请确保代码运行环境配置了相应环境变量
                    .setAccessKeyId(aliSmsConfig.getAccessKeyId())
                    .setAccessKeySecret(aliSmsConfig.getAccessKeySecret());
        }
        return config;
    }

}
