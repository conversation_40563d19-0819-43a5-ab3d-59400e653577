package com.panda.pollen.sender.core.wecom;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.panda.pollen.sender.core.model.SendResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信群机器人消息发送
 * 官方文档：https://developer.work.weixin.qq.com/document/path/91770
 * <AUTHOR>
 * @Date : 2024年12月24日 14:57
 */
@Component
@Slf4j
public class WeComSender {

    private static final String WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=";

    /**
     * 发送消息
     * @param msgType   消息类型
     * @param content   消息内容
     * @param key       群机器人key
     * @return
     */
    public SendResult sendMsg(WeComMsgType msgType, String content, String key, boolean atAll) {
        if (msgType == WeComMsgType.TEXT) {
            return sendTextMsg(content, key, atAll);
        } else if (msgType == WeComMsgType.MARKDOWN) {
            return sendMarkdownMsg(content, key, atAll);
        }
        return null;
    }


    /**
     * 发送文本消息
     * @param content   消息内容
     * @param key       群机器人key
     * @return
     */
    public SendResult sendTextMsg(String content, String key, boolean atAll) {
        Map<String, Object> bodyParams = buildTextMsgParams(content, atAll);
        return executeRequest(bodyParams, key);
    }

    /**
     * 发送Markdown消息
     * @param content   消息内容
     * @param key       群机器人key
     * @return
     */
    public SendResult sendMarkdownMsg(String content, String key, boolean atAll) {
        Map<String, Object> bodyParams = buildMarkdownMsgParams(content, atAll);
        return executeRequest(bodyParams, key);
    }


    /**
     * 发送http请求发送消息
     * @param bodyParams
     * @param key
     * @return
     */
    private SendResult executeRequest(Map<String, Object> bodyParams, String key) {
        String requestUrl = WEBHOOK_URL + key;
        HttpRequest httpRequest = HttpRequest.post(requestUrl);
        httpRequest.contentType("application/json");
        httpRequest.body(JSON.toJSONString(bodyParams));
        HttpResponse response = httpRequest.execute();
        SendResult sendResult = new SendResult();
        if (response.isOk()) {
            String body = response.body();
            sendResult.setResponse(body);
            log.info("发送企业微信消息返回结果为：{}", body);
            JSONObject resultObject = JSONObject.parseObject(body);
            int code = resultObject.getIntValue("errcode", 0);
            if (code != 0) {
                log.error("发送企业微信消息失败，接口返回结果为：{}", body);
                sendResult.setSuccess(false);
            } else {
                sendResult.setSuccess(true);
            }
        } else {
            log.error("请求企业微信发送消息失败，http status code is : {}", response.getStatus());
            sendResult.setSuccess(false);
            sendResult.setResponse("请求企业微信发送消息失败，http status code is : {}" + response.getStatus());
        }
        return sendResult;
    }


    /**
     * 构建markdown消息请求body体
     * @param content
     * @return
     */
    private Map<String, Object> buildMarkdownMsgParams(String content, boolean atAll) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("msgtype", WeComMsgType.MARKDOWN.getType());
        Map<String, Object> markdown = new HashMap<>();
        markdown.put("content", content);
        // atAll
        if (atAll) {
            markdown.put("mentioned_list", CollUtil.newArrayList("@all"));
        }
        messageMap.put("markdown", markdown);
        return messageMap;
    }


    /**
     * 构建文本消息请求body体
     * @param content   消息内容
     * @return
     */
    private Map<String, Object> buildTextMsgParams(String content, boolean atAll) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("msgtype", WeComMsgType.TEXT.getType());
        // 消息内容
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("content", content);
        // atAll
        if (atAll) {
            contentMap.put("mentioned_list", CollUtil.newArrayList("@all"));
        }
        messageMap.put("text", contentMap);

        return messageMap;
    }
}
