package com.panda.pollen.sender.core.dingtalk;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.panda.pollen.sender.core.model.SendResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 钉钉群机器人消息发送
 * 官方文档：https://open.dingtalk.com/document/orgapp/custom-robot-access
 * <AUTHOR>
 * @Date : 2024年12月24日 10:17
 */
@Component
@Slf4j
public class DingTalkSender {

    private static final String WEBHOOK_URL = "https://oapi.dingtalk.com/robot/send";

    /** 测试CID预警机器人secret */
    public static final String DEFAULT_SECRET = "SECd6e6eadda8f9afe77cef6c2d1743458d886f04ac2189c0504575e6d9785e1097";

    /** 测试CID预警机器人签名 */
    public static final String DEFAULT_TOKEN = "8d2a6b4b938dd2cccdad3f1a5bab098645d534178edda0deda1753cce2fa2431";

    /**
     * 发送钉钉消息
     * @param msgType       消息类型
     * @param title         消息标题
     * @param content       消息内容
     * @param accessToken   群机器人token
     * @param sign          群机器人sign
     * @return
     */
    public SendResult sendMsg(DingTalkMsgType msgType, String title, String content, String accessToken, String sign, boolean atAll) {
        if (msgType == DingTalkMsgType.TEXT) {
            return sendTextMsg(content, accessToken, sign, atAll);
        } else if (msgType == DingTalkMsgType.MARKDOWN) {
            return sendMarkdownMsg(title, content, accessToken, sign, atAll);
        }
        return null;
    }

    /**
     * 发送文本消息
     * @param content           消息内容
     * @param accessToken       钉钉群机器人token
     * @param sign              钉钉群机器人签名
     * @return
     */
    public SendResult sendTextMsg(String content, String accessToken, String sign, boolean atAll) {
        Map<String, Object> bodyParams = buildTextMsgParams(content, atAll);
        return executeRequest(bodyParams, accessToken, sign);
    }

    /**
     * 发送Markdown消息
     * @param title             消息标题
     * @param content           消息内容
     * @param accessToken       钉钉群机器人token
     * @param sign              钉钉群机器人签名
     * @return
     */
    public SendResult sendMarkdownMsg(String title, String content, String accessToken, String sign, boolean atAll) {
        Map<String, Object> bodyParams = buildMarkdownMsgParams(title, content, atAll);
        return executeRequest(bodyParams, accessToken, sign);
    }


    /**
     * 发送http请求钉钉发送消息
     * @param bodyParams
     * @param accessToken
     * @param sign
     * @return
     */
    private SendResult executeRequest(Map<String, Object> bodyParams, String accessToken, String sign) {
        String requestUrl = buildUrl(accessToken, sign);
        HttpRequest httpRequest = HttpRequest.post(requestUrl);
        httpRequest.contentType("application/json");
        httpRequest.body(JSON.toJSONString(bodyParams));
        HttpResponse response = httpRequest.execute();
        SendResult sendResult = new SendResult();
        if (response.isOk()) {
            String body = response.body();
            sendResult.setResponse(body);
            log.info("发送钉钉消息返回结果为：{}", body);
            JSONObject resultObject = JSONObject.parseObject(body);
            int code = resultObject.getIntValue("errcode", 0);
            if (code != 0) {
                log.error("发送钉钉消息失败，接口返回结果为：{}", body);
                sendResult.setSuccess(false);
            } else {
                sendResult.setSuccess(true);
            }
        } else {
            log.error("请求钉钉发送消息失败，http status code is : {}", response.getStatus());
            sendResult.setSuccess(false);
            sendResult.setResponse(StrUtil.format("请求钉钉发送消息失败，http status code is : {}", response.getStatus()));
        }
        return sendResult;
    }


    /**
     * 构建markdown消息请求body体
     * @param title
     * @param content
     * @return
     */
    private Map<String, Object> buildMarkdownMsgParams(String title, String content, boolean atAll) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("msgtype", DingTalkMsgType.MARKDOWN.getType());

        Map<String, String> markdown = new HashMap<>();
        markdown.put("title", title);
        markdown.put("text", content);
        messageMap.put("markdown", markdown);

        // at
        if (atAll) {
            Map<String, Object> at = new HashMap<>();
            at.put("isAtAll", true);
            messageMap.put("at", at);
        }

        return messageMap;
    }


    /**
     * 构建文本消息请求body体
     * @param content   消息内容
     * @return
     */
    private Map<String, Object> buildTextMsgParams(String content, boolean atAll) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("msgtype", DingTalkMsgType.TEXT.getType());
        // 消息内容
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("content", content);
        messageMap.put("text", contentMap);

        // at
        if (atAll) {
            Map<String, Object> at = new HashMap<>();
            at.put("isAtAll", true);
            messageMap.put("at", at);
        }

        return messageMap;
    }

    /**
     * 构建请求url
     * @param accessToken
     * @param secret
     * @return
     */
    private String buildUrl(String accessToken, String secret) {
        Long timestamp = System.currentTimeMillis();
        String sign = generateSign(timestamp, secret);
        return StrUtil.format("{}?access_token={}&timestamp={}&sign={}", WEBHOOK_URL, accessToken, timestamp, sign);
    }

    /**
     * 生成签名
     * @param timestamp
     * @param secret
     * @return
     */
    private String generateSign(Long timestamp, String secret) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal((timestamp + "\n" + secret).getBytes("UTF-8"));
            return URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        } catch (Exception e) {
            log.error("生成签名时出错：", e);
            throw new RuntimeException("生成钉钉消息签名出错：" + e.getMessage());
        }
    }


}
