package com.panda.pollen.sender.core.alisms;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date : 2024年12月23日 18:08
 */
@Data
@Component
@ConfigurationProperties(prefix = AliSmsConfig.PREFIX)
@ConditionalOnProperty(prefix = AliSmsConfig.PREFIX, name = "enabled", havingValue = "true")
public class AliSmsConfig {

    public static final String PREFIX = "panda.ali-sms";

    /**
     * 短信accessKeyId
     */
    private String accessKeyId;

    /**
     * 短信accessKeySecret
     */
    private String accessKeySecret;

    /**
     * 短信签名
     */
    private String signName;

}
