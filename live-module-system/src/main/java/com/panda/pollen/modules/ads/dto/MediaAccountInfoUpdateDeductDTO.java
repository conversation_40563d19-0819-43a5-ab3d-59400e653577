package com.panda.pollen.modules.ads.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;

/**
 * ClassName:com.panda.pollen.modules.ads.domain.dto
 * Description:媒体修改回传区间开关以及最大最小值DTO对象
 *
 * <AUTHOR> <br>
 * date 2023年 08月29日 10:22 <br>
 * @version v1.0 <br>
 */
@Data
public class MediaAccountInfoUpdateDeductDTO {

    /**
     * 主键ID
     */
    @NotNull
    private Long id;
    /**
     * 扣回传区间开关（1-开 0-关）
     */
    @NotNull
    private Boolean deductAreaEnable;

    /**
     * 扣回传区间最小值
     */
    @PositiveOrZero
    private int deductAreaMin;

    /**
     * 扣回传区间最大值
     */
    @PositiveOrZero
    private int deductAreaMax;

    /**
     * 区间外的sku订单是否回传
     */
    @NotNull
    private Boolean deductOutsideAreaEnable;

}
