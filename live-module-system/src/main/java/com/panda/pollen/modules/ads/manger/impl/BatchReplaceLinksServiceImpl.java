package com.panda.pollen.modules.ads.manger.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.common.utils.StringUtils;
import com.panda.pollen.common.utils.collect.MapUtils;
import com.panda.pollen.common.utils.sentinel.ThreadPoolUtils;
import com.panda.pollen.modules.ads.domain.PlanOperateRecord;
import com.panda.pollen.modules.ads.manger.IBatchReplaceLinksService;
import com.panda.pollen.modules.ads.mapper.PlanOperateRecordMapper;
import com.panda.pollen.modules.ads.service.IPlanOperateRecordService;
import com.panda.pollen.modules.ads.vo.AnchorMaterialVO;
import com.panda.pollen.ocean.entity.Message;
import com.panda.pollen.ocean.model.AdResponse;
import com.panda.pollen.ocean.service.OceanAdService;
import com.panda.pollen.system.enums.PlanOperateRecordEnum;
import com.panda.pollen.tencent.model.V3.TencentDynamicCreativeUpdateResponse;
import com.panda.pollen.tencent.service.V3.TencentDynamicCreativeV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 批量替换链接service业务处理层
 *
 * <AUTHOR> <br>
 * date 2023年 06月06日 11:39 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class BatchReplaceLinksServiceImpl extends ServiceImpl<PlanOperateRecordMapper, PlanOperateRecord> implements IBatchReplaceLinksService {

    @Autowired
    private OceanAdService oceanAdService;

    @Autowired
    private IPlanOperateRecordService planOperateRecordService;

    @Autowired(required = false)
    private TencentDynamicCreativeV3Service tencentDynamicCreativeV3Service;

    /**
     * @param accessToken        token验证
     * @param advertiserId       帐户ID
     * @param externalUrl        落地页链接
     * @param time               当前时间年月日时分秒(数字格式)
     * @param userName           当前用户名称
     * @param planIds            计划ID集合
     * @Description 批量修改落地页
     * <AUTHOR>
     * @param:
     * @Date: 2024/8/20 14:50
     * @return: boolean
     */
    @Override
    public boolean batchReplaceLinks(String accessToken, String advertiserId, String externalUrl, String time, String userName, Map<String, Object> planIds) {
        boolean isUpdate = false;
        ConcurrentHashMap<String, AnchorMaterialVO> anchorMaterialMap = new ConcurrentHashMap<>();
        //获取广告计划
        List<JSONObject> listUpgraded = oceanAdService.getAdvList(advertiserId, accessToken, planIds);
        //判断查询的广告计划集合是否为空
        if (CollectionUtil.isNotEmpty(listUpgraded)) {
            // 创建线程池跑任务，并通过sentinel限流
            ThreadPoolExecutor executorService = ThreadPoolUtils.createThreadPoolExecutor("BatchReplaceLinksServiceImpl", 4, 4);
            for (JSONObject jsonObject : listUpgraded) {
                //替换
                executorService.submit(() -> doUpdate(anchorMaterialMap, jsonObject, accessToken, advertiserId, externalUrl, time, userName));
            }
            ThreadPoolUtils.waitThreadPoolFinish(executorService);
            //是否更新成功
            isUpdate = anchorMaterialMap.remove("isUpdate") != null;
            //更新锚点信息
            Set<AnchorMaterialVO> anchorMaterialSet = new HashSet<>();
            for (Map.Entry<String, AnchorMaterialVO> anchor : anchorMaterialMap.entrySet()) {
                anchorMaterialSet.add(anchor.getValue());
            }
            updateNativeanchor(anchorMaterialSet, accessToken, advertiserId, time, userName, externalUrl);
        } else {
            //记录异常的广告日志
            exceptionLog(advertiserId, time, userName, PlanOperateRecordEnum.REPLACE.getKey(), null);
        }
        return isUpdate;
    }

    public void doUpdate(ConcurrentHashMap<String, AnchorMaterialVO> anchorMaterialMap, JSONObject jsonObject, String accessToken, String advertiserId, String externalUrl, String time, String userName) {
        try {
            if (replaceUrl(jsonObject, externalUrl)) {
                JSONObject resultJson = oceanAdService.updateAdv(accessToken, jsonObject);
                //新广告的日志记录
                calendarRecord(jsonObject, advertiserId, resultJson, time, userName, PlanOperateRecordEnum.REPLACE.getKey());
                if (ObjectUtils.isNotEmpty(resultJson)) {
                    if (!"0".equals(resultJson.getString("code"))) {
                        log.error("【商品模块】替换商品2.0,advertiserId{},广告数据{},DP替换异常{}", advertiserId, jsonObject, resultJson);
                    } else {
                        anchorMaterialMap.put("isUpdate", new AnchorMaterialVO());
                    }
                    log.info("【商品模块】替换商品2.0,advertiserId{},广告ID{},更新DP{}", advertiserId, jsonObject.getString("promotion_id"), resultJson);
                }
                //添加锚点信息到集合
                getAnchorMaterialSet(jsonObject, anchorMaterialMap);
            }
        } catch (Exception e) {
            log.error("【商品模块】替换商品2.0,advertiserId{},广告数据{},DP替换异常{}", advertiserId, jsonObject, e.getMessage(), e);
        }
    }

    private void getAnchorMaterialSet(JSONObject jsonObject, ConcurrentHashMap<String, AnchorMaterialVO> anchorMaterialMap) {
        JSONArray anchorMaterialList = (JSONArray) jsonObject.getByPath("promotion_materials.anchor_material_list");
        if (ObjectUtils.isNotEmpty(anchorMaterialList)) {
            //判断锚点信息不为空
            for (Object object : anchorMaterialList) {
                JSONObject anchorMaterial = (JSONObject) object;
                //转换为对象
                AnchorMaterialVO anchorMaterialVO = JSON.parseObject(anchorMaterial.toString(), AnchorMaterialVO.class);
                anchorMaterialMap.put(anchorMaterialVO.getAnchorId(), anchorMaterialVO);
            }
        }
    }

    public void updateNativeanchor(Set<AnchorMaterialVO> anchorMaterialVOS, String accessToken, String advertiserId, String time, String userName, String externalUrl) {
        //通过瞄点类型分组 把相同的类型分为一组
        Map<String, List<AnchorMaterialVO>> anchorMaterialMap = anchorMaterialVOS.stream().collect(Collectors.groupingBy(AnchorMaterialVO::getAnchorType));
        for (Map.Entry<String, List<AnchorMaterialVO>> entry : anchorMaterialMap.entrySet()) {
            try {
                //获取锚点类型
                String anchorType = entry.getKey();
                //暂时只支持购物锚点类型的修改
                if (!"SHOPPING_CART".equals(anchorType)) {
                    continue;
                }
                //获取锚点id集合
                List<String> anchorIdList = entry.getValue().stream().map(AnchorMaterialVO::getAnchorId).collect(Collectors.toList());
                //创建整数流 从0开始 范围限制20
                IntStream.range(0, (anchorIdList.size() + 20 - 1) / 20)
                        .forEach(i -> {
                            //起始位置
                            int start = i * 20;
                            //结束位置
                            int end = Math.min(start + 20, anchorIdList.size());
                            //获取这一批数据的锚点id
                            List<String> anchorIds = anchorIdList.subList(start, end);
                            // 查询锚点详情
                            List<JSONObject> nativeanchorDetail = oceanAdService.getNativeanchorDetail(advertiserId, accessToken, anchorType, anchorIds);
                            for (JSONObject jsonObject : nativeanchorDetail) {
                                try {
                                    //修改
                                    Message message = oceanAdService.updateNativeAnchor(advertiserId, accessToken, anchorType, jsonObject, externalUrl);
                                    //判断状态码不为空 才需要进行日志存储
                                    if (message.getCode() != null) {
                                        //日志记录
                                        PlanOperateRecord planOperateRecord = new PlanOperateRecord();
                                        planOperateRecord.setTraceId(time);
                                        planOperateRecord.setMediaPlatformType(MediaTypeEnum.OCEAN.getCode());
                                        planOperateRecord.setBusinessId(message.getAnchorId());
                                        planOperateRecord.setAdvertiserId(NumberUtil.parseLong(advertiserId));
                                        planOperateRecord.setOperateType(PlanOperateRecordEnum.MODIFIED_ANCHOR.getKey());
                                        planOperateRecord.setOperateTime(new Date());
                                        //状态码等于0 则操作成功
                                        if (message.getCode() == PlanOperateRecordEnum.MODIFIED_SUCCESSFULLY.getKey()) {
                                            planOperateRecord.setOperateState(PlanOperateRecordEnum.RECORD_SUCCESS.getKey());
                                        } else {
                                            planOperateRecord.setOperateState(PlanOperateRecordEnum.RECORD_FAILURE.getKey());
                                        }
                                        //是否成功的日志信息
                                        planOperateRecord.setOperateErrorMessage(message.getMessage());
                                        planOperateRecord.setCreateBy(userName);
                                        planOperateRecord.setCreateTime(new Date());
                                        planOperateRecord.setUpdateBy(userName);
                                        planOperateRecord.setUpdateTime(new Date());
                                        save(planOperateRecord);
                                    }
                                } catch (Exception e) {
                                    //记录异常的广告日志
                                    exceptionLog(advertiserId, time, userName, PlanOperateRecordEnum.MODIFIED_ANCHOR.getKey(), e.getMessage());
                                }
                            }
                        });
            } catch (Exception e) {
                //记录异常的广告日志
                exceptionLog(advertiserId, time, userName, PlanOperateRecordEnum.MODIFIED_ANCHOR.getKey(), e.getMessage());
            }
        }
    }

    /**
     * 修改url的json对象
     *
     * @param jsonObject
     * @param externalUrl
     * @return
     */
    private boolean replaceUrl(JSONObject jsonObject, String externalUrl) {
        boolean contains = false;
        JSONObject promotionMaterials = jsonObject.getJSONObject("promotion_materials");
        if (ObjectUtils.isNotEmpty(promotionMaterials)) {
            //获取图文素材广告信息
            JSONArray carouselMaterialArray = promotionMaterials.getJSONArray("carousel_material_list");
            //如果图文素材广告不为空,则需要看carousel_id和item_id是否为空,如果为空则需要删除,否则修改报错
            if(ObjectUtils.isNotEmpty(carouselMaterialArray)){
                for (int i = carouselMaterialArray.size() - 1; i >= 0; i--) {
                    JSONObject carouselMaterial = carouselMaterialArray.getJSONObject(i);
//                    if (ObjectUtils.isNotEmpty(carouselMaterial) && carouselMaterial.containsKey("carousel_id") ) {
//                        if (StringUtils.isEmpty(carouselMaterial.getString("carousel_id")) || "0".equals(carouselMaterial.getString("carousel_id"))) {
//                            carouselMaterial.remove("carousel_id");
//                            carouselMaterial.put("carousel_id", carouselMaterial.getString("material_id"));
//                        }
//                    }
                    if (ObjectUtils.isNotEmpty(carouselMaterial) && carouselMaterial.containsKey("item_id") ) {
                        if (StringUtils.isEmpty(carouselMaterial.getString("item_id")) || "0".equals(carouselMaterial.getString("item_id"))){
                            carouselMaterial.remove("item_id");
                            carouselMaterial.put("carousel_id", carouselMaterial.getString("material_id"));
                        }
                    }
                }
            }
            JSONArray externalUrlJsonArray = promotionMaterials.getJSONArray("external_url_material_list");
            //替换一跳
            if (ObjectUtils.isNotEmpty(externalUrlJsonArray)) {
                for (int i = externalUrlJsonArray.size() -1; i >= 0 ; i--) {
                    //如果替换媒体则只替换成当前商品的链接其余链接清空
                    if (StringUtils.isNotBlank(externalUrl)) {
                        externalUrlJsonArray.clear();
                        externalUrlJsonArray.add(externalUrl);
                        contains = true;
                        break;
                    }
                }
            }
        }
        return contains;
    }

    /**
     * @param advertiserId 广告主id
     * @param time         当前时间年月日时分秒(数字格式)
     * @param userName     当前用户名称
     * @param operateType  操作类型(1暂停计划,2替换链接,3:更新监控链接)枚举值
     * @Description 异常的日志记录方法
     * <AUTHOR>
     * @Date: 2023/8/11 16:06
     * @return: void
     */
    private void exceptionLog(String advertiserId, String time, String userName, Integer operateType, String errMsg) {
        PlanOperateRecord planOperateRecord = getPlanOperateRecord(advertiserId, time, userName, operateType, MediaTypeEnum.OCEAN.getCode(), errMsg);
        planOperateRecordService.save(planOperateRecord);
    }

    private static PlanOperateRecord getPlanOperateRecord(String advertiserId, String time, String userName, Integer operateType, Integer mediaPlatformType, String errMsg) {
        PlanOperateRecord planOperateRecord = new PlanOperateRecord();
        planOperateRecord.setTraceId(time);
        planOperateRecord.setMediaPlatformType(mediaPlatformType);
        planOperateRecord.setAdvertiserId(Long.valueOf(advertiserId));
        planOperateRecord.setOperateType(operateType);
        planOperateRecord.setOperateTime(new Date());
        planOperateRecord.setCreateTime(new Date());
        planOperateRecord.setCreateBy(userName);
        planOperateRecord.setUpdateTime(new Date());
        planOperateRecord.setUpdateBy(userName);
        planOperateRecord.setOperateState(0);
        if (StringUtils.isBlank(errMsg)) {
            errMsg = "广告列表为空或授权异常!";
        }
        planOperateRecord.setOperateErrorMessage(errMsg);
        return planOperateRecord;
    }

    /**
     * 巨量 日志记录封装对象
     *
     * @param jsonData     列表json对象
     * @param advertiserId 媒体账户id
     * @param jsonObject   修改成功的json对象
     * @param time         当前时间年月日时分秒(数字格式)
     * @param userName     当前用户名称
     * @param operateType  操作类型(1暂停计划,2替换链接,3:更新监控链接)枚举值
     */
    public void calendarRecord(JSONObject jsonData, String advertiserId, JSONObject jsonObject, String time, String userName, Integer operateType) {
        //通用的数据提出来封装成为一个对象
        PlanOperateRecord planOperateRecord = getPlanOperateRecord(jsonObject, advertiserId, time, userName, operateType, MediaTypeEnum.OCEAN.getCode());
        //替换链接的日志记录
        if (PlanOperateRecordEnum.REPLACE.getKey().equals(operateType)) {
            //新版广告替换日志
            planOperateRecord.setBusinessId(String.valueOf(jsonData.getString("promotion_id")));
            planOperateRecord.setBusinessName(String.valueOf(jsonData.getString("promotion_name")));
        }
        //监控链接的日志记录
        if (PlanOperateRecordEnum.SUPERVISORY_CONTROL.getKey().equals(operateType)) {
            //新广告监控日志
            planOperateRecord.setBusinessId(String.valueOf(jsonData.getString("project_id")));
            planOperateRecord.setBusinessName(String.valueOf(jsonData.getString("name")));
        }
        planOperateRecordService.save(planOperateRecord);
    }

    /**
     * @param jsonObject        修改成功的json对象
     * @param advertiserId      媒体账户id
     * @param time              当前时间年月日时分秒(数字格式)
     * @param userName          当前用户名称
     * @param operateType       操作类型(1暂停计划,2替换链接,3:更新监控链接)枚举值
     * @param mediaPlatformType 媒体对象(1巨量,2快手,3微博,4腾讯)
     * @Description 通用对象的封装方法
     *  <AUTHOR>
     * @Date: 2023/7/13 15:52
     */
    private static PlanOperateRecord getPlanOperateRecord(JSONObject jsonObject, String advertiserId, String time, String userName, Integer operateType, Integer mediaPlatformType) {
        PlanOperateRecord planOperateRecord = new PlanOperateRecord();
        planOperateRecord.setTraceId(time);
        planOperateRecord.setMediaPlatformType(mediaPlatformType);
        planOperateRecord.setAdvertiserId(Long.valueOf(advertiserId));
        planOperateRecord.setOperateType(operateType);
        planOperateRecord.setCreateTime(new Date());
        planOperateRecord.setCreateBy(userName);
        planOperateRecord.setUpdateTime(new Date());
        planOperateRecord.setUpdateBy(userName);
        planOperateRecord.setOperateTime(new Date());
        Integer code = jsonObject.getInteger("code");
        //code状态标识=0 则标识修改成功
        if (Objects.equals(PlanOperateRecordEnum.MODIFIED_SUCCESSFULLY.getKey(), code)) {
            //获取修改成功日志
            JSONObject data = jsonObject.getJSONObject("data");
            //判断日志是否为空
            JSONArray list = data.getJSONArray("error_list");
            //若广告更新成功，则返回为空数组
            //若更新失败，则返回错误的部分及原因
            if (CollectionUtil.isNotEmpty(list)) {
                planOperateRecord.setOperateState(PlanOperateRecordEnum.RECORD_FAILURE.getKey());
            } else {
                planOperateRecord.setOperateState(PlanOperateRecordEnum.RECORD_SUCCESS.getKey());
            }
        } else {
            planOperateRecord.setOperateState(PlanOperateRecordEnum.RECORD_FAILURE.getKey());
        }
        planOperateRecord.setOperateErrorMessage(jsonObject.toString());
        return planOperateRecord;
    }

}
