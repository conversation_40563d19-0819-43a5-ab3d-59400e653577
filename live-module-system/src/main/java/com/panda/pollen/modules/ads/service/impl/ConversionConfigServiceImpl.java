package com.panda.pollen.modules.ads.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.common.annotation.DataScope;
import com.panda.pollen.common.core.redis.RedisCache;
import com.panda.pollen.common.enums.ads.ConversionConfigTypeEnum;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.modules.ads.domain.ConversionConfig;
import com.panda.pollen.modules.ads.domain.MediaAccountInfo;
import com.panda.pollen.modules.ads.dto.*;
import com.panda.pollen.modules.ads.manger.RedisInfoManager;
import com.panda.pollen.modules.ads.mapper.ConversionConfigMapper;
import com.panda.pollen.modules.ads.service.IConversionConfigService;
import com.panda.pollen.modules.ads.service.IPlanOperateRecordService;
import com.panda.pollen.modules.ads.vo.ConversionConfigListVO;
import com.panda.pollen.modules.ads.vo.FormStatisticsVO;
import com.panda.pollen.modules.ads.vo.MediaAccountInfoBriefVO;
import com.panda.pollen.modules.domain.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.panda.pollen.modules.ads.constant.Constants.CONVERSION_CONFIG_KEY;

/**
 * 扣回传和扣金额的统一配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ConversionConfigServiceImpl extends ServiceImpl<ConversionConfigMapper, ConversionConfig> implements IConversionConfigService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisInfoManager redisInfoManager;

    @Autowired
    private IPlanOperateRecordService planOperateRecordService;

    /**
     * 扣回传生效范围是账户时使用的计划id固定代替key
     */
    public final static String CONVERSION_ACCOUNT_FIXED_VALUE_REDIS_KEY = "panda88888888";


    @Override
    public ConversionConfig selectConversionConfigByBusinessIdAndType(String businessId, Integer type, Integer goodsPlatformType, Integer mediaPlatformType) {
        QueryWrapper<ConversionConfig> queryWrapper = Wrappers.query();
        queryWrapper.eq("type", type);
        queryWrapper.eq("business_id", businessId);
        if (ObjectUtils.isNotEmpty(goodsPlatformType) && goodsPlatformType != 0) {
            queryWrapper.eq("goods_platform_type", goodsPlatformType);
        }
        if (ObjectUtils.isNotEmpty(mediaPlatformType) && mediaPlatformType != 0) {
            queryWrapper.eq("media_platform_type", mediaPlatformType);
        }
        queryWrapper.eq("deleted", 0);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public Integer insertConversionConfig(ConversionConfig conversionConfig) {
        return this.getBaseMapper().insertConversionConfig(conversionConfig);
    }

    @Override
    public Integer updateConversionConfig(ConversionConfig conversionConfig) {
        return this.getBaseMapper().updateConversionConfig(conversionConfig);
    }

    @Override
    public void conversionConfig(List<ConversionConfigDTO> dtos, String userName) {
        for (ConversionConfigDTO dto : dtos) {
            if (ObjectUtils.isNotEmpty(dto.getDeductAreaEnable())) {
                if (dto.getDeductAreaEnable()) {
                    //区间的最小值且不能大于最大值
                    if (dto.getDeductAreaMin() > dto.getDeductAreaMax()) {
                        throw new ServiceException("输入的区间范围数值不合理！");
                    }
                }
            }
            //如果是自定义的时间段，则需要验证json参数
            if (dto.getConversionDayOrHour() == 3) {
                if (ObjectUtils.isEmpty(dto.getTimeSpanConversionProportionJson())) {
                    throw new ServiceException("配置自定义时段回传有误,请检查后重新提交！");
                } else {
                    try {
                        JSONArray.parseArray(dto.getTimeSpanConversionProportionJson());
                    } catch (Exception e) {
                        throw new ServiceException("配置自定义时段回传有误,请检查后重新提交！");
                    }
                }
            }
            //防钓鱼判断是否开启(目前回传区域固定成都,深圳)
            if (ObjectUtils.isNotEmpty(dto.getEntrapmentEnable()) && dto.getEntrapmentEnable()) {
                dto.setEntrapmentCity("510100,440300");
            }
            ConversionConfig config = new ConversionConfig();
            ConversionConfig configOld = this.selectConversionConfigByBusinessIdAndType(dto.getBusinessId(), dto.getType(), dto.getGoodsPlatformType(), dto.getMediaPlatformType());
            if (ObjectUtils.isNotEmpty(configOld)) {
                BeanUtil.copyProperties(dto, config);
                config.setId(configOld.getId());
                config.setUpdateBy(userName);
                //如果改变过则需要清除缓存
                if (!checkConfigChange(configOld, config)) {
                    deleteCache(config);
                }
                this.updateConversionConfig(config);
            } else {
                config = new ConversionConfig();
                BeanUtil.copyProperties(dto, config);
                config.setId(IdUtil.getSnowflakeNextId());
                config.setCreateBy(userName);
                this.insertConversionConfig(config);
            }
            setConversionConfigCache(config);

            // 保存操作记录
            planOperateRecordService.saveConversionOperateRecord(config);
        }
    }

    @Override
    public void setConversionConfigCache(ConversionConfig config) {
        String key = getConversionConfigKey(config);
        redisCache.setCacheMapValue(CONVERSION_CONFIG_KEY, key, config);
    }

    @Override
    public ConversionConfig getConversionConfigCache(ConversionConfigDTO dto) {
        ConversionConfig config = new ConversionConfig();
        BeanUtil.copyProperties(dto, config);
        return getConversionConfigCache(config);
    }

    @Override
    public ConversionConfig getConversionConfigCache(ConversionConfig config) {
        ConversionConfig result;
        String key = getConversionConfigKey(config);
        result = redisCache.getCacheMapValue(CONVERSION_CONFIG_KEY, key);
        if (ObjectUtils.isEmpty(result)) {
            result = selectConversionConfigByBusinessIdAndType(config.getBusinessId(), config.getType(), config.getGoodsPlatformType(), config.getMediaPlatformType());
            if (ObjectUtils.isNotEmpty(result)) {
                setConversionConfigCache(result);
            } else {
                config.setEnableConversion(false);
                config.setEnableAmount(false);
                config.setDelayEnable(false);
                config.setConversionDeductionCsite(false);
                setConversionConfigCache(config);
            }
        }
        return result;
    }

    @Override
    public <T> void listSetConversionConfig(List<T> list) {
        for (T t : list) {
            //商品获取缓存的数据
            ConversionConfigDTO dto = new ConversionConfigDTO();
            if (t instanceof MediaAccountInfo) {//媒体户获取缓存的数据
                MediaAccountInfo mediaAccountInfo = (MediaAccountInfo) t;
                dto.setBusinessId(mediaAccountInfo.getAdvertiserId());
                dto.setMediaPlatformType(mediaAccountInfo.getMediaType());
                dto.setType(ConversionConfigTypeEnum.ACCOUNT.getCode());
                mediaAccountInfo.setConversionConfig(this.getConversionConfigCache(dto));
            }
        }
    }

    @Override
    public void listSetConversionConfig(String groupBy, FormStatisticsVO statistics) {
        //商品获取缓存的数据
        ConversionConfigDTO dto = new ConversionConfigDTO();
        if ("plan_id".equals(groupBy)) {//计划报表获取缓存的数据
            dto.setBusinessId(statistics.getPlanId());
            dto.setMediaPlatformType(statistics.getMediaType());
            dto.setType(ConversionConfigTypeEnum.PLAN.getCode());
        } else if ("advertiser_id".equals(groupBy)) {//媒体户获取缓存的数据
            dto.setBusinessId(statistics.getAdvertiserId());
            dto.setMediaPlatformType(statistics.getMediaType());
            dto.setType(ConversionConfigTypeEnum.ACCOUNT.getCode());
        }
        statistics.setConversionConfig(this.getConversionConfigCache(dto));
    }

    @Override
    public ConversionOperationDTO operationGetConversionConfig(String planId, String advertiserId, String goodsId, String userName, Integer mediaPlatformType, Integer goodsPlatform) {
        /**
         * 计划的key:config的type+媒体type+计划id
         * 账户的key:生效范围计划config的type+媒体type+媒体户id+计划id;生效范围账户config的type+媒体type+媒体户id+固定值
         * 商品的key:生效范围计划config的type+商品type+媒体type+商品id+媒体id+计划id;生效范围账户config的type+商品type+媒体type+商品id+媒体id+固定值
         * 用户的key:生效范围计划config的type+用户id+媒体type+媒体户id+计划id;生效范围账户config的type+用户id+媒体type+媒体户id+固定值
         */
        ConversionOperationDTO conversionOperationDTO = new ConversionOperationDTO();
        String redisKey = "";
        String redisRootKey = "";
        //计划获取回传配置缓存的数据
        ConversionConfigDTO dto = new ConversionConfigDTO();
        dto.setBusinessId(planId);
        dto.setMediaPlatformType(mediaPlatformType);
        dto.setType(ConversionConfigTypeEnum.PLAN.getCode());
        ConversionConfig configPlan = this.getConversionConfigCache(dto);
        bulidConversionOperation(configPlan, conversionOperationDTO);
        if (ObjectUtils.isNotEmpty(configPlan) && ObjectUtils.isNotEmpty(configPlan.getEnableConversion()) && configPlan.getEnableConversion()
                && conversionOperationDTO.getConversionDeductionConfigDTO().getType() == ConversionConfigTypeEnum.PLAN.getCode()) {
            redisKey = ConversionConfigTypeEnum.PLAN.getCode() + ":" + mediaPlatformType + ":" + planId;
            redisRootKey = redisKey;
        }
        //媒体获取回传配置缓存的数据
        dto = new ConversionConfigDTO();
        dto.setBusinessId(advertiserId);
        dto.setMediaPlatformType(mediaPlatformType);
        dto.setType(ConversionConfigTypeEnum.ACCOUNT.getCode());
        ConversionConfig configAccount = this.getConversionConfigCache(dto);
        bulidConversionOperation(configAccount, conversionOperationDTO);
        if (ObjectUtils.isNotEmpty(configAccount) && ObjectUtils.isNotEmpty(configAccount.getEnableConversion())
                && configAccount.getEnableConversion()
                && ObjectUtils.isNotEmpty(conversionOperationDTO.getConversionDeductionConfigDTO())
                && conversionOperationDTO.getConversionDeductionConfigDTO().getType() == ConversionConfigTypeEnum.ACCOUNT.getCode()) {
            if (conversionOperationDTO.getConversionDeductionConfigDTO().getConversionDeductionScope() != null && conversionOperationDTO.getConversionDeductionConfigDTO().getConversionDeductionScope() == 0) {
                redisKey = ConversionConfigTypeEnum.ACCOUNT.getCode() + ":"
                        + mediaPlatformType + ":" + advertiserId + ":" + CONVERSION_ACCOUNT_FIXED_VALUE_REDIS_KEY;
            } else {
                redisKey = ConversionConfigTypeEnum.ACCOUNT.getCode() + ":"
                        + mediaPlatformType + ":" + advertiserId + ":" + planId;
            }
            redisRootKey = ConversionConfigTypeEnum.ACCOUNT.getCode() + ":" + mediaPlatformType + ":" + advertiserId;
        }

        //商品获取回传配置缓存的数据
        dto = new ConversionConfigDTO();
        dto.setBusinessId(goodsId);
        dto.setMediaPlatformType(mediaPlatformType);
        dto.setGoodsPlatformType(goodsPlatform);
        dto.setType(ConversionConfigTypeEnum.GOODS.getCode());
        ConversionConfig configGoods = this.getConversionConfigCache(dto);
        bulidConversionOperation(configGoods, conversionOperationDTO);
        if (ObjectUtils.isNotEmpty(configGoods) && ObjectUtils.isNotEmpty(configGoods.getEnableConversion())
                && configGoods.getEnableConversion()
                && ObjectUtils.isNotEmpty(conversionOperationDTO.getConversionDeductionConfigDTO())
                && conversionOperationDTO.getConversionDeductionConfigDTO().getType() == ConversionConfigTypeEnum.GOODS.getCode()) {
            if (conversionOperationDTO.getConversionDeductionConfigDTO().getConversionDeductionScope() != null && conversionOperationDTO.getConversionDeductionConfigDTO().getConversionDeductionScope() == 0) {
                redisKey = ConversionConfigTypeEnum.GOODS.getCode() + ":" + goodsPlatform + ":"
                        + mediaPlatformType + ":" + goodsId + ":" + advertiserId + ":" + CONVERSION_ACCOUNT_FIXED_VALUE_REDIS_KEY;
            } else {
                redisKey = ConversionConfigTypeEnum.GOODS.getCode() + ":" + goodsPlatform + ":"
                        + mediaPlatformType + ":" + goodsId + ":" + advertiserId + ":" + planId;
            }
            redisRootKey = ConversionConfigTypeEnum.GOODS.getCode() + ":" + goodsPlatform + ":"
                    + mediaPlatformType + ":" + goodsId;
        }

        //用户获取回传配置缓存的数据
        dto = new ConversionConfigDTO();
        dto.setBusinessId(userName);
        dto.setType(ConversionConfigTypeEnum.USER.getCode());
        ConversionConfig configUser = this.getConversionConfigCache(dto);
        bulidConversionOperation(configUser, conversionOperationDTO);
        if (ObjectUtils.isNotEmpty(configUser) && ObjectUtils.isNotEmpty(configUser.getEnableConversion())
                && configUser.getEnableConversion()
                && ObjectUtils.isNotEmpty(conversionOperationDTO.getConversionDeductionConfigDTO())
                && conversionOperationDTO.getConversionDeductionConfigDTO().getType() == ConversionConfigTypeEnum.USER.getCode()) {
            if (conversionOperationDTO.getConversionDeductionConfigDTO().getConversionDeductionScope() != null && conversionOperationDTO.getConversionDeductionConfigDTO().getConversionDeductionScope() == 0) {
                redisKey = ConversionConfigTypeEnum.USER.getCode() + ":" + userName + ":"
                        + mediaPlatformType + ":" + advertiserId + ":" + CONVERSION_ACCOUNT_FIXED_VALUE_REDIS_KEY;
            } else {
                redisKey = ConversionConfigTypeEnum.USER.getCode() + ":" + userName + ":"
                        + mediaPlatformType + ":" + advertiserId + ":" + planId;
            }
            redisRootKey = ConversionConfigTypeEnum.USER.getCode() + ":" + userName;
        }
        if (ObjectUtil.isNotEmpty(conversionOperationDTO.getConversionDeductionConfigDTO())) {
            conversionOperationDTO.getConversionDeductionConfigDTO().setRedisKey(redisKey);
            conversionOperationDTO.getConversionDeductionConfigDTO().setRedisRootKey("redisRootKey:" + redisRootKey);
        }
        //扣除回传板块构建
        bulidConversionDeductionCsite(configPlan,configAccount,configGoods,configUser, conversionOperationDTO);
        return conversionOperationDTO;
    }

    /**
      * @Description 扣除回传板块数据构建
      * <AUTHOR>
      * @param:
      * @param configPlan
      * @param configAccount
      * @param configGoods
      * @param configUser
      * @param conversionOperationDTO
      * @Date : 2025/3/26 15:36
      * @return: void
      *
    */
    private void bulidConversionDeductionCsite(ConversionConfig configPlan,ConversionConfig configAccount,ConversionConfig configGoods,
                                               ConversionConfig configUser, ConversionOperationDTO conversionOperationDTO) {
        boolean conversionDeductionCsite = false;
        String conversionDeductionCsiteJson = null;
        //按层级重新构建巨量,快手,腾讯板块是否扣回传独立生效,从计划配置按计划>账户>商品>用户来获取配置
        if (ObjectUtils.isNotEmpty(configPlan) && ObjectUtil.isNotEmpty(configPlan.getConversionDeductionCsite()) && configPlan.getConversionDeductionCsite()) {
            conversionDeductionCsite = true;
            conversionDeductionCsiteJson = configPlan.getConversionDeductionCsiteJson();
        } else if (ObjectUtils.isNotEmpty(configAccount) && ObjectUtil.isNotEmpty(configAccount.getConversionDeductionCsite()) && configAccount.getConversionDeductionCsite()) {
            conversionDeductionCsite = true;
            conversionDeductionCsiteJson = configAccount.getConversionDeductionCsiteJson();
        } else if (ObjectUtils.isNotEmpty(configGoods) && ObjectUtil.isNotEmpty(configGoods.getConversionDeductionCsite()) && configGoods.getConversionDeductionCsite()) {
            conversionDeductionCsite = true;
            conversionDeductionCsiteJson = configGoods.getConversionDeductionCsiteJson();
        } else if (ObjectUtils.isNotEmpty(configUser) && ObjectUtil.isNotEmpty(configUser.getConversionDeductionCsite()) && configUser.getConversionDeductionCsite()) {
            conversionDeductionCsite = true;
            conversionDeductionCsiteJson = configUser.getConversionDeductionCsiteJson();
        }
        if (conversionDeductionCsite) {
            if (ObjectUtil.isNotEmpty(conversionOperationDTO.getConversionDeductionConfigDTO())) {
                conversionOperationDTO.getConversionDeductionConfigDTO().setConversionDeductionCsite(true);
                conversionOperationDTO.getConversionDeductionConfigDTO().setConversionDeductionCsiteJson(conversionDeductionCsiteJson);
            } else {
                ConversionDeductionConfigDTO conversionDeductionConfigDTO = new ConversionDeductionConfigDTO();
                conversionDeductionConfigDTO.setConversionDeductionCsite(true);
                conversionDeductionConfigDTO.setConversionDeductionCsiteJson(conversionDeductionCsiteJson);
                conversionOperationDTO.setConversionDeductionConfigDTO(conversionDeductionConfigDTO);
            }
        }
    }

    /**
     * @param config
     * @Description 清除redis缓存重置操作
     * <AUTHOR>
     * @Date : 2024/4/12 15:14
     * @return: void
     */
    private void deleteCache(ConversionConfig config) {
        /**
         * 计划的key:config的type+媒体type+计划id
         * 账户的key:生效范围计划config的type+媒体type+媒体户id+计划id;生效范围账户config的type+媒体type+媒体户id+固定值
         * 商品的key:生效范围计划config的type+商品type+媒体type+商品id+媒体id+计划id;生效范围账户config的type+商品type+媒体type+商品id+媒体id+固定值
         * 用户的key:生效范围计划config的type+用户id+媒体type+媒体户id+计划id;生效范围账户config的type+用户id+媒体type+媒体户id+固定值
         */
        Set<String> orderKeys = new HashSet<>();
//        Set<String> deductKeys = new HashSet<>();
        String key = getConversionConfigKey(config);
        Map<String, Object> cacheMap = redisCache.getCacheMap("redisRootKey:" + key);
        if (ObjectUtil.isNotEmpty(cacheMap)) {
            orderKeys = cacheMap.keySet();
            for (String cacheKey : orderKeys) {
                redisCache.deleteObject(cacheKey);
                redisCache.deleteCacheMapValue("redisRootKey:" + key, cacheKey);
            }
        }
//        else {
//            orderKeys = (Set<String>) redisCache.keys(CONVERSION_ORDER_COUNT_KEY + key + "*");
//            deductKeys = (Set<String>) redisCache.keys(CONVERSION_DEDUCT_COUNT_KEY + key + "*");
//        }
//        for (String cacheKey : deductKeys) {
//            redisCache.deleteObject(cacheKey);
//        }
    }


    /**
     * 存储所有订单统计的key
     *
     * @param orderCountKey
     * @param orderDeductCountKey
     */
    @Override
    public void storeKeysForDelete(String orderCountKey, String orderDeductCountKey, String redisRootKey) {
        redisCache.setCacheMapValue(redisRootKey, orderCountKey, 1L);
        redisCache.setCacheMapValue(redisRootKey, orderDeductCountKey, 1L);
    }

    /**
      * @Description 扣回传配置列表
      * <AUTHOR>
      * @param:
      * @param dto
      * @Date : 2024/9/13 10:18
      * @return: java.util.List<com.panda.pollen.modules.ads.vo.ConversionConfigListVO>
      *
    */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<ConversionConfigListVO> getConversionConfigList(ConversionConfigListDTO dto) {
        List<ConversionConfigListVO> list = this.getBaseMapper().getConversionConfigList(dto);
        for (int i = 0; i < list.size(); i++) {
            ConversionConfigListVO configListVO = list.get(i);
            String bussinessName = "";
            if(configListVO.getType() == ConversionConfigTypeEnum.ACCOUNT.getCode()){
                MediaAccountInfoBriefVO mat = redisInfoManager.getMediaAccountInfo(configListVO.getBusinessId(), configListVO.getMediaPlatformType());
                if (null != mat) {
                    bussinessName = mat.getAdvertiserName();
                }
            } else if (configListVO.getType() == ConversionConfigTypeEnum.USER.getCode()) {
                UserVO userVO = redisInfoManager.getUser(configListVO.getBusinessId());
                if (null != userVO) {
                    bussinessName = userVO.getNickName();
                }
            }
            configListVO.setBusinessName(bussinessName);
        }
        return list;
    }

    /**
     * @param configOld
     * @param config
     * @Description 对比数据是否修改
     * <AUTHOR>
     * @Date : 2024/4/15 10:14
     * @return: boolean
     */
    private boolean checkConfigChange(ConversionConfig configOld, ConversionConfig config) {
        if (config == configOld) return true;
        if (configOld == null || config == null) return false;
        return ObjectUtil.equals(config.getEnableConversion(), configOld.getEnableConversion())
                && ObjectUtil.equals(config.getConversionProportion(), configOld.getConversionProportion())
                && ObjectUtil.equals(config.getConversionStartOrderCount(), configOld.getConversionStartOrderCount())
                && ObjectUtil.equals(config.getDeductAreaEnable(), configOld.getDeductAreaEnable())
                && ObjectUtil.equals(config.getDeductAreaMin(), configOld.getDeductAreaMin())
                && ObjectUtil.equals(config.getDeductAreaMax(), configOld.getDeductAreaMax())
                && ObjectUtil.equals(config.getDeductOutsideAreaEnable(), configOld.getDeductOutsideAreaEnable())
                && ObjectUtil.equals(config.getConversionDayOrHour(), configOld.getConversionDayOrHour())
                && ObjectUtil.equals(config.getTimeSpanConversionProportionJson(), configOld.getTimeSpanConversionProportionJson())
                && ObjectUtil.equals(config.getEntrapmentEnable(), configOld.getEntrapmentEnable())
                && ObjectUtil.equals(config.getBatchConversionEnable(), configOld.getBatchConversionEnable())
                && ObjectUtil.equals(config.getBatchConversionNum(), configOld.getBatchConversionNum())
                && ObjectUtil.equals(config.getBatchNum(), configOld.getBatchNum());
    }


    /**
     * @param config
     * @Description 构建config, redis的key
     * <AUTHOR>
     * @Date : 2024/4/15 15:21
     * @return: java.lang.String
     */
    private String getConversionConfigKey(ConversionConfig config) {
        /**
         * 计划的key:config的type+媒体type+计划id
         * 账户的key:生效范围计划config的type+媒体type+媒体户id+计划id;生效范围账户config的type+媒体type+媒体户id+固定值
         * 商品的key:生效范围计划config的type+商品type+媒体type+商品id+媒体id+计划id;生效范围账户config的type+商品type+媒体type+商品id+媒体id+固定值
         * 用户的key:生效范围计划config的type+用户id+媒体type+媒体户id+计划id;生效范围账户config的type+用户id+媒体type+媒体户id+固定值
         */
        String key = "";
        if (config.getType() == ConversionConfigTypeEnum.PLAN.getCode()
                || config.getType() == ConversionConfigTypeEnum.ACCOUNT.getCode()) {
            key = config.getType() + ":" + config.getMediaPlatformType() + ":" + config.getBusinessId();
        } else if (config.getType() == ConversionConfigTypeEnum.GOODS.getCode()) {
            key = config.getType() + ":" + config.getGoodsPlatformType() + ":" + config.getMediaPlatformType() + ":" + config.getBusinessId();
        } else {
            key = config.getType() + ":" + config.getBusinessId();
        }
        return key;
    }

    /**
     * @param config
     * @param conversionOperationDTO
     * @Description 构建回传配置的返回对象
     * <AUTHOR>
     * @param:
     * @Date : 2024/7/24 15:33
     * @return: void
     */
    private void bulidConversionOperation(ConversionConfig config, ConversionOperationDTO conversionOperationDTO) {
        if (ObjectUtils.isEmpty(config)) {
            return;
        }
        //扣回传比例构建
        if (ObjectUtils.isNotEmpty(config.getEnableConversion()) && config.getEnableConversion() && ObjectUtil.isEmpty(conversionOperationDTO.getConversionDeductionConfigDTO())) {
            ConversionDeductionConfigDTO conversionDeductionConfigDTO = new ConversionDeductionConfigDTO();
            BeanUtil.copyProperties(config, conversionDeductionConfigDTO);
            conversionOperationDTO.setConversionDeductionConfigDTO(conversionDeductionConfigDTO);
        }
        //延迟回传构建
        if (ObjectUtils.isNotEmpty(config.getDelayEnable()) && config.getDelayEnable() && ObjectUtil.isEmpty(conversionOperationDTO.getConversionDelayConfigDTO())) {
            ConversionDelayConfigDTO conversionDelayConfigDTO = new ConversionDelayConfigDTO();
            BeanUtil.copyProperties(config, conversionDelayConfigDTO);
            conversionOperationDTO.setConversionDelayConfigDTO(conversionDelayConfigDTO);
        }
    }
}
