package com.panda.pollen.modules.ads.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.panda.pollen.common.constant.CacheConstants;
import com.panda.pollen.common.core.domain.entity.SysDept;
import com.panda.pollen.common.core.domain.entity.SysUser;
import com.panda.pollen.common.core.redis.RedisCache;
import com.panda.pollen.common.core.service.ISysConfigService;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.common.utils.StringUtils;
import com.panda.pollen.common.utils.poi.ExcelUtil;
import com.panda.pollen.modules.ads.constant.TypeConstans;
import com.panda.pollen.modules.ads.domain.DomainInfo;
import com.panda.pollen.modules.ads.dto.MediaAccountInfoUpdateDTO;
import com.panda.pollen.modules.ads.dto.MediaAccountInfoUpdateFundThresholdValueDTO;
import com.panda.pollen.modules.ads.service.SpecialConfigService;
import com.panda.pollen.modules.ads.vo.DomainInfoVO;
import com.panda.pollen.modules.ads.vo.SpecialConfigExcelVO;
import com.panda.pollen.modules.domain.service.IDomainInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import static com.panda.pollen.common.utils.SecurityUtils.getUsername;
import static com.panda.pollen.modules.ads.constant.Constants.*;

/**
 * 个性化配置服务impl
 *
 * <AUTHOR>
 * @date 2023/06/23
 */
@Service
public class SpecialConfigServiceImpl implements SpecialConfigService {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IDomainInfoService domainInfoService;


    @Override
    public String domainNameGet(String firstDeptId) {
        return domainInfoService.selectDomainUrlByCompanyId(Long.valueOf(firstDeptId));
    }

    @Override
    public void domainNameSet(String firstDeptId, String domainName) {
        if (StringUtils.isEmpty(domainName)) {
            // 删除域名信息
            domainInfoService.removeDomainByCompanyId(DomainInfoVO.of(Long.valueOf(firstDeptId)));
            return;
        }
        // 域名信息同步到数据库
        SysUser user = SecurityUtils.getLoginUser().getUser();
        domainInfoService.updateDomainInfoByCompanyId(new DomainInfo(user.getDept().getFirstDeptId(), user.getDept().getFirstDeptName(), domainName, user.getUserName()));
    }

    @Override
    public void duoDuoSet(String username, String duoduo) {
        if (StringUtils.isEmpty(duoduo)) {
            redisCache.deleteCacheMapValue(USER_DUODUO_ID, username);
            return;
        }
        redisCache.setCacheMapValue(USER_DUODUO_ID, username, duoduo);
    }

    @Override
    public String duoDuoGet(String username) {
        String duoId = redisCache.getCacheMapValue(USER_DUODUO_ID, username);
        if (org.apache.commons.lang3.StringUtils.isBlank(duoId)) {
            duoId = sysConfigService.selectConfigByKey("ddjb_duo_id_36708975");
        }
        return duoId;
    }


    /**
     * 获取部门使用的多多进宝ID
     *
     * @param deptId 部门id
     * @return {@link String}
     */
    @Override
    public String duoDuoDeptGet(Long deptId) {
        return redisCache.getCacheMapValue(DEPT_DUODUO_ID, String.valueOf(deptId));
    }


    @Override
    public void orderNotificationSet(String userId, Integer enable) {
        if ((enable == null || enable < 0) && StringUtils.isBlank(userId)) {
            throw new ServiceException("参数异常！");
        }
        //为了方便使用所以设置后的value保存用户id,关闭则删除
        if (enable == 0) {
            redisCache.deleteCacheMapValue(ORDER_NOTIFICATION_USER_KEY, userId);
        } else {
            redisCache.setCacheMapValue(ORDER_NOTIFICATION_USER_KEY, userId, NumberUtil.parseLong(userId));
        }
    }

    @Override
    public Long orderNotificationGet(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new ServiceException("参数异常！");
        }
        return redisCache.getCacheMapValue(ORDER_NOTIFICATION_USER_KEY, userId);
    }

    /**
     * @param type(1-回传金额比例 2-回传订单比例 3-扣回传开关与区间金额 4-余额阈值 5-千展阈值)
     * @param userName      用户名称
     * @Description 获取缓存数据
     * <AUTHOR>
     * @Date : 2023/9/7 14:35
     * @return: java.lang.Object
     */
    @Override
    public Object getCacheData(Integer type, String userName) {
        Object data = null;
        if (TypeConstans.AMOUNT_RETURN_RATIO.equals(type)) {
            //金额回传比例
            data = redisCache.getCacheMapValue(AMOUNT_RETURN_RATIO, userName);
        } else if (TypeConstans.ORDER_RETURN_RATIO.equals(type)) {
            //订单回传比例
            data = redisCache.getCacheMapValue(ORDER_RETURN_RATIO, userName);
        } else if (TypeConstans.RETURN_INTERVAL_AMOUNT_SWITCH.equals(type)) {
            //扣回传开关与区间金额
            data = redisCache.getCacheMapValue(RETURN_INTERVAL_AMOUNT_SWITCH, userName);
        } else if (TypeConstans.BALANCE_THRESHOLD.equals(type)) {
            //余额阈值
            data = redisCache.getCacheMapValue(BALANCE_THRESHOLD, userName);
        } else if (TypeConstans.THOUSAND_SPREAD_THRESHOLD.equals(type)) {
            //千展阈值
            data = redisCache.getCacheMapValue(THOUSAND_SPREAD_THRESHOLD, userName);
        }
        return data;
    }


    /**
     * @param dto
     * @Description 设置用户中心账户余额阈值
     * <AUTHOR>
     * @Date : 2023/9/7 10:09
     * @return: void
     */
    @Override
    public void setBalanceThreshold(MediaAccountInfoUpdateFundThresholdValueDTO dto) {
        if (ObjectUtil.isNull(dto.getFundThresholdValue())) {
            throw new RuntimeException("输入的数值不能为空!");
        }
        //判断余额阈值小于0 则开关状态为闭合
        if (NumberUtil.isLess(dto.getFundThresholdValue(), new BigDecimal(0))) {
            redisCache.deleteCacheMapValue(BALANCE_THRESHOLD, getUsername());
        } else {
            redisCache.setCacheMapValue(BALANCE_THRESHOLD, getUsername(), dto.getFundThresholdValue());
        }
    }

    /**
     * @param dto
     * @Description 设置用户中心千展阈值
     * <AUTHOR>
     * @Date : 2023/9/7 10:09
     * @return: void
     */
    @Override
    public void setThousandSpreadThreshold(MediaAccountInfoUpdateDTO dto) {
        if (ObjectUtil.isNull(dto.getKilobarThreshold())) {
            throw new RuntimeException("输入的数值不能为空!");
        }
        //判断前展阈值小于0 则开关状态为闭合
        if (NumberUtil.isLess(dto.getKilobarThreshold(), new BigDecimal(0))) {
            redisCache.deleteCacheMapValue(THOUSAND_SPREAD_THRESHOLD, getUsername());
        } else {
            redisCache.setCacheMapValue(THOUSAND_SPREAD_THRESHOLD, getUsername(), dto.getKilobarThreshold());
        }
    }


    /**
     * @param username 当前登陆人名称
     * @Description 解锁用户
     * <AUTHOR>
     * @Date : 2023/10/12 10:54
     * @return: void
     */
    @Override
    public void unlock(String username) {
        //删除密码错误锁定的对象
        redisCache.deleteObject(CacheConstants.PWD_ERR_CNT_KEY + username);
    }

    /**
     * @param dept 部门信息对象
     * @Description 缓存群峰认证
     * <AUTHOR>
     * @param:
     * @Date : 2023/10/26 9:39
     * @return: void
     */
    @Override
    public void setMountainPeaks(SysDept dept) {
        if (ObjectUtil.isNotEmpty(dept.getQunFeng())) {
            redisCache.setCacheMapValue(DEPT_QUNFENG_APP_ID, dept.getDeptId() + "", dept.getQunFeng());
        }
    }

    /**
     * Title：importExcel <br>
     * Description：导入Execl <br>
     *
     * @return
     * <AUTHOR>  <br>
     * @date 2023/11/01 9:09<br>
     */
    @Override
    public void importExecl(MultipartFile file) throws IOException {
        List<SpecialConfigExcelVO> list = ExcelUtil.readExcel(file.getInputStream(), SpecialConfigExcelVO.class);
        for (SpecialConfigExcelVO excelVO : list) {
            String trim = StringUtils.trim(excelVO.getAdvertiserId());
            if (StringUtils.isNotBlank(trim)) {
                redisCache.setCacheMapValue(ADV_QUNFENG_APP_ID, trim, 1780174923571209L);
            }
        }
    }

}
