
/**
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：MediaAccountDTO.java <br>
 * Package：com.panda.pollen.modules.ads.domain.dto <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 *
 * <AUTHOR> <br>
 * date 2023年4月23日 下午9:14:12 <br>
 * @version v1.0 <br>
 */
package com.panda.pollen.modules.ads.dto;

import lombok.Data;

@Data
public class MediaAccountConversionDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * (1为回传次数比例2为回传金额比例)
     */
    private Integer type;

    /**
     * 是否开启回传限制(0:不开启;1:开启)
     */
    private Boolean isEnable;
    /**
     * 回传比例(包括100以内整数)
     */
    private Integer proportion;

    /**
     * 回传比例开始扣的订单数
     */
    private Integer conversionStartOrderCount;

    /**
     * 是否开启修改出价
     */
    private Boolean bidEnable;

    /**
     * 广告原始出价价格
     */
    private Double bid;

    /**
     * 按比例计算后广告新的出价价格
     */
    private Double newBid;

    /**
     * 按比例回传的生效失效(0按天,1按小时,2不重置)
     */
    private Integer conversionDayOrHour;

    /**
     * 扣回传区间开关（1-开 0-关）
     */
    private Boolean deductAreaEnable;

    /**
     * 扣回传区间最小值
     */
    private Integer deductAreaMin;

    /**
     * 扣回传区间最大值
     */
    private Integer deductAreaMax;

    /**
     * 区间外的sku订单是否回传
     */
    private Boolean deductOutsideAreaEnable;

    /**
     * 金额按比例回传类型(0按比例,1是按固定金额)
     */
    private Integer conversionAmountType;

    /**
     * 回传金额固定金额
     */
    private Integer conversionAmount;

    /**
     * 媒体回传扣除回传板块(目前只是开关1打开0关闭,打开就扣除番茄小说,火山版,穿山甲的回传)
     */
    private Boolean conversionDeductionCsite;
}
