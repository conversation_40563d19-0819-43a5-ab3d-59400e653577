package com.panda.pollen.modules.ads.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 回传配置统一对象 ads_conversion_config
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "ads_conversion_config")
public class ConversionConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 类型(1计划,2媒体账户,3商品,4用户)
     */
    @Excel(name = "类型(1计划,2媒体账户,3商品,4用户)")
    private Integer type;

    /**
     * 业务id根据type对应不同id
     */
    @Excel(name = "业务id根据type对应不同id")
    private String businessId;

    /**
     * 商品的电商平台
     */
    @Excel(name = "商品的电商平台")
    private Integer goodsPlatformType;

    /**
     * 媒体的媒体平台
     */
    @Excel(name = "媒体的媒体平台")
    private Integer mediaPlatformType;

    /**
     * 是否开启回传限制(0:不开启;1:开启)
     */
    @Excel(name = "是否开启回传限制(0:不开启;1:开启)")
    private Boolean enableConversion;

    /**
     * 回传比例
     */
    @Excel(name = "回传比例")
    private Integer conversionProportion;

    /**
     * 回传比例开始扣的订单数
     */
    @Excel(name = "回传比例开始扣的订单数")
    private Integer conversionStartOrderCount;

    /**
     * 扣回传区间开关（1-开 0-关）
     */
    @Excel(name = "扣回传区间开关", readConverterExp = "1=-开,0=-关")
    private Boolean deductAreaEnable;

    /**
     * 扣回传区间最小值
     */
    @Excel(name = "扣回传区间最小值")
    private Integer deductAreaMin;

    /**
     * 扣回传区间最大值
     */
    @Excel(name = "扣回传区间最大值")
    private Integer deductAreaMax;

    /**
     * 区间外的是否回传(0全回传1全不回传)
     */
    @Excel(name = "区间外的是否回传(0全回传1全不回传)")
    private Boolean deductOutsideAreaEnable;

    /**
     * 按比例计算后广告新的出价价格
     */
    @Excel(name = "按比例计算后广告新的出价价格")
    private Double newBid;

    /**
     * 原始广告出价价格
     */
    @Excel(name = "原始广告出价价格")
    private Double bid;

    /**
     * 是否开启修改出价
     */
    @TableField(exist = false)
    private Boolean bidEnable;

    /**
     * 按比例回传的生效失效(0按天,1按小时,2不重置,3自定义)
     */
    @Excel(name = "按比例回传的生效失效(0按天,1按小时,2不重置)")
    private Integer conversionDayOrHour;

    /**
     * 是否开启回传金额控制(0:不开启;1:开启)
     */
    @Excel(name = "是否开启回传金额控制(0:不开启;1:开启)")
    private Boolean enableAmount;

    /**
     * 回传金额比例
     */
    @Excel(name = "回传金额比例")
    private Integer amountProportion;

    /**
     * 延迟回传开关（1-开 0-关）
     */
    @Excel(name = "延迟回传开关(1-开 0-关)")
    private Boolean delayEnable;

    /**
     * 延迟回传设置的秒数
     */
    @Excel(name = "延迟回传设置的秒数")
    private Integer delaySecond;

    /**
     * 金额按比例回传类型(0按比例,1是按固定金额)
     */
    private Integer conversionAmountType;
    /**
     * 回传金额固定金额
     */
    private Integer conversionAmount;

    /**
     * 媒体回传扣除回传板块(目前只是开关1打开0关闭,打开就扣除番茄小说,火山版,穿山甲的回传)
     */
    private Boolean conversionDeductionCsite;

    /**
     * 扣回传生效范围(默认0:账户,1计划)
     */
    private Integer conversionDeductionScope;

    /**
     * 时段回传比例配置的json数据
     */
    private String timeSpanConversionProportionJson;

    /**
     * 扣除回传版位配置的json数据
     */
    private String conversionDeductionCsiteJson;

    /**
     * 防钓鱼配置开关
     */
    @Excel(name = "防钓鱼配置开关(1-开 0-关)")
    private Boolean entrapmentEnable;

    /**
     * 防钓鱼配置城市数据,正常回传的行政区域的数据,多个逗号隔开
     */
    @Excel(name = "防钓鱼配置城市数据,正常回传的行政区域的数据,多个逗号隔开")
    private String entrapmentCity;

    /**
     * 删除状态（0-未删除，1-删除）
     */
    @Excel(name = "删除状态", readConverterExp = "0=-未删除，1-删除")
    @TableLogic
    private Integer deleted;

    /** 扣回传是按照比例还是按照批次,false关闭则为比例true开启则为批次 */
    private Boolean batchConversionEnable;

    /** 扣回传每批次订单数 */
    private Integer batchNum;

    /** 扣回传每批次的回传的个数 */
    private Integer batchConversionNum;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("type", getType())
                .append("businessId", getBusinessId())
                .append("goodsPlatformType", getGoodsPlatformType())
                .append("mediaPlatformType", getMediaPlatformType())
                .append("enableConversion", getEnableConversion())
                .append("conversionProportion", getConversionProportion())
                .append("conversionStartOrderCount", getConversionStartOrderCount())
                .append("enableAmount", getEnableAmount())
                .append("amountProportion", getAmountProportion())
                .append("deductAreaEnable", getDeductAreaEnable())
                .append("deductAreaMin", getDeductAreaMin())
                .append("deductAreaMax", getDeductAreaMax())
                .append("deductOutsideAreaEnable", getDeductOutsideAreaEnable())
                .append("newBid", getNewBid())
                .append("bid", getBid())
                .append("conversionDayOrHour", getConversionDayOrHour())
                .append("delayEnable", getDelayEnable())
                .append("delaySecond", getDelaySecond())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleted", getDeleted())
                .append("remark", getRemark())
                .toString();
    }
}
