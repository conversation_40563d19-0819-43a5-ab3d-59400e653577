package com.panda.pollen.system.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 我的消息对象 sys_notice_receiver
 * 
 * <AUTHOR>
 * @date 2023-05-27
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName(value = "sys_notice_receiver")
public class SysNoticeReceiver extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 消息ID */
    @Excel(name = "消息ID")
    private Long noticeId;
    
    /** 接收者 */
    @Excel(name = "接收者")
    private Long receiver;
    
    /** 状态（0-未读、1-已读） */
    @Excel(name = "状态", readConverterExp = "0-未读、1-已读")
    private Integer status;
    
    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiverTime;
    
    /** 阅读时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date readTime;
    
    /** 删除状态(1-删除，0-未删除) */
    @TableLogic
    private Integer deleted;
    

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("noticeId", getNoticeId())
            .append("receiver", getReceiver())
            .append("status", getStatus())
            .append("receiverTime", getReceiverTime())
            .append("readTime", getReadTime())
            .append("deleted", getDeleted())
            .toString();
    }
}
