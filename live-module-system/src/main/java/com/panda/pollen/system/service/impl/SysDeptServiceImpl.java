package com.panda.pollen.system.service.impl;

import cn.hutool.core.util.IdUtil;
import com.panda.pollen.common.annotation.DataScope;
import com.panda.pollen.common.constant.UserConstants;
import com.panda.pollen.common.core.caffeine.LocalCache1;
import com.panda.pollen.common.core.domain.TreeSelect;
import com.panda.pollen.common.core.domain.dto.SysDeptDTO;
import com.panda.pollen.common.core.domain.entity.SysDept;
import com.panda.pollen.common.core.domain.entity.SysRole;
import com.panda.pollen.common.core.domain.entity.SysUser;
import com.panda.pollen.common.core.domain.model.LoginUser;
import com.panda.pollen.common.core.redis.RedisCache;
import com.panda.pollen.common.core.service.ISysConfigService;
import com.panda.pollen.common.core.text.Convert;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.common.utils.StringUtils;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.common.utils.spring.SpringUtils;
import com.panda.pollen.modules.ads.service.SpecialConfigService;
import com.panda.pollen.system.mapper.SysDeptMapper;
import com.panda.pollen.system.mapper.SysRoleMapper;
import com.panda.pollen.system.service.ISysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.panda.pollen.common.constant.CacheConstants.COMBO_DEPT_TREE;
import static com.panda.pollen.common.constant.CacheConstants.COMBO_PERSONAL_TREE;
import static com.panda.pollen.common.utils.SecurityUtils.getLoginUser;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysDeptServiceImpl implements ISysDeptService {

    //定义一个常量来比较是否等于0级目录
    private static Long ZERO = 0L;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SpecialConfigService specialConfigService;


    @Autowired
    private RedisCache redisCache;

    @Autowired
    private LocalCache1 localCache1;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept) {
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 查询部门树结构信息
     *
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    @Override
    public List<TreeSelect> selectDeptTreeList(SysDept dept) {
        List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
        return buildDeptTreeSelect(depts);
    }

    /**
     * @param dept
     * @Description 从缓存中获取部门树结构
     * <AUTHOR>
     * @param:
     * @Date : 2024/8/1 15:34
     * @return: java.util.List<com.panda.pollen.common.core.domain.TreeSelect>
     */
    @Override
    public List<TreeSelect> getDeptTree(SysDept dept) {
        // 查询部门树结构信息 构建key
        String key = COMBO_DEPT_TREE + getLoginUser().getUser().getUserId();
        return localCache1.getCacheObject(key, () -> selectDeptTreeList(dept));
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts) {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        for (SysDept dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDept selectDeptById(Long deptId) {
        SysDept sysDept = deptMapper.selectDeptById(deptId);
        if (StringUtils.isNotNull(sysDept)) {
            String duoduoId = specialConfigService.duoDuoDeptGet(deptId);
            Map<String, Object> params = new HashMap<>();
            params.put("duoduo", duoduoId);
            sysDept.setParams(params);
        }
        return sysDept;
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId) {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId) {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId) {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public boolean checkDeptNameUnique(SysDept dept) {
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(Long deptId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysDept dept = new SysDept();
            dept.setDeptId(deptId);
            List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (StringUtils.isEmpty(depts)) {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public Long insertDept(SysDept dept) {
        //删除部门树结构信息缓存信息
        String key = COMBO_DEPT_TREE + getLoginUser().getUser().getUserId();
        redisCache.deleteObject(key);
        //删除个人树结构信息缓存信息
        key = COMBO_PERSONAL_TREE + getLoginUser().getUser().getUserId();
        redisCache.deleteObject(key);
        SysDept info = deptMapper.selectDeptById(dept.getParentId());

        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new ServiceException("部门停用，不允许新增");
        }
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());

        //属于一级部门时
        if (ZERO.equals(info.getParentId())) {
            //判断商务人是否为空
            if (StringUtils.isBlank(dept.getCreateBy())) {
                //商务为空，取顶级部门的创建人
                dept.setCreateBy(info.getCreateBy());
            }
            //缓存群峰认证
            specialConfigService.setMountainPeaks(dept);
            dept.setFirstDeptName(dept.getDeptName());
            dept.setDeptId(IdUtil.getSnowflakeNextId());
            deptMapper.insertDept(dept);

            //获取到新增的数据的部门id
            Long deptId = dept.getDeptId();
            //设置新的一级部门ID
            deptMapper.updateFirstDeptId(deptId);

        } else {
            dept.setCreateBy(getLoginUser().getUsername());
            //属于二级部门及以上时
            dept.setFirstDeptId(info.getFirstDeptId());
            dept.setFirstDeptName(info.getFirstDeptName());
//            dept.setDeptId(IdWorker.getId());
            dept.setDeptId(IdUtil.getSnowflakeNextId());
            deptMapper.insertDept(dept);
        }
        return dept.getDeptId();
    }

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int updateDept(SysDept dept) {
        //删除部门树结构信息缓存信息
        String key = COMBO_DEPT_TREE + getLoginUser().getUser().getUserId();
        redisCache.deleteObject(key);

        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        //判断创建者是否为脱敏字段
        if (ObjectUtils.isNotEmpty(dept.getCreateBy())) {
            if (dept.getCreateBy().contains("*")) {
                dept.setCreateBy(null);
            }
        }
        //定义一个常量来比较是否等于0级目录
        final Long zero = 0L;
        //获取当前登录用户 用户权限
        List<SysRole> roles = getLoginUser().getUser().getRoles();
        String[] array = roles.stream().map(SysRole::getRoleKey).toArray(String[]::new);
        //顶级部门
        if (zero.equals(dept.getParentId())) {
            //权限验证 只有超级管理员还有商务可以修改部门名称
            permissionVerification(dept, array, oldDept);
        } else if (zero.equals(newParentDept.getParentId())) {
            //权限验证 只有超级管理员还有商务可以修改部门名称
            permissionVerification(dept, array, oldDept);
            //缓存群峰认证
            specialConfigService.setMountainPeaks(dept);
        } else {
            //属于二级部门及以下时
            dept.setFirstDeptId(newParentDept.getFirstDeptId());
            dept.setFirstDeptName(newParentDept.getFirstDeptName());
        }
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept, newAncestors, oldAncestors);
        }
        int result = deptMapper.updateDept(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    private void permissionVerification(SysDept dept, String[] array, SysDept oldDept) {
        if ((Arrays.asList(array).contains("admin") || Arrays.asList(array).contains("business"))) {
            dept.setFirstDeptName(dept.getDeptName());
            dept.setFirstDeptId(dept.getDeptId());
        } else {
            dept.setFirstDeptName(oldDept.getFirstDeptName());
            dept.setDeptName(oldDept.getDeptName());
            dept.setFirstDeptId(oldDept.getFirstDeptId());
        }
    }


    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        deptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     *
     * @param dept         被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(SysDept dept, String newAncestors, String oldAncestors) {
        List<SysDept> children = deptMapper.selectChildrenDeptById(dept.getDeptId());
        for (SysDept child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
            child.setFirstDeptId(dept.getFirstDeptId());
            child.setFirstDeptName(dept.getFirstDeptName());
        }
        if (children.size() > 0) {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(Long deptId) {
        return deptMapper.deleteDeptById(deptId);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t) {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext()) {
            SysDept n = (SysDept) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t) {
        return getChildList(list, t).size() > 0;
    }


    /**
     * @param user 用户信息
     * @Description 个人部门树列表
     * <AUTHOR>
     * @Date : 2024/8/19 17:29
     * @return: java.util.List<com.panda.pollen.common.core.domain.TreeSelect>
     */
    @Override
    public List<TreeSelect> personalTree(LoginUser user) {
        //获取一级部门ID
        Long firstDeptId = user.getUser().getDept().getFirstDeptId();
        // 查询部门树结构信息 构建key
        String key = COMBO_PERSONAL_TREE + getLoginUser().getUser().getUserId();
        return redisCache.getCacheOrUpdate(key, 2, TimeUnit.MINUTES, () -> getPersonalTree(firstDeptId));
    }


    private List<TreeSelect> getPersonalTree(Long firstDeptId) {
        List<SysDept> depts = deptMapper.selectDeptPersonalTree(firstDeptId);
        return buildDeptTreeSelect(depts);
    }


    /**
     * @param dto 部门信息
     * @Description 部门转移
     * <AUTHOR>
     * @Date : 2024/8/19 17:29
     * @return: void
     */
    @Override
    public void deptTransfer(SysDeptDTO dto) {
        //查询需要更新子节点部门信息
        List<SysDept> children = deptMapper.selectChildrenDeptById(dto.getTransfereeId());
        if (ListUtils.isEmpty(children)) {
            return;
        }
        //接受转移信息的人的部门信息
        SysDept sysDept = selectDeptById(dto.getTransferorId());
        for (SysDept child : children) {
            if (!ObjectUtils.equals(child.getDeptId(), child.getFirstDeptId())) {
                continue;
            }
            //新的祖级列表
            String newAncestors = sysDept.getAncestors() + "," + sysDept.getDeptId();
            //旧的祖级列表
            String oldAncestors = child.getAncestors();
            child.setCreateBy(sysDept.getCreateBy());
            child.setAncestors(newAncestors);
            child.setParentId(sysDept.getDeptId());
            updateDept(child);
            //修改
            updateDeptChildren(child, newAncestors, oldAncestors);
        }
    }

    /**
     * @Description 获取顶级部门ID
     * <AUTHOR>
     * @Date : 2024/8/19 17:30
     * @return: java.util.List<com.panda.pollen.common.core.domain.entity.SysDept>
     */
    @Override
    public List<SysDept> getTopDepartmentId() {
        return deptMapper.getTopDepartmentId();
    }


    @Override
    public List<Long> selectSubDeptIdList(Long deptId) {
        return deptMapper.selectSubDeptIdList(deptId);
    }
}
