
/**  
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：SysUserDistributionVO.java <br>
 * Package：com.panda.pollen.system.domain.vo <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2023年6月5日 下午11:16:46 <br>
 * @version v1.0 <br>
 */ 
package com.panda.pollen.system.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.panda.pollen.common.desensitize.EmailDesensitize;
import com.panda.pollen.common.desensitize.PhoneDesensitize;
import com.panda.pollen.common.encrypt.annotation.EncryptField;
import lombok.Data;

/**   
 * ClassName：com.panda.pollen.system.domain.vo.SysUserDistributionVO <br>
 * Description：用户VO对象 <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2023年6月5日 下午11:16:46 <br>
 * @version v1.0 <br>  
 */
@Data
public class SysUserVO {

    private Long userId;

    /** 部门ID */
    private Long deptId;

    /** 用户账号 */
    @JsonSerialize(using = PhoneDesensitize.class)
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 用户邮箱 */
    @JsonSerialize(using = EmailDesensitize.class)
    private String email;

    /** 手机号码 */
    @JsonSerialize(using = PhoneDesensitize.class)
    @EncryptField()
    private String phonenumber;

    /** 用户性别 */
    private String sex;

}
