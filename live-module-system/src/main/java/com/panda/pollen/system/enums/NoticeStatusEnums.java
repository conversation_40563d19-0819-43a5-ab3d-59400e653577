
/**  
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：NoticeStatusEnums.java <br>
 * Package：com.panda.pollen.system.enums <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2023年5月28日 下午7:30:25 <br>
 * @version v1.0 <br>
 */ 
package com.panda.pollen.system.enums;


/**   
 * ClassName：com.panda.pollen.system.enums.NoticeStatusEnums <br>
 * Description：公告状态枚举对象 <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2023年5月28日 下午7:30:25 <br>
 * @version v1.0 <br>  
 */
public enum NoticeStatusEnums {
    UNREAD, READ;
}
