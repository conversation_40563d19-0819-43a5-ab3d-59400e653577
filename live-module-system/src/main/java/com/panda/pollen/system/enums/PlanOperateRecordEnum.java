package com.panda.pollen.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName:com.ruoyi.modules.ads.domain.enums
 * Description: 替换记录枚举类
 *
 * <AUTHOR> <br>
 * date 2023年 07月11日 13:48 <br>
 * @version v1.0 <br>
 */
@Getter
@AllArgsConstructor
public enum PlanOperateRecordEnum {

    /*暂停计划*/
    PAUSE(1, "1"),
    /*替换链接*/
    REPLACE(2, "2"),
    /*监控链接*/
    SUPERVISORY_CONTROL(3, "3"),
    /*更新出价*/
    UPDATE_BID(4, "4"),
    /*修改回传*/
    MODIFIED_BACKPASS(5, "5"),
    /*修改锚点*/
    MODIFIED_ANCHOR(6, "6"),

    //修改成功日志状态
    MODIFIED_SUCCESSFULLY(0, "0"),
    //修改失败日志状态
    MODIFICATION_FAILURE(1, "1"),

    //记录成功状态标识
    RECORD_SUCCESS(1, "1"),
    //记录失败状态标识
    RECORD_FAILURE(0, "0");

    /**
     * key
     */
    private Integer key;
    /**
     * value
     */
    private String value;
}
