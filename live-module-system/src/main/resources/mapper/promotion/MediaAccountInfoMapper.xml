<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.MediaAccountInfoMapper">

    <resultMap type="com.panda.pollen.modules.ads.domain.MediaAccountInfo" id="MediaAccountInfoResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="mediaType" column="media_type"/>
        <result property="mediaUserId" column="media_user_id"/>
        <result property="accountRole" column="account_role"/>
        <result property="advertiserId" column="advertiser_id"/>
        <result property="advertiserName" column="advertiser_name"/>
        <result property="parentAccountId" column="parent_account_id"/>
        <result property="isValid" column="is_valid"/>
        <result property="controlStatus" column="control_status"/>
        <result property="evaluateStartTime" column="evaluate_start_time"/>
        <result property="accessToken" column="access_token"/>
        <result property="expiresIn" column="expires_in"/>
        <result property="refreshToken" column="refresh_token"/>
        <result property="refreshTokenExpiresIn" column="refresh_token_expires_in"/>
        <result property="commander" column="commander"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="reportStatisticsErrorMsg" column="report_statistics_error_msg"/>
        <result property="tokenRefreshErrorMsg" column="token_refresh_error_msg"/>
        <result property="isEnableConversion" column="is_enable_conversion"/>
        <result property="conversionProportion" column="conversion_proportion"/>
        <result property="fund" column="fund"/>
        <result property="fundUpdateTime" column="fund_update_time"/>
        <result property="enableAmount" column="enable_amount"/>
        <result property="conversionAmountType" column="conversion_amount_type"/>
        <result property="amountProportion" column="amount_proportion"/>
        <result property="conversionAmount" column="conversion_amount"/>
        <result property="firstDeptName" column="first_dept_name"/>
        <result property="firstDeptId" column="first_dept_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="emotionType" column="emotion_type"/>
        <result property="remark" column="remark"/>
        <result property="kilobarThreshold" column="kilobar_threshold"/>
        <result property="deductAreaEnable" column="deduct_area_enable"/>
        <result property="deductAreaMin" column="deduct_area_min"/>
        <result property="deductAreaMax" column="deduct_area_max"/>
        <result property="deductOutsideAreaEnable" column="deduct_outside_area_enable"/>
        <result property="conversionStartOrderCount" column="conversion_start_order_count"/>
        <result property="firstIndustryName" column="first_industry_name"/>
        <result property="secondIndustryName" column="second_industry_name"/>
        <result property="bizToken" column="biz_token"/>
        <result property="newBid" column="new_bid"/>
        <result property="bid" column="bid"/>
        <result property="conversionDayOrHour" column="conversion_day_or_hour"/>
        <result property="appKey" column="app_key"/>
        <result property="appSecret" column="app_secret"/>
        <result property="conversionDeductionCsite" column="conversion_deduction_csite"/>
    </resultMap>

    <sql id="selectMediaAccountInfoVo">
        select *
        from ads_media_account_info
    </sql>

    <sql id="listMediaAccountInfoVo">
        select m.id,
               m.dept_id,
               m.media_type,
               m.advertiser_id,
               m.advertiser_name,
               m.parent_account_id,
               m.is_valid,
               m.control_status,
               m.commander,
               m.create_by,
               m.create_time,
               m.update_by,
               m.update_time,
               m.evaluate_start_time,
               m.report_statistics_error_msg,
               m.token_refresh_error_msg,
               m.is_enable_conversion,
               m.conversion_proportion,
               m.enable_amount,
               m.conversion_amount_type,
               m.amount_proportion,
               m.conversion_amount,
               m.fund,
               m.fund_update_time,
               m.deduct_area_enable,
               m.deduct_area_min,
               m.deduct_area_max,
               m.deduct_outside_area_enable,
               m.conversion_start_order_count,
               m.uc_username,
               m.uc_password,
               m.uc_token,
               m.principal_tag,
               m.biz_token,
               m.new_bid,
               m.bid,
               m.conversion_day_or_hour,
               app_key,
               app_secret,
               m.conversion_deduction_csite
        from ads_media_account_info m
    </sql>

    <select id="selectMediaAccountInfoList" parameterType="com.panda.pollen.modules.ads.domain.MediaAccountInfo" resultMap="MediaAccountInfoResult">
        select m.id, m.dept_id, m.media_type,
        m.advertiser_id,m.parent_account_id,m.expires_in,m.refresh_token_expires_in,
        m.advertiser_name, m.is_valid, m.control_status, m.commander, d.dept_name,
        u.nick_name ,m.create_by, m.create_time, m.update_by, m.update_time,
        m.report_statistics_error_msg, m.token_refresh_error_msg,
        m.is_enable_conversion,m.conversion_proportion,m.enable_amount,m.conversion_amount_type,m.amount_proportion,m.conversion_amount,m.fund,m.fund_update_time,d.first_dept_name,m.emotion_type,dept.create_by
        as business,
        m.remark,m.kilobar_threshold,m.deduct_area_enable,m.deduct_area_min,m.deduct_area_max,m.deduct_outside_area_enable,m.fund_threshold_value,m.profit,m.conversion_start_order_count,m.profit_roi,m.profit_back_rate,
        m.new_bid,m.bid,m.conversion_day_or_hour,m.app_key,m.app_secret,m.conversion_deduction_csite
        from ads_media_account_info m
        left join sys_user u on u.user_name = m.create_by and u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        LEFT JOIN sys_dept dept ON dept.dept_id = d.first_dept_id AND d.del_flag = '0'
        <where>
            and m.deleted = 0
            <if test="mediaType != null and mediaType > 0">and m.media_type = #{mediaType}</if>
            <if test="remark != null and remark != ''">and m.remark like concat('%', #{remark}, '%')</if>
            <if test="advertiserId != null  and advertiserId != ''">and m.advertiser_id = #{advertiserId}</if>
            <if test="params.advertiserIds != null and params.advertiserIds.size()>0">
                and m.advertiser_id  in
                <foreach collection="params.advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="advertiserName != null  and advertiserName != ''">and m.advertiser_name like concat('%',
                #{advertiserName}, '%')
            </if>
            <if test="business != null  and business != ''">and dept.create_by =#{business}</if>
            <if test="params.parentAccountId == 1">and m.parent_account_id is not null</if>
            <if test="(searchValue == null or searchValue == '0' or searchValue == '') and (parentAccountId == null or parentAccountId == '')">
                and m.parent_account_id is null
            </if>
            <if test="parentAccountId != null and parentAccountId != ''">
                and m.parent_account_id = #{parentAccountId}
            </if>
            <if test="isValid != null ">and m.is_valid = #{isValid}</if>
            <if test="controlStatus != null  and controlStatus != ''">and m.control_status = #{controlStatus}</if>
            <if test="commander != null  and commander != ''">and m.commander = #{commander}</if>
            <if test="reportStatisticsErrorMsg != null">and m.report_statistics_error_msg = #{reportStatisticsErrorMsg}
            </if>
            <if test="tokenRefreshErrorMsg != null">and m.token_refresh_error_msg = #{tokenRefreshErrorMsg}</if>
            <if test="deptIds != null">
                AND u.dept_id IN
                <foreach collection="deptIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="createBy != null and createBy != ''">and m.create_by = #{createBy}</if>
            <if test="isEnableConversion != null">
                and m.is_enable_conversion=#{isEnableConversion}
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        group by m.id
        order by m.id desc
    </select>

    <select id="selectMediaAccountList" parameterType="com.panda.pollen.modules.ads.dto.MediaAccountDTO"
            resultMap="MediaAccountInfoResult">
        select m.id, m.dept_id, m.media_type, m.advertiser_id, m.advertiser_name, m.parent_account_id,
        m.access_token,m.media_user_id,
        m.is_valid, m.control_status, m.commander, m.create_by, m.create_time, m.update_by, m.update_time,
        m.evaluate_start_time,
        m.report_statistics_error_msg,
        m.token_refresh_error_msg,m.is_enable_conversion,m.conversion_proportion,m.enable_amount,m.amount_proportion,
        m.conversion_amount,m.conversion_amount_type,m.expires_in,
        m.refresh_token_expires_in,m.emotion_type,m.conversion_start_order_count,m.new_bid,m.bid,m.conversion_day_or_hour,m.conversion_deduction_csite
        from ads_media_account_info m
        <if test="cost!=null or fund!=null or negativeFund!=null ">
            left join
            ads_advert_statistics s on s.advertiser_id = m.advertiser_id
            AND   m.media_type = s.media_type
            <if test="statDatetime!=null">and s.stat_datetime=#{statDatetime}</if>
        </if>
        <if test="firstDeptIds!=null">
            left join sys_user u on u.user_name = m.create_by and u.del_flag = '0'
            LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        </if>
        <where>
            m.deleted = 0
            <if test="mediaType != null and mediaType > 0">and m.media_type = #{mediaType}</if>
            <if test="advertiserId != null  and advertiserId != ''">and m.advertiser_id = #{advertiserId}</if>
            <if test="parentAccountId != null and parentAccountId != ''">and m.parent_account_id = #{parentAccountId}
            </if>
            <if test="controlStatus != null  and controlStatus != ''">and m.control_status = #{controlStatus}</if>
            <if test="userNames != null and userNames.size() > 0">
                and m.create_by in
                <foreach item="create_by" collection="userNames" open="(" separator="," close=")">
                    #{create_by}
                </foreach>
            </if>
            <if test="cost!=null">and s.cost > #{cost}</if>
            <if test="parentNode!=null">and m.parent_account_id is not null</if>
            <if test="aids != null and aids.size() > 0">
                and m.advertiser_id in
                <foreach item="aid" collection="aids" open="(" separator="," close=")">
                    #{aid}
                </foreach>
            </if>
            <if test="fund!=null ">
                and (m.fund &gt;= #{fund}
                and m.parent_account_id is not null or s.advertiser_id is not null)
            </if>
            <if test="negativeFund!=null">
                and ((m.fund &lt;= #{negativeFund} or m.fund is null)
                and m.parent_account_id is not null or s.advertiser_id is not null)
            </if>
            <if test="firstDeptIds!=null">
                and d.first_dept_id in
                <foreach item="first_dept_id" collection="firstDeptIds" open="(" separator="," close=")">
                    #{first_dept_id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectMediaAccountForCombo" parameterType="com.panda.pollen.modules.ads.vo.MediaAccountComboVO"
            resultMap="MediaAccountInfoResult">
        select m.id, m.advertiser_id, m.advertiser_name from ads_media_account_info m
        left join sys_user u on u.user_name = m.create_by and u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        <where>
            and deleted = 0 and parent_account_id is not null
            <if test="mediaType != null and mediaType > 0">and media_type = #{mediaType}</if>
            <if test="keyWords != null  and keyWords != ''">and (m.advertiser_id = #{keyWords} or m.advertiser_name like
                concat('%', #{keyWords}, '%'))
            </if>
            <if test="advertiserIds!=null">
                and m.advertiser_id in
                <foreach item="advertiser_id" collection="advertiserIds" open="(" separator="," close=")">
                    #{advertiser_id}
                </foreach>
            </if>
            ${params.dataScope}
        </where>
        order by m.id desc
        <if test="size!=null">limit #{size}</if>
        <if test="size==null">limit 1000</if>
    </select>

    <select id="selectMasterMediaAccountInfoList" resultMap="MediaAccountInfoResult">
        <include refid="selectMediaAccountInfoVo"/>
        <where>
            parent_account_id is null and deleted = 0
            <if test="mediaType != null">
                and media_type in
                <foreach item="mt" collection="mediaType" open="(" separator="," close=")">
                    #{mt}
                </foreach>
            </if>
            <if test="id!=null">
                and id>#{id}
            </if>

        </where>
        <if test="id!=null">
            order by id
            limit 100
        </if>
    </select>

    <select id="selectMediaAccountInfoById" parameterType="Long" resultMap="MediaAccountInfoResult">
        <include refid="selectMediaAccountInfoVo"/>
        where id = #{id} and deleted = 0
    </select>

    <select id="selectMediaAccountInfoByAdvertiserId" resultMap="MediaAccountInfoResult">
        <include refid="selectMediaAccountInfoVo"/>
        where
        advertiser_id = #{advertiserId} and deleted = 0
        <if test="mediaType != null and mediaType != 0">and media_type = #{mediaType}</if>
        LIMIT 1
    </select>
    <select id="selectMediaAccountInfoListChild" resultMap="MediaAccountInfoResult">
        select * from ads_media_account_info t where t.parent_account_id is not null and deleted = 0
        <if test="type != null">and t.media_type=#{type}</if>
    </select>

    <insert id="insertMediaAccountInfo" parameterType="MediaAccountInfo" useGeneratedKeys="true" keyProperty="id">
        insert into ads_media_account_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="mediaType != null">media_type,</if>
            <if test="mediaUserId != null">media_user_id,</if>
            <if test="accountRole !=null and accountRole != ''">account_role,</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id,</if>
            <if test="advertiserName != null and advertiserName != ''">advertiser_name,</if>
            <if test="parentAccountId != null">parent_account_id,</if>
            <if test="isValid != null">is_valid,</if>
            <if test="controlStatus != null">control_status,</if>
            <if test="accessToken != null">access_token,</if>
            <if test="expiresIn != null">expires_in,</if>
            <if test="refreshToken != null">refresh_token,</if>
            <if test="refreshTokenExpiresIn != null">refresh_token_expires_in,</if>
            <if test="commander != null">commander,</if>
            <if test="isEnableConversion != null">is_enable_conversion,</if>
            <if test="conversionProportion != null">conversion_proportion,</if>
            <if test="enableAmount != null">enable_amount,</if>
            <if test="conversionAmountType != null">conversion_amount_type,</if>
            <if test="amountProportion != null">amount_proportion,</if>
            <if test="conversionAmount != null">conversion_amount,</if>
            <if test="fund != null">fund,</if>
            <if test="fundUpdateTime != null">fund_update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="conversionStartOrderCount != null">conversion_start_order_count,</if>
            <if test="firstIndustryName != null">first_industry_name,</if>
            <if test="secondIndustryName != null">second_industry_name,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="mediaType != null">#{mediaType},</if>
            <if test="mediaUserId != null">#{mediaUserId},</if>
            <if test="accountRole !=null and accountRole != ''">#{accountRole},</if>
            <if test="advertiserId != null and advertiserId != ''">#{advertiserId},</if>
            <if test="advertiserName != null and advertiserName != ''">#{advertiserName},</if>
            <if test="parentAccountId != null">#{parentAccountId},</if>
            <if test="isValid != null">#{isValid},</if>
            <if test="controlStatus != null">#{controlStatus},</if>
            <if test="accessToken != null">#{accessToken},</if>
            <if test="expiresIn != null">#{expiresIn},</if>
            <if test="refreshToken != null">#{refreshToken},</if>
            <if test="refreshTokenExpiresIn != null">#{refreshTokenExpiresIn},</if>
            <if test="commander != null">#{commander},</if>
            <if test="isEnableConversion != null">#{isEnableConversion},</if>
            <if test="conversionProportion != null">#{conversionProportion},</if>
            <if test="enableAmount != null">#{enableAmount},</if>
            <if test="conversionAmountType != null">#{conversionAmountType}</if>
            <if test="amountProportion != null">#{amountProportion},</if>
            <if test="conversionAmount != null">#{conversionAmount},</if>
            <if test="fund != null">#{fund},</if>
            <if test="fundUpdateTime != null">#{fundUpdateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="conversionStartOrderCount != null">#{conversionStartOrderCount},</if>
            <if test="firstIndustryName != null">#{firstIndustryName},</if>
            <if test="secondIndustryName != null">#{secondIndustryName},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateMediaAccountInfo" parameterType="MediaAccountInfo">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="mediaUserId != null">media_user_id = #{mediaUserId},</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
            <if test="advertiserName != null and advertiserName != ''">advertiser_name = #{advertiserName},</if>
            <if test="parentAccountId != null">parent_account_id = #{parentAccountId},</if>
            <if test="isValid != null">is_valid = #{isValid},</if>
            <if test="controlStatus != null">control_status = #{controlStatus},</if>
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="expiresIn != null">expires_in = #{expiresIn},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="refreshTokenExpiresIn != null">refresh_token_expires_in = #{refreshTokenExpiresIn},</if>
            <if test="commander != null">commander = #{commander},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isEnableConversion != null">is_enable_conversion = #{isEnableConversion},</if>
            <if test="conversionProportion != null">conversion_proportion = #{conversionProportion},</if>
            <if test="enableAmount != null">enable_amount = #{enableAmount},</if>
            <if test="conversionAmountType != null">conversion_amount_type = #{conversionAmountType},</if>
            <if test="amountProportion != null">amount_proportion = #{amountProportion},</if>
            <if test="conversionAmount != null">conversion_amount = #{conversionAmount},</if>
            <if test="fund != null">fund = #{fund},</if>
            <if test="fundUpdateTime != null">fund_update_time = #{fundUpdateTime},</if>
            <if test="newBid != null">new_bid = #{newBid},</if>
            <if test="bid != null">bid = #{bid},</if>
            <if test="conversionDayOrHour != null">conversion_day_or_hour = #{conversionDayOrHour},</if>
            <if test="conversionStartOrderCount != null">conversion_start_order_count = #{conversionStartOrderCount},
            </if>
            <if test="deductAreaEnable != null">deduct_area_enable = #{deductAreaEnable},</if>
            <if test="deductAreaMin != null">deduct_area_min = #{deductAreaMin},</if>
            <if test="deductAreaMax != null">deduct_area_max = #{deductAreaMax},</if>
            <if test="deductOutsideAreaEnable != null">deduct_outside_area_enable = #{deductOutsideAreaEnable},</if>
            <if test="conversionDeductionCsite != null">conversion_deduction_csite = #{conversionDeductionCsite},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="updateMediaAccountInfoTokenByAdvertiserId"
            parameterType="com.panda.pollen.common.core.domain.model.OceanAuthorize">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="mediaUserId != null">media_user_id = #{mediaUserId},</if>
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="expiresIn != null">expires_in = #{expiresIn},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="refreshTokenExpiresIn != null">refresh_token_expires_in = #{refreshTokenExpiresIn},</if>
            report_statistics_error_msg = #{reportStatisticsErrorMsg},
            token_refresh_error_msg = #{tokenRefreshErrorMsg},
            update_time = sysdate()
        </trim>
        where
        deleted = 0 and (
        <if test="advertiserId != null">
            advertiser_id = #{advertiserId} or parent_account_id = #{advertiserId} or
        </if>
        media_user_id = #{mediaUserId}
        )
    </update>

    <update id="updateTokenWithChild" parameterType="MediaAccountInfo">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="expiresIn != null">expires_in = #{expiresIn},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="refreshTokenExpiresIn != null">refresh_token_expires_in = #{refreshTokenExpiresIn},</if>
            <if test="mediaUserId != null">media_user_id = #{mediaUserId},</if>
            report_statistics_error_msg = null,
            token_refresh_error_msg = null,
            update_time = sysdate()
        </trim>
        where (advertiser_id = #{advertiserId}
        or parent_account_id = #{advertiserId}
        or advertiser_id like concat(#{advertiserId}, '--%')
        or parent_account_id like concat(#{advertiserId}, '--%'))
        and media_type = #{mediaType}
        and deleted = 0
    </update>

    <delete id="deleteMediaAccountInfoById" parameterType="String">
        <!--         delete from ads_media_account_info where id = #{id} -->
        update ads_media_account_info set deleted = 1 where advertiser_id = #{id} OR parent_account_id = #{id}
    </delete>

    <delete id="deleteMediaAccountInfoByIds" parameterType="String">
        <!-- delete from ads_media_account_info where id in  -->
        update ads_media_account_info set deleted = 1 where advertiser_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        or parent_account_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateMediaTokenOrReportState" parameterType="MediaAccountInfo">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="reportStatisticsErrorMsg != null">report_statistics_error_msg = #{reportStatisticsErrorMsg},</if>
            <if test="tokenRefreshErrorMsg != null">token_refresh_error_msg = #{tokenRefreshErrorMsg},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="changeControlStatus" parameterType="com.panda.pollen.modules.ads.dto.MediaAccountDTO">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="controlStatus != null and controlStatus != ''">control_status = #{controlStatus},</if>
            <if test="evaluateStartTime != null">evaluate_start_time = #{evaluateStartTime},</if>
        </trim>
        where
        <if test="id != null">
            id = #{id}
        </if>
        <if test="ids != null and ids.size() > 0">
            id IN
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectMediaAccountInfoListByIC" resultMap="MediaAccountInfoResult">
        <include refid="selectMediaAccountInfoVo"/>
        <where>
            is_enable_conversion = 1 and conversion_proportion > 100 and deleted = 0 and media_type=#{mediaType}
            and id > #{id}
        </where>
        order by id asc
        limit 100
    </select>

    <select id="getBriefInfoByAdvertiserId" resultType="com.panda.pollen.modules.ads.vo.MediaAccountInfoBriefVO">
        select mai.advertiser_id,
               mai.advertiser_name,
               mai.media_type,
               mai.create_by
        from ads_media_account_info mai
        where mai.advertiser_id = #{advertiserId}
          and mai.deleted = 0 LIMIT 1
    </select>

    <select id="selectMediaAccountInfoByAdvertiserIds"
            resultType="com.panda.pollen.modules.ads.vo.MediaAccountInfoListVO">
        select
        advertiser_id as aId,
        advertiser_name,
        first_industry_name,
        second_industry_name
        from
        ads_media_account_info
        where
        deleted=0
        AND
        advertiser_id IN
        <foreach collection="advertiserIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectMediaAccountFundList" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select advertiser_id,
               advertiser_name,
               fund,
               fund_update_time,
               create_by
        from ads_media_account_info
        where media_type = #{mediaType}
          and fund_update_time &gt;= #{fundUpdateTime}
    </select>

    <update id="updateMediaAccountFund" parameterType="MediaAccountInfo">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="fund != null">fund = #{fund},</if>
            <if test="fundUpdateTime != null">fund_update_time = #{fundUpdateTime},</if>
            report_statistics_error_msg = null,
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="updateMediaAccountIndustry" parameterType="MediaAccountInfo">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="firstIndustryName != null">first_industry_name = #{firstIndustryName},</if>
            <if test="secondIndustryName != null">second_industry_name = #{secondIndustryName},</if>
            <if test="reportStatisticsErrorMsg != null">report_statistics_error_msg = #{reportStatisticsErrorMsg},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <select id="selectMediaAccountInfoListChildWithFundPositive" resultMap="MediaAccountInfoResult">
        select t.*, d.first_dept_id from ads_media_account_info t
        left join sys_user u on u.user_name = t.create_by and u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        where t.parent_account_id is not null and t.deleted = 0
        and t.id &gt; #{id}
        <if test="dto.mediaType != null">and t.media_type=#{dto.mediaType}</if>
        <if test="dto.fund!=null ">
            and (t.fund >= #{dto.fund} OR t.create_time >= DATE_FORMAT(NOW() - INTERVAL 3 DAY,'%Y-%m-%d 00:00:00') OR t.fund is null)
        </if>
        <if test="dto.negativeFund!=null">
            and t.fund &lt;= #{dto.negativeFund} and t.create_time &lt; DATE_FORMAT(NOW() - INTERVAL 3 DAY,'%Y-%m-%d 00:00:00')
        </if>
        <if test="dto.filterLowActive">
            and t.fund_update_time > NOW() - INTERVAL 30 DAY
        </if>
        order by t.id
        limit #{size}
    </select>

    <select id="selectMediaAccountInfoListChildWithFundZero" resultMap="MediaAccountInfoResult">
        select * from ads_media_account_info t where t.parent_account_id is not null and deleted = 0 and (fund = 0 or
        fund is null)
        and id &gt; #{id}
        <if test="type != null">and t.media_type=#{type}</if>
        limit #{size}
    </select>

    <select id="pageSelectMediaAccountInfoListChild" resultMap="MediaAccountInfoResult">
        select * from ads_media_account_info t where t.parent_account_id is not null and deleted = 0
        and id &gt; #{id}
        <if test="type != null">and t.media_type=#{type}</if>
        limit #{size}
    </select>

    <select id="pageSelectMediaAccountInfoListChildWithNullIndustry" resultMap="MediaAccountInfoResult">
        select * from ads_media_account_info t where t.parent_account_id is not null and deleted = 0
        and id &gt; #{id}
        and first_industry_name is null
        and second_industry_name is null
        <if test="type != null">and t.media_type=#{type}</if>
        limit #{size}
    </select>

    <update id="updateMediaAccountAuthorizeInfo" parameterType="MediaAccountInfo">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="mediaUserId != null">media_user_id = #{mediaUserId},</if>
            <if test="advertiserId != null and advertiserId != ''">advertiser_id = #{advertiserId},</if>
            <if test="advertiserName != null and advertiserName != ''">advertiser_name = #{advertiserName},</if>
            <if test="parentAccountId != null">parent_account_id = #{parentAccountId},</if>
            <if test="isValid != null">is_valid = #{isValid},</if>
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="expiresIn != null">expires_in = #{expiresIn},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="refreshTokenExpiresIn != null">refresh_token_expires_in = #{refreshTokenExpiresIn},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="firstIndustryName != null">first_industry_name = #{firstIndustryName},</if>
            <if test="secondIndustryName != null">second_industry_name = #{secondIndustryName},</if>
            report_statistics_error_msg = null,
            token_refresh_error_msg = null,
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <select id="selectRefreshTokenByParentAccountId" resultType="java.lang.String">
        select distinct refresh_token
        from ads_media_account_info
        where (parent_account_id = #{parentAccountId} or advertiser_id = #{parentAccountId})
          and deleted = 0
    </select>

    <update id="updateTokenInfoByRefreshToken">
        update ads_media_account_info
        set
        access_token = #{accessToken},
        <if test="expiresIn != null">expires_in = #{expiresIn},</if>
        refresh_token = #{refreshToken},
        <if test="refreshTokenExpiresIn != null">refresh_token_expires_in = #{refreshTokenExpiresIn},</if>
        token_refresh_error_msg = null,
        update_time = sysdate()
        where refresh_token = #{oldRefreshToken}
        and deleted = 0
    </update>

    <update id="updateTokenErrorInfoByRefreshToken">
        update ads_media_account_info
        set token_refresh_error_msg = #{tokenRefreshErrorMsg},
            update_time             = sysdate()
        where refresh_token = #{oldRefreshToken}
          and deleted = 0
    </update>

    <update id="updateTokenByMediaUser">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="expiresIn != null">expires_in = #{expiresIn},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="refreshTokenExpiresIn != null">refresh_token_expires_in = #{refreshTokenExpiresIn},</if>
            report_statistics_error_msg = null,
            token_refresh_error_msg = null,
            update_time = sysdate()
        </trim>
        where media_user_id = #{userId}
        and deleted = 0
    </update>

    <select id="selectMediaAccountListByCursor" fetchSize="-**********" statementType="CALLABLE"
            resultSetType="FORWARD_ONLY" parameterType="com.panda.pollen.modules.ads.dto.MediaAccountDTO"
            resultMap="MediaAccountInfoResult">
        select m.id, m.dept_id, m.media_type, m.advertiser_id, m.advertiser_name, m.parent_account_id, m.access_token,
        m.is_valid, m.control_status, m.commander, m.create_by, m.create_time, m.update_by, m.update_time,
        m.evaluate_start_time,
        m.report_statistics_error_msg,
        m.token_refresh_error_msg,m.is_enable_conversion,m.conversion_proportion,m.enable_amount,m.amount_proportion,
        m.expires_in,
        m.refresh_token_expires_in,m.emotion_type,m.conversion_start_order_count,m.new_bid,m.bid,m.conversion_day_or_hour,m.conversion_deduction_csite
        from ads_media_account_info m
        <where>
            and m.deleted = 0
            <if test="mediaType != null and mediaType > 0">and media_type = #{mediaType}</if>
            <if test="advertiserId != null  and advertiserId != ''">and advertiser_id = #{advertiserId}</if>
            <if test="parentAccountId != null and parentAccountId != ''">and parent_account_id = #{parentAccountId}</if>
            <if test="controlStatus != null  and controlStatus != ''">and control_status = #{controlStatus}</if>
            <if test="userNames != null and userNames.size() > 0">
                and m.create_by in
                <foreach item="create_by" collection="userNames" open="(" separator="," close=")">
                    #{create_by}
                </foreach>
            </if>
        </where>
    </select>

    <select id="parentAccountList" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        SELECT
        advertiser_id,
        advertiser_name
        FROM
        ads_media_account_info m
        LEFT JOIN sys_user u ON u.user_name = m.create_by
        AND u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        AND d.del_flag = 0
        <where>
            m.deleted = 0
            AND m.parent_account_id is null
            <if test="mediaType!=null and mediaType>0">
                AND m.media_type = #{mediaType}
            </if>
            ${entity.params.dataScope}
        </where>
    </select>


    <select id="selectMediaAccountListByAdvertiserIdList" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select m.id, m.advertiser_id, m.advertiser_name from ads_media_account_info m
        where m.advertiser_id in
        <foreach collection="advertiserIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and m.media_type = #{mediaType}
        and m.deleted = 0
    </select>

    <select id="selectMediaIndustry" resultType="com.panda.pollen.modules.ads.vo.MediaIndustryVO">
        select first_industry_name                                       flcy,
               GROUP_CONCAT(distinct second_industry_name separator '|') slcy_arr
        from ads_media_account_info amai
        where first_industry_name is not null
        group by first_industry_name
    </select>

    <select id="selectOceanLsAdvSubscribe" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select
        t.id,t.advertiser_id,t.media_user_id
        from
        ads_media_account_info t
        inner join (select max(m.id) id from ads_media_account_info m where m.media_type = 1 GROUP BY m.advertiser_id)
        tt on t.id= tt.id
        where (t.media_user_id is not null or t.media_user_id != '')
        <if test="advType == 1">
            <if test="lsAdvSubscribe != null">AND t.ls_adv_subscribe = #{lsAdvSubscribe}</if>
        </if>
        <if test="advType == 2">
            <if test="lsAdvSubscribe != null">AND t.ls_adv_material_subscribe = #{lsAdvSubscribe}</if>
        </if>
        <if test="lsAdvSubscribe == 0">
            AND t.expires_in &gt; SYSDATE()
            AND t.deleted = 0
        </if>
        <if test="lsAdvSubscribe == 1">
            AND (t.expires_in &lt; SYSDATE() or t.deleted = 1)
        </if>
        AND (t.parent_account_id != '' or t.parent_account_id is not null)
    </select>

    <select id="selectOceanMaterialLsAdvSubscribe" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        SELECT max(t.id),
               t.advertiser_id,
               t.media_user_id
        FROM (SELECT a.advertiser_id
              FROM ads_advert_statistics a
                       LEFT JOIN ads_media_account_info m ON
                  a.advertiser_id = m.advertiser_id
            AND a.media_type = m.media_type
              WHERE a.stat_datetime = #{statDate}
                AND m.media_type = 1) ad
                 LEFT JOIN ads_media_account_info t ON t.advertiser_id = ad.advertiser_id
        WHERE (t.media_user_id IS NOT NULL OR t.media_user_id != '')
          AND t.ls_adv_material_subscribe = 0
        GROUP BY t.advertiser_id
    </select>

    <select id="selectOceanMaterialLsAdvSubscribe2" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        SELECT
        m.id,
        m.advertiser_id,
        m.media_user_id
        FROM
        ads_advert_statistics a
        LEFT JOIN ads_media_account_info m ON a.advertiser_id = m.advertiser_id
        AND a.media_type = m.media_type
        LEFT JOIN sys_user u ON u.user_name = m.create_by AND u.del_flag = 0
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id AND d.del_flag = 0
        WHERE
        d.first_dept_id IN
        <foreach collection="firstDeptIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND a.stat_datetime = #{statDate}
        AND m.media_type = 1
        AND m.deleted = 0
        AND ( m.media_user_id IS NOT NULL OR m.media_user_id != '' )
        AND m.ls_adv_material_subscribe = 0
    </select>

    <update id="batchUpdateOceanLsAdvSubscribeInfoByAdvertiserId">
        update ads_media_account_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="advType == 1">
                <if test="lsAdvSubscribe != null">ls_adv_subscribe = #{lsAdvSubscribe},</if>
            </if>
            <if test="advType == 2">
                <if test="lsAdvSubscribe != null">ls_adv_material_subscribe = #{lsAdvSubscribe},</if>
            </if>
            update_time = sysdate()
        </trim>
        where id in
        <foreach collection="idsList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectMediaAccountUserVo" resultType="com.panda.pollen.modules.domain.vo.UserVO">
        select d.dept_id,d.dept_name,u.user_id,u.user_name,u.nick_name,d.first_dept_id,d.first_dept_name
        from ads_media_account_info m
        left join sys_user u on u.user_name = m.create_by and u.del_flag = 0
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        <where>
            and m.deleted = 0
            <if test="mediaPlatformType != null and mediaPlatformType > 0">and media_type = #{mediaPlatformType}</if>
            <if test="mediaAccountId != null  and mediaAccountId != ''">and advertiser_id = #{mediaAccountId}</if>
        </where>
        order by m.id desc limit 1
    </select>

    <update id="batchUpdateMediaTokenOrReportState">
        <foreach collection="advertiserIdList" item="advertiserId" open="" close="" separator=";">
            update ads_media_account_info
            <set>
                report_statistics_error_msg = #{message},
                update_time = sysdate()
            </set>
            where advertiser_id = #{advertiserId}
        </foreach>
    </update>

    <update id="batchUpdateMediaAccountFund">
        <foreach collection="adFundResultInfos" item="info" index="index" open="" close="" separator=";">
            update ads_media_account_info
            <set>
                fund = #{info.balance},
                fund_update_time = #{fundUpdateTime},
                report_statistics_error_msg = null,
                update_time = sysdate()
            </set>
            where advertiser_id = #{info.advertiserId} and media_type = 1
        </foreach>
    </update>


    <select id="selectAccessTokenUnexpiredOne" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select advertiser_id, access_token from ads_media_account_info where media_type=#{mediaType} and parent_account_id is not null and expires_in > sysdate() limit 1
    </select>

    <select id="getMediaAccountInfo" resultType="com.panda.pollen.modules.ads.vo.MediaAccountInfoBriefVO">
        select mai.advertiser_id,
               mai.advertiser_name,
               mai.media_type,
               mai.access_token,
               mai.create_by
        from ads_media_account_info mai
        where mai.advertiser_id = #{advertisrId}
          and mai.media_type = #{mediaPlatformType}
          and mai.deleted = 0 LIMIT 1
    </select>

    <select id="selectMediaAccountInfo" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select mai.advertiser_id,
               mai.media_type
        from ads_media_account_info mai
    </select>
    <select id="getMediaAccountInfoByAdvertisrId" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select mai.advertiser_id,
        mai.advertiser_name,
        mai.media_type,
        mai.create_by
        from ads_media_account_info mai
        left join sys_user u on u.user_name = mai.create_by and u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        <where>
            mai.advertiser_id = #{advertiserId}
            and mai.media_type = #{mediaType}
            and mai.deleted = 0
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        LIMIT 1
    </select>

    <select id="findAdvertStatReport" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select m.id,m.advertiser_id, m.advertiser_name, m.parent_account_id,m.media_type,
        m.access_token,m.report_statistics_error_msg,m.expires_in
        from ads_media_account_info m
        left join
        ads_advert_statistics s on s.advertiser_id = m.advertiser_id
        AND   m.media_type = s.media_type
        AND s.stat_datetime=#{date}
        <where>
            m.deleted = 0
            and m.media_type = #{mediaPlatformType}
            and (m.fund &gt;= #{fund} and m.parent_account_id is not null or s.advertiser_id is not null)
        </where>
    </select>


    <select id="selectSetConversion" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select DISTINCT t.* from
        ads_media_account_info t
        left join ads_advert_statistics p on t.advertiser_id = p.advertiser_id
        where t.deleted = 0
        AND (t.enable_amount =1 or t.is_enable_conversion = 1 or t.conversion_deduction_csite = 1) and p.stat_datetime >= '2024-07-20'
    </select>


    <select id="selectMediaAccountInfoLists" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select m.id,m.advertiser_id, m.advertiser_name, m.parent_account_id,m.media_type,m.media_user_id,
        m.access_token,m.report_statistics_error_msg,m.expires_in,m.evaluate_start_time,m.emotion_type,m.fund,d.first_dept_id
        from ads_media_account_info m
        left join sys_user u on u.user_name = m.create_by and u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        <if test="dto.cost!=null or dto.fund!=null or dto.negativeFund!=null or dto.negativeCost!=null">
            left join
            ads_advert_statistics s on s.advertiser_id = m.advertiser_id
            AND   m.media_type = s.media_type
            <if test="dto.statDatetime!=null">and s.stat_datetime=#{dto.statDatetime}</if>
        </if>
        <where>
            m.deleted = 0
            <if test="dto.mediaType != null and dto.mediaType > 0">and m.media_type = #{dto.mediaType}</if>
            <if test="dto.advertiserId != null  and dto.advertiserId != ''">and m.advertiser_id = #{dto.advertiserId}</if>
            <if test="dto.parentAccountId != null and dto.parentAccountId != ''">and m.parent_account_id = #{dto.parentAccountId}
            </if>
            <if test="dto.controlStatus != null  and dto.controlStatus != ''">and m.control_status = #{dto.controlStatus}</if>
            <if test="dto.userNames != null and dto.userNames.size() > 0">
                and m.create_by in
                <foreach item="create_by" collection="dto.userNames" open="(" separator="," close=")">
                    #{create_by}
                </foreach>
            </if>
            <if test="dto.cost!=null">and s.cost > #{dto.cost}</if>
            <if test="dto.negativeCost!=null">and s.cost &lt;= #{dto.negativeCost}</if>
            <if test="dto.parentNode!=null">and m.parent_account_id is not null</if>
            <if test="dto.aids != null and dto.aids.size() > 0">
                and m.advertiser_id in
                <foreach item="aid" collection="dto.aids" open="(" separator="," close=")">
                    #{aid}
                </foreach>
            </if>
            <if test="dto.fund!=null ">
                and (m.fund &gt;= #{dto.fund} and m.parent_account_id is not null
                or s.advertiser_id is not null)
            </if>
            <if test="dto.negativeFund!=null">
                and ((m.fund &lt;= #{dto.negativeFund} or m.fund is null) and m.parent_account_id is not null
                or s.advertiser_id is not null)
            </if>
            <if test="dto.firstDeptIds!=null">
                and d.first_dept_id in
                <foreach item="first_dept_id" collection="dto.firstDeptIds" open="(" separator="," close=")">
                    #{first_dept_id}
                </foreach>
            </if>
            <if test="id!=null">
                and m.id > #{id}
            </if>
            <if test="id==null">
                and m.id > 0
            </if>
        </where>
        <if test="id!=null">
            order by m.id asc
            limit 100
        </if>
    </select>

    <select id="test" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select m.id,m.advertiser_id, m.advertiser_name, m.parent_account_id,m.media_type,
        m.access_token,m.report_statistics_error_msg,m.expires_in
        from ads_media_account_info m
        <if test="cost!=null or fund!=null or negativeFund!=null ">
            left join
            ads_advert_statistics s on s.advertiser_id = m.advertiser_id
            AND   m.media_type = s.media_type
            <if test="statDatetime!=null">and s.stat_datetime=#{dto.statDatetime}</if>
        </if>
        <if test="firstDeptIds!=null">
            left join sys_user u on u.user_name = m.create_by and u.del_flag = '0'
            LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        </if>
        <where>
            m.deleted = 0
            and m.id>=#{id}
            <if test="dto.mediaType != null and dto.mediaType > 0">and m.media_type = #{dto.mediaType}</if>
            <if test="dto.advertiserId != null  and dto.advertiserId != ''">and m.advertiser_id = #{dto.advertiserId}</if>
            <if test="dto.parentAccountId != null and dto.parentAccountId != ''">and m.parent_account_id = #{dto.parentAccountId}
            </if>
            <if test="dto.controlStatus != null  and dto.controlStatus != ''">and m.control_status = #{dto.controlStatus}</if>
            <if test="dto.userNames != null and dto.userNames.size() > 0">
                and m.create_by in
                <foreach item="create_by" collection="dto.userNames" open="(" separator="," close=")">
                    #{create_by}
                </foreach>
            </if>
            <if test="dto.cost!=null">and s.cost > #{dto.cost}</if>
            <if test="dto.parentNode!=null">and m.parent_account_id is not null</if>
            <if test="dto.aids != null and dto.aids.size() > 0">
                and m.advertiser_id in
                <foreach item="aid" collection="dto.aids" open="(" separator="," close=")">
                    #{aid}
                </foreach>
            </if>
            <if test="dto.fund!=null ">
                and (m.fund &gt;= #{dto.fund}
                and m.parent_account_id is not null or s.advertiser_id is not null)
            </if>
            <if test="dto.negativeFund!=null">
                and ((m.fund &lt;= #{dto.negativeFund} or m.fund is null)
                and m.parent_account_id is not null or s.advertiser_id is not null)
            </if>
            <if test="dto.firstDeptIds!=null">
                and d.first_dept_id in
                <foreach item="first_dept_id" collection="dto.firstDeptIds" open="(" separator="," close=")">
                    #{first_dept_id}
                </foreach>
            </if>
        </where>
        limit #{size}
    </select>

    <select id="selectMediaAccountInfoListChildWithNullIndustrys" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select m.id,m.advertiser_id, m.advertiser_name, m.parent_account_id,m.media_type,
        m.access_token,m.report_statistics_error_msg,m.expires_in
        from ads_media_account_info m
        left join
            ads_advert_statistics s
        on s.advertiser_id = m.advertiser_id
            AND   m.media_type = s.media_type
        and s.stat_datetime >= #{startTime} and s.stat_datetime &lt;= #{endTime}
        where m.deleted = 0
        and m.id>#{id}
        and s.stat_datetime >= #{startTime} and s.stat_datetime &lt;= #{endTime}
        and m.parent_account_id is not null
        and (m.first_industry_name is null or m.first_industry_name is null or m.company_name is null)
        <if test="mediaPlatformType != null">and m.media_type=#{mediaPlatformType}</if>
        order by m.id
        limit #{size}
    </select>

    <select id="listMediaAccountInfoByDeptId"  resultType="com.panda.pollen.modules.ads.vo.MediaAccountInfoListVO">
        select advertiser_id aId, advertiser_name  from ads_media_account_info t
        left join sys_user u on u.user_name = t.create_by and u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
        <where>
            and t.deleted = 0
            and u.dept_id in (SELECT dept_id FROM sys_dept WHERE dept_id = #{deptId} or find_in_set(#{deptId}, ancestors))
            <if test="advertiserName != null  and advertiserName != ''">and t.advertiser_name like concat('%', #{advertiserName}, '%')
            </if>
        </where>

    </select>
    <!-- 批量更新巨量名称数据 -->
    <update id="updateMediaAccountNameBatch" parameterType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        update ads_media_account_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="advertiser_name =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.advertiserName!=null and item.advertiserName!='' ">
                        when id=#{item.id} then #{item.advertiserName}
                    </if>
                </foreach>
            </trim>
            update_by ='admin',
            update_time = sysdate()
        </trim>
        where
        id in (
        <foreach collection="list" separator="," item="item" index="index">
            #{item.id}
        </foreach>
        )
    </update>

    <update id="recordMediaAccountReportStatisticsErrorMsg">
        update ads_media_account_info set report_statistics_error_code = #{code}, report_statistics_error_msg = #{errMsg}
        where advertiser_id = #{advertiserId}
    </update>

    <select id="selectDuplicateMediaAccount" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        SELECT
            media_type,
            advertiser_id,
            count( * )
        FROM
            ads_media_account_info
        WHERE
            deleted = 0
          AND media_type IS NOT NULL
          AND advertiser_id IS NOT NULL
        GROUP BY
            media_type,
            advertiser_id
        HAVING count( * ) >1
    </select>

    <select id="selectMediaAccountListByCondition" resultType="com.panda.pollen.modules.ads.domain.MediaAccountInfo">
        select * from ads_media_account_info t
        <where>
            and t.deleted = 0
            <if test="mediaType != null">
                and t.media_type = #{mediaType}
            </if>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and t.advertiser_id in
                <foreach collection="advertiserIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="minFund != null">
                and t.fund &gt;= #{minFund}
            </if>
            <if test="cursorId != null">
                and t.id &gt; #{cursorId}
            </if>
            and t.parent_account_id is not null
        </where>

        <if test="pageSize != null">
            order by t.id asc
            limit #{pageSize}
        </if>

    </select>

</mapper>
