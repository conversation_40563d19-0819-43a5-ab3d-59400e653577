/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './index.html',
    './src/**/*.{vue,js,jsx,ts,tsx}',
    // 添加更多特定路径以确保捕获所有使用 Tailwind 类的文件
    './public/**/*.html',
    './src/**/*.{vue,js,ts,jsx,tsx,html,md}'
  ],
  // 禁用可能与 Element UI 冲突的 Tailwind 基础样式
  corePlugins: {
    preflight: false // 禁用 Tailwind 的基础样式重置
  },
  theme: {
    extend: {
      colors: {
        primary: '#1890ff',
        success: '#13ce66',
        warning: '#ffba00',
        danger: '#ff4949'
      },
      spacing: {
        '15': '60px',
        '65': '260px'
      },
      height: {
        '15': '60px'
      },
      width: {
        '65': '260px'
      },
      margin: {
        '15': '60px'
      },
      zIndex: {
        '40': '40',
        '50': '50'
      }
    }
  },
  plugins: []
}

