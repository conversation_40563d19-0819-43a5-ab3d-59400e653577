//package com.panda.pollen.order.runner;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.date.DateUnit;
//import cn.hutool.core.date.DateUtil;
//import com.alibaba.fastjson2.JSON;
//import com.alibaba.fastjson2.JSONObject;
//import com.panda.pollen.common.enums.ads.MediaAccountConversionEnum;
//import com.panda.pollen.common.exception.ServiceException;
//import com.panda.pollen.common.utils.DateUtils;
//import com.panda.pollen.common.utils.ExceptionUtil;
//import com.panda.pollen.framework.lock.RedisLock;
//import com.panda.pollen.modules.ads.utils.RedissonUtils;
//import com.panda.pollen.scrm.domain.*;
//import com.panda.pollen.scrm.dto.ContactWayLongClickDTO;
//import com.panda.pollen.scrm.enums.ConvertType;
//import com.panda.pollen.scrm.service.*;
//import com.panda.pollen.scrm.utils.FormsResultsPlanIdUtils;
//import com.panda.pollen.wecom.service.WeComCustomerService;
//import com.panda.pollen.wecom.service.WeComTokenService;
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.api.RBlockingDeque;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//import java.util.concurrent.atomic.AtomicBoolean;
//
//import static com.panda.pollen.common.constant.Constants.MA_LONG_CLICK_QUEUE;
//
///**
// * 获客链接[联系我]长按事件延迟队列消费
// *
// * <AUTHOR>
// * @date 2023/06/28
// */
//@Component
//@Slf4j
//public class ContactWayDelayConvertRunner implements ApplicationRunner {
//
//    @Autowired
//    private RedissonUtils redissonUtils;
//
//    @Autowired
//    private ICustomerLinkUserService customerLinkUserService;
//
//    @Autowired
//    private IAuthCorpService authCorpService;
//
//    @Autowired
//    private WeComCustomerService weComCustomerService;
//
//    @Autowired
//    private WeComTokenService weComTokenService;
//
//    @Autowired
//    private IAuthCorpUserCustomerService authCorpUserCustomerService;
//
//    @Autowired
//    private RedisLock redisLock;
//
//    @Autowired
//    private IFormsResultsService formsResultsService;
//
//    @Autowired
//    private IAuthCorpUserService authCorpUserService;
//
//    /**
//     * 消费pdd延迟回传队列
//     */
//    @Async
//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        log.info("获客链接[联系我]长按事件延迟队列消费启动。。。。。。。。");
//        RBlockingDeque<Object> blockingDeque = redissonUtils.getDelayedQueueConsumer(MA_LONG_CLICK_QUEUE);
//        ContactWayLongClickDTO longClickDTO = null;
//        while (true) {
//            try {
//                longClickDTO = (ContactWayLongClickDTO) blockingDeque.take();
//                Date now = new Date();
//
//                // 处理长按事件队列消息信息获取、回传
//                boolean handleSuccess = handleContactWayLongClick(longClickDTO);
//
//                // 如果60秒内，没获取到添加好友信息，继续加到延时队列中
//                long between = DateUtil.between(longClickDTO.getCreateTime(), now, DateUnit.SECOND, true);
//                if (between <= 60 && !handleSuccess) {
//                    redissonUtils.getDelayedQueueProducer(MA_LONG_CLICK_QUEUE).offer(longClickDTO, 10, TimeUnit.SECONDS);
//                }
//
//            } catch (Exception e) {
//                log.error("获客链接[联系我]长按事件延迟队列消费错误，longClickDTO={}", JSON.toJSONString(longClickDTO), e);
//            }
//        }
//    }
//
//    /**
//     * 处理长按事件队列消息信息获取、回传
//     * @param clickDTO
//     * @return
//     */
//    private boolean handleContactWayLongClick(ContactWayLongClickDTO clickDTO) {
//        // 根据获客链接id找到获客链接的成员
//        CustomerLinkUser linkUser = customerLinkUserService.selectOneByLinkId(clickDTO.getLinkId());
//        if (linkUser == null) {
//            throw new ServiceException("处理长按事件队列消息信息时候未找到获客链接成员信息");
//        }
//
//        FormsResults formsResults = formsResultsService.selectByOrderNo(clickDTO.getOrderNo());
//        if (formsResults == null) {
//            log.error("处理长按事件队列消息失败：未知的订单编号");
//            throw new ServiceException("处理长按事件队列消息失败：未知的订单编号");
//        }
//
//        AtomicBoolean handleResult = new AtomicBoolean(false);
//        redisLock.tryLock("ContactWayDelayConvert:" + clickDTO.getLinkId(), () -> {
//            // 根据成员userId获取客户列表
//            AuthCorp authCorp = authCorpService.selectAuthCorpByCorpId(linkUser.getCorpId());
//            String accessToken = weComTokenService.getAccessToken(authCorp.getCorpId(), authCorp.getPermanentCode());
//            JSONObject result = weComCustomerService.getExternalContactList(accessToken, linkUser.getUserId());
//            List<String> newExternalUserIdList = result.getList("external_userid", String.class);
//            if (CollUtil.isEmpty(newExternalUserIdList)) {
//                handleResult.set(false);
//                return;
//            }
//            // 获取数据库现在的记录
//            List<String> existsExternalUserIdList = authCorpUserCustomerService.selectExistsList(linkUser.getUserId());
//
//            // 找出差异性的来，将差异性的加到数据库中
//            List<String> differenceList = CollUtil.subtractToList(newExternalUserIdList, existsExternalUserIdList);
//            if (CollUtil.isEmpty(differenceList)) {
//                handleResult.set(false);
//                return;
//            }
//
//            log.info("找到了渠道活码客户信息差异性，userId={}, newExternalUserIdList={}, existsExternalUserIdList={}", linkUser.getUserId(), JSON.toJSONString(newExternalUserIdList), JSON.toJSONString(existsExternalUserIdList));
//            AuthCorpUser corpUser = authCorpUserService.selectByCorpIdAndUserId(linkUser.getCorpId(), linkUser.getUserId());
//            authCorpUserCustomerService.saveAuthCorpUserCustomers(corpUser, differenceList);
//
//            // 添加CustomerLinkCount记录
//            String externalUserId = differenceList.get(0);
//
//            // 处理链接组内链接明细当天获客数量
//            authCorpUserCustomerService.handleCustomerLinkGroupCount(formsResults, clickDTO.getLinkId());
//
//            // 开始回传加微成功
//            formsResultsService.sendFormConvert(ConvertType.FORM_WECHAT, formsResults, MediaAccountConversionEnum.SUCCESS_CONVERSION);
//
//            // 标记处理成功
//            handleResult.set(true);
//
//        }, () -> handleResult.set(false));
//        return handleResult.get();
//    }
//
//}
