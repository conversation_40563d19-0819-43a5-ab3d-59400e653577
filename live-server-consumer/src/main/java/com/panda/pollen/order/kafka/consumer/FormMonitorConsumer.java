package com.panda.pollen.order.kafka.consumer;

import com.alibaba.fastjson2.JSONObject;
import com.panda.pollen.scrm.service.mongo.MongoFormMonitorService;
import com.panda.pollen.scrm.constants.KafkaConstants;
import com.panda.pollen.scrm.domain.FormsResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 表单监控消费类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FormMonitorConsumer {

    @Value("${monitor.mongo.enabled}")
    private boolean mongoFlag;

    @Autowired
    private MongoFormMonitorService mongoFormMonitorService;

    /**
     * 表单监控消费事件
     *
     * @param objList
     * @param ack
     */
    @KafkaListener(topics = KafkaConstants.TOPIC_FORM_ORDER_MONITOR, containerFactory = "kafkaListenerContainerFactoryBatch", id = KafkaConstants.TOPIC_FORM_ORDER_MONITOR)
    public void getFormMonitorMessage(List<ConsumerRecord<?, String>> objList, Acknowledgment ack) {
        try {
            objList.forEach(obj -> {
                Optional<String> message = Optional.ofNullable(obj.value());
                if (!message.isPresent()) {
                    // 消息不存在的情况
                    log.error("消息格式不正确:{}", obj);
                    return;
                }
                String msg = message.get();
                FormsResults formsResults = JSONObject.parseObject(msg, FormsResults.class);
                log.info("当前消费ClickId：{}", formsResults.getClickId());
                if (mongoFlag) {
                    try {
                        this.mongoFormMonitorService.insertFormMonitorLinkService(formsResults);
                    } catch (Exception e) {
                        log.error("mongodb写入form_monitor失败", e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("消费失败:{}", e.getMessage(), e);
        } finally {
            ack.acknowledge();
        }
    }
}
