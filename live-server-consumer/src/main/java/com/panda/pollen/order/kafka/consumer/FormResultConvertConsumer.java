package com.panda.pollen.order.kafka.consumer;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.panda.pollen.scrm.constants.KafkaConstants;
import com.panda.pollen.common.utils.ExceptionUtil;
import com.panda.pollen.order.form.IFormConvertConsumerService;
import com.panda.pollen.scrm.domain.AuthCorp;
import com.panda.pollen.scrm.domain.AuthCorpUserCustomer;
import com.panda.pollen.scrm.dto.FormsResultConvertDTO;
import com.panda.pollen.scrm.service.IAuthCorpService;
import com.panda.pollen.scrm.service.IAuthCorpUserCustomerService;
import com.panda.pollen.scrm.vo.AuthCorpUserCustomerSyncVO;
import com.panda.pollen.scrm.vo.CustomerDetailSyncDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class FormResultConvertConsumer {

    @Autowired
    private IFormConvertConsumerService formConvertConsumerService;

    @Autowired
    private IAuthCorpService authCorpService;

    @Autowired
    private IAuthCorpUserCustomerService authCorpUserCustomerService;


    /**
     * 表单回传消费事件
     *
     * @param objList
     * @param ack
     */
    @KafkaListener(topics = KafkaConstants.TOPIC_FORM_ORDER_CONVERT, containerFactory = "kafkaListenerContainerFactoryBatch", id = KafkaConstants.TOPIC_FORM_ORDER_CONVERT)
    public void getFormConvertMessage(List<ConsumerRecord<?, String>> objList, Acknowledgment ack) {
        try {
            List<FormsResultConvertDTO> formsResultConvertDTOList = buildFormsResultConvertDTOList(objList);
            formConvertConsumerService.formOrderConvert(formsResultConvertDTOList);
        } catch (Exception e) {
            log.error("表单回传消费失败：", e);
        } finally {
            ack.acknowledge();
        }
    }

    /**
     * 企业微信加粉用户信息同步处理
     *
     * @param objList
     * @param ack
     */
    @KafkaListener(topics = KafkaConstants.TOPIC_FORM_SYNC_USER_INFO, containerFactory = "kafkaListenerContainerFactoryBatch", id = KafkaConstants.TOPIC_FORM_SYNC_USER_INFO)
    public void syncUserInfoMessage(List<ConsumerRecord<?, String>> objList, Acknowledgment ack) {
        try {
            List<CustomerDetailSyncDTO> list = buildSyncUserInfoList(objList);
            // 为空下面不执行
            if (list.isEmpty()) {
                return;
            }
            // 同步企业微信加粉用户信息数据
            syncExternalUserInfo(list);
        } catch (Exception e) {
            log.error("消费失败：{}", ExceptionUtil.getExceptionMessage(e));
        } finally {
            ack.acknowledge();
        }
    }


    /**
     * 构建表单回传DTO对象
     *
     * @param objList
     * @return
     */
    private List<FormsResultConvertDTO> buildFormsResultConvertDTOList(List<ConsumerRecord<?, String>> objList) {
        List<FormsResultConvertDTO> formsResultConvertDTOList = new ArrayList<>();
        try {
            objList.forEach(obj -> {
                Optional<String> message = Optional.ofNullable(obj.value());
                if (!message.isPresent()) {
                    // 消息不存在的情况
                    log.error("消息格式不正确:{}", obj);
                    return;
                }
                String msg = message.get();
                FormsResultConvertDTO formsResultConvertDTO = JSON.parseObject(msg, FormsResultConvertDTO.class);
                // 判断消息列表中是否已经存在
                boolean exists = false;
                for (FormsResultConvertDTO item : formsResultConvertDTOList) {
                    if (ObjectUtil.equals(formsResultConvertDTO.getConvertType(), item.getConvertType())
                            && ObjectUtil.equals(formsResultConvertDTO.getFormsResults().getOrderNo(), item.getFormsResults().getOrderNo())) {
                        exists = true;
                        break;
                    }
                }
                if (!exists) {
                    formsResultConvertDTOList.add(formsResultConvertDTO);
                }
            });
        } catch (Exception e) {
            log.error("消费失败:{},数据:{}", e.getMessage(), formsResultConvertDTOList, e);
            throw e;
        }
        return formsResultConvertDTOList;
    }

    /**
     * 构建表单回传DTO对象
     *
     * @param objList
     * @return
     */
    private List<CustomerDetailSyncDTO> buildSyncUserInfoList(List<ConsumerRecord<?, String>> objList) {
        List<CustomerDetailSyncDTO> customerDetailSyncDTOList = new ArrayList<>();
        try {
            objList.forEach(obj -> {
                Optional<String> message = Optional.ofNullable(obj.value());
                if (!message.isPresent()) {
                    // 消息不存在的情况
                    log.error("消息格式不正确:{}", obj);
                    return;
                }
                String msg = message.get();
                CustomerDetailSyncDTO customerDetailSyncDTO = JSON.parseObject(msg, CustomerDetailSyncDTO.class);
                customerDetailSyncDTOList.add(customerDetailSyncDTO);

            });
        } catch (Exception e) {
            log.error("消费失败:{},数据:{}", e.getMessage(), customerDetailSyncDTOList, e);
            throw e;
        }
        return customerDetailSyncDTOList;
    }

    /**
     * 同步企业微信加粉用户信息数据
     *
     * @param customerLinkCountList
     */
    public void syncExternalUserInfo(List<CustomerDetailSyncDTO> customerLinkCountList) {
        for (CustomerDetailSyncDTO customerDetailSyncDTO : customerLinkCountList) {
            String corpId = customerDetailSyncDTO.getCorpId();
            AuthCorpUserCustomer userCustomer = authCorpUserCustomerService.getById(customerDetailSyncDTO.getId());
            if (userCustomer == null) {
                log.error("通过消费者服务，同步客户详情信息的时候，数据库中找不到客户信息");
                continue;
            }
            AuthCorp authCorp = authCorpService.selectAuthCorpByCorpId(corpId, false);
            AuthCorpUserCustomerSyncVO userCustomerSyncVO = new AuthCorpUserCustomerSyncVO();
            userCustomerSyncVO.setId(userCustomer.getId());
            userCustomerSyncVO.setUserId(userCustomer.getUserId());
            userCustomerSyncVO.setExternalUserId(userCustomer.getExternalUserId());
            userCustomerSyncVO.setCorpId(authCorp.getCorpId());
            userCustomerSyncVO.setPermanentCode(authCorp.getPermanentCode());
            authCorpUserCustomerService.syncUserCustomerDetail(userCustomerSyncVO);

        }
    }

}
