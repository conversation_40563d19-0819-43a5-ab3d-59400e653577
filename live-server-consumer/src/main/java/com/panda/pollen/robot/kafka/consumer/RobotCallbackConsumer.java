
/**  
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：RobotInstantlyPushProducer.java <br>
 * Package：com.panda.pollen.robot.producer <br> 
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2025年6月25日 下午10:46:17 <br>
 * @version v1.0 <br>
 */ 
package com.panda.pollen.robot.kafka.consumer;

import java.util.List;
import java.util.Optional;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.panda.pollen.common.constant.Constants;
import com.panda.pollen.robot.consts.RobotConst;
import com.panda.pollen.robot.converter.MessageDetailsDOConvert;
import com.panda.pollen.robot.dto.RobotMessageDetailsDTO;
import com.panda.pollen.robot.enums.MsgExecuteStatus;
import com.panda.pollen.robot.service.RobotMessageDetailsService;
import com.panda.robot.domain.dto.CallBackDataDTO;
import com.panda.robot.enums.CallbackMode;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**   
 * ClassName：com.panda.pollen.robot.producer.RobotInstantlyPushProducer <br>
 * Description：Robot回调消息Kafka消费者 <br>
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2025年6月25日 下午10:46:17 <br>
 * @version v1.0 <br>  
 */
@Slf4j
@Component
public class RobotCallbackConsumer {

	@Autowired
    private RobotMessageDetailsService detailService;
	
    @KafkaListener(topics = RobotConst.ROBOT_CALLBACK_TOPIC, containerFactory = "kafkaListenerContainerFactoryBatch",
        id = "robotCallbackTopic")
    public void robotMessageConsumer(List<ConsumerRecord<?, String>> records, Acknowledgment ack) {
        try {
            records.forEach(d -> {
                Optional<String> data = Optional.ofNullable(d.value());
                if (!data.isPresent()) {
                    // 消息不存在的情况
                    log.error("消息格式不正确:{}", d);
                    return;
                } 
                log.info("【CBK-Consumer】--->>> {}", d.value());
                JSONObject obj = JSON.parseObject(d.value());
                String mode = obj.getString("mode");
                if(CallbackMode.isReceipt(mode)) {
                    log.info("【Receipt】------>>{}", obj);
                }
                if (CallbackMode.isCallback(mode)) {
                    CallBackDataDTO cd = JSON.to(CallBackDataDTO.class, obj.get("data"));
                    Integer status = Constants.LOGIN_SUCCESS.equalsIgnoreCase(cd.getStatus()) ? 1 : -1;
                    RobotMessageDetailsDTO detail = RobotMessageDetailsDTO.builder().robotTaskId(cd.getTaskId())
                        .remark(cd.getMessage()) 
                        .execStatus(MsgExecuteStatus.get(status)).build();
                    log.info("【CallbackUp】<<<--- {}", detail);
                    detail.setUpdateTime(DateUtil.date());
                    if(detailService.updateByTaskId(MessageDetailsDOConvert.INSTANCE.toDO(detail))) {
                    	log.info("【Consumer-Update】{} --->> 更新成功", detail.getRobotTaskId());
                    }
                }
            });
        } catch (Exception e) {
            log.error("【Robot Kafka Linstener Error】 ---->>> {}", e);
        } finally {
            ack.acknowledge();
        }
       
    } 
    
}
